@echo off
REM JTAG Writer 构建脚本 (Windows)

echo 开始构建 JTAG Writer...

REM 检查是否存在构建目录
if not exist build (
    echo 创建构建目录...
    mkdir build
)

REM 进入构建目录
cd build

REM 运行CMake配置
echo 配置项目...
cmake .. -G "MinGW Makefiles"
if %errorlevel% neq 0 (
    echo CMake配置失败！
    pause
    exit /b 1
)

REM 编译项目
echo 编译项目...
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 构建完成！
echo 可执行文件位于: build\jtag_writer.exe

REM 返回上级目录
cd ..

echo 运行 --help 查看使用说明:
build\jtag_writer.exe --help

pause
