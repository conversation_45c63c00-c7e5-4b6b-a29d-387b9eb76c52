#include "jtag_writer.h"
#include "logger.h"

static bool g_initialized = false;

/**
 * 初始化JTAG Writer
 */
jtag_error_t jtag_writer_init(void) {
    if (g_initialized) {
        return JTAG_SUCCESS;
    }
    
#ifdef _WIN32
    // 初始化Winsock
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        return JTAG_ERROR_NETWORK_ERROR;
    }
#endif
    
    g_initialized = true;
    return JTAG_SUCCESS;
}

/**
 * 清理JTAG Writer
 */
void jtag_writer_cleanup(void) {
    if (!g_initialized) {
        return;
    }
    
#ifdef _WIN32
    WSACleanup();
#endif
    
    g_initialized = false;
}

/**
 * 获取错误字符串
 */
const char* jtag_error_string(jtag_error_t error) {
    switch (error) {
        case JTAG_SUCCESS:
            return "成功";
        case JTAG_ERROR_INVALID_ARGS:
            return "无效参数";
        case JTAG_ERROR_FILE_NOT_FOUND:
            return "文件未找到";
        case JTAG_ERROR_NETWORK_ERROR:
            return "网络错误";
        case JTAG_ERROR_OPENOCD_ERROR:
            return "OpenOCD错误";
        case JTAG_ERROR_CONFIG_ERROR:
            return "配置错误";
        case JTAG_ERROR_FLASH_ERROR:
            return "Flash操作错误";
        case JTAG_ERROR_MEMORY_ERROR:
            return "内存错误";
        case JTAG_ERROR_TIMEOUT:
            return "超时错误";
        case JTAG_ERROR_UNKNOWN:
        default:
            return "未知错误";
    }
}
