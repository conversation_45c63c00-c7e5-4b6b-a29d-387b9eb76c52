# Example of a udp server which sends a response

# Listen on port 20000. No host specified means 0.0.0.0
set s [socket dgram.server 20000]

# For each request...
$s readable {
	# Get the request (max 80 chars) - need the source address
	set buf [$s recvfrom 80 addr]

	puts -nonewline "read '$buf' from $addr"

	try {
		set result "$buf = [expr $buf]"
	} on error {msg} {
		set result "Error: $buf => $msg"
	}

	puts ", sending '$result' to $addr"

	# Send the result back to where it came from
	$s sendto $result $addr
}

vwait done
