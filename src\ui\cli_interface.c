#include "cli_interface.h"
#include "logger.h"

// 命令行选项定义
static const cli_option_t g_options[] = {
    {'h', "help",        false, "显示帮助信息"},
    {'v', "version",     false, "显示版本信息"},
    {'V', "verbose",     false, "详细输出模式"},
    {'c', "config",      true,  "指定配置文件"},
    {'f', "file",        true,  "指定固件文件"},
    {'t', "target",      true,  "指定目标类型"},
    {'H', "host",        true,  "指定OpenOCD主机地址"},
    {'p', "port",        true,  "指定OpenOCD TCL端口"},
    {'P', "telnet-port", true,  "指定OpenOCD Telnet端口"},
    {'a', "address",     true,  "指定基地址"},
    {'T', "type",        true,  "指定文件类型 (bin/hex/elf/s19)"},
    {'m', "mode",        true,  "指定操作模式 (write/verify/erase/read)"},
    {'l', "log-level",   true,  "指定日志级别 (error/warn/info/debug)"},
    {0,   "erase",       false, "仅擦除Flash"},
    {0,   "verify",      false, "仅验证固件"},
    {0,   "read",        false, "读取Flash内容"},
    {0,   "no-erase",    false, "写入前不擦除"},
    {0,   "no-verify",   false, "写入后不验证"},
    {0,   "no-reset",    false, "写入后不重置"},
    {0,   NULL,          false, NULL}
};

/**
 * 初始化CLI界面
 */
jtag_error_t cli_init(void) {
    return JTAG_SUCCESS;
}

/**
 * 清理CLI界面
 */
void cli_cleanup(void) {
    // 清理资源
}

/**
 * 显示帮助信息
 */
void cli_show_help(const char* program_name) {
    printf("JTAG Writer - OpenOCD前端烧写工具\n\n");
    printf("用法: %s [选项] [固件文件]\n\n", program_name);
    
    printf("选项:\n");
    for (int i = 0; g_options[i].long_opt || g_options[i].short_opt; i++) {
        const cli_option_t* opt = &g_options[i];
        
        if (opt->short_opt && opt->long_opt) {
            printf("  -%c, --%-15s", opt->short_opt, opt->long_opt);
        } else if (opt->long_opt) {
            printf("      --%-15s", opt->long_opt);
        } else {
            continue;
        }
        
        if (opt->has_arg) {
            printf(" <参数>");
        }
        
        printf("  %s\n", opt->description);
    }
    
    printf("\n示例:\n");
    cli_show_examples();
    
    printf("\n支持的目标类型:\n");
    cli_show_supported_targets();
    
    printf("\n支持的文件类型:\n");
    cli_show_supported_file_types();
}

/**
 * 显示版本信息
 */
void cli_show_version(void) {
    printf("JTAG Writer v%s\n", JTAG_WRITER_VERSION);
    printf("基于OpenOCD的固件烧写工具\n");
    printf("编译时间: %s %s\n", __DATE__, __TIME__);
}

/**
 * 显示使用示例
 */
void cli_show_examples(void) {
    printf("  # 烧写固件到STM32F103\n");
    printf("  %s -f firmware.bin -t stm32f1x -a 0x08000000\n\n", "jtag_writer");
    
    printf("  # 验证已烧写的固件\n");
    printf("  %s --verify -f firmware.bin -t stm32f1x\n\n", "jtag_writer");
    
    printf("  # 擦除整个Flash\n");
    printf("  %s --erase -t stm32f1x\n\n", "jtag_writer");
    
    printf("  # 连接到远程OpenOCD服务器\n");
    printf("  %s -H ************* -p 6666 -f firmware.hex\n\n", "jtag_writer");
    
    printf("  # 使用配置文件\n");
    printf("  %s -c config.conf -f firmware.elf\n", "jtag_writer");
}

/**
 * 显示支持的目标类型
 */
void cli_show_supported_targets(void) {
    printf("  stm32f1x    - STM32F1系列\n");
    printf("  stm32f2x    - STM32F2系列\n");
    printf("  stm32f4x    - STM32F4系列\n");
    printf("  stm32f7x    - STM32F7系列\n");
    printf("  stm32h7x    - STM32H7系列\n");
    printf("  stm32l4x    - STM32L4系列\n");
    printf("  at91sam3    - Atmel SAM3系列\n");
    printf("  at91sam4    - Atmel SAM4系列\n");
    printf("  lpc2000     - NXP LPC2000系列\n");
    printf("  lpc1700     - NXP LPC1700系列\n");
}

/**
 * 显示支持的文件类型
 */
void cli_show_supported_file_types(void) {
    printf("  bin         - 二进制文件\n");
    printf("  hex         - Intel HEX文件\n");
    printf("  elf         - ELF可执行文件\n");
    printf("  s19         - Motorola S-record文件\n");
    printf("  auto        - 自动检测（根据文件扩展名）\n");
}

/**
 * 进度显示回调函数
 */
void cli_progress_callback(int percentage, const char* message) {
    static int last_percentage = -1;
    
    if (percentage != last_percentage) {
        printf("\r[");
        int pos = percentage / 2; // 50个字符的进度条
        for (int i = 0; i < 50; i++) {
            if (i < pos) {
                printf("=");
            } else if (i == pos) {
                printf(">");
            } else {
                printf(" ");
            }
        }
        printf("] %3d%% %s", percentage, message ? message : "");
        fflush(stdout);
        
        if (percentage >= 100) {
            printf("\n");
        }
        
        last_percentage = percentage;
    }
}

/**
 * 显示错误信息
 */
void cli_show_error(jtag_error_t error, const char* message) {
    fprintf(stderr, "\033[31m错误\033[0m: %s", message ? message : jtag_error_string(error));
    if (message && error != JTAG_SUCCESS) {
        fprintf(stderr, " (%s)", jtag_error_string(error));
    }
    fprintf(stderr, "\n");
}

/**
 * 显示成功信息
 */
void cli_show_success(const char* message) {
    printf("\033[32m成功\033[0m: %s\n", message);
}

/**
 * 显示警告信息
 */
void cli_show_warning(const char* message) {
    printf("\033[33m警告\033[0m: %s\n", message);
}

/**
 * 显示信息
 */
void cli_show_info(const char* message) {
    printf("\033[36m信息\033[0m: %s\n", message);
}

/**
 * 询问用户确认
 */
bool cli_ask_confirmation(const char* message) {
    printf("%s (y/N): ", message);
    fflush(stdout);
    
    char response[10];
    if (fgets(response, sizeof(response), stdin)) {
        return (response[0] == 'y' || response[0] == 'Y');
    }
    
    return false;
}

/**
 * 等待用户按键
 */
void cli_wait_for_key(const char* message) {
    printf("%s", message ? message : "按任意键继续...");
    fflush(stdout);
    getchar();
}

/**
 * 显示Flash bank信息
 */
void cli_show_flash_banks(const flash_bank_info_t* banks, int num_banks) {
    if (!banks || num_banks <= 0) {
        printf("没有找到Flash bank信息\n");
        return;
    }
    
    printf("Flash Banks:\n");
    printf("ID  驱动名称     基地址      大小\n");
    printf("--- ------------ ----------- -----------\n");
    
    for (int i = 0; i < num_banks; i++) {
        const flash_bank_info_t* bank = &banks[i];
        printf("%-3d %-12s 0x%08x  %u KB\n", 
               bank->bank_id, bank->driver_name, 
               bank->base_address, bank->size / 1024);
    }
}

/**
 * 显示烧写结果
 */
void cli_show_flash_result(const flash_result_t* result) {
    if (!result) {
        return;
    }
    
    if (result->success) {
        printf("\n操作成功完成:\n");
        printf("  处理字节数: %u\n", result->bytes_processed);
        printf("  总字节数: %u\n", result->total_bytes);
        printf("  耗时: %.2f 秒\n", result->elapsed_time);
        
        if (result->total_bytes > 0 && result->elapsed_time > 0) {
            double speed = result->bytes_processed / result->elapsed_time / 1024.0;
            printf("  平均速度: %.2f KB/s\n", speed);
        }
    } else {
        printf("\n操作失败:\n");
        printf("  错误信息: %s\n", result->error_message);
        if (result->bytes_processed > 0) {
            printf("  已处理字节数: %u\n", result->bytes_processed);
        }
    }
}

/**
 * 显示配置信息
 */
void cli_show_config(const jtag_writer_config_t* config) {
    if (!config) {
        return;
    }
    
    config_print(config);
}

/**
 * 解析命令行参数
 */
jtag_error_t cli_parse_args(int argc, char* argv[], cli_args_t* args) {
    if (!argv || !args) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    // 初始化参数结构
    memset(args, 0, sizeof(cli_args_t));
    args->openocd_tcl_port = -1;
    args->openocd_telnet_port = -1;
    args->file_type = FIRMWARE_TYPE_AUTO;
    args->flash_mode = FLASH_MODE_WRITE;
    args->log_level = LOG_LEVEL_INFO;
    args->erase_before_write = true;
    args->verify_after_write = true;
    args->reset_after_write = true;

    // 简化的命令行解析
    for (int i = 1; i < argc; i++) {
        char* arg = argv[i];

        if (strcmp(arg, "-h") == 0 || strcmp(arg, "--help") == 0) {
            args->help_requested = true;
        } else if (strcmp(arg, "-v") == 0 || strcmp(arg, "--version") == 0) {
            args->version_requested = true;
        } else if (strcmp(arg, "-V") == 0 || strcmp(arg, "--verbose") == 0) {
            args->verbose = true;
        } else if (strcmp(arg, "-c") == 0 || strcmp(arg, "--config") == 0) {
            if (i + 1 < argc) {
                strncpy(args->config_file, argv[++i], sizeof(args->config_file) - 1);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "选项 -c 需要参数");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-f") == 0 || strcmp(arg, "--file") == 0) {
            if (i + 1 < argc) {
                strncpy(args->firmware_file, argv[++i], sizeof(args->firmware_file) - 1);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "选项 -f 需要参数");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-t") == 0 || strcmp(arg, "--target") == 0) {
            if (i + 1 < argc) {
                strncpy(args->target_type, argv[++i], sizeof(args->target_type) - 1);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "选项 -t 需要参数");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-H") == 0 || strcmp(arg, "--host") == 0) {
            if (i + 1 < argc) {
                strncpy(args->openocd_host, argv[++i], sizeof(args->openocd_host) - 1);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "选项 -H 需要参数");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-p") == 0 || strcmp(arg, "--port") == 0) {
            if (i + 1 < argc) {
                args->openocd_tcl_port = atoi(argv[++i]);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "选项 -p 需要参数");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-P") == 0 || strcmp(arg, "--telnet-port") == 0) {
            if (i + 1 < argc) {
                args->openocd_telnet_port = atoi(argv[++i]);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "选项 -P 需要参数");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-a") == 0 || strcmp(arg, "--address") == 0) {
            if (i + 1 < argc) {
                jtag_error_t ret = cli_parse_address(argv[++i], &args->base_address);
                if (ret != JTAG_SUCCESS) {
                    cli_show_error(ret, "无效的地址格式");
                    return ret;
                }
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "选项 -a 需要参数");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-T") == 0 || strcmp(arg, "--type") == 0) {
            if (i + 1 < argc) {
                args->file_type = cli_parse_file_type(argv[++i]);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "选项 -T 需要参数");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-m") == 0 || strcmp(arg, "--mode") == 0) {
            if (i + 1 < argc) {
                args->flash_mode = cli_parse_flash_mode(argv[++i]);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "选项 -m 需要参数");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-l") == 0 || strcmp(arg, "--log-level") == 0) {
            if (i + 1 < argc) {
                args->log_level = logger_parse_level(argv[++i]);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "选项 -l 需要参数");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "--erase") == 0) {
            args->flash_mode = FLASH_MODE_ERASE;
        } else if (strcmp(arg, "--verify") == 0) {
            args->flash_mode = FLASH_MODE_VERIFY;
        } else if (strcmp(arg, "--read") == 0) {
            args->flash_mode = FLASH_MODE_READ;
        } else if (strcmp(arg, "--no-erase") == 0) {
            args->erase_before_write = false;
        } else if (strcmp(arg, "--no-verify") == 0) {
            args->verify_after_write = false;
        } else if (strcmp(arg, "--no-reset") == 0) {
            args->reset_after_write = false;
        } else if (arg[0] != '-') {
            // 非选项参数，假设是固件文件
            if (strlen(args->firmware_file) == 0) {
                strncpy(args->firmware_file, arg, sizeof(args->firmware_file) - 1);
            }
        } else {
            cli_show_error(JTAG_ERROR_INVALID_ARGS, "未知选项");
            printf("使用 --help 查看帮助信息\n");
            return JTAG_ERROR_INVALID_ARGS;
        }
    }

    return JTAG_SUCCESS;
}

/**
 * 从字符串解析文件类型
 */
firmware_type_t cli_parse_file_type(const char* type_str) {
    if (!type_str) {
        return FIRMWARE_TYPE_AUTO;
    }

    if (strcasecmp(type_str, "bin") == 0) {
        return FIRMWARE_TYPE_BIN;
    } else if (strcasecmp(type_str, "hex") == 0) {
        return FIRMWARE_TYPE_HEX;
    } else if (strcasecmp(type_str, "elf") == 0) {
        return FIRMWARE_TYPE_ELF;
    } else if (strcasecmp(type_str, "s19") == 0 || strcasecmp(type_str, "srec") == 0) {
        return FIRMWARE_TYPE_S19;
    } else if (strcasecmp(type_str, "auto") == 0) {
        return FIRMWARE_TYPE_AUTO;
    }

    return FIRMWARE_TYPE_AUTO;
}

/**
 * 从字符串解析烧写模式
 */
flash_mode_t cli_parse_flash_mode(const char* mode_str) {
    if (!mode_str) {
        return FLASH_MODE_WRITE;
    }

    if (strcasecmp(mode_str, "write") == 0) {
        return FLASH_MODE_WRITE;
    } else if (strcasecmp(mode_str, "verify") == 0) {
        return FLASH_MODE_VERIFY;
    } else if (strcasecmp(mode_str, "erase") == 0) {
        return FLASH_MODE_ERASE;
    } else if (strcasecmp(mode_str, "read") == 0) {
        return FLASH_MODE_READ;
    }

    return FLASH_MODE_WRITE;
}

/**
 * 从字符串解析地址
 */
jtag_error_t cli_parse_address(const char* addr_str, uint32_t* address) {
    if (!addr_str || !address) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    char* endptr;
    unsigned long addr = strtoul(addr_str, &endptr, 0);

    if (*endptr != '\0') {
        return JTAG_ERROR_INVALID_ARGS;
    }

    *address = (uint32_t)addr;
    return JTAG_SUCCESS;
}
