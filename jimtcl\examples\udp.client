# Example of sending from an unconnected socket

set s [socket dgram]

foreach i [range 1 5] {
	# Specify the address and port with sendto
	$s sendto "$i + $i + 10" 127.0.0.1:20000

	# Receive the response - max length of 100
	puts [$s recvfrom 100]
}

$s close

# Now sending via a connected udp socket

set s [socket dgram 127.0.0.1:20000]
$s buffering none

foreach i [range 5 10] {
	# Socket is connected, so can just use puts here
	# No need to flush because we set 'buffering none' above.
	$s puts -nonewline "$i * $i"
	#$s flush

	# Receive the response - max length of 100
	puts [$s recvfrom 100]
}
