/** @page targetnotarm OpenOCD Non-ARM Targets

This page describes outstanding issues w.r.t. non-ARM targets.

@section targetnotarmflash Flash drivers

The flash drivers contain ARM32 code that is used
to execute code on the target.

This needs to be handled in some CPU independent
manner.

The ocl and ecos flash drivers compile the flash
driver code to run on the target on the developer
machine.

The ocl and ecos flash drivers should be unified
and instructions should be written on how to
compile the target flash drivers. Perhaps
using automake?


eCos has CFI driver that could probably be compiled
for all targets. The trick is to figure out a
way to make the compiled flash drivers work
on all target memory maps + sort out all the
little details

@section targetnotarm32v64 32 vs. 64 bit

Currently OpenOCD only supports 32 bit targets.

Adding 64 bit support would be nice but there
hasn't been any call for it in the openocd development
mailing list

@section targetnotarmsupport Target Support

target.h is relatively CPU agnostic and
the intention is to move in the direction of less
instruction set specific.

Non-CPU targets are also supported, but there isn't
a lot of activity on it in the mailing list currently.
An example is FPGA programming support via JTAG,
but also flash chips can be programmed directly
using JTAG.

@section targetnotarmphy non-JTAG physical layer

JTAG is not the only physical protocol used to talk to
CPUs.

OpenOCD does not today have targets that use non-JTAG.

The actual physical layer is a relatively modest part
of the total OpenOCD system.


@section targetnotarmppc PowerPC

there exists open source implementations of PowerPC
target manipulation, but there hasn't been a lot
of activity in the mailing list.

@section targetnotarmmips MIPS

Currently OpenOCD has a MIPS target defined. This is the
first non-ARM example of a CPU target

 */
