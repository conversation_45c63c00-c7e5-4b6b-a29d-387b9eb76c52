/** @page scripting Scripting Overview

@section scriptingisnt What scripting will not do

The scripting support is intended for developers of OpenOCD.
It is not the intention that normal OpenOCD users will
use tcl scripting extensively, write lots of clever scripts,
or contribute back to OpenOCD.

Target scripts can contain new procedures that end users may
tinker to their needs without really understanding tcl.

Since end users are not expected to mess with the scripting
language, the choice of language is not terribly important
to those same end users.

<PERSON> was chosen as it was easy to integrate, works
great in an embedded environment and <PERSON><PERSON><PERSON><PERSON>
had experience with it.

@section scriptinguses Uses of scripting

Default implementation of procedures in tcl/procedures.tcl.

- Polymorphic commands for target scripts.
  - there will be added some commands in Tcl that the target
    scripts can replace.
  - produce \<productionfile\> \<serialnumber\>. Default implementation
    is to ignore serial number and write a raw binary file
    to beginning of first flash. Target script can dictate
    file format and structure of serialnumber. Tcl allows
    an argument to consist of e.g. a list so the structure of
    the serial number is not limited to a single string.
  - reset handling. Precise control of how srst, trst &
    tms is handled.
- replace some parts of the current command line handler.
  This is only to simplify the implementation of OpenOCD
  and will have no externally visible consequences.
  Tcl has an advantage in that it's syntax is backwards
  compatible with the current OpenOCD syntax.
- external scripting. Low level tcl functions will be defined
  that return machine readable output. These low level tcl
  functions constitute the tcl api. flash_banks is such
  a low level tcl proc. "flash banks" is an example of
  a command that has human readable output. The human
  readable output is expected to change in between versions
  of OpenOCD. The output from flash_banks may not be
  in the preferred form for the client. The client then
  has two choices a) parse the output from flash_banks
  or b) write a small piece of tcl to output the
  flash_banks output to a more suitable form. The latter may
  be simpler.


@section scriptingexternal External scripting

The embedded Jim Tcl interpreter in OpenOCD is very limited
compared to any full scale PC hosted scripting language.

The goal is to keep the internal Jim Tcl interpreter as
small as possible and allow any advanced scripting,
especially scripting that interacts with the host,
run on the host and talk to OpenOCD via the TCP/IP
scripting connection.

Another problem with Jim Tcl is that there is no debugger
for it.

With a bit of trickery it should be possible to run Jim
Tcl scripts under a Tcl interpreter on a PC. The advantage
would be that the Jim Tcl scripts could be debugged using
a standard PC Tcl debugger.

The rough idea is to write an unknown proc that sends
unknown commands to OpenOCD.

Basically a PC version of startup.tcl. Patches most
gratefully accepted! :-)

 */
