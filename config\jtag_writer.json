{"openocd": {"host": "localhost", "tcl_port": 6666, "telnet_port": 4444, "timeout_ms": 5000}, "flash": {"firmware_file": "", "target_type": "stm32f1x", "base_address": "0x08000000", "file_type": "auto", "mode": "write", "erase_before_write": true, "verify_after_write": true, "reset_after_write": true}, "logging": {"level": "info", "output": "console", "file": "jtag_writer.log", "timestamp": true, "color": true}}