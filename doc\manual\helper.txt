/** @page helperdocs OpenOCD Helper APIs

OpenOCD uses several low-level APIs as the foundation for high-level APIs:

  - @subpage helperporting
  - @subpage helperjim
  - @subpage helpercommand
  - @subpage helperlogging
  - @subpage helperbuffers

This section needs to be expanded.

 */

/** @page helperporting OpenOCD Types/Portability APIs

This section needs to be expanded to describe OpenOCD's type and
portability API.

 */

/** @page helperjim OpenOCD Jim API

The Jim API provides access to a small-footprint TCL implementation.

Visit http://jim.tcl.tk/ for more information on <PERSON>.

This section needs to be expanded to describe OpenOCD's Jim API.

 */

/** @page helpercommand OpenOCD Command API

OpenOCD's command API allows modules to register callbacks that are then
available to the scripting services.  It provides the mechanism for
these commands to be dispatched to the module using a standard
interface.  It provides macros for defining functions that use and
extend this interface.

@section helpercmdhandler Command Handlers

Command handlers are functions with a particular signature, which can
be extended by modules for passing additional parameters to helpers or
another layer of handlers.

@subsection helpercmdhandlerdef Defining and Calling Command Handlers

These functions should be defined using the @c COMMAND_HANDLER macro.
These methods must be defined as static, as their principal entry point
should be the run_command dispatch mechanism.

Command helper functions that require access to the full set of
parameters should be defined using the @c COMMAND_HELPER.  These must be
declared static by you, as sometimes you might want to share a helper
among several files (e.g. @c s3c24xx_nand.h).

Both types of routines must be called using the @c CALL_COMMAND_HANDLER macro.
Calls using this macro to normal handlers require the name of the command
handler (which can be a name or function pointer).  Calls to helpers and
derived handlers must pass those extra parameters specified by their
definitions; however, lexical capture is used for the core parameters.
This dirty trick is being used as a stop-gap measure while the API is
migrated to one that passes a pointer to a structure containing the
same ingredients.  At that point, this macro will be removed and callers
will be able to use direct invocations.

Thus, the following macros can be used to define and call command
handlers or helpers:

- @c COMMAND_HANDLER - declare or define a command handler.
- @c COMMAND_HELPER - declare or define a derived command handler or helper.
- @c CALL_COMMAND_HANDLER - call a command handler/helper.

@subsection helpercmdhandlermacros Command Handler Macros

In addition, the following macros may be used in the context of
command handlers and helpers:
- @c CMD_CTX - the current @c command_context
- @c CMD_NAME - invoked command name
- @c CMD_ARGC - the number of command arguments
- @c CMD_ARGV - array of command argument strings

@section helpercmdregister Command Registration

In order to use a command handler, it must be registered with the
command subsystem.  All commands are registered with command_registration
structures, specifying the name of the command, its handler, its allowed
mode(s) of execution, and strings that provide usage and help text.
A single handler may be registered using multiple names, but any name
may have only one handler associated with it.

The @c register_command() and @c register_commands() functions provide
registration, while the @c unregister_command() and
@c unregister_all_commands() functions will remove existing commands.
These may be called at any time, allowing the command set to change in
response to system actions.

@subsection helpercmdjim Jim Command Registration

The command_registration structure provides support for registering
native Jim command handlers (@c jim_handler) too.  For these handlers,
the module can provide help and usage support; however, this mechanism
allows Jim handlers to be called as sub-commands of other commands.
These commands may be registered with a private data value (@c
jim_handler_data) that will be available when called, as with low-level
Jim command registration.

A command may have a normal @c handler or a @c jim_handler, but not both.

@subsection helpercmdregisterchains Command Chaining

When using register_commands(), the array of commands may reference
other arrays.  When the @c chain field is filled in a
command_registration record, the commands on in the chained list will
added in one of two places.  If the record defines a new command, then
the chained commands are added under it; otherwise, the commands are
added in the same context as the other commands in the array.

@section helpercmdprimer Command Development Primer

This @ref primercommand provides details about the @c hello module,
showing how the pieces described on this page fit together.

 */

/** @page helperlogging OpenOCD Logging API

This section needs to be expanded to describe OpenOCD's Logging API.

 */

/** @page helperbuffers OpenOCD Byte Buffer API

This section needs to be expanded to describe OpenOCD's Byte Buffer API.

 */
