/***************************************************************************
 *   Copyright (C) 2011 by <PERSON>                              *
 *   and<PERSON><PERSON>.<EMAIL>                                          *
 *                                                                         *
 *   This program is free software; you can redistribute it and/or modify  *
 *   it under the terms of the GNU General Public License as published by  *
 *   the Free Software Foundation; either version 2 of the License, or     *
 *   (at your option) any later version.                                   *
 *                                                                         *
 *   This program is distributed in the hope that it will be useful,       *
 *   but WITHOUT ANY WARRANTY; without even the implied warranty of        *
 *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the         *
 *   GNU General Public License for more details.                          *
 ***************************************************************************/

	.text
	.syntax unified
	.cpu cortex-m0
	.thumb

	/* Params:
	 * r0 - flash base (in), status (out)
	 * r1 - count (halfword-16bit)
	 * r2 - workarea start
	 * r3 - workarea end
	 * r4 - target address
	 * Clobbered:
	 * r5 - rp
	 * r6 - wp, tmp
	 * r7 - tmp
	 */

#define STM32_FLASH_SR_OFFSET 0x0c /* offset of SR register from flash reg base */

	.thumb_func
	.global _start
_start:
wait_fifo:
	ldr 	r6, [r2, #0]	/* read wp */
	cmp 	r6, #0			/* abort if wp == 0 */
	beq 	exit
	ldr 	r5, [r2, #4]	/* read rp */
	cmp 	r5, r6			/* wait until rp != wp */
	beq 	wait_fifo
	ldrh	r6, [r5]	/* "*target_address++ = *rp++" */
	strh	r6, [r4] // [target address] <- [rp]
	adds	r5, #2 // rp + 2
	adds	r4, #2 // address + 2
busy:
	ldr 	r6, [r0, #STM32_FLASH_SR_OFFSET]	/* wait until BSY flag is reset */
	movs	r7, #1
	tst 	r6, r7
	bne 	busy // wait busy
	movs	r7, #0x14		/* check the error bits */
	tst 	r6, r7
	bne 	error
	cmp 	r5, r3			/* wrap rp at end of buffer */
	bcc	no_wrap
	mov	r5, r2
	adds	r5, #8
no_wrap:
	str 	r5, [r2, #4]	/* store rp */
	subs	r1, r1, #1		/* decrement halfword count */
	cmp     r1, #0
	beq     exit		/* loop if not done */
	b	wait_fifo
error:
	movs	r0, #0
	str 	r0, [r2, #4]	/* set rp = 0 on error */
exit:
	mov		r0, r6			/* return status in r0 */
	bkpt	#0
