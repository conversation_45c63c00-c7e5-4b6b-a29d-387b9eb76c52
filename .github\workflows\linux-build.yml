on: pull_request

name: Linux Build

jobs:
  # 32-bit, clang
  build32:
    runs-on: ubuntu-latest
    env:
      CFLAGS: -m32
      CC: clang
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
      - name: Install required packages (apt-get)
        run: |
          sudo apt-get update
          sudo apt-get install clang gcc-multilib
      - run: ./bootstrap
      - run: ./configure --enable-remote-bitbang --enable-jtag_vpi --disable-target64
      - run: make -j`nproc`
      - run: file src/openocd | grep 32-bit
      - run: src/openocd --version


  # 64-bit, gcc
  build64:
    runs-on: ubuntu-latest
    env:
      CFLAGS: -m64
      CC: gcc
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
      - name: Install required packages (apt-get)
        run: |
          sudo apt-get update
          sudo apt-get install libusb-1.0-0 libusb-1.0-0-dev
      - run: ./bootstrap
      - run: ./configure --enable-remote-bitbang --enable-jtag_vpi --enable-ftdi-cjtag
      - run: make -j`nproc`
      - run: file src/openocd | grep 64-bit
      - run: src/openocd --version
