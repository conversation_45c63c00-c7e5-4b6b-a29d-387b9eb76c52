# Example of a udp server listening on ipv6 which sends a response
# Note that on many hosts, this will also respond to ipv4 requests too

# Listen on port 20000.
set s [socket -ipv6 dgram.server {[::]:20000}]

# For each request...
$s readable {
	# Get the request (max 80 chars) - need the source address
	set buf [$s recvfrom 80 addr]

	puts -nonewline "read '$buf' from $addr"

	try {
		set result "$buf = [expr $buf]"
	} on error {msg} {
		set result "Error: $buf => $msg"
	}

	puts ", sending '$result'"

	# Send the result back to where it came from
	$s sendto $result $addr
}

vwait done
