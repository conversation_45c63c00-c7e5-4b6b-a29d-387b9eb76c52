
D:/work/2022/al9000/sw/debugger/al_openocd/contrib/loaders/flash/emmc/dwcmshc/build/emmc_async_riscv_32.elf:     file format elf32-littleriscv


Disassembly of section .text:

61000000 <_start>:
61000000:	00000117          	auipc	sp,0x0
61000004:	2c410113          	addi	sp,sp,708 # 610002c4 <stack_end>
61000008:	11c000ef          	jal	ra,61000124 <emmc_dwcmshc>
6100000c:	00100073          	ebreak

61000010 <emmc_wait_fifo>:
61000010:	00050793          	mv	a5,a0
61000014:	0007a703          	lw	a4,0(a5)
61000018:	0047a503          	lw	a0,4(a5)
6100001c:	fea70ce3          	beq	a4,a0,61000014 <emmc_wait_fifo+0x4>
61000020:	00008067          	ret

61000024 <emmc_poll_int>:
61000024:	00100793          	li	a5,1
61000028:	00b797b3          	sll	a5,a5,a1
6100002c:	03052703          	lw	a4,48(a0)
61000030:	00e7f6b3          	and	a3,a5,a4
61000034:	fe068ce3          	beqz	a3,6100002c <emmc_poll_int+0x8>
61000038:	01075693          	srli	a3,a4,0x10
6100003c:	fe0698e3          	bnez	a3,6100002c <emmc_poll_int+0x8>
61000040:	00100793          	li	a5,1
61000044:	00b795b3          	sll	a1,a5,a1
61000048:	00e5e5b3          	or	a1,a1,a4
6100004c:	02b52823          	sw	a1,48(a0)
61000050:	00000513          	li	a0,0
61000054:	00008067          	ret

61000058 <emmc_write_block>:
61000058:	183a07b7          	lui	a5,0x183a0
6100005c:	00b52423          	sw	a1,8(a0)
61000060:	08278793          	addi	a5,a5,130 # 183a0082 <_start-0x48c5ff7e>
61000064:	00f52623          	sw	a5,12(a0)
61000068:	03052783          	lw	a5,48(a0)
6100006c:	0107f713          	andi	a4,a5,16
61000070:	fe070ce3          	beqz	a4,61000068 <emmc_write_block+0x10>
61000074:	0107d793          	srli	a5,a5,0x10
61000078:	fe0798e3          	bnez	a5,61000068 <emmc_write_block+0x10>
6100007c:	20060793          	addi	a5,a2,512
61000080:	00062703          	lw	a4,0(a2)
61000084:	00460613          	addi	a2,a2,4
61000088:	02e52023          	sw	a4,32(a0)
6100008c:	fef61ae3          	bne	a2,a5,61000080 <emmc_write_block+0x28>
61000090:	03052783          	lw	a5,48(a0)
61000094:	0027f713          	andi	a4,a5,2
61000098:	fe070ce3          	beqz	a4,61000090 <emmc_write_block+0x38>
6100009c:	0107d713          	srli	a4,a5,0x10
610000a0:	fe0718e3          	bnez	a4,61000090 <emmc_write_block+0x38>
610000a4:	0027e793          	ori	a5,a5,2
610000a8:	02f52823          	sw	a5,48(a0)
610000ac:	00008067          	ret

610000b0 <emmc_read_block>:
610000b0:	ff010113          	addi	sp,sp,-16
610000b4:	00812423          	sw	s0,8(sp)
610000b8:	00912223          	sw	s1,4(sp)
610000bc:	00112623          	sw	ra,12(sp)
610000c0:	113a07b7          	lui	a5,0x113a0
610000c4:	09278793          	addi	a5,a5,146 # 113a0092 <_start-0x4fc5ff6e>
610000c8:	00c52423          	sw	a2,8(a0)
610000cc:	00b12023          	sw	a1,0(sp)
610000d0:	00f52623          	sw	a5,12(a0)
610000d4:	00500593          	li	a1,5
610000d8:	00050413          	mv	s0,a0
610000dc:	00068493          	mv	s1,a3
610000e0:	f45ff0ef          	jal	ra,61000024 <emmc_poll_int>
610000e4:	00012703          	lw	a4,0(sp)
610000e8:	00000793          	li	a5,0
610000ec:	02979063          	bne	a5,s1,6100010c <emmc_read_block+0x5c>
610000f0:	00040513          	mv	a0,s0
610000f4:	00812403          	lw	s0,8(sp)
610000f8:	00c12083          	lw	ra,12(sp)
610000fc:	00412483          	lw	s1,4(sp)
61000100:	00100593          	li	a1,1
61000104:	01010113          	addi	sp,sp,16
61000108:	f1dff06f          	j	61000024 <emmc_poll_int>
6100010c:	02042603          	lw	a2,32(s0)
61000110:	00279693          	slli	a3,a5,0x2
61000114:	00d706b3          	add	a3,a4,a3
61000118:	00c6a023          	sw	a2,0(a3)
6100011c:	00178793          	addi	a5,a5,1
61000120:	fcdff06f          	j	610000ec <emmc_read_block+0x3c>

61000124 <emmc_dwcmshc>:
61000124:	fd810113          	addi	sp,sp,-40
61000128:	02812023          	sw	s0,32(sp)
6100012c:	00078413          	mv	s0,a5
61000130:	00868793          	addi	a5,a3,8
61000134:	00912e23          	sw	s1,28(sp)
61000138:	02112223          	sw	ra,36(sp)
6100013c:	00a12023          	sw	a0,0(sp)
61000140:	00058293          	mv	t0,a1
61000144:	00060493          	mv	s1,a2
61000148:	ffc5f393          	andi	t2,a1,-4
6100014c:	00f12223          	sw	a5,4(sp)
61000150:	00904c63          	bgtz	s1,61000168 <emmc_dwcmshc+0x44>
61000154:	02412083          	lw	ra,36(sp)
61000158:	02012403          	lw	s0,32(sp)
6100015c:	01c12483          	lw	s1,28(sp)
61000160:	02810113          	addi	sp,sp,40
61000164:	00008067          	ret
61000168:	00068513          	mv	a0,a3
6100016c:	00e12c23          	sw	a4,24(sp)
61000170:	00512a23          	sw	t0,20(sp)
61000174:	00712823          	sw	t2,16(sp)
61000178:	00d12623          	sw	a3,12(sp)
6100017c:	e95ff0ef          	jal	ra,61000010 <emmc_wait_fifo>
61000180:	00050613          	mv	a2,a0
61000184:	00a12423          	sw	a0,8(sp)
61000188:	00012503          	lw	a0,0(sp)
6100018c:	00040593          	mv	a1,s0
61000190:	00140413          	addi	s0,s0,1
61000194:	ec5ff0ef          	jal	ra,61000058 <emmc_write_block>
61000198:	00812603          	lw	a2,8(sp)
6100019c:	01012383          	lw	t2,16(sp)
610001a0:	01812703          	lw	a4,24(sp)
610001a4:	00c12683          	lw	a3,12(sp)
610001a8:	00760333          	add	t1,a2,t2
610001ac:	01412283          	lw	t0,20(sp)
610001b0:	00e31463          	bne	t1,a4,610001b8 <emmc_dwcmshc+0x94>
610001b4:	00412303          	lw	t1,4(sp)
610001b8:	0066a223          	sw	t1,4(a3)
610001bc:	405484b3          	sub	s1,s1,t0
610001c0:	f91ff06f          	j	61000150 <emmc_dwcmshc+0x2c>

Disassembly of section .data:

610001c4 <stack>:
610001c4:	5309                	li	t1,-30
610001c6:	53090867          	jalr	a6,1328(s2)
610001ca:	53090867          	jalr	a6,1328(s2)
610001ce:	53090867          	jalr	a6,1328(s2)
610001d2:	53090867          	jalr	a6,1328(s2)
610001d6:	53090867          	jalr	a6,1328(s2)
610001da:	53090867          	jalr	a6,1328(s2)
610001de:	53090867          	jalr	a6,1328(s2)
610001e2:	53090867          	jalr	a6,1328(s2)
610001e6:	53090867          	jalr	a6,1328(s2)
610001ea:	53090867          	jalr	a6,1328(s2)
610001ee:	53090867          	jalr	a6,1328(s2)
610001f2:	53090867          	jalr	a6,1328(s2)
610001f6:	53090867          	jalr	a6,1328(s2)
610001fa:	53090867          	jalr	a6,1328(s2)
610001fe:	53090867          	jalr	a6,1328(s2)
61000202:	53090867          	jalr	a6,1328(s2)
61000206:	53090867          	jalr	a6,1328(s2)
6100020a:	53090867          	jalr	a6,1328(s2)
6100020e:	53090867          	jalr	a6,1328(s2)
61000212:	53090867          	jalr	a6,1328(s2)
61000216:	53090867          	jalr	a6,1328(s2)
6100021a:	53090867          	jalr	a6,1328(s2)
6100021e:	53090867          	jalr	a6,1328(s2)
61000222:	53090867          	jalr	a6,1328(s2)
61000226:	53090867          	jalr	a6,1328(s2)
6100022a:	53090867          	jalr	a6,1328(s2)
6100022e:	53090867          	jalr	a6,1328(s2)
61000232:	53090867          	jalr	a6,1328(s2)
61000236:	53090867          	jalr	a6,1328(s2)
6100023a:	53090867          	jalr	a6,1328(s2)
6100023e:	53090867          	jalr	a6,1328(s2)
61000242:	53090867          	jalr	a6,1328(s2)
61000246:	53090867          	jalr	a6,1328(s2)
6100024a:	53090867          	jalr	a6,1328(s2)
6100024e:	53090867          	jalr	a6,1328(s2)
61000252:	53090867          	jalr	a6,1328(s2)
61000256:	53090867          	jalr	a6,1328(s2)
6100025a:	53090867          	jalr	a6,1328(s2)
6100025e:	53090867          	jalr	a6,1328(s2)
61000262:	53090867          	jalr	a6,1328(s2)
61000266:	53090867          	jalr	a6,1328(s2)
6100026a:	53090867          	jalr	a6,1328(s2)
6100026e:	53090867          	jalr	a6,1328(s2)
61000272:	53090867          	jalr	a6,1328(s2)
61000276:	53090867          	jalr	a6,1328(s2)
6100027a:	53090867          	jalr	a6,1328(s2)
6100027e:	53090867          	jalr	a6,1328(s2)
61000282:	53090867          	jalr	a6,1328(s2)
61000286:	53090867          	jalr	a6,1328(s2)
6100028a:	53090867          	jalr	a6,1328(s2)
6100028e:	53090867          	jalr	a6,1328(s2)
61000292:	53090867          	jalr	a6,1328(s2)
61000296:	53090867          	jalr	a6,1328(s2)
6100029a:	53090867          	jalr	a6,1328(s2)
6100029e:	53090867          	jalr	a6,1328(s2)
610002a2:	53090867          	jalr	a6,1328(s2)
610002a6:	53090867          	jalr	a6,1328(s2)
610002aa:	53090867          	jalr	a6,1328(s2)
610002ae:	53090867          	jalr	a6,1328(s2)
610002b2:	53090867          	jalr	a6,1328(s2)
610002b6:	53090867          	jalr	a6,1328(s2)
610002ba:	53090867          	jalr	a6,1328(s2)
610002be:	53090867          	jalr	a6,1328(s2)
610002c2:	          	jalr	a6,zero # 0 <_start-0x61000000>

Disassembly of section .riscv.attributes:

00000000 <.riscv.attributes>:
   0:	1b41                	addi	s6,s6,-16
   2:	0000                	unimp
   4:	7200                	flw	fs0,32(a2)
   6:	7369                	lui	t1,0xffffa
   8:	01007663          	bgeu	zero,a6,14 <_start-0x60ffffec>
   c:	0011                	c.nop	4
   e:	0000                	unimp
  10:	0404                	addi	s1,sp,512
  12:	7205                	lui	tp,0xfffe1
  14:	3376                	fld	ft6,376(sp)
  16:	6532                	flw	fa0,12(sp)
  18:	7031                	c.lui	zero,0xfffec
  1a:	0039                	c.nop	14

Disassembly of section .comment:

00000000 <.comment>:
   0:	3a434347          	fmsub.d	ft6,ft6,ft4,ft7,rmm
   4:	2820                	fld	fs0,80(s0)
   6:	29554e47          	fmsub.s	ft8,fa0,fs5,ft5,rmm
   a:	3120                	fld	fs0,96(a0)
   c:	2e30                	fld	fa2,88(a2)
   e:	2e32                	fld	ft8,264(sp)
  10:	0030                	addi	a2,sp,8

Disassembly of section .debug_line:

00000000 <.debug_line>:
   0:	009f 0000 0003      	0x30000009f
   6:	007d                	c.nop	31
   8:	0000                	unimp
   a:	0101                	addi	sp,sp,0
   c:	000d0efb          	dkhmx8	t4,s10,zero
  10:	0101                	addi	sp,sp,0
  12:	0101                	addi	sp,sp,0
  14:	0000                	unimp
  16:	0100                	addi	s0,sp,128
  18:	0000                	unimp
  1a:	4401                	li	s0,0
  1c:	2f3a                	fld	ft10,392(sp)
  1e:	6b726f77          	0x6b726f77
  22:	3230322f          	0x3230322f
  26:	2f32                	fld	ft10,264(sp)
  28:	6c61                	lui	s8,0x18
  2a:	3039                	jal	fffff838 <stack_end+0x9efff574>
  2c:	3030                	fld	fa2,96(s0)
  2e:	2f77732f          	0x2f77732f
  32:	6564                	flw	fs1,76(a0)
  34:	7562                	flw	fa0,56(sp)
  36:	72656767          	0x72656767
  3a:	5f6c612f          	0x5f6c612f
  3e:	6e65706f          	j	57724 <_start-0x60fa88dc>
  42:	2f64636f          	jal	t1,46338 <_start-0x60fb9cc8>
  46:	746e6f63          	bltu	t3,t1,7a4 <_start-0x60fff85c>
  4a:	6972                	flw	fs2,28(sp)
  4c:	2f62                	fld	ft10,24(sp)
  4e:	6f6c                	flw	fa1,92(a4)
  50:	6461                	lui	s0,0x18
  52:	7265                	lui	tp,0xffff9
  54:	6c662f73          	csrrs	t5,0x6c6,a2
  58:	7361                	lui	t1,0xffff8
  5a:	2f68                	fld	fa0,216(a4)
  5c:	6d65                	lui	s10,0x19
  5e:	636d                	lui	t1,0x1b
  60:	6377642f          	0x6377642f
  64:	736d                	lui	t1,0xffffb
  66:	6368                	flw	fa0,68(a4)
  68:	6372732f          	0x6372732f
  6c:	6f6f622f          	0x6f6f622f
  70:	2f74                	fld	fa3,216(a4)
  72:	6972                	flw	fs2,28(sp)
  74:	00766373          	csrrsi	t1,0x7,12
  78:	7700                	flw	fs0,40(a4)
  7a:	6172                	flw	ft2,28(sp)
  7c:	7070                	flw	fa2,100(s0)
  7e:	7265                	lui	tp,0xffff9
  80:	532e                	lw	t1,232(sp)
  82:	0100                	addi	s0,sp,128
  84:	0000                	unimp
  86:	0000                	unimp
  88:	0205                	addi	tp,tp,1
  8a:	0000                	unimp
  8c:	6100                	flw	fs0,0(a0)
  8e:	03010d03          	lb	s10,48(sp)
  92:	0901                	addi	s2,s2,0
  94:	0008                	0x8
  96:	0301                	addi	t1,t1,0
  98:	0901                	addi	s2,s2,0
  9a:	0004                	0x4
  9c:	0901                	addi	s2,s2,0
  9e:	0004                	0x4
  a0:	0100                	addi	s0,sp,128
  a2:	ac01                	j	2b2 <_start-0x60fffd4e>
  a4:	0004                	0x4
  a6:	0300                	addi	s0,sp,384
  a8:	3f00                	fld	fs0,56(a4)
  aa:	0001                	nop
  ac:	0100                	addi	s0,sp,128
  ae:	fb01                	bnez	a4,ffffffbe <stack_end+0x9efffcfa>
  b0:	0d0e                	slli	s10,s10,0x3
  b2:	0100                	addi	s0,sp,128
  b4:	0101                	addi	sp,sp,0
  b6:	0001                	nop
  b8:	0000                	unimp
  ba:	0001                	nop
  bc:	0100                	addi	s0,sp,128
  be:	3a44                	fld	fs1,176(a2)
  c0:	726f772f          	0x726f772f
  c4:	30322f6b          	0x30322f6b
  c8:	3232                	fld	ft4,296(sp)
  ca:	396c612f          	0x396c612f
  ce:	3030                	fld	fa2,96(s0)
  d0:	2f30                	fld	fa2,88(a4)
  d2:	642f7773          	csrrci	a4,0x642,30
  d6:	6265                	lui	tp,0x19
  d8:	6775                	lui	a4,0x1d
  da:	2f726567          	0x2f726567
  de:	6c61                	lui	s8,0x18
  e0:	6f5f 6570 6f6e      	0x6f6e65706f5f
  e6:	632f6463          	bltu	t5,s2,70e <_start-0x60fff8f2>
  ea:	72746e6f          	jal	t3,47010 <_start-0x60fb8ff0>
  ee:	6269                	lui	tp,0x1a
  f0:	616f6c2f          	0x616f6c2f
  f4:	6564                	flw	fs1,76(a0)
  f6:	7372                	flw	ft6,60(sp)
  f8:	616c662f          	0x616c662f
  fc:	652f6873          	csrrsi	a6,0x652,30
 100:	6d6d                	lui	s10,0x1b
 102:	77642f63          	0x77642f63
 106:	68736d63          	bltu	t1,t2,7a0 <_start-0x60fff860>
 10a:	72732f63          	0x72732f63
 10e:	3a640063          	beq	s0,t1,4ae <_start-0x60fffb52>
 112:	775c                	flw	fa5,44(a4)
 114:	5c6b726f          	jal	tp,b76da <_start-0x60f48926>
 118:	3032                	fld	ft0,296(sp)
 11a:	3232                	fld	ft4,296(sp)
 11c:	615c                	flw	fa5,4(a0)
 11e:	396c                	fld	fa1,240(a0)
 120:	3030                	fld	fa2,96(s0)
 122:	5c30                	lw	a2,120(s0)
 124:	735c7773          	csrrci	a4,0x735,24
 128:	6b64                	flw	fs1,84(a4)
 12a:	735c                	flw	fa5,36(a4)
 12c:	612d636f          	jal	t1,d673e <_start-0x60f298c2>
 130:	7570                	flw	fa2,108(a0)
 132:	732d                	lui	t1,0xfffeb
 134:	6b64                	flw	fs1,84(a4)
 136:	745c                	flw	fa5,44(s0)
 138:	736c6f6f          	jal	t5,c686e <_start-0x60f39792>
 13c:	775c                	flw	fa5,44(a4)
 13e:	6e69                	lui	t3,0x1a
 140:	725c                	flw	fa5,36(a2)
 142:	7369                	lui	t1,0xffffa
 144:	725c7663          	bgeu	s8,t0,870 <_start-0x60fff790>
 148:	7369                	lui	t1,0xffffa
 14a:	6e2d7663          	bgeu	s10,sp,836 <_start-0x60fff7ca>
 14e:	6375                	lui	t1,0x1d
 150:	656c                	flw	fa1,76(a0)
 152:	2d69                	jal	7ec <_start-0x60fff814>
 154:	6c65                	lui	s8,0x19
 156:	5c66                	lw	s8,120(sp)
 158:	6e69                	lui	t3,0x1a
 15a:	64756c63          	bltu	a0,t2,7b2 <_start-0x60fff84e>
 15e:	5c65                	li	s8,-7
 160:	616d                	addi	sp,sp,240
 162:	6e696863          	bltu	s2,t1,852 <_start-0x60fff7ae>
 166:	0065                	c.nop	25
 168:	3a64                	fld	fs1,240(a2)
 16a:	775c                	flw	fa5,44(a4)
 16c:	5c6b726f          	jal	tp,b7732 <_start-0x60f488ce>
 170:	3032                	fld	ft0,296(sp)
 172:	3232                	fld	ft4,296(sp)
 174:	615c                	flw	fa5,4(a0)
 176:	396c                	fld	fa1,240(a0)
 178:	3030                	fld	fa2,96(s0)
 17a:	5c30                	lw	a2,120(s0)
 17c:	735c7773          	csrrci	a4,0x735,24
 180:	6b64                	flw	fs1,84(a4)
 182:	735c                	flw	fa5,36(a4)
 184:	612d636f          	jal	t1,d6796 <_start-0x60f2986a>
 188:	7570                	flw	fa2,108(a0)
 18a:	732d                	lui	t1,0xfffeb
 18c:	6b64                	flw	fs1,84(a4)
 18e:	745c                	flw	fa5,44(s0)
 190:	736c6f6f          	jal	t5,c68c6 <_start-0x60f3973a>
 194:	775c                	flw	fa5,44(a4)
 196:	6e69                	lui	t3,0x1a
 198:	725c                	flw	fa5,36(a2)
 19a:	7369                	lui	t1,0xffffa
 19c:	725c7663          	bgeu	s8,t0,8c8 <_start-0x60fff738>
 1a0:	7369                	lui	t1,0xffffa
 1a2:	6e2d7663          	bgeu	s10,sp,88e <_start-0x60fff772>
 1a6:	6375                	lui	t1,0x1d
 1a8:	656c                	flw	fa1,76(a0)
 1aa:	2d69                	jal	844 <_start-0x60fff7bc>
 1ac:	6c65                	lui	s8,0x19
 1ae:	5c66                	lw	s8,120(sp)
 1b0:	6e69                	lui	t3,0x1a
 1b2:	64756c63          	bltu	a0,t2,80a <_start-0x60fff7f6>
 1b6:	5c65                	li	s8,-7
 1b8:	00737973          	csrrci	s2,0x7,6
 1bc:	6400                	flw	fs0,8(s0)
 1be:	736d6377          	0x736d6377
 1c2:	6368                	flw	fa0,68(a4)
 1c4:	632e                	flw	ft6,200(sp)
 1c6:	0100                	addi	s0,sp,128
 1c8:	0000                	unimp
 1ca:	645f 6665 7561      	0x75616665645f
 1d0:	746c                	flw	fa1,108(s0)
 1d2:	745f 7079 7365      	0x73657079745f
 1d8:	682e                	flw	fa6,200(sp)
 1da:	0200                	addi	s0,sp,256
 1dc:	0000                	unimp
 1de:	735f 6474 6e69      	0x6e696474735f
 1e4:	2e74                	fld	fa3,216(a2)
 1e6:	0068                	addi	a0,sp,12
 1e8:	00000003          	lb	zero,0(zero) # 0 <_start-0x61000000>
 1ec:	0105                	addi	sp,sp,1
 1ee:	0500                	addi	s0,sp,640
 1f0:	1002                	c.slli	zero,0x20
 1f2:	0000                	unimp
 1f4:	1561                	addi	a0,a0,-8
 1f6:	0505                	addi	a0,a0,1
 1f8:	04090103          	lb	sp,64(s2)
 1fc:	0100                	addi	s0,sp,128
 1fe:	00090103          	lb	sp,0(s2)
 202:	0100                	addi	s0,sp,128
 204:	0a05                	addi	s4,s4,1
 206:	00090003          	lb	zero,0(s2)
 20a:	0100                	addi	s0,sp,128
 20c:	0905                	addi	s2,s2,1
 20e:	00090203          	lb	tp,0(s2)
 212:	0100                	addi	s0,sp,128
 214:	0c05                	addi	s8,s8,1
 216:	0306                	slli	t1,t1,0x1
 218:	0900                	addi	s0,sp,144
 21a:	0000                	unimp
 21c:	0501                	addi	a0,a0,0
 21e:	0609                	addi	a2,a2,2
 220:	04090103          	lb	sp,64(s2)
 224:	0100                	addi	s0,sp,128
 226:	0c05                	addi	s8,s8,1
 228:	0306                	slli	t1,t1,0x1
 22a:	0900                	addi	s0,sp,144
 22c:	0000                	unimp
 22e:	0501                	addi	a0,a0,0
 230:	060a                	slli	a2,a2,0x2
 232:	04097d03          	0x4097d03
 236:	0100                	addi	s0,sp,128
 238:	0505                	addi	a0,a0,1
 23a:	04090503          	lb	a0,64(s2)
 23e:	0100                	addi	s0,sp,128
 240:	0105                	addi	sp,sp,1
 242:	0306                	slli	t1,t1,0x1
 244:	0901                	addi	s2,s2,0
 246:	0000                	unimp
 248:	0601                	addi	a2,a2,0
 24a:	04090303          	lb	t1,64(s2)
 24e:	0100                	addi	s0,sp,128
 250:	0505                	addi	a0,a0,1
 252:	00090103          	lb	sp,0(s2)
 256:	0100                	addi	s0,sp,128
 258:	00090103          	lb	sp,0(s2)
 25c:	0100                	addi	s0,sp,128
 25e:	08090103          	lb	sp,128(s2)
 262:	0100                	addi	s0,sp,128
 264:	0905                	addi	s2,s2,1
 266:	00090203          	lb	tp,0(s2)
 26a:	0100                	addi	s0,sp,128
 26c:	1105                	addi	sp,sp,-31
 26e:	0306                	slli	t1,t1,0x1
 270:	0900                	addi	s0,sp,144
 272:	0000                	unimp
 274:	0501                	addi	a0,a0,0
 276:	0609                	addi	a2,a2,2
 278:	04090103          	lb	sp,64(s2)
 27c:	0100                	addi	s0,sp,128
 27e:	00090103          	lb	sp,0(s2)
 282:	0100                	addi	s0,sp,128
 284:	0b05                	addi	s6,s6,1
 286:	0306                	slli	t1,t1,0x1
 288:	0900                	addi	s0,sp,144
 28a:	0000                	unimp
 28c:	0501                	addi	a0,a0,0
 28e:	04020023          	sb	zero,64(tp) # 1a040 <_start-0x60fe5fc0>
 292:	0301                	addi	t1,t1,0
 294:	0900                	addi	s0,sp,144
 296:	0008                	0x8
 298:	0501                	addi	a0,a0,0
 29a:	0016                	c.slli	zero,0x5
 29c:	0402                	c.slli64	s0
 29e:	0301                	addi	t1,t1,0
 2a0:	0900                	addi	s0,sp,144
 2a2:	0004                	0x4
 2a4:	0501                	addi	a0,a0,0
 2a6:	0605                	addi	a2,a2,1
 2a8:	04090303          	lb	t1,64(s2)
 2ac:	0100                	addi	s0,sp,128
 2ae:	1c05                	addi	s8,s8,-31
 2b0:	0306                	slli	t1,t1,0x1
 2b2:	0900                	addi	s0,sp,144
 2b4:	0000                	unimp
 2b6:	0501                	addi	a0,a0,0
 2b8:	0900030f          	0x900030f
 2bc:	0008                	0x8
 2be:	0501                	addi	a0,a0,0
 2c0:	0605                	addi	a2,a2,1
 2c2:	04090103          	lb	sp,64(s2)
 2c6:	0100                	addi	s0,sp,128
 2c8:	00090003          	lb	zero,0(s2)
 2cc:	0100                	addi	s0,sp,128
 2ce:	04090003          	lb	zero,64(s2)
 2d2:	0100                	addi	s0,sp,128
 2d4:	00090103          	lb	sp,0(s2)
 2d8:	0100                	addi	s0,sp,128
 2da:	0105                	addi	sp,sp,1
 2dc:	0306                	slli	t1,t1,0x1
 2de:	0901                	addi	s2,s2,0
 2e0:	0000                	unimp
 2e2:	0601                	addi	a2,a2,0
 2e4:	08090303          	lb	t1,128(s2)
 2e8:	0100                	addi	s0,sp,128
 2ea:	0505                	addi	a0,a0,1
 2ec:	00090103          	lb	sp,0(s2)
 2f0:	0100                	addi	s0,sp,128
 2f2:	00090103          	lb	sp,0(s2)
 2f6:	0100                	addi	s0,sp,128
 2f8:	00090203          	lb	tp,0(s2)
 2fc:	0100                	addi	s0,sp,128
 2fe:	00090003          	lb	zero,0(s2)
 302:	0100                	addi	s0,sp,128
 304:	0306                	slli	t1,t1,0x1
 306:	0901                	addi	s2,s2,0
 308:	0000                	unimp
 30a:	0301                	addi	t1,t1,0
 30c:	0004097f          	radd16	s2,s0,zero
 310:	0601                	addi	a2,a2,0
 312:	04090003          	lb	zero,64(s2)
 316:	0100                	addi	s0,sp,128
 318:	00090103          	lb	sp,0(s2)
 31c:	0100                	addi	s0,sp,128
 31e:	00090003          	lb	zero,0(s2)
 322:	0100                	addi	s0,sp,128
 324:	0200                	addi	s0,sp,256
 326:	0104                	addi	s1,sp,128
 328:	08090003          	lb	zero,128(s2)
 32c:	0100                	addi	s0,sp,128
 32e:	0200                	addi	s0,sp,256
 330:	0104                	addi	s1,sp,128
 332:	00090103          	lb	sp,0(s2)
 336:	0100                	addi	s0,sp,128
 338:	0905                	addi	s2,s2,1
 33a:	0200                	addi	s0,sp,256
 33c:	0104                	addi	s1,sp,128
 33e:	00090203          	lb	tp,0(s2)
 342:	0100                	addi	s0,sp,128
 344:	1105                	addi	sp,sp,-31
 346:	0200                	addi	s0,sp,256
 348:	0104                	addi	s1,sp,128
 34a:	0306                	slli	t1,t1,0x1
 34c:	0900                	addi	s0,sp,144
 34e:	0000                	unimp
 350:	0501                	addi	a0,a0,0
 352:	0009                	c.nop	2
 354:	0402                	c.slli64	s0
 356:	0601                	addi	a2,a2,0
 358:	04090103          	lb	sp,64(s2)
 35c:	0100                	addi	s0,sp,128
 35e:	0200                	addi	s0,sp,256
 360:	0104                	addi	s1,sp,128
 362:	00090103          	lb	sp,0(s2)
 366:	0100                	addi	s0,sp,128
 368:	0b05                	addi	s6,s6,1
 36a:	0200                	addi	s0,sp,256
 36c:	0104                	addi	s1,sp,128
 36e:	0306                	slli	t1,t1,0x1
 370:	0900                	addi	s0,sp,144
 372:	0000                	unimp
 374:	0501                	addi	a0,a0,0
 376:	04020023          	sb	zero,64(tp) # 40 <_start-0x60ffffc0>
 37a:	0301                	addi	t1,t1,0
 37c:	0900                	addi	s0,sp,144
 37e:	0008                	0x8
 380:	0501                	addi	a0,a0,0
 382:	0016                	c.slli	zero,0x5
 384:	0402                	c.slli64	s0
 386:	0301                	addi	t1,t1,0
 388:	0900                	addi	s0,sp,144
 38a:	0004                	0x4
 38c:	0501                	addi	a0,a0,0
 38e:	0009                	c.nop	2
 390:	0402                	c.slli64	s0
 392:	04030603          	lb	a2,64(t1) # 1d040 <_start-0x60fe2fc0>
 396:	0809                	addi	a6,a6,2
 398:	0100                	addi	s0,sp,128
 39a:	0200                	addi	s0,sp,256
 39c:	0304                	addi	s1,sp,384
 39e:	00090003          	lb	zero,0(s2)
 3a2:	0100                	addi	s0,sp,128
 3a4:	0505                	addi	a0,a0,1
 3a6:	0200                	addi	s0,sp,256
 3a8:	0304                	addi	s1,sp,384
 3aa:	0306                	slli	t1,t1,0x1
 3ac:	0004097f          	radd16	s2,s0,zero
 3b0:	0501                	addi	a0,a0,0
 3b2:	0009                	c.nop	2
 3b4:	0402                	c.slli64	s0
 3b6:	09010303          	lb	t1,144(sp)
 3ba:	0004                	0x4
 3bc:	0001                	nop
 3be:	0402                	c.slli64	s0
 3c0:	00030603          	lb	a2,0(t1)
 3c4:	0409                	addi	s0,s0,2
 3c6:	0100                	addi	s0,sp,128
 3c8:	2605                	jal	6e8 <_start-0x60fff918>
 3ca:	0200                	addi	s0,sp,256
 3cc:	0304                	addi	s1,sp,384
 3ce:	00097f03          	0x97f03
 3d2:	0100                	addi	s0,sp,128
 3d4:	0e05                	addi	t3,t3,1
 3d6:	0200                	addi	s0,sp,256
 3d8:	0304                	addi	s1,sp,384
 3da:	00090003          	lb	zero,0(s2)
 3de:	0100                	addi	s0,sp,128
 3e0:	0505                	addi	a0,a0,1
 3e2:	0200                	addi	s0,sp,256
 3e4:	0304                	addi	s1,sp,384
 3e6:	0306                	slli	t1,t1,0x1
 3e8:	0900                	addi	s0,sp,144
 3ea:	0000                	unimp
 3ec:	0601                	addi	a2,a2,0
 3ee:	04090203          	lb	tp,64(s2)
 3f2:	0100                	addi	s0,sp,128
 3f4:	0905                	addi	s2,s2,1
 3f6:	00090203          	lb	tp,0(s2)
 3fa:	0100                	addi	s0,sp,128
 3fc:	1105                	addi	sp,sp,-31
 3fe:	0306                	slli	t1,t1,0x1
 400:	0900                	addi	s0,sp,144
 402:	0000                	unimp
 404:	0501                	addi	a0,a0,0
 406:	0609                	addi	a2,a2,2
 408:	04090103          	lb	sp,64(s2)
 40c:	0100                	addi	s0,sp,128
 40e:	00090103          	lb	sp,0(s2)
 412:	0100                	addi	s0,sp,128
 414:	0b05                	addi	s6,s6,1
 416:	0306                	slli	t1,t1,0x1
 418:	0900                	addi	s0,sp,144
 41a:	0000                	unimp
 41c:	0501                	addi	a0,a0,0
 41e:	04020023          	sb	zero,64(tp) # 40 <_start-0x60ffffc0>
 422:	0301                	addi	t1,t1,0
 424:	0900                	addi	s0,sp,144
 426:	0008                	0x8
 428:	0501                	addi	a0,a0,0
 42a:	0016                	c.slli	zero,0x5
 42c:	0402                	c.slli64	s0
 42e:	0301                	addi	t1,t1,0
 430:	0900                	addi	s0,sp,144
 432:	0004                	0x4
 434:	0501                	addi	a0,a0,0
 436:	0605                	addi	a2,a2,1
 438:	04090303          	lb	t1,64(s2)
 43c:	0100                	addi	s0,sp,128
 43e:	00090003          	lb	zero,0(s2)
 442:	0100                	addi	s0,sp,128
 444:	08090003          	lb	zero,128(s2)
 448:	0100                	addi	s0,sp,128
 44a:	0105                	addi	sp,sp,1
 44c:	0306                	slli	t1,t1,0x1
 44e:	0902                	c.slli64	s2
 450:	0000                	unimp
 452:	0601                	addi	a2,a2,0
 454:	04090303          	lb	t1,64(s2)
 458:	0100                	addi	s0,sp,128
 45a:	0505                	addi	a0,a0,1
 45c:	00090103          	lb	sp,0(s2)
 460:	0100                	addi	s0,sp,128
 462:	00090203          	lb	tp,0(s2)
 466:	0100                	addi	s0,sp,128
 468:	00090003          	lb	zero,0(s2)
 46c:	0100                	addi	s0,sp,128
 46e:	0105                	addi	sp,sp,1
 470:	0306                	slli	t1,t1,0x1
 472:	097d                	addi	s2,s2,31
 474:	0000                	unimp
 476:	0501                	addi	a0,a0,0
 478:	0305                	addi	t1,t1,1
 47a:	0904                	addi	s1,sp,144
 47c:	0010                	0x10
 47e:	0301                	addi	t1,t1,0
 480:	0008097f          	radd16	s2,a6,zero
 484:	0601                	addi	a2,a2,0
 486:	04090003          	lb	zero,64(s2)
 48a:	0100                	addi	s0,sp,128
 48c:	00090103          	lb	sp,0(s2)
 490:	0100                	addi	s0,sp,128
 492:	00090003          	lb	zero,0(s2)
 496:	0100                	addi	s0,sp,128
 498:	0105                	addi	sp,sp,1
 49a:	0306                	slli	t1,t1,0x1
 49c:	097c                	addi	a5,sp,156
 49e:	0000                	unimp
 4a0:	0501                	addi	a0,a0,0
 4a2:	0305                	addi	t1,t1,1
 4a4:	0904                	addi	s1,sp,144
 4a6:	0004                	0x4
 4a8:	0601                	addi	a2,a2,0
 4aa:	04090003          	lb	zero,64(s2)
 4ae:	0100                	addi	s0,sp,128
 4b0:	00090103          	lb	sp,0(s2)
 4b4:	0100                	addi	s0,sp,128
 4b6:	0105                	addi	sp,sp,1
 4b8:	0306                	slli	t1,t1,0x1
 4ba:	0004097b          	dkhmx8	s2,s0,zero
 4be:	0501                	addi	a0,a0,0
 4c0:	0305                	addi	t1,t1,1
 4c2:	0905                	addi	s2,s2,1
 4c4:	0008                	0x8
 4c6:	0601                	addi	a2,a2,0
 4c8:	04090203          	lb	tp,64(s2)
 4cc:	0100                	addi	s0,sp,128
 4ce:	0a05                	addi	s4,s4,1
 4d0:	0306                	slli	t1,t1,0x1
 4d2:	0900                	addi	s0,sp,144
 4d4:	0004                	0x4
 4d6:	0501                	addi	a0,a0,0
 4d8:	000e                	c.slli	zero,0x3
 4da:	0402                	c.slli64	s0
 4dc:	0601                	addi	a2,a2,0
 4de:	04090003          	lb	zero,64(s2)
 4e2:	0100                	addi	s0,sp,128
 4e4:	0505                	addi	a0,a0,1
 4e6:	0200                	addi	s0,sp,256
 4e8:	0104                	addi	s1,sp,128
 4ea:	0306                	slli	t1,t1,0x1
 4ec:	0900                	addi	s0,sp,144
 4ee:	0000                	unimp
 4f0:	0601                	addi	a2,a2,0
 4f2:	04090203          	lb	tp,64(s2)
 4f6:	0100                	addi	s0,sp,128
 4f8:	0105                	addi	sp,sp,1
 4fa:	0306                	slli	t1,t1,0x1
 4fc:	0901                	addi	s2,s2,0
 4fe:	0004                	0x4
 500:	0501                	addi	a0,a0,0
 502:	0305                	addi	t1,t1,1
 504:	000c097f          	radd16	s2,s8,zero
 508:	0501                	addi	a0,a0,0
 50a:	0301                	addi	t1,t1,0
 50c:	0901                	addi	s2,s2,0
 50e:	0004                	0x4
 510:	0501                	addi	a0,a0,0
 512:	0305                	addi	t1,t1,1
 514:	0004097f          	radd16	s2,s0,zero
 518:	0501                	addi	a0,a0,0
 51a:	0009                	c.nop	2
 51c:	0402                	c.slli64	s0
 51e:	7f030603          	lb	a2,2032(t1)
 522:	0409                	addi	s0,s0,2
 524:	0100                	addi	s0,sp,128
 526:	1705                	addi	a4,a4,-31
 528:	0200                	addi	s0,sp,256
 52a:	0304                	addi	s1,sp,384
 52c:	0306                	slli	t1,t1,0x1
 52e:	0900                	addi	s0,sp,144
 530:	0000                	unimp
 532:	0501                	addi	a0,a0,0
 534:	001c                	0x1c
 536:	0402                	c.slli64	s0
 538:	7f030603          	lb	a2,2032(t1)
 53c:	1009                	c.nop	-30
 53e:	0100                	addi	s0,sp,128
 540:	1d05                	addi	s10,s10,-31
 542:	0200                	addi	s0,sp,256
 544:	0304                	addi	s1,sp,384
 546:	0306                	slli	t1,t1,0x1
 548:	0900                	addi	s0,sp,144
 54a:	0000                	unimp
 54c:	0901                	addi	s2,s2,0
 54e:	0008                	0x8
 550:	0100                	addi	s0,sp,128
 552:	5901                	li	s2,-32
 554:	0002                	c.slli64	zero
 556:	0300                	addi	s0,sp,384
 558:	5200                	lw	s0,32(a2)
 55a:	0001                	nop
 55c:	0100                	addi	s0,sp,128
 55e:	fb01                	bnez	a4,46e <_start-0x60fffb92>
 560:	0d0e                	slli	s10,s10,0x3
 562:	0100                	addi	s0,sp,128
 564:	0101                	addi	sp,sp,0
 566:	0001                	nop
 568:	0000                	unimp
 56a:	0001                	nop
 56c:	0100                	addi	s0,sp,128
 56e:	3a44                	fld	fs1,176(a2)
 570:	726f772f          	0x726f772f
 574:	30322f6b          	0x30322f6b
 578:	3232                	fld	ft4,296(sp)
 57a:	396c612f          	0x396c612f
 57e:	3030                	fld	fa2,96(s0)
 580:	2f30                	fld	fa2,88(a4)
 582:	642f7773          	csrrci	a4,0x642,30
 586:	6265                	lui	tp,0x19
 588:	6775                	lui	a4,0x1d
 58a:	2f726567          	0x2f726567
 58e:	6c61                	lui	s8,0x18
 590:	6f5f 6570 6f6e      	0x6f6e65706f5f
 596:	632f6463          	bltu	t5,s2,bbe <_start-0x60fff442>
 59a:	72746e6f          	jal	t3,474c0 <_start-0x60fb8b40>
 59e:	6269                	lui	tp,0x1a
 5a0:	616f6c2f          	0x616f6c2f
 5a4:	6564                	flw	fs1,76(a0)
 5a6:	7372                	flw	ft6,60(sp)
 5a8:	616c662f          	0x616c662f
 5ac:	652f6873          	csrrsi	a6,0x652,30
 5b0:	6d6d                	lui	s10,0x1b
 5b2:	77642f63          	0x77642f63
 5b6:	68736d63          	bltu	t1,t2,c50 <_start-0x60fff3b0>
 5ba:	72732f63          	0x72732f63
 5be:	3a640063          	beq	s0,t1,95e <_start-0x60fff6a2>
 5c2:	775c                	flw	fa5,44(a4)
 5c4:	5c6b726f          	jal	tp,b7b8a <_start-0x60f48476>
 5c8:	3032                	fld	ft0,296(sp)
 5ca:	3232                	fld	ft4,296(sp)
 5cc:	615c                	flw	fa5,4(a0)
 5ce:	396c                	fld	fa1,240(a0)
 5d0:	3030                	fld	fa2,96(s0)
 5d2:	5c30                	lw	a2,120(s0)
 5d4:	735c7773          	csrrci	a4,0x735,24
 5d8:	6b64                	flw	fs1,84(a4)
 5da:	735c                	flw	fa5,36(a4)
 5dc:	612d636f          	jal	t1,d6bee <_start-0x60f29412>
 5e0:	7570                	flw	fa2,108(a0)
 5e2:	732d                	lui	t1,0xfffeb
 5e4:	6b64                	flw	fs1,84(a4)
 5e6:	745c                	flw	fa5,44(s0)
 5e8:	736c6f6f          	jal	t5,c6d1e <_start-0x60f392e2>
 5ec:	775c                	flw	fa5,44(a4)
 5ee:	6e69                	lui	t3,0x1a
 5f0:	725c                	flw	fa5,36(a2)
 5f2:	7369                	lui	t1,0xffffa
 5f4:	725c7663          	bgeu	s8,t0,d20 <_start-0x60fff2e0>
 5f8:	7369                	lui	t1,0xffffa
 5fa:	6e2d7663          	bgeu	s10,sp,ce6 <_start-0x60fff31a>
 5fe:	6375                	lui	t1,0x1d
 600:	656c                	flw	fa1,76(a0)
 602:	2d69                	jal	c9c <_start-0x60fff364>
 604:	6c65                	lui	s8,0x19
 606:	5c66                	lw	s8,120(sp)
 608:	6e69                	lui	t3,0x1a
 60a:	64756c63          	bltu	a0,t2,c62 <_start-0x60fff39e>
 60e:	5c65                	li	s8,-7
 610:	616d                	addi	sp,sp,240
 612:	6e696863          	bltu	s2,t1,d02 <_start-0x60fff2fe>
 616:	0065                	c.nop	25
 618:	3a64                	fld	fs1,240(a2)
 61a:	775c                	flw	fa5,44(a4)
 61c:	5c6b726f          	jal	tp,b7be2 <_start-0x60f4841e>
 620:	3032                	fld	ft0,296(sp)
 622:	3232                	fld	ft4,296(sp)
 624:	615c                	flw	fa5,4(a0)
 626:	396c                	fld	fa1,240(a0)
 628:	3030                	fld	fa2,96(s0)
 62a:	5c30                	lw	a2,120(s0)
 62c:	735c7773          	csrrci	a4,0x735,24
 630:	6b64                	flw	fs1,84(a4)
 632:	735c                	flw	fa5,36(a4)
 634:	612d636f          	jal	t1,d6c46 <_start-0x60f293ba>
 638:	7570                	flw	fa2,108(a0)
 63a:	732d                	lui	t1,0xfffeb
 63c:	6b64                	flw	fs1,84(a4)
 63e:	745c                	flw	fa5,44(s0)
 640:	736c6f6f          	jal	t5,c6d76 <_start-0x60f3928a>
 644:	775c                	flw	fa5,44(a4)
 646:	6e69                	lui	t3,0x1a
 648:	725c                	flw	fa5,36(a2)
 64a:	7369                	lui	t1,0xffffa
 64c:	725c7663          	bgeu	s8,t0,d78 <_start-0x60fff288>
 650:	7369                	lui	t1,0xffffa
 652:	6e2d7663          	bgeu	s10,sp,d3e <_start-0x60fff2c2>
 656:	6375                	lui	t1,0x1d
 658:	656c                	flw	fa1,76(a0)
 65a:	2d69                	jal	cf4 <_start-0x60fff30c>
 65c:	6c65                	lui	s8,0x19
 65e:	5c66                	lw	s8,120(sp)
 660:	6e69                	lui	t3,0x1a
 662:	64756c63          	bltu	a0,t2,cba <_start-0x60fff346>
 666:	5c65                	li	s8,-7
 668:	00737973          	csrrci	s2,0x7,6
 66c:	6400                	flw	fs0,8(s0)
 66e:	736d6377          	0x736d6377
 672:	6368                	flw	fa0,68(a4)
 674:	615f 7973 636e      	0x636e7973615f
 67a:	632e                	flw	ft6,200(sp)
 67c:	0100                	addi	s0,sp,128
 67e:	0000                	unimp
 680:	645f 6665 7561      	0x75616665645f
 686:	746c                	flw	fa1,108(s0)
 688:	745f 7079 7365      	0x73657079745f
 68e:	682e                	flw	fa6,200(sp)
 690:	0200                	addi	s0,sp,256
 692:	0000                	unimp
 694:	735f 6474 6e69      	0x6e696474735f
 69a:	2e74                	fld	fa3,216(a2)
 69c:	0068                	addi	a0,sp,12
 69e:	64000003          	lb	zero,1600(zero) # 640 <_start-0x60fff9c0>
 6a2:	736d6377          	0x736d6377
 6a6:	6368                	flw	fa0,68(a4)
 6a8:	682e                	flw	fa6,200(sp)
 6aa:	0100                	addi	s0,sp,128
 6ac:	0000                	unimp
 6ae:	0500                	addi	s0,sp,640
 6b0:	0001                	nop
 6b2:	0205                	addi	tp,tp,1
 6b4:	0124                	addi	s1,sp,136
 6b6:	6100                	flw	fs0,0(a0)
 6b8:	0516                	slli	a0,a0,0x5
 6ba:	0305                	addi	t1,t1,1
 6bc:	0901                	addi	s2,s2,0
 6be:	0000                	unimp
 6c0:	0301                	addi	t1,t1,0
 6c2:	0901                	addi	s2,s2,0
 6c4:	0000                	unimp
 6c6:	0501                	addi	a0,a0,0
 6c8:	0601                	addi	a2,a2,0
 6ca:	00097e03          	0x97e03
 6ce:	0100                	addi	s0,sp,128
 6d0:	1005                	c.nop	-31
 6d2:	0c090c03          	lb	s8,192(s2)
 6d6:	0100                	addi	s0,sp,128
 6d8:	0105                	addi	sp,sp,1
 6da:	04097403          	0x4097403
 6de:	0100                	addi	s0,sp,128
 6e0:	08090003          	lb	zero,128(s2)
 6e4:	0100                	addi	s0,sp,128
 6e6:	0c05                	addi	s8,s8,1
 6e8:	0c090903          	lb	s2,192(s2)
 6ec:	0100                	addi	s0,sp,128
 6ee:	1005                	c.nop	-31
 6f0:	04090303          	lb	t1,64(s2)
 6f4:	0100                	addi	s0,sp,128
 6f6:	0a05                	addi	s4,s4,1
 6f8:	0306                	slli	t1,t1,0x1
 6fa:	0976                	slli	s2,s2,0x1d
 6fc:	0004                	0x4
 6fe:	0501                	addi	a0,a0,0
 700:	0601                	addi	a2,a2,0
 702:	04091003          	lh	zero,64(s2)
 706:	0100                	addi	s0,sp,128
 708:	1a05                	addi	s4,s4,-31
 70a:	14097203          	0x14097203
 70e:	0100                	addi	s0,sp,128
 710:	0905                	addi	s2,s2,1
 712:	0306                	slli	t1,t1,0x1
 714:	0900                	addi	s0,sp,144
 716:	0010                	0x10
 718:	0501                	addi	a0,a0,0
 71a:	061a                	slli	a2,a2,0x6
 71c:	00090003          	lb	zero,0(s2)
 720:	0100                	addi	s0,sp,128
 722:	0905                	addi	s2,s2,1
 724:	0306                	slli	t1,t1,0x1
 726:	0902                	c.slli64	s2
 728:	000c                	0xc
 72a:	0501                	addi	a0,a0,0
 72c:	0614                	addi	a3,sp,768
 72e:	0c090203          	lb	tp,192(s2)
 732:	0100                	addi	s0,sp,128
 734:	0905                	addi	s2,s2,1
 736:	04097e03          	0x4097e03
 73a:	0100                	addi	s0,sp,128
 73c:	0306                	slli	t1,t1,0x1
 73e:	0902                	c.slli64	s2
 740:	0004                	0x4
 742:	0301                	addi	t1,t1,0
 744:	0901                	addi	s2,s2,0
 746:	0000                	unimp
 748:	0501                	addi	a0,a0,0
 74a:	060c                	addi	a1,sp,768
 74c:	00090003          	lb	zero,0(s2)
 750:	0100                	addi	s0,sp,128
 752:	0b05                	addi	s6,s6,1
 754:	08090203          	lb	tp,128(s2)
 758:	0100                	addi	s0,sp,128
 75a:	0c05                	addi	s8,s8,1
 75c:	08097e03          	0x8097e03
 760:	0100                	addi	s0,sp,128
 762:	0905                	addi	s2,s2,1
 764:	0306                	slli	t1,t1,0x1
 766:	0902                	c.slli64	s2
 768:	0004                	0x4
 76a:	0501                	addi	a0,a0,0
 76c:	0003060b          	0x3060b
 770:	0009                	c.nop	2
 772:	0100                	addi	s0,sp,128
 774:	0d05                	addi	s10,s10,1
 776:	0306                	slli	t1,t1,0x1
 778:	0901                	addi	s2,s2,0
 77a:	0008                	0x8
 77c:	0501                	addi	a0,a0,0
 77e:	0610                	addi	a2,sp,768
 780:	00090003          	lb	zero,0(s2)
 784:	0100                	addi	s0,sp,128
 786:	0905                	addi	s2,s2,1
 788:	0306                	slli	t1,t1,0x1
 78a:	0902                	c.slli64	s2
 78c:	0004                	0x4
 78e:	0501                	addi	a0,a0,0
 790:	061a                	slli	a2,a2,0x6
 792:	00090003          	lb	zero,0(s2)
 796:	0100                	addi	s0,sp,128
 798:	0905                	addi	s2,s2,1
 79a:	0306                	slli	t1,t1,0x1
 79c:	0902                	c.slli64	s2
 79e:	0004                	0x4
 7a0:	0501                	addi	a0,a0,0
 7a2:	0003060f          	0x3060f
 7a6:	0009                	c.nop	2
 7a8:	0100                	addi	s0,sp,128
 7aa:	0809                	addi	a6,a6,2
 7ac:	0000                	unimp
 7ae:	0101                	addi	sp,sp,0

Disassembly of section .debug_info:

00000000 <.debug_info>:
   0:	0022                	c.slli	zero,0x8
   2:	0000                	unimp
   4:	0002                	c.slli64	zero
   6:	0000                	unimp
   8:	0000                	unimp
   a:	0104                	addi	s1,sp,128
   c:	0000                	unimp
   e:	0000                	unimp
  10:	0000                	unimp
  12:	6100                	flw	fs0,0(a0)
  14:	0010                	0x10
  16:	6100                	flw	fs0,0(a0)
  18:	0000                	unimp
  1a:	0000                	unimp
  1c:	00000067          	jr	zero # 0 <_start-0x61000000>
  20:	00b5                	addi	ra,ra,13
  22:	0000                	unimp
  24:	8001                	c.srli64	s0
  26:	0261                	addi	tp,tp,24
  28:	0000                	unimp
  2a:	0004                	0x4
  2c:	0014                	0x14
  2e:	0000                	unimp
  30:	0104                	addi	s1,sp,128
  32:	01df 0000 830c      	0x830c000001df
  38:	0001                	nop
  3a:	6700                	flw	fs0,8(a4)
  3c:	0000                	unimp
  3e:	1000                	addi	s0,sp,32
  40:	0000                	unimp
  42:	1461                	addi	s0,s0,-8
  44:	0001                	nop
  46:	a300                	fsd	fs0,0(a4)
  48:	0000                	unimp
  4a:	0200                	addi	s0,sp,256
  4c:	0601                	addi	a2,a2,0
  4e:	00f8                	addi	a4,sp,76
  50:	0000                	unimp
  52:	0000cd03          	lbu	s10,0(ra)
  56:	0200                	addi	s0,sp,256
  58:	0038182b          	0x38182b
  5c:	0000                	unimp
  5e:	0102                	c.slli64	sp
  60:	f608                	fsw	fa0,40(a2)
  62:	0000                	unimp
  64:	0200                	addi	s0,sp,256
  66:	0502                	c.slli64	a0
  68:	0000024b          	fnmsub.s	ft4,ft0,ft0,ft0,rne
  6c:	0202                	c.slli64	tp
  6e:	00010407          	vle8.v	v8,(sp),v0.t
  72:	0200                	addi	s0,sp,256
  74:	0504                	addi	s1,sp,640
  76:	0242                	slli	tp,tp,0x10
  78:	0000                	unimp
  7a:	00013f03          	0x13f03
  7e:	0200                	addi	s0,sp,256
  80:	0060194f          	fnmadd.s	fs2,ft0,ft6,ft0,rtz
  84:	0000                	unimp
  86:	0402                	c.slli64	s0
  88:	00017107          	vle64.v	v2,(sp),v0.t
  8c:	0200                	addi	s0,sp,256
  8e:	0508                	addi	a0,sp,640
  90:	023d                	addi	tp,tp,15
  92:	0000                	unimp
  94:	0802                	c.slli64	a6
  96:	00016c07          	vle32.v	v24,(sp),v0.t
  9a:	0400                	addi	s0,sp,512
  9c:	0504                	addi	s1,sp,640
  9e:	6e69                	lui	t3,0x1a
  a0:	0074                	addi	a3,sp,12
  a2:	0402                	c.slli64	s0
  a4:	00017607          	vle64.v	v12,(sp),v0.t
  a8:	0300                	addi	s0,sp,384
  aa:	000000cf          	fnmadd.s	ft1,ft0,ft0,ft0,rne
  ae:	2c131803          	lh	a6,705(t1) # 1d2c1 <_start-0x60fe2d3f>
  b2:	0000                	unimp
  b4:	0300                	addi	s0,sp,384
  b6:	0141                	addi	sp,sp,16
  b8:	0000                	unimp
  ba:	54143003          	0x54143003
  be:	0000                	unimp
  c0:	0500                	addi	s0,sp,640
  c2:	0000008f          	0x8f
  c6:	8f06                	mv	t5,ra
  c8:	0000                	unimp
  ca:	0700                	addi	s0,sp,896
  cc:	000000d7          	vadd.vv	v1,v0,v0,v0.t
  d0:	3901                	jal	fffffce0 <stack_end+0x9efffa1c>
  d2:	b006                	fsd	ft1,32(sp)
  d4:	0000                	unimp
  d6:	7461                	lui	s0,0xffff8
  d8:	0000                	unimp
  da:	0100                	addi	s0,sp,128
  dc:	399c                	fld	fa5,48(a1)
  de:	0001                	nop
  e0:	0800                	addi	s0,sp,16
  e2:	000000c3          	fmadd.s	ft1,ft0,ft0,ft0,rne
  e6:	3901                	jal	fffffcf6 <stack_end+0x9efffa32>
  e8:	3929                	jal	fffffd02 <stack_end+0x9efffa3e>
  ea:	0001                	nop
  ec:	0000                	unimp
  ee:	0000                	unimp
  f0:	0800                	addi	s0,sp,16
  f2:	0255                	addi	tp,tp,21
  f4:	0000                	unimp
  f6:	3901                	jal	fffffd06 <stack_end+0x9efffa42>
  f8:	3f3e                	fld	ft10,488(sp)
  fa:	0001                	nop
  fc:	4200                	lw	s0,0(a2)
  fe:	0000                	unimp
 100:	0800                	addi	s0,sp,16
 102:	0000026b          	0x26b
 106:	3901                	jal	fffffd16 <stack_end+0x9efffa52>
 108:	00008f4f          	fnmadd.s	ft10,ft1,ft0,ft0,rne
 10c:	7900                	flw	fs0,48(a0)
 10e:	0000                	unimp
 110:	0800                	addi	s0,sp,16
 112:	0000015b          	0x15b
 116:	3901                	jal	fffffd26 <stack_end+0x9efffa62>
 118:	8f60                	0x8f60
 11a:	0000                	unimp
 11c:	9a00                	0x9a00
 11e:	0000                	unimp
 120:	0900                	addi	s0,sp,144
 122:	0069                	c.nop	26
 124:	3b01                	jal	fffffe34 <stack_end+0x9efffb70>
 126:	8f0e                	mv	t5,gp
 128:	0000                	unimp
 12a:	d100                	sw	s0,32(a0)
 12c:	0000                	unimp
 12e:	0a00                	addi	s0,sp,272
 130:	00e4                	addi	s1,sp,76
 132:	6100                	flw	fs0,0(a0)
 134:	01b8                	addi	a4,sp,200
 136:	0000                	unimp
 138:	0122                	slli	sp,sp,0x8
 13a:	0000                	unimp
 13c:	025a010b          	0x25a010b
 140:	0078                	addi	a4,sp,12
 142:	015b010b          	0x15b010b
 146:	0035                	c.nop	13
 148:	0c0c                	addi	a1,sp,528
 14a:	0001                	nop
 14c:	b861                	j	fffff9e4 <stack_end+0x9efff720>
 14e:	0001                	nop
 150:	0b00                	addi	s0,sp,400
 152:	5a01                	li	s4,-32
 154:	5a01f303          	0x5a01f303
 158:	015b010b          	0x15b010b
 15c:	0031                	c.nop	12
 15e:	0d00                	addi	s0,sp,656
 160:	9b04                	0x9b04
 162:	0000                	unimp
 164:	0d00                	addi	s0,sp,656
 166:	8f04                	0x8f04
 168:	0000                	unimp
 16a:	0700                	addi	s0,sp,896
 16c:	014a                	slli	sp,sp,0x12
 16e:	0000                	unimp
 170:	1e01                	addi	t3,t3,-32
 172:	5806                	lw	a6,96(sp)
 174:	0000                	unimp
 176:	5861                	li	a6,-8
 178:	0000                	unimp
 17a:	0100                	addi	s0,sp,128
 17c:	b29c                	fsd	fa5,32(a3)
 17e:	0001                	nop
 180:	0e00                	addi	s0,sp,784
 182:	000000c3          	fmadd.s	ft1,ft0,ft0,ft0,rne
 186:	1e01                	addi	t3,t3,-32
 188:	392a                	fld	fs2,168(sp)
 18a:	0001                	nop
 18c:	0100                	addi	s0,sp,128
 18e:	0e5a                	slli	t3,t3,0x16
 190:	0000026b          	0x26b
 194:	1e01                	addi	t3,t3,-32
 196:	8f3e                	mv	t5,a5
 198:	0000                	unimp
 19a:	0100                	addi	s0,sp,128
 19c:	0255085b          	0x255085b
 1a0:	0000                	unimp
 1a2:	1e01                	addi	t3,t3,-32
 1a4:	b256                	fsd	fs5,288(sp)
 1a6:	0001                	nop
 1a8:	fb00                	fsw	fs0,48(a4)
 1aa:	0000                	unimp
 1ac:	0f00                	addi	s0,sp,912
 1ae:	0069                	c.nop	26
 1b0:	2001                	jal	1b0 <_start-0x60fffe50>
 1b2:	8f0e                	mv	t5,gp
 1b4:	0000                	unimp
 1b6:	1000                	addi	s0,sp,32
 1b8:	0164                	addi	s1,sp,140
 1ba:	0000                	unimp
 1bc:	2001                	jal	1bc <_start-0x60fffe44>
 1be:	8f11                	sub	a4,a4,a2
 1c0:	0000                	unimp
 1c2:	2a00                	fld	fs0,16(a2)
 1c4:	0001                	nop
 1c6:	1000                	addi	s0,sp,32
 1c8:	00000117          	auipc	sp,0x0
 1cc:	2101                	jal	5cc <_start-0x60fffa34>
 1ce:	830d                	srli	a4,a4,0x3
 1d0:	0000                	unimp
 1d2:	4800                	lw	s0,16(s0)
 1d4:	0001                	nop
 1d6:	0000                	unimp
 1d8:	040d                	addi	s0,s0,3
 1da:	00a0                	addi	s0,sp,72
 1dc:	0000                	unimp
 1de:	2111                	jal	5e2 <_start-0x60fffa1e>
 1e0:	0001                	nop
 1e2:	0100                	addi	s0,sp,128
 1e4:	050e                	slli	a0,a0,0x3
 1e6:	0075                	c.nop	29
 1e8:	0000                	unimp
 1ea:	0024                	addi	s1,sp,8
 1ec:	6100                	flw	fs0,0(a0)
 1ee:	0034                	addi	a3,sp,8
 1f0:	0000                	unimp
 1f2:	9c01                	0x9c01
 1f4:	021f 0000 c308      	0xc3080000021f
 1fa:	0000                	unimp
 1fc:	0100                	addi	s0,sp,128
 1fe:	260e                	fld	fa2,192(sp)
 200:	0139                	addi	sp,sp,14
 202:	0000                	unimp
 204:	0172                	slli	sp,sp,0x1c
 206:	0000                	unimp
 208:	6608                	flw	fa0,8(a2)
 20a:	0002                	c.slli64	zero
 20c:	0100                	addi	s0,sp,128
 20e:	390e                	fld	fs2,224(sp)
 210:	00000083          	lb	ra,0(zero) # 0 <_start-0x61000000>
 214:	00000193          	li	gp,0
 218:	6412                	flw	fs0,4(sp)
 21a:	0001                	nop
 21c:	0100                	addi	s0,sp,128
 21e:	0e10                	addi	a2,sp,784
 220:	0000008f          	0x8f
 224:	5e01                	li	t3,-32
 226:	5c12                	lw	s8,36(sp)
 228:	0002                	c.slli64	zero
 22a:	0100                	addi	s0,sp,128
 22c:	1710                	addi	a2,sp,928
 22e:	0000008f          	0x8f
 232:	5b01                	li	s6,-32
 234:	1710                	addi	a2,sp,928
 236:	0001                	nop
 238:	0100                	addi	s0,sp,128
 23a:	0d11                	addi	s10,s10,4
 23c:	00000083          	lb	ra,0(zero) # 0 <_start-0x61000000>
 240:	01b4                	addi	a3,sp,200
 242:	0000                	unimp
 244:	1300                	addi	s0,sp,416
 246:	000000e7          	jalr	zero # 0 <_start-0x61000000>
 24a:	0301                	addi	t1,t1,0
 24c:	00008f0b          	0x8f0b
 250:	1000                	addi	s0,sp,32
 252:	0000                	unimp
 254:	1461                	addi	s0,s0,-8
 256:	0000                	unimp
 258:	0100                	addi	s0,sp,128
 25a:	089c                	addi	a5,sp,80
 25c:	0000012f          	0x12f
 260:	0301                	addi	t1,t1,0
 262:	3f24                	fld	fs1,120(a4)
 264:	0001                	nop
 266:	da00                	sw	s0,48(a2)
 268:	0001                	nop
 26a:	0900                	addi	s0,sp,144
 26c:	01007077          	0x1007077
 270:	0e05                	addi	t3,t3,1
 272:	0000008f          	0x8f
 276:	01f8                	addi	a4,sp,204
 278:	0000                	unimp
 27a:	7209                	lui	tp,0xfffe2
 27c:	0070                	addi	a2,sp,12
 27e:	0501                	addi	a0,a0,0
 280:	8f16                	mv	t5,t0
 282:	0000                	unimp
 284:	1700                	addi	s0,sp,928
 286:	0002                	c.slli64	zero
 288:	0000                	unimp
 28a:	7a00                	flw	fs0,48(a2)
 28c:	0001                	nop
 28e:	0400                	addi	s0,sp,512
 290:	4500                	lw	s0,8(a0)
 292:	0001                	nop
 294:	0400                	addi	s0,sp,512
 296:	df01                	beqz	a4,1ae <_start-0x60fffe52>
 298:	0001                	nop
 29a:	0c00                	addi	s0,sp,528
 29c:	027c                	addi	a5,sp,268
 29e:	0000                	unimp
 2a0:	00000067          	jr	zero # 0 <_start-0x61000000>
 2a4:	0124                	addi	s1,sp,136
 2a6:	6100                	flw	fs0,0(a0)
 2a8:	00a0                	addi	s0,sp,72
 2aa:	0000                	unimp
 2ac:	00000553          	fadd.s	fa0,ft0,ft0,rne
 2b0:	0102                	c.slli64	sp
 2b2:	f806                	fsw	ft1,48(sp)
 2b4:	0000                	unimp
 2b6:	0200                	addi	s0,sp,256
 2b8:	0801                	addi	a6,a6,0
 2ba:	00f6                	slli	ra,ra,0x1d
 2bc:	0000                	unimp
 2be:	0202                	c.slli64	tp
 2c0:	4b05                	li	s6,1
 2c2:	0002                	c.slli64	zero
 2c4:	0200                	addi	s0,sp,256
 2c6:	0702                	c.slli64	a4
 2c8:	0104                	addi	s1,sp,128
 2ca:	0000                	unimp
 2cc:	00027203          	0x27203
 2d0:	0200                	addi	s0,sp,256
 2d2:	184d                	addi	a6,a6,-13
 2d4:	004d                	c.nop	19
 2d6:	0000                	unimp
 2d8:	0402                	c.slli64	s0
 2da:	4205                	li	tp,1
 2dc:	0002                	c.slli64	zero
 2de:	0300                	addi	s0,sp,384
 2e0:	0000013f 60194f02 	0x60194f020000013f
 2e8:	0000                	unimp
 2ea:	0200                	addi	s0,sp,256
 2ec:	0704                	addi	s1,sp,896
 2ee:	0171                	addi	sp,sp,28
 2f0:	0000                	unimp
 2f2:	0802                	c.slli64	a6
 2f4:	3d05                	jal	124 <_start-0x60fffedc>
 2f6:	0002                	c.slli64	zero
 2f8:	0200                	addi	s0,sp,256
 2fa:	0708                	addi	a0,sp,896
 2fc:	016c                	addi	a1,sp,140
 2fe:	0000                	unimp
 300:	0404                	addi	s1,sp,512
 302:	6905                	lui	s2,0x1
 304:	746e                	flw	fs0,248(sp)
 306:	0200                	addi	s0,sp,256
 308:	0704                	addi	s1,sp,896
 30a:	0176                	slli	sp,sp,0x1d
 30c:	0000                	unimp
 30e:	00027403          	0x27403
 312:	0300                	addi	s0,sp,384
 314:	132c                	addi	a1,sp,424
 316:	0041                	c.nop	16
 318:	0000                	unimp
 31a:	00014103          	lbu	sp,0(sp) # 1c8 <_start-0x60fffe38>
 31e:	0300                	addi	s0,sp,384
 320:	1430                	addi	a2,sp,552
 322:	0054                	addi	a3,sp,4
 324:	0000                	unimp
 326:	8f05                	sub	a4,a4,s1
 328:	0000                	unimp
 32a:	0600                	addi	s0,sp,768
 32c:	0302                	c.slli64	t1
 32e:	0000                	unimp
 330:	0401                	addi	s0,s0,0
 332:	2406                	fld	fs0,64(sp)
 334:	0001                	nop
 336:	a061                	j	3be <_start-0x60fffc42>
 338:	0000                	unimp
 33a:	0100                	addi	s0,sp,128
 33c:	599c                	lw	a5,48(a1)
 33e:	0001                	nop
 340:	0700                	addi	s0,sp,896
 342:	000000c3          	fmadd.s	ft1,ft0,ft0,ft0,rne
 346:	0401                	addi	s0,s0,0
 348:	5926                	lw	s2,104(sp)
 34a:	0001                	nop
 34c:	3600                	fld	fs0,40(a2)
 34e:	0002                	c.slli64	zero
 350:	0700                	addi	s0,sp,896
 352:	02e9                	addi	t0,t0,26
 354:	0000                	unimp
 356:	0401                	addi	s0,s0,0
 358:	8339                	srli	a4,a4,0xe
 35a:	0000                	unimp
 35c:	6d00                	flw	fs0,24(a0)
 35e:	0002                	c.slli64	zero
 360:	0700                	addi	s0,sp,896
 362:	02fc                	addi	a5,sp,332
 364:	0000                	unimp
 366:	0401                	addi	s0,s0,0
 368:	7549                	lui	a0,0xffff2
 36a:	0000                	unimp
 36c:	8e00                	0x8e00
 36e:	0002                	c.slli64	zero
 370:	0700                	addi	s0,sp,896
 372:	0000030f          	0x30f
 376:	0401                	addi	s0,s0,0
 378:	5f5a                	lw	t5,180(sp)
 37a:	0001                	nop
 37c:	b700                	fsd	fs0,40(a4)
 37e:	0002                	c.slli64	zero
 380:	0700                	addi	s0,sp,896
 382:	02f4                	addi	a3,sp,332
 384:	0000                	unimp
 386:	0401                	addi	s0,s0,0
 388:	00015f6f          	jal	t5,15388 <_start-0x60feac78>
 38c:	fa00                	fsw	fs0,48(a2)
 38e:	0002                	c.slli64	zero
 390:	0700                	addi	s0,sp,896
 392:	02de                	slli	t0,t0,0x17
 394:	0000                	unimp
 396:	0401                	addi	s0,s0,0
 398:	8f81                	sub	a5,a5,s0
 39a:	0000                	unimp
 39c:	1b00                	addi	s0,sp,432
 39e:	08000003          	lb	zero,128(zero) # 80 <_start-0x60ffff80>
 3a2:	7072                	flw	ft0,60(sp)
 3a4:	0100                	addi	s0,sp,128
 3a6:	0f06                	slli	t5,t5,0x1
 3a8:	015f 0000 0367      	0x3670000015f
 3ae:	0000                	unimp
 3b0:	8009                	srli	s0,s0,0x2
 3b2:	0001                	nop
 3b4:	6561                	lui	a0,0x18
 3b6:	0001                	nop
 3b8:	3a00                	fld	fs0,48(a2)
 3ba:	0001                	nop
 3bc:	0a00                	addi	s0,sp,272
 3be:	5a01                	li	s4,-32
 3c0:	06649103          	lh	sp,102(s1)
 3c4:	0b00                	addi	s0,sp,400
 3c6:	0198                	addi	a4,sp,192
 3c8:	6100                	flw	fs0,0(a0)
 3ca:	0171                	addi	sp,sp,28
 3cc:	0000                	unimp
 3ce:	010a                	slli	sp,sp,0x2
 3d0:	035a                	slli	t1,t1,0x16
 3d2:	0072                	c.slli	zero,0x1c
 3d4:	0a06                	slli	s4,s4,0x1
 3d6:	5b01                	li	s6,-32
 3d8:	7802                	flw	fa6,32(sp)
 3da:	5c010a7f          	sll8	s4,sp,zero
 3de:	06609103          	lh	sp,102(ra)
 3e2:	0000                	unimp
 3e4:	040c                	addi	a1,sp,512
 3e6:	0000009b          	0x9b
 3ea:	040c                	addi	a1,sp,512
 3ec:	0000008f          	0x8f
 3f0:	e70d                	bnez	a4,41a <_start-0x60fffbe6>
 3f2:	0000                	unimp
 3f4:	e700                	fsw	fs0,8(a4)
 3f6:	0000                	unimp
 3f8:	0400                	addi	s0,sp,512
 3fa:	0b2e                	slli	s6,s6,0xb
 3fc:	4a0d                	li	s4,3
 3fe:	0001                	nop
 400:	4a00                	lw	s0,16(a2)
 402:	0001                	nop
 404:	0400                	addi	s0,sp,512
 406:	          	0x62f

Disassembly of section .debug_abbrev:

00000000 <.debug_abbrev>:
   0:	1101                	addi	sp,sp,-32
   2:	1000                	addi	s0,sp,32
   4:	1106                	slli	sp,sp,0x21
   6:	1201                	addi	tp,tp,-32
   8:	0301                	addi	t1,t1,0
   a:	1b0e                	slli	s6,s6,0x23
   c:	250e                	fld	fa0,192(sp)
   e:	130e                	slli	t1,t1,0x23
  10:	0005                	c.nop	1
  12:	0000                	unimp
  14:	1101                	addi	sp,sp,-32
  16:	2501                	jal	616 <_start-0x60fff9ea>
  18:	130e                	slli	t1,t1,0x23
  1a:	1b0e030b          	0x1b0e030b
  1e:	110e                	slli	sp,sp,0x23
  20:	1201                	addi	tp,tp,-32
  22:	1006                	c.slli	zero,0x21
  24:	02000017          	auipc	zero,0x2000
  28:	0024                	addi	s1,sp,8
  2a:	0b3e0b0b          	0xb3e0b0b
  2e:	00000e03          	lb	t3,0(zero) # 0 <_start-0x61000000>
  32:	03001603          	lh	a2,48(zero) # 30 <_start-0x60ffffd0>
  36:	3a0e                	fld	fs4,224(sp)
  38:	390b3b0b          	0x390b3b0b
  3c:	0013490b          	0x13490b
  40:	0400                	addi	s0,sp,512
  42:	0024                	addi	s1,sp,8
  44:	0b3e0b0b          	0xb3e0b0b
  48:	00000803          	lb	a6,0(zero) # 0 <_start-0x61000000>
  4c:	3505                	jal	fffffe6c <stack_end+0x9efffba8>
  4e:	4900                	lw	s0,16(a0)
  50:	06000013          	li	zero,96
  54:	0026                	c.slli	zero,0x9
  56:	1349                	addi	t1,t1,-14
  58:	0000                	unimp
  5a:	3f012e07          	flw	ft8,1008(sp)
  5e:	0319                	addi	t1,t1,6
  60:	3a0e                	fld	fs4,224(sp)
  62:	390b3b0b          	0x390b3b0b
  66:	1119270b          	0x1119270b
  6a:	1201                	addi	tp,tp,-32
  6c:	4006                	0x4006
  6e:	9718                	0x9718
  70:	1942                	slli	s2,s2,0x30
  72:	1301                	addi	t1,t1,-32
  74:	0000                	unimp
  76:	0508                	addi	a0,sp,640
  78:	0300                	addi	s0,sp,384
  7a:	3a0e                	fld	fs4,224(sp)
  7c:	390b3b0b          	0x390b3b0b
  80:	0213490b          	0x213490b
  84:	09000017          	auipc	zero,0x9000
  88:	0034                	addi	a3,sp,8
  8a:	0b3a0803          	lb	a6,179(s4)
  8e:	0b390b3b          	0xb390b3b
  92:	1349                	addi	t1,t1,-14
  94:	1702                	slli	a4,a4,0x20
  96:	0000                	unimp
  98:	890a                	mv	s2,sp
  9a:	0182                	c.slli64	gp
  9c:	1101                	addi	sp,sp,-32
  9e:	3101                	jal	fffffc9e <stack_end+0x9efff9da>
  a0:	00130113          	addi	sp,t1,1
  a4:	0b00                	addi	s0,sp,400
  a6:	828a                	mv	t0,sp
  a8:	0001                	nop
  aa:	1802                	slli	a6,a6,0x20
  ac:	4291                	li	t0,4
  ae:	0018                	0x18
  b0:	0c00                	addi	s0,sp,528
  b2:	8289                	srli	a3,a3,0x2
  b4:	0101                	addi	sp,sp,0
  b6:	0111                	addi	sp,sp,4
  b8:	4295                	li	t0,5
  ba:	3119                	jal	fffffcc0 <stack_end+0x9efff9fc>
  bc:	0d000013          	li	zero,208
  c0:	0b0b000f          	0xb0b000f
  c4:	1349                	addi	t1,t1,-14
  c6:	0000                	unimp
  c8:	050e                	slli	a0,a0,0x3
  ca:	0300                	addi	s0,sp,384
  cc:	3a0e                	fld	fs4,224(sp)
  ce:	390b3b0b          	0x390b3b0b
  d2:	0213490b          	0x213490b
  d6:	0018                	0x18
  d8:	0f00                	addi	s0,sp,912
  da:	0034                	addi	a3,sp,8
  dc:	0b3a0803          	lb	a6,179(s4)
  e0:	0b390b3b          	0xb390b3b
  e4:	1349                	addi	t1,t1,-14
  e6:	0000                	unimp
  e8:	3410                	fld	fa2,40(s0)
  ea:	0300                	addi	s0,sp,384
  ec:	3a0e                	fld	fs4,224(sp)
  ee:	390b3b0b          	0x390b3b0b
  f2:	0213490b          	0x213490b
  f6:	11000017          	auipc	zero,0x11000
  fa:	012e                	slli	sp,sp,0xb
  fc:	0e03193f 0b3b0b3a 	0xb3b0b3a0e03193f
 104:	0b39                	addi	s6,s6,14
 106:	13491927          	fsh	fs4,306(s2) # 1132 <_start-0x60ffeece>
 10a:	0111                	addi	sp,sp,4
 10c:	0612                	slli	a2,a2,0x4
 10e:	1840                	addi	s0,sp,52
 110:	01194297          	auipc	t0,0x1194
 114:	12000013          	li	zero,288
 118:	0034                	addi	a3,sp,8
 11a:	0b3a0e03          	lb	t3,179(s4)
 11e:	0b390b3b          	0xb390b3b
 122:	1349                	addi	t1,t1,-14
 124:	1802                	slli	a6,a6,0x20
 126:	0000                	unimp
 128:	3f012e13          	slti	t3,sp,1008
 12c:	0319                	addi	t1,t1,6
 12e:	3a0e                	fld	fs4,224(sp)
 130:	390b3b0b          	0x390b3b0b
 134:	4919270b          	0x4919270b
 138:	12011113          	0x12011113
 13c:	4006                	0x4006
 13e:	9718                	0x9718
 140:	1942                	slli	s2,s2,0x30
 142:	0000                	unimp
 144:	0100                	addi	s0,sp,128
 146:	0111                	addi	sp,sp,4
 148:	0e25                	addi	t3,t3,9
 14a:	0e030b13          	addi	s6,t1,224
 14e:	01110e1b          	0x1110e1b
 152:	0612                	slli	a2,a2,0x4
 154:	1710                	addi	a2,sp,928
 156:	0000                	unimp
 158:	2402                	fld	fs0,0(sp)
 15a:	0b00                	addi	s0,sp,400
 15c:	030b3e0b          	0x30b3e0b
 160:	000e                	c.slli	zero,0x3
 162:	0300                	addi	s0,sp,384
 164:	0016                	c.slli	zero,0x5
 166:	0b3a0e03          	lb	t3,179(s4)
 16a:	0b390b3b          	0xb390b3b
 16e:	1349                	addi	t1,t1,-14
 170:	0000                	unimp
 172:	2404                	fld	fs1,8(s0)
 174:	0b00                	addi	s0,sp,400
 176:	030b3e0b          	0x30b3e0b
 17a:	0008                	0x8
 17c:	0500                	addi	s0,sp,640
 17e:	0035                	c.nop	13
 180:	1349                	addi	t1,t1,-14
 182:	0000                	unimp
 184:	2e06                	fld	ft8,64(sp)
 186:	3f01                	jal	96 <_start-0x60ffff6a>
 188:	0319                	addi	t1,t1,6
 18a:	3a0e                	fld	fs4,224(sp)
 18c:	390b3b0b          	0x390b3b0b
 190:	1119270b          	0x1119270b
 194:	1201                	addi	tp,tp,-32
 196:	4006                	0x4006
 198:	9718                	0x9718
 19a:	1942                	slli	s2,s2,0x30
 19c:	1301                	addi	t1,t1,-32
 19e:	0000                	unimp
 1a0:	03000507          	vle8ff.v	v10,(zero)
 1a4:	3a0e                	fld	fs4,224(sp)
 1a6:	390b3b0b          	0x390b3b0b
 1aa:	0213490b          	0x213490b
 1ae:	08000017          	auipc	zero,0x8000
 1b2:	0034                	addi	a3,sp,8
 1b4:	0b3a0803          	lb	a6,179(s4)
 1b8:	0b390b3b          	0xb390b3b
 1bc:	1349                	addi	t1,t1,-14
 1be:	1702                	slli	a4,a4,0x20
 1c0:	0000                	unimp
 1c2:	8909                	andi	a0,a0,2
 1c4:	0182                	c.slli64	gp
 1c6:	1101                	addi	sp,sp,-32
 1c8:	3101                	jal	fffffdc8 <stack_end+0x9efffb04>
 1ca:	00130113          	addi	sp,t1,1
 1ce:	0a00                	addi	s0,sp,272
 1d0:	828a                	mv	t0,sp
 1d2:	0001                	nop
 1d4:	1802                	slli	a6,a6,0x20
 1d6:	4291                	li	t0,4
 1d8:	0018                	0x18
 1da:	0b00                	addi	s0,sp,400
 1dc:	8289                	srli	a3,a3,0x2
 1de:	0101                	addi	sp,sp,0
 1e0:	0111                	addi	sp,sp,4
 1e2:	1331                	addi	t1,t1,-20
 1e4:	0000                	unimp
 1e6:	0f0c                	addi	a1,sp,912
 1e8:	0b00                	addi	s0,sp,400
 1ea:	0013490b          	0x13490b
 1ee:	0d00                	addi	s0,sp,656
 1f0:	002e                	c.slli	zero,0xb
 1f2:	193c193f 0e030e6e 	0xe030e6e193c193f
 1fa:	0b3a                	slli	s6,s6,0xe
 1fc:	0b390b3b          	0xb390b3b
 200:	0000                	unimp
	...

Disassembly of section .debug_aranges:

00000000 <.debug_aranges>:
   0:	001c                	0x1c
   2:	0000                	unimp
   4:	0002                	c.slli64	zero
   6:	0000                	unimp
   8:	0000                	unimp
   a:	0004                	0x4
   c:	0000                	unimp
   e:	0000                	unimp
  10:	0000                	unimp
  12:	6100                	flw	fs0,0(a0)
  14:	0010                	0x10
	...
  1e:	0000                	unimp
  20:	001c                	0x1c
  22:	0000                	unimp
  24:	0002                	c.slli64	zero
  26:	0026                	c.slli	zero,0x9
  28:	0000                	unimp
  2a:	0004                	0x4
  2c:	0000                	unimp
  2e:	0000                	unimp
  30:	0010                	0x10
  32:	6100                	flw	fs0,0(a0)
  34:	0114                	addi	a3,sp,128
	...
  3e:	0000                	unimp
  40:	001c                	0x1c
  42:	0000                	unimp
  44:	0002                	c.slli64	zero
  46:	0000028b          	0x28b
  4a:	0004                	0x4
  4c:	0000                	unimp
  4e:	0000                	unimp
  50:	0124                	addi	s1,sp,136
  52:	6100                	flw	fs0,0(a0)
  54:	00a0                	addi	s0,sp,72
	...

Disassembly of section .debug_str:

00000000 <.debug_str>:
   0:	3a44                	fld	fs1,176(a2)
   2:	726f772f          	0x726f772f
   6:	30322f6b          	0x30322f6b
   a:	3232                	fld	ft4,296(sp)
   c:	396c612f          	0x396c612f
  10:	3030                	fld	fa2,96(s0)
  12:	2f30                	fld	fa2,88(a4)
  14:	642f7773          	csrrci	a4,0x642,30
  18:	6265                	lui	tp,0x19
  1a:	6775                	lui	a4,0x1d
  1c:	2f726567          	0x2f726567
  20:	6c61                	lui	s8,0x18
  22:	6f5f 6570 6f6e      	0x6f6e65706f5f
  28:	632f6463          	bltu	t5,s2,650 <_start-0x60fff9b0>
  2c:	72746e6f          	jal	t3,46f52 <_start-0x60fb90ae>
  30:	6269                	lui	tp,0x1a
  32:	616f6c2f          	0x616f6c2f
  36:	6564                	flw	fs1,76(a0)
  38:	7372                	flw	ft6,60(sp)
  3a:	616c662f          	0x616c662f
  3e:	652f6873          	csrrsi	a6,0x652,30
  42:	6d6d                	lui	s10,0x1b
  44:	77642f63          	0x77642f63
  48:	68736d63          	bltu	t1,t2,6e2 <_start-0x60fff91e>
  4c:	72732f63          	0x72732f63
  50:	6f622f63          	0x6f622f63
  54:	722f746f          	jal	s0,f7776 <_start-0x60f0888a>
  58:	7369                	lui	t1,0xffffa
  5a:	775c7663          	bgeu	s8,s5,7c6 <_start-0x60fff83a>
  5e:	6172                	flw	ft2,28(sp)
  60:	7070                	flw	fa2,100(s0)
  62:	7265                	lui	tp,0xffff9
  64:	532e                	lw	t1,232(sp)
  66:	4400                	lw	s0,8(s0)
  68:	5c3a                	lw	s8,172(sp)
  6a:	6b726f77          	0x6b726f77
  6e:	325c                	fld	fa5,160(a2)
  70:	3230                	fld	fa2,96(a2)
  72:	5c32                	lw	s8,44(sp)
  74:	6c61                	lui	s8,0x18
  76:	3039                	jal	fffff884 <stack_end+0x9efff5c0>
  78:	3030                	fld	fa2,96(s0)
  7a:	735c                	flw	fa5,36(a4)
  7c:	65645c77          	0x65645c77
  80:	7562                	flw	fa0,56(sp)
  82:	72656767          	0x72656767
  86:	615c                	flw	fa5,4(a0)
  88:	5f6c                	lw	a1,124(a4)
  8a:	6e65706f          	j	57770 <_start-0x60fa8890>
  8e:	5c64636f          	jal	t1,46654 <_start-0x60fb99ac>
  92:	746e6f63          	bltu	t3,t1,7f0 <_start-0x60fff810>
  96:	6972                	flw	fs2,28(sp)
  98:	5c62                	lw	s8,56(sp)
  9a:	6f6c                	flw	fa1,92(a4)
  9c:	6461                	lui	s0,0x18
  9e:	7265                	lui	tp,0xffff9
  a0:	6c665c73          	csrrwi	s8,0x6c6,12
  a4:	7361                	lui	t1,0xffff8
  a6:	5c68                	lw	a0,124(s0)
  a8:	6d65                	lui	s10,0x19
  aa:	636d                	lui	t1,0x1b
  ac:	645c                	flw	fa5,12(s0)
  ae:	736d6377          	0x736d6377
  b2:	6368                	flw	fa0,68(a4)
  b4:	4700                	lw	s0,8(a4)
  b6:	554e                	lw	a0,240(sp)
  b8:	4120                	lw	s0,64(a0)
  ba:	2e322053          	0x2e322053
  be:	312e3633          	0x312e3633
  c2:	6300                	flw	fs0,0(a4)
  c4:	7274                	flw	fa3,100(a2)
  c6:	5f6c                	lw	a1,124(a4)
  c8:	6162                	flw	ft2,24(sp)
  ca:	5f006573          	csrrsi	a0,0x5f0,0
  ce:	755f 6e69 3874      	0x38746e69755f
  d4:	745f 6500 6d6d      	0x6d6d6500745f
  da:	65725f63          	bge	tp,s7,738 <_start-0x60fff8c8>
  de:	6461                	lui	s0,0x18
  e0:	625f 6f6c 6b63      	0x6b636f6c625f
  e6:	6500                	flw	fs0,8(a0)
  e8:	6d6d                	lui	s10,0x1b
  ea:	61775f63          	bge	a4,s7,708 <_start-0x60fff8f8>
  ee:	7469                	lui	s0,0xffffa
  f0:	665f 6669 006f      	0x6f6669665f
  f6:	6e75                	lui	t3,0x1d
  f8:	6e676973          	csrrsi	s2,0x6e6,14
  fc:	6465                	lui	s0,0x19
  fe:	6320                	flw	fs0,64(a4)
 100:	6168                	flw	fa0,68(a0)
 102:	0072                	c.slli	zero,0x1c
 104:	726f6873          	csrrsi	a6,0x726,30
 108:	2074                	fld	fa3,192(s0)
 10a:	6e75                	lui	t3,0x1d
 10c:	6e676973          	csrrsi	s2,0x6e6,14
 110:	6465                	lui	s0,0x19
 112:	6920                	flw	fs0,80(a0)
 114:	746e                	flw	fs0,248(sp)
 116:	6400                	flw	fs0,8(s0)
 118:	5f656e6f          	jal	t3,5670e <_start-0x60fa98f2>
 11c:	6c66                	flw	fs8,88(sp)
 11e:	6761                	lui	a4,0x18
 120:	6500                	flw	fs0,8(a0)
 122:	6d6d                	lui	s10,0x1b
 124:	6f705f63          	blez	s7,822 <_start-0x60fff7de>
 128:	6c6c                	flw	fa1,92(s0)
 12a:	695f 746e 7700      	0x7700746e695f
 130:	5f6b726f          	jal	tp,b7726 <_start-0x60f488da>
 134:	7261                	lui	tp,0xffff8
 136:	6165                	addi	sp,sp,112
 138:	735f 6174 7472      	0x74726174735f
 13e:	5f00                	lw	s0,56(a4)
 140:	755f 6e69 3374      	0x33746e69755f
 146:	5f32                	lw	t5,44(sp)
 148:	0074                	addi	a3,sp,12
 14a:	6d65                	lui	s10,0x19
 14c:	636d                	lui	t1,0x1b
 14e:	775f 6972 6574      	0x65746972775f
 154:	625f 6f6c 6b63      	0x6b636f6c625f
 15a:	7700                	flw	fs0,40(a4)
 15c:	5f64726f          	jal	tp,47752 <_start-0x60fb88ae>
 160:	00746e63          	bltu	s0,t2,17c <_start-0x60fffe84>
 164:	6e69                	lui	t3,0x1a
 166:	5f74                	lw	a3,124(a4)
 168:	6176                	flw	ft2,92(sp)
 16a:	006c                	addi	a1,sp,12
 16c:	6f6c                	flw	fa1,92(a4)
 16e:	676e                	flw	fa4,216(sp)
 170:	6c20                	flw	fs0,88(s0)
 172:	20676e6f          	jal	t3,76378 <_start-0x60f89c88>
 176:	6e75                	lui	t3,0x1d
 178:	6e676973          	csrrsi	s2,0x6e6,14
 17c:	6465                	lui	s0,0x19
 17e:	6920                	flw	fs0,80(a0)
 180:	746e                	flw	fs0,248(sp)
 182:	4400                	lw	s0,8(s0)
 184:	2f3a                	fld	ft10,392(sp)
 186:	6b726f77          	0x6b726f77
 18a:	3230322f          	0x3230322f
 18e:	2f32                	fld	ft10,264(sp)
 190:	6c61                	lui	s8,0x18
 192:	3039                	jal	fffff9a0 <stack_end+0x9efff6dc>
 194:	3030                	fld	fa2,96(s0)
 196:	2f77732f          	0x2f77732f
 19a:	6564                	flw	fs1,76(a0)
 19c:	7562                	flw	fa0,56(sp)
 19e:	72656767          	0x72656767
 1a2:	5f6c612f          	0x5f6c612f
 1a6:	6e65706f          	j	5788c <_start-0x60fa8774>
 1aa:	2f64636f          	jal	t1,464a0 <_start-0x60fb9b60>
 1ae:	746e6f63          	bltu	t3,t1,90c <_start-0x60fff6f4>
 1b2:	6972                	flw	fs2,28(sp)
 1b4:	2f62                	fld	ft10,24(sp)
 1b6:	6f6c                	flw	fa1,92(a4)
 1b8:	6461                	lui	s0,0x18
 1ba:	7265                	lui	tp,0xffff9
 1bc:	6c662f73          	csrrs	t5,0x6c6,a2
 1c0:	7361                	lui	t1,0xffff8
 1c2:	2f68                	fld	fa0,216(a4)
 1c4:	6d65                	lui	s10,0x19
 1c6:	636d                	lui	t1,0x1b
 1c8:	6377642f          	0x6377642f
 1cc:	736d                	lui	t1,0xffffb
 1ce:	6368                	flw	fa0,68(a4)
 1d0:	6372732f          	0x6372732f
 1d4:	6377642f          	0x6377642f
 1d8:	736d                	lui	t1,0xffffb
 1da:	6368                	flw	fa0,68(a4)
 1dc:	632e                	flw	ft6,200(sp)
 1de:	4700                	lw	s0,8(a4)
 1e0:	554e                	lw	a0,240(sp)
 1e2:	4320                	lw	s0,64(a4)
 1e4:	3731                	jal	f0 <_start-0x60ffff10>
 1e6:	3120                	fld	fs0,96(a0)
 1e8:	2e30                	fld	fa2,88(a2)
 1ea:	2e32                	fld	ft8,264(sp)
 1ec:	2030                	fld	fa2,64(s0)
 1ee:	6d2d                	lui	s10,0xb
 1f0:	7261                	lui	tp,0xffff8
 1f2:	723d6863          	bltu	s10,gp,922 <_start-0x60fff6de>
 1f6:	3376                	fld	ft6,376(sp)
 1f8:	6532                	flw	fa0,12(sp)
 1fa:	2d20                	fld	fs0,88(a0)
 1fc:	616d                	addi	sp,sp,240
 1fe:	6962                	flw	fs2,24(sp)
 200:	693d                	lui	s2,0xf
 202:	706c                	flw	fa1,100(s0)
 204:	20653233          	0x20653233
 208:	6d2d                	lui	s10,0xb
 20a:	7574                	flw	fa3,108(a0)
 20c:	656e                	flw	fa0,216(sp)
 20e:	723d                	lui	tp,0xfffef
 210:	656b636f          	jal	t1,b6866 <_start-0x60f4979a>
 214:	2074                	fld	fa3,192(s0)
 216:	6d2d                	lui	s10,0xb
 218:	7261                	lui	tp,0xffff8
 21a:	723d6863          	bltu	s10,gp,94a <_start-0x60fff6b6>
 21e:	3376                	fld	ft6,376(sp)
 220:	6532                	flw	fa0,12(sp)
 222:	2d20                	fld	fs0,88(a0)
 224:	4f2d2067          	0x4f2d2067
 228:	662d2073          	csrs	0x662,s10
 22c:	6f6e                	flw	ft10,216(sp)
 22e:	622d                	lui	tp,0xb
 230:	6975                	lui	s2,0x1d
 232:	746c                	flw	fa1,108(s0)
 234:	6e69                	lui	t3,0x1a
 236:	2d20                	fld	fs0,88(a0)
 238:	5066                	0x5066
 23a:	4349                	li	t1,18
 23c:	6c00                	flw	fs0,24(s0)
 23e:	20676e6f          	jal	t3,76444 <_start-0x60f89bbc>
 242:	6f6c                	flw	fa1,92(a4)
 244:	676e                	flw	fa4,216(sp)
 246:	6920                	flw	fs0,80(a0)
 248:	746e                	flw	fs0,248(sp)
 24a:	7300                	flw	fs0,32(a4)
 24c:	6f68                	flw	fa0,92(a4)
 24e:	7472                	flw	fs0,60(sp)
 250:	6920                	flw	fs0,80(a0)
 252:	746e                	flw	fs0,248(sp)
 254:	6200                	flw	fs0,0(a2)
 256:	6675                	lui	a2,0x1d
 258:	6566                	flw	fa0,88(sp)
 25a:	0072                	c.slli	zero,0x1c
 25c:	61656c63          	bltu	a0,s6,874 <_start-0x60fff78c>
 260:	5f72                	lw	t5,60(sp)
 262:	6572                	flw	fa0,28(sp)
 264:	6c660067          	jr	1734(a2) # 1d6c6 <_start-0x60fe293a>
 268:	6761                	lui	a4,0x18
 26a:	6f5f 6666 6573      	0x657366666f5f
 270:	0074                	addi	a3,sp,12
 272:	5f5f 6e69 3374      	0x33746e695f5f
 278:	5f32                	lw	t5,44(sp)
 27a:	0074                	addi	a3,sp,12
 27c:	3a44                	fld	fs1,176(a2)
 27e:	726f772f          	0x726f772f
 282:	30322f6b          	0x30322f6b
 286:	3232                	fld	ft4,296(sp)
 288:	396c612f          	0x396c612f
 28c:	3030                	fld	fa2,96(s0)
 28e:	2f30                	fld	fa2,88(a4)
 290:	642f7773          	csrrci	a4,0x642,30
 294:	6265                	lui	tp,0x19
 296:	6775                	lui	a4,0x1d
 298:	2f726567          	0x2f726567
 29c:	6c61                	lui	s8,0x18
 29e:	6f5f 6570 6f6e      	0x6f6e65706f5f
 2a4:	632f6463          	bltu	t5,s2,8cc <_start-0x60fff734>
 2a8:	72746e6f          	jal	t3,471ce <_start-0x60fb8e32>
 2ac:	6269                	lui	tp,0x1a
 2ae:	616f6c2f          	0x616f6c2f
 2b2:	6564                	flw	fs1,76(a0)
 2b4:	7372                	flw	ft6,60(sp)
 2b6:	616c662f          	0x616c662f
 2ba:	652f6873          	csrrsi	a6,0x652,30
 2be:	6d6d                	lui	s10,0x1b
 2c0:	77642f63          	0x77642f63
 2c4:	68736d63          	bltu	t1,t2,95e <_start-0x60fff6a2>
 2c8:	72732f63          	0x72732f63
 2cc:	77642f63          	0x77642f63
 2d0:	68736d63          	bltu	t1,t2,96a <_start-0x60fff696>
 2d4:	73615f63          	bge	sp,s6,a12 <_start-0x60fff5ee>
 2d8:	6e79                	lui	t3,0x1e
 2da:	00632e63          	0x632e63
 2de:	6c62                	flw	fs8,24(sp)
 2e0:	5f6b636f          	jal	t1,b68d6 <_start-0x60f4972a>
 2e4:	6461                	lui	s0,0x18
 2e6:	7264                	flw	fs1,100(a2)
 2e8:	6200                	flw	fs0,0(a2)
 2ea:	6f6c                	flw	fa1,92(a4)
 2ec:	735f6b63          	bltu	t5,s5,a22 <_start-0x60fff5de>
 2f0:	7a69                	lui	s4,0xffffa
 2f2:	0065                	c.nop	25
 2f4:	7562                	flw	fa0,56(sp)
 2f6:	5f66                	lw	t5,120(sp)
 2f8:	6e65                	lui	t3,0x19
 2fa:	0064                	addi	s1,sp,12
 2fc:	6e756f63          	bltu	a0,t2,9fa <_start-0x60fff606>
 300:	0074                	addi	a3,sp,12
 302:	6d65                	lui	s10,0x19
 304:	636d                	lui	t1,0x1b
 306:	645f 6377 736d      	0x736d6377645f
 30c:	6368                	flw	fa0,68(a4)
 30e:	6200                	flw	fs0,0(a2)
 310:	6675                	lui	a2,0x1d
 312:	735f 6174 7472      	0x74726174735f
	...

Disassembly of section .debug_loc:

00000000 <.debug_loc>:
   0:	00a0                	addi	s0,sp,72
   2:	0000                	unimp
   4:	000000d3          	fadd.s	ft1,ft0,ft0,rne
   8:	0001                	nop
   a:	d35a                	sw	s6,164(sp)
   c:	0000                	unimp
   e:	e800                	fsw	fs0,16(s0)
  10:	0000                	unimp
  12:	0100                	addi	s0,sp,128
  14:	5800                	lw	s0,48(s0)
  16:	00e8                	addi	a0,sp,76
  18:	0000                	unimp
  1a:	000000fb          	dkhmx8	ra,zero,zero
  1e:	0001                	nop
  20:	fb5a                	fsw	fs6,180(sp)
  22:	0000                	unimp
  24:	fc00                	fsw	fs0,56(s0)
  26:	0000                	unimp
  28:	0400                	addi	s0,sp,512
  2a:	f300                	fsw	fs0,32(a4)
  2c:	5a01                	li	s4,-32
  2e:	fc9f 0000 1400      	0x14000000fc9f
  34:	0001                	nop
  36:	0100                	addi	s0,sp,128
  38:	5800                	lw	s0,48(s0)
	...
  42:	00a0                	addi	s0,sp,72
  44:	0000                	unimp
  46:	00c8                	addi	a0,sp,68
  48:	0000                	unimp
  4a:	0001                	nop
  4c:	0000c85b          	0xc85b
  50:	f800                	fsw	fs0,48(s0)
  52:	0000                	unimp
  54:	0200                	addi	s0,sp,256
  56:	7200                	flw	fs0,32(a2)
  58:	f800                	fsw	fs0,48(s0)
  5a:	0000                	unimp
  5c:	fc00                	fsw	fs0,56(s0)
  5e:	0000                	unimp
  60:	0200                	addi	s0,sp,256
  62:	7200                	flw	fs0,32(a2)
  64:	fc70                	fsw	fa2,124(s0)
  66:	0000                	unimp
  68:	1400                	addi	s0,sp,544
  6a:	0001                	nop
  6c:	0200                	addi	s0,sp,256
  6e:	7200                	flw	fs0,32(a2)
	...
  78:	a000                	fsd	fs0,0(s0)
  7a:	0000                	unimp
  7c:	d300                	sw	s0,32(a4)
  7e:	0000                	unimp
  80:	0100                	addi	s0,sp,128
  82:	5c00                	lw	s0,56(s0)
  84:	000000d3          	fadd.s	ft1,ft0,ft0,rne
  88:	0114                	addi	a3,sp,128
  8a:	0000                	unimp
  8c:	0004                	0x4
  8e:	9f5c01f3          	0x9f5c01f3
	...
  9a:	00a0                	addi	s0,sp,72
  9c:	0000                	unimp
  9e:	000000d3          	fadd.s	ft1,ft0,ft0,rne
  a2:	0001                	nop
  a4:	d35d                	beqz	a4,4a <_start-0x60ffffb6>
  a6:	0000                	unimp
  a8:	f000                	fsw	fs0,32(s0)
  aa:	0000                	unimp
  ac:	0100                	addi	s0,sp,128
  ae:	5900                	lw	s0,48(a0)
  b0:	00f0                	addi	a2,sp,76
  b2:	0000                	unimp
  b4:	00fc                	addi	a5,sp,76
  b6:	0000                	unimp
  b8:	0004                	0x4
  ba:	9f5d01f3          	0x9f5d01f3
  be:	00fc                	addi	a5,sp,76
  c0:	0000                	unimp
  c2:	0114                	addi	a3,sp,128
  c4:	0000                	unimp
  c6:	0001                	nop
  c8:	0059                	c.nop	22
  ca:	0000                	unimp
  cc:	0000                	unimp
  ce:	0000                	unimp
  d0:	d400                	sw	s0,40(s0)
  d2:	0000                	unimp
  d4:	dc00                	sw	s0,56(s0)
  d6:	0000                	unimp
  d8:	0200                	addi	s0,sp,256
  da:	3000                	fld	fs0,32(s0)
  dc:	dc9f 0000 fb00      	0xfb000000dc9f
  e2:	0000                	unimp
  e4:	0100                	addi	s0,sp,128
  e6:	5f00                	lw	s0,56(a4)
  e8:	00fc                	addi	a5,sp,76
  ea:	0000                	unimp
  ec:	0114                	addi	a3,sp,128
  ee:	0000                	unimp
  f0:	0001                	nop
  f2:	005f 0000 0000      	0x5f
  f8:	0000                	unimp
  fa:	4800                	lw	s0,16(s0)
  fc:	0000                	unimp
  fe:	7000                	flw	fs0,32(s0)
 100:	0000                	unimp
 102:	0100                	addi	s0,sp,128
 104:	5c00                	lw	s0,56(s0)
 106:	0070                	addi	a2,sp,12
 108:	0000                	unimp
 10a:	0080                	addi	s0,sp,64
 10c:	0000                	unimp
 10e:	0004                	0x4
 110:	9f7c807f          	khmx8	zero,s9,s7
 114:	0080                	addi	s0,sp,64
 116:	0000                	unimp
 118:	00a0                	addi	s0,sp,72
 11a:	0000                	unimp
 11c:	0004                	0x4
 11e:	9f5c01f3          	0x9f5c01f3
	...
 12a:	005c                	addi	a5,sp,4
 12c:	0000                	unimp
 12e:	0068                	addi	a0,sp,12
 130:	0000                	unimp
 132:	0001                	nop
 134:	845f 0000 9800      	0x98000000845f
 13a:	0000                	unimp
 13c:	0100                	addi	s0,sp,128
 13e:	5f00                	lw	s0,56(a4)
	...
 148:	005c                	addi	a5,sp,4
 14a:	0000                	unimp
 14c:	0068                	addi	a0,sp,12
 14e:	0000                	unimp
 150:	007f0007          	0x7f0007
 154:	2534                	fld	fa3,72(a0)
 156:	1a31                	addi	s4,s4,-20
 158:	849f 0000 9800      	0x98000000849f
 15e:	0000                	unimp
 160:	0700                	addi	s0,sp,896
 162:	7f00                	flw	fs0,56(a4)
 164:	3100                	fld	fs0,32(a0)
 166:	3125                	jal	fffffd8e <stack_end+0x9efffaca>
 168:	9f1a                	add	t5,t5,t1
	...
 172:	0014                	0x14
 174:	0000                	unimp
 176:	0044                	addi	s1,sp,4
 178:	0000                	unimp
 17a:	0001                	nop
 17c:	445a                	lw	s0,148(sp)
 17e:	0000                	unimp
 180:	4800                	lw	s0,16(s0)
 182:	0000                	unimp
 184:	0400                	addi	s0,sp,512
 186:	f300                	fsw	fs0,32(a4)
 188:	5a01                	li	s4,-32
 18a:	009f 0000 0000      	0x9f
 190:	0000                	unimp
 192:	1400                	addi	s0,sp,544
 194:	0000                	unimp
 196:	3800                	fld	fs0,48(s0)
 198:	0000                	unimp
 19a:	0100                	addi	s0,sp,128
 19c:	5b00                	lw	s0,48(a4)
 19e:	0038                	addi	a4,sp,8
 1a0:	0000                	unimp
 1a2:	0048                	addi	a0,sp,4
 1a4:	0000                	unimp
 1a6:	0004                	0x4
 1a8:	9f5b01f3          	0x9f5b01f3
	...
 1b4:	0014                	0x14
 1b6:	0000                	unimp
 1b8:	001c                	0x1c
 1ba:	0000                	unimp
 1bc:	0002                	c.slli64	zero
 1be:	9f30                	0x9f30
 1c0:	0020                	addi	s0,sp,8
 1c2:	0000                	unimp
 1c4:	0038                	addi	a4,sp,8
 1c6:	0000                	unimp
 1c8:	0008                	0x8
 1ca:	007e                	c.slli	zero,0x1f
 1cc:	3125007b          	dkmda32	zero,a0,s2
 1d0:	9f1a                	add	t5,t5,t1
	...
 1de:	0004                	0x4
 1e0:	0000                	unimp
 1e2:	0001                	nop
 1e4:	045a                	slli	s0,s0,0x16
 1e6:	0000                	unimp
 1e8:	1400                	addi	s0,sp,544
 1ea:	0000                	unimp
 1ec:	0100                	addi	s0,sp,128
 1ee:	5f00                	lw	s0,56(a4)
	...
 1f8:	0004                	0x4
 1fa:	0000                	unimp
 1fc:	0008                	0x8
 1fe:	0000                	unimp
 200:	0002                	c.slli64	zero
 202:	9f30                	0x9f30
 204:	0008                	0x8
 206:	0000                	unimp
 208:	0014                	0x14
 20a:	0000                	unimp
 20c:	0001                	nop
 20e:	005e                	c.slli	zero,0x17
 210:	0000                	unimp
 212:	0000                	unimp
 214:	0000                	unimp
 216:	0400                	addi	s0,sp,512
 218:	0000                	unimp
 21a:	0c00                	addi	s0,sp,528
 21c:	0000                	unimp
 21e:	0200                	addi	s0,sp,256
 220:	3000                	fld	fs0,32(s0)
 222:	0c9f 0000 1400      	0x140000000c9f
 228:	0000                	unimp
 22a:	0100                	addi	s0,sp,128
 22c:	5a00                	lw	s0,48(a2)
	...
 23a:	002c                	addi	a1,sp,8
 23c:	0000                	unimp
 23e:	0001                	nop
 240:	2c5a                	fld	fs8,400(sp)
 242:	0000                	unimp
 244:	4000                	lw	s0,0(s0)
 246:	0000                	unimp
 248:	0200                	addi	s0,sp,256
 24a:	7200                	flw	fs0,32(a2)
 24c:	4000                	lw	s0,0(s0)
 24e:	0000                	unimp
 250:	4400                	lw	s0,8(s0)
 252:	0000                	unimp
 254:	0200                	addi	s0,sp,256
 256:	7200                	flw	fs0,32(a2)
 258:	4458                	lw	a4,12(s0)
 25a:	0000                	unimp
 25c:	a000                	fsd	fs0,0(s0)
 25e:	0000                	unimp
 260:	0200                	addi	s0,sp,256
 262:	7200                	flw	fs0,32(a2)
	...
 270:	2c00                	fld	fs0,24(s0)
 272:	0000                	unimp
 274:	0100                	addi	s0,sp,128
 276:	5b00                	lw	s0,48(a4)
 278:	002c                	addi	a1,sp,8
 27a:	0000                	unimp
 27c:	00a0                	addi	s0,sp,72
 27e:	0000                	unimp
 280:	0004                	0x4
 282:	9f5b01f3          	0x9f5b01f3
	...
 292:	002c                	addi	a1,sp,8
 294:	0000                	unimp
 296:	0001                	nop
 298:	2c5c                	fld	fa5,152(s0)
 29a:	0000                	unimp
 29c:	3c00                	fld	fs0,56(s0)
 29e:	0000                	unimp
 2a0:	0100                	addi	s0,sp,128
 2a2:	5900                	lw	s0,48(a0)
 2a4:	0044                	addi	s1,sp,4
 2a6:	0000                	unimp
 2a8:	00a0                	addi	s0,sp,72
 2aa:	0000                	unimp
 2ac:	0001                	nop
 2ae:	0059                	c.nop	22
	...
 2b8:	0000                	unimp
 2ba:	2c00                	fld	fs0,24(s0)
 2bc:	0000                	unimp
 2be:	0100                	addi	s0,sp,128
 2c0:	5d00                	lw	s0,56(a0)
 2c2:	002c                	addi	a1,sp,8
 2c4:	0000                	unimp
 2c6:	0040                	addi	s0,sp,4
 2c8:	0000                	unimp
 2ca:	0006                	c.slli	zero,0x1
 2cc:	5c91                	li	s9,-28
 2ce:	3806                	fld	fa6,96(sp)
 2d0:	9f1c                	0x9f1c
 2d2:	0040                	addi	s0,sp,4
 2d4:	0000                	unimp
 2d6:	0044                	addi	s1,sp,4
 2d8:	0000                	unimp
 2da:	0006                	c.slli	zero,0x1
 2dc:	5c72                	lw	s8,60(sp)
 2de:	3806                	fld	fa6,96(sp)
 2e0:	9f1c                	0x9f1c
 2e2:	0044                	addi	s1,sp,4
 2e4:	0000                	unimp
 2e6:	00a0                	addi	s0,sp,72
 2e8:	0000                	unimp
 2ea:	0006                	c.slli	zero,0x1
 2ec:	5c91                	li	s9,-28
 2ee:	3806                	fld	fa6,96(sp)
 2f0:	9f1c                	0x9f1c
	...
 2fe:	002c                	addi	a1,sp,8
 300:	0000                	unimp
 302:	0001                	nop
 304:	2c5e                	fld	fs8,464(sp)
 306:	0000                	unimp
 308:	a000                	fsd	fs0,0(s0)
 30a:	0000                	unimp
 30c:	0400                	addi	s0,sp,512
 30e:	f300                	fsw	fs0,32(a4)
 310:	5e01                	li	t3,-32
 312:	009f 0000 0000      	0x9f
 318:	0000                	unimp
 31a:	0000                	unimp
 31c:	0000                	unimp
 31e:	1000                	addi	s0,sp,32
 320:	0000                	unimp
 322:	0100                	addi	s0,sp,128
 324:	5f00                	lw	s0,56(a4)
 326:	0010                	0x10
 328:	0000                	unimp
 32a:	0038                	addi	a4,sp,8
 32c:	0000                	unimp
 32e:	0001                	nop
 330:	4458                	lw	a4,12(s0)
 332:	0000                	unimp
 334:	7000                	flw	fs0,32(s0)
 336:	0000                	unimp
 338:	0100                	addi	s0,sp,128
 33a:	5800                	lw	s0,48(s0)
 33c:	0070                	addi	a2,sp,12
 33e:	0000                	unimp
 340:	00000073          	ecall
 344:	0001                	nop
 346:	0000735b          	0x735b
 34a:	7400                	flw	fs0,40(s0)
 34c:	0000                	unimp
 34e:	0300                	addi	s0,sp,384
 350:	7800                	flw	fs0,48(s0)
 352:	00749f7f          	kaddw	t5,s1,t2
 356:	0000                	unimp
 358:	00a0                	addi	s0,sp,72
 35a:	0000                	unimp
 35c:	0001                	nop
 35e:	0058                	addi	a4,sp,4
 360:	0000                	unimp
 362:	0000                	unimp
 364:	0000                	unimp
 366:	6000                	flw	fs0,0(s0)
 368:	0000                	unimp
 36a:	6800                	flw	fs0,16(s0)
 36c:	0000                	unimp
 36e:	0100                	addi	s0,sp,128
 370:	5a00                	lw	s0,48(a2)
 372:	0068                	addi	a0,sp,12
 374:	0000                	unimp
 376:	00000073          	ecall
 37a:	0001                	nop
 37c:	735c                	flw	fa5,36(a4)
 37e:	0000                	unimp
 380:	8800                	0x8800
 382:	0000                	unimp
 384:	0200                	addi	s0,sp,256
 386:	9100                	0x9100
 388:	8860                	0x8860
 38a:	0000                	unimp
 38c:	a000                	fsd	fs0,0(s0)
 38e:	0000                	unimp
 390:	0100                	addi	s0,sp,128
 392:	5600                	lw	s0,40(a2)
	...

Disassembly of section .debug_frame:

00000000 <.debug_frame>:
   0:	000c                	0xc
   2:	0000                	unimp
   4:	ffffffff          	0xffffffff
   8:	7c010003          	lb	zero,1984(sp)
   c:	0d01                	addi	s10,s10,0
   e:	0002                	c.slli64	zero
  10:	000c                	0xc
  12:	0000                	unimp
  14:	0000                	unimp
  16:	0000                	unimp
  18:	0010                	0x10
  1a:	6100                	flw	fs0,0(a0)
  1c:	0014                	0x14
  1e:	0000                	unimp
  20:	000c                	0xc
  22:	0000                	unimp
  24:	0000                	unimp
  26:	0000                	unimp
  28:	0024                	addi	s1,sp,8
  2a:	6100                	flw	fs0,0(a0)
  2c:	0034                	addi	a3,sp,8
  2e:	0000                	unimp
  30:	000c                	0xc
  32:	0000                	unimp
  34:	0000                	unimp
  36:	0000                	unimp
  38:	0058                	addi	a4,sp,4
  3a:	6100                	flw	fs0,0(a0)
  3c:	0058                	addi	a4,sp,4
  3e:	0000                	unimp
  40:	0024                	addi	s1,sp,8
  42:	0000                	unimp
  44:	0000                	unimp
  46:	0000                	unimp
  48:	00b0                	addi	a2,sp,72
  4a:	6100                	flw	fs0,0(a0)
  4c:	0074                	addi	a3,sp,12
  4e:	0000                	unimp
  50:	0e44                	addi	s1,sp,788
  52:	4c10                	lw	a2,24(s0)
  54:	0288                	addi	a0,sp,320
  56:	0389                	addi	t2,t2,2
  58:	0181                	addi	gp,gp,0
  5a:	0a78                	addi	a4,sp,284
  5c:	44c8                	lw	a0,12(s1)
  5e:	44c1                	li	s1,16
  60:	48c9                	li	a7,18
  62:	000e                	c.slli	zero,0x3
  64:	0b44                	addi	s1,sp,404
  66:	0000                	unimp
  68:	000c                	0xc
  6a:	0000                	unimp
  6c:	ffffffff          	0xffffffff
  70:	7c010003          	lb	zero,1984(sp)
  74:	0d01                	addi	s10,s10,0
  76:	0002                	c.slli64	zero
  78:	0024                	addi	s1,sp,8
  7a:	0000                	unimp
  7c:	0068                	addi	a0,sp,12
  7e:	0000                	unimp
  80:	0124                	addi	s1,sp,136
  82:	6100                	flw	fs0,0(a0)
  84:	00a0                	addi	s0,sp,72
  86:	0000                	unimp
  88:	0e44                	addi	s1,sp,788
  8a:	4428                	lw	a0,72(s0)
  8c:	0288                	addi	a0,sp,320
  8e:	8950                	0x8950
  90:	5c018103          	lb	sp,1472(gp)
  94:	c10a                	sw	sp,128(sp)
  96:	c844                	sw	s1,20(s0)
  98:	c944                	sw	s1,20(a0)
  9a:	0e44                	addi	s1,sp,788
  9c:	4400                	lw	s0,8(s0)
  9e:	          	0xb
