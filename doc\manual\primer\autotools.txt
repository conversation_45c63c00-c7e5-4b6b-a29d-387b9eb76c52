/** @page primerautotools OpenOCD Autotools Primer

This page provides an overview to OpenOCD's use of the GNU autotool suite:
- @ref primerautoconf
- @ref primerautomake
- @ref primerlibtool

Most developers do not need to concern themselves with these tools, as
the @ref primerbootstrap script runs these tools in the required sequence.

@section primerbootstrap Autotools Bootstrap

The @c bootstrap script should be used by developers to run the
autotools in the correct sequence.

When run after a fresh checkout, this script generates the build files
required to compile the project, producing the project configure script.

@subsection primerbootstrapcures Problems Solved By Bootstrap

For example, the build system can fail in unexpected ways after running
<code>git pull</code>.  Here, the <code>make maintainer-clean</code>
should be used to remove all of the files generated by the @c bootstrap
script and subsequent build processes.

In this particular case, one may also need to remove stray files by hand
after running this command to ensure everything is rebuilt properly.
This step should be necessary only if the @c maintainer-clean was run
@b after altering the build system files with git. If it is run
@b before any updates, the build system should never leave artifacts
in the tree.

Without such precautions, changes can be introduced that leave the tree
timestamps in an inconsistent state, producing strange compile errors
that are resolve after such diligence.

@subsection primermaintainerclean Autotools Cleaning

Normally, all files generated by the bootstrap script, configure
process, and build system should be removed after running <code>make
maintainer-clean</code>.  Automatically generated files that remain
after this should be listed in @c MAINTAINERCLEANFILES,
@c DISTCLEANFILES, or @c CLEANFILES, depending on which stage of the
build process they are produced.

@section primerautoconf Autoconf Configuration Script

The @c autoconf program generates the @c configure script from
@c configure.in, using serious Perl voodoo.  The resulting script is
included in the project distribution packages and run by users to
configure the build process for their system.

@section primerautomake Automake Makefiles

The @c automake program generates @c Makefile.in files (from @c
Makefile.am files).  These files are later processed by the configure
script produced by @c autoconf.

@subsection primerautomakenewfiles Creating Makefile.am Files

This section shows how to add a @c Makefile.am in a new directory (or
one that lacks one).
-# The new directory must be listed in the @c SUBDIRS variable in the
parent directory's Makefile.am:
@code
$ echo 'SUBDIRS += directory' >>../Makefile.am
@endcode
-# Create an bare-bones Makefile.am file in directory that needs it:
@code
$ echo "MAINTAINERCLEANFILES = Makefile.in" >Makefile.am
@endcode
-# The @c configure.in script must be updated, so it generates the required
Makefile when the @a configure script is run by the user:
@verbatim
AC_OUTPUT([
		...
		path/to/new/Makefile
	])
@endverbatim

Note: these instructions are @b not meant to be used literally, rather
they are shown for demonstration purposes.

The default MAINTAINERCLEANFILES rule ensures that the
automake-generated @c Makefile.in file will be removed when developers
run <code>make maintainer-clean</code>.  Additional rules may be added
after this; however, the project should bootstrap and tear down cleanly
after taking these minimal steps, with the new directory being visited
during the @c make sequence.

@subsection primerautomaketweaks Updating Makefile.am Files

Adding, removing, and renaming files from the project tree usually
requires updating the autotools inputs. This section will help describe
how to do this as questions arise.

@section primerlibtool Libtool and Libraries

The @c libtool program provides the means of generating libraries in a
portable and painless manner (relatively speaking).

This section will contain an answer to "what does libtool give OpenOCD?"
and "what do developers need to consider in new code?"

@section primerautotoolsmation Autotools Automation

This section outlines three ways the autotools provides automation to
assist with testing and distribution:
- @ref primerautocheck -- automatic unit and smoke tests
- @ref primerautodistcheck -- automatic distribution and packaging tests

@subsection primerautocheck make check

The <code>make check</code> command will run the OpenOCD test suite,
once it has been integrated as such.  This section will contain
information about how to extend the testing build system components to
implement new checks.

@subsection primerautodistcheck make distcheck

The <code>make distcheck</code> command produces an archive of the
project deliverables (using <code>make dist</code>) and verifies its
integrity for distribution by attempting to use the package in the same
manner as a user.

These checks includes the following steps:
-# Unpack the project archive into its expected directory.
-# Configure and build the project in a temporary out-of-tree directory.
-# Run <code>make check</code> to ensure the distributed code passes all tests.
-# Run <code>make install</code> into a temporary installation directory.
-# Check that <code>make uninstall</code> removes all files that were installed.
-# Check that <code>make distclean</code> removes all files created
during all other steps (except the first).

If all of these steps complete successfully, the @c make process will
output a friendly message indicating the archive is ready to be
distributed.

 */
/** @file

This file contains the @ref primerautotools page.

 */
