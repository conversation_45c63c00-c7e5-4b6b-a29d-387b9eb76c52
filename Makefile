# JTAG Writer Makefile

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -Iinclude
LDFLAGS = 

# Windows特定设置
ifeq ($(OS),Windows_NT)
    LDFLAGS += -lws2_32
    TARGET = jtag_writer.exe
else
    TARGET = jtag_writer
endif

# 源文件目录
SRCDIR = src
OBJDIR = build/obj
BINDIR = build

# 源文件 (只包含现有文件)
SOURCES = $(SRCDIR)/main.c \
          $(SRCDIR)/core/jtag_writer.c \
          $(SRCDIR)/core/flash_operations.c \
          $(SRCDIR)/network/openocd_client.c \
          $(SRCDIR)/config/config_manager.c \
          $(SRCDIR)/ui/cli_interface.c \
          $(SRCDIR)/utils/logger.c

# 目标文件
OBJECTS = $(SOURCES:$(SRCDIR)/%.c=$(OBJDIR)/%.o)

# 默认目标
all: $(BINDIR)/$(TARGET)

# 创建目录
$(OBJDIR):
	mkdir -p $(OBJDIR)/core $(OBJDIR)/network $(OBJDIR)/config $(OBJDIR)/ui $(OBJDIR)/utils

$(BINDIR):
	mkdir -p $(BINDIR)

# 编译目标文件
$(OBJDIR)/%.o: $(SRCDIR)/%.c | $(OBJDIR)
	$(CC) $(CFLAGS) -c $< -o $@

# 链接可执行文件
$(BINDIR)/$(TARGET): $(OBJECTS) | $(BINDIR)
	$(CC) $(OBJECTS) -o $@ $(LDFLAGS)

# 清理
clean:
	rm -rf $(OBJDIR) $(BINDIR)/$(TARGET)

# 安装
install: $(BINDIR)/$(TARGET)
	cp $(BINDIR)/$(TARGET) /usr/local/bin/ 2>/dev/null || echo "Install requires sudo or admin privileges"

# 测试编译
test: $(BINDIR)/$(TARGET)
	$(BINDIR)/$(TARGET) --version

.PHONY: all clean install test
