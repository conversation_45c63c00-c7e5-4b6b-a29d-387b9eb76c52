
D:/work/2022/al9000/sw/debugger/al_openocd/contrib/loaders/flash/emmc/dwcmshc/build/emmc_crc_aarch_64.elf:     file format elf64-littleaarch64


Disassembly of section .text:

0000000061000000 <_start>:
    61000000:	5800009c 	ldr	x28, 61000010 <_start+0x10>
    61000004:	9100039f 	mov	sp, x28
    61000008:	94000047 	bl	61000124 <emmc_dwcmshc>
    6100000c:	d4400160 	hlt	#0xb
    61000010:	610007e0 	.inst	0x610007e0 ; undefined
    61000014:	00000000 	udf	#0

0000000061000018 <emmc_wait_fifo>:
    61000018:	aa0003e1 	mov	x1, x0
    6100001c:	b9400022 	ldr	w2, [x1]
    61000020:	b9400420 	ldr	w0, [x1, #4]
    61000024:	6b00005f 	cmp	w2, w0
    61000028:	54ffffa0 	b.eq	6100001c <emmc_wait_fifo+0x4>  // b.none
    6100002c:	d65f03c0 	ret

0000000061000030 <emmc_poll_int>:
    61000030:	52800022 	mov	w2, #0x1                   	// #1
    61000034:	12001c23 	and	w3, w1, #0xff
    61000038:	1ac12041 	lsl	w1, w2, w1
    6100003c:	b9403002 	ldr	w2, [x0, #48]
    61000040:	6a02003f 	tst	w1, w2
    61000044:	54ffffc0 	b.eq	6100003c <emmc_poll_int+0xc>  // b.none
    61000048:	6b4243ff 	cmp	wzr, w2, lsr #16
    6100004c:	54ffff81 	b.ne	6100003c <emmc_poll_int+0xc>  // b.any
    61000050:	52800021 	mov	w1, #0x1                   	// #1
    61000054:	1ac32021 	lsl	w1, w1, w3
    61000058:	2a020021 	orr	w1, w1, w2
    6100005c:	b9003001 	str	w1, [x0, #48]
    61000060:	52800000 	mov	w0, #0x0                   	// #0
    61000064:	d65f03c0 	ret

0000000061000068 <emmc_write_block>:
    61000068:	b9000801 	str	w1, [x0, #8]
    6100006c:	52801041 	mov	w1, #0x82                  	// #130
    61000070:	72a30741 	movk	w1, #0x183a, lsl #16
    61000074:	b9000c01 	str	w1, [x0, #12]
    61000078:	b9403001 	ldr	w1, [x0, #48]
    6100007c:	3627ffe1 	tbz	w1, #4, 61000078 <emmc_write_block+0x10>
    61000080:	6b4143ff 	cmp	wzr, w1, lsr #16
    61000084:	54ffffa1 	b.ne	61000078 <emmc_write_block+0x10>  // b.any
    61000088:	d2800001 	mov	x1, #0x0                   	// #0
    6100008c:	b8617843 	ldr	w3, [x2, x1, lsl #2]
    61000090:	91000421 	add	x1, x1, #0x1
    61000094:	b9002003 	str	w3, [x0, #32]
    61000098:	f102003f 	cmp	x1, #0x80
    6100009c:	54ffff81 	b.ne	6100008c <emmc_write_block+0x24>  // b.any
    610000a0:	b9403001 	ldr	w1, [x0, #48]
    610000a4:	360fffe1 	tbz	w1, #1, 610000a0 <emmc_write_block+0x38>
    610000a8:	6b4143ff 	cmp	wzr, w1, lsr #16
    610000ac:	54ffffa1 	b.ne	610000a0 <emmc_write_block+0x38>  // b.any
    610000b0:	321f0021 	orr	w1, w1, #0x2
    610000b4:	b9003001 	str	w1, [x0, #48]
    610000b8:	d65f03c0 	ret

00000000610000bc <emmc_read_block>:
    610000bc:	a9bd7bfd 	stp	x29, x30, [sp, #-48]!
    610000c0:	910003fd 	mov	x29, sp
    610000c4:	a90153f3 	stp	x19, x20, [sp, #16]
    610000c8:	aa0003f3 	mov	x19, x0
    610000cc:	2a0303f4 	mov	w20, w3
    610000d0:	f90013f5 	str	x21, [sp, #32]
    610000d4:	aa0103f5 	mov	x21, x1
    610000d8:	52801241 	mov	w1, #0x92                  	// #146
    610000dc:	b9000802 	str	w2, [x0, #8]
    610000e0:	72a22741 	movk	w1, #0x113a, lsl #16
    610000e4:	b9000c01 	str	w1, [x0, #12]
    610000e8:	528000a1 	mov	w1, #0x5                   	// #5
    610000ec:	97ffffd1 	bl	61000030 <emmc_poll_int>
    610000f0:	d2800000 	mov	x0, #0x0                   	// #0
    610000f4:	6b00029f 	cmp	w20, w0
    610000f8:	540000e8 	b.hi	61000114 <emmc_read_block+0x58>  // b.pmore
    610000fc:	aa1303e0 	mov	x0, x19
    61000100:	52800021 	mov	w1, #0x1                   	// #1
    61000104:	a94153f3 	ldp	x19, x20, [sp, #16]
    61000108:	f94013f5 	ldr	x21, [sp, #32]
    6100010c:	a8c37bfd 	ldp	x29, x30, [sp], #48
    61000110:	17ffffc8 	b	61000030 <emmc_poll_int>
    61000114:	b9402261 	ldr	w1, [x19, #32]
    61000118:	b8207aa1 	str	w1, [x21, x0, lsl #2]
    6100011c:	91000400 	add	x0, x0, #0x1
    61000120:	17fffff5 	b	610000f4 <emmc_read_block+0x38>

0000000061000124 <emmc_dwcmshc>:
    61000124:	a9ba7bfd 	stp	x29, x30, [sp, #-96]!
    61000128:	910003fd 	mov	x29, sp
    6100012c:	a90153f3 	stp	x19, x20, [sp, #16]
    61000130:	2a0103f4 	mov	w20, w1
    61000134:	2a0203f3 	mov	w19, w2
    61000138:	a9025bf5 	stp	x21, x22, [sp, #32]
    6100013c:	2a0303f5 	mov	w21, w3
    61000140:	12800016 	mov	w22, #0xffffffff            	// #-1
    61000144:	a90363f7 	stp	x23, x24, [sp, #48]
    61000148:	90000017 	adrp	x23, 61000000 <_start>
    6100014c:	aa0003f8 	mov	x24, x0
    61000150:	911f82f7 	add	x23, x23, #0x7e0
    61000154:	a9046bf9 	stp	x25, x26, [sp, #64]
    61000158:	90000019 	adrp	x25, 61000000 <_start>
    6100015c:	91078339 	add	x25, x25, #0x1e0
    61000160:	f9002bfb 	str	x27, [sp, #80]
    61000164:	13027c3b 	asr	w27, w1, #2
    61000168:	7100027f 	cmp	w19, #0x0
    6100016c:	5400012c 	b.gt	61000190 <emmc_dwcmshc+0x6c>
    61000170:	2a1603e0 	mov	w0, w22
    61000174:	a94153f3 	ldp	x19, x20, [sp, #16]
    61000178:	a9425bf5 	ldp	x21, x22, [sp, #32]
    6100017c:	a94363f7 	ldp	x23, x24, [sp, #48]
    61000180:	a9446bf9 	ldp	x25, x26, [sp, #64]
    61000184:	f9402bfb 	ldr	x27, [sp, #80]
    61000188:	a8c67bfd 	ldp	x29, x30, [sp], #96
    6100018c:	d65f03c0 	ret
    61000190:	6b14027f 	cmp	w19, w20
    61000194:	aa1703e1 	mov	x1, x23
    61000198:	1a94d27a 	csel	w26, w19, w20, le
    6100019c:	aa1803e0 	mov	x0, x24
    610001a0:	2a1b03e3 	mov	w3, w27
    610001a4:	2a1503e2 	mov	w2, w21
    610001a8:	97ffffc5 	bl	610000bc <emmc_read_block>
    610001ac:	aa1703e1 	mov	x1, x23
    610001b0:	2a1a03e0 	mov	w0, w26
    610001b4:	51000400 	sub	w0, w0, #0x1
    610001b8:	3100041f 	cmn	w0, #0x1
    610001bc:	54000081 	b.ne	610001cc <emmc_dwcmshc+0xa8>  // b.any
    610001c0:	4b1a0273 	sub	w19, w19, w26
    610001c4:	110006b5 	add	w21, w21, #0x1
    610001c8:	17ffffe8 	b	61000168 <emmc_dwcmshc+0x44>
    610001cc:	38401422 	ldrb	w2, [x1], #1
    610001d0:	4a566042 	eor	w2, w2, w22, lsr #24
    610001d4:	b8627b22 	ldr	w2, [x25, x2, lsl #2]
    610001d8:	4a162056 	eor	w22, w2, w22, lsl #8
    610001dc:	17fffff6 	b	610001b4 <emmc_dwcmshc+0x90>

Disassembly of section .rodata:

00000000610001e0 <crc32_table>:
    610001e0:	00000000 	udf	#0
    610001e4:	04c11db7 	sub	z23.d, p7/m, z23.d, z13.d
    610001e8:	09823b6e 	.inst	0x09823b6e ; undefined
    610001ec:	0d4326d9 	.inst	0x0d4326d9 ; undefined
    610001f0:	130476dc 	sbfx	w28, w22, #4, #26
    610001f4:	17c56b6b 	b	6015afa0 <_start-0xea5060>
    610001f8:	1a864db2 	.inst	0x1a864db2 ; undefined
    610001fc:	1e475005 	.inst	0x1e475005 ; undefined
    61000200:	2608edb8 	.inst	0x2608edb8 ; undefined
    61000204:	22c9f00f 	.inst	0x22c9f00f ; undefined
    61000208:	2f8ad6d6 	.inst	0x2f8ad6d6 ; undefined
    6100020c:	2b4bcb61 	.inst	0x2b4bcb61 ; undefined
    61000210:	350c9b64 	cbnz	w4, 6101957c <out_buf+0x18d9c>
    61000214:	31cd86d3 	.inst	0x31cd86d3 ; undefined
    61000218:	3c8ea00a 	stur	q10, [x0, #234]
    6100021c:	384fbdbd 	ldrb	w29, [x13, #251]!
    61000220:	4c11db70 	.inst	0x4c11db70 ; undefined
    61000224:	48d0c6c7 	.inst	0x48d0c6c7 ; undefined
    61000228:	4593e01e 	ssra	z30.d, z0.d, #45
    6100022c:	4152fda9 	.inst	0x4152fda9 ; undefined
    61000230:	5f15adac 	.inst	0x5f15adac ; undefined
    61000234:	5bd4b01b 	.inst	0x5bd4b01b ; undefined
    61000238:	569796c2 	.inst	0x569796c2 ; undefined
    6100023c:	52568b75 	.inst	0x52568b75 ; undefined
    61000240:	6a1936c8 	ands	w8, w22, w25, lsl #13
    61000244:	6ed82b7f 	.inst	0x6ed82b7f ; undefined
    61000248:	639b0da6 	.inst	0x639b0da6 ; undefined
    6100024c:	675a1011 	.inst	0x675a1011 ; undefined
    61000250:	791d4014 	strh	w20, [x0, #3744]
    61000254:	7ddc5da3 	.inst	0x7ddc5da3 ; undefined
    61000258:	709f7b7a 	adr	x26, 60f3f1c7 <_start-0xc0e39>
    6100025c:	745e66cd 	.inst	0x745e66cd ; undefined
    61000260:	9823b6e0 	ldrsw	x0, 6104793c <out_buf+0x4715c>
    61000264:	9ce2ab57 	ldr	q23, 60fc57cc <_start-0x3a834>
    61000268:	91a18d8e 	.inst	0x91a18d8e ; undefined
    6100026c:	95609039 	bl	66824350 <out_buf+0x5823b70>
    61000270:	8b27c03c 	add	x28, x1, w7, sxtw
    61000274:	8fe6dd8b 	.inst	0x8fe6dd8b ; undefined
    61000278:	82a5fb52 	.inst	0x82a5fb52 ; undefined
    6100027c:	8664e6e5 	.inst	0x8664e6e5 ; undefined
    61000280:	be2b5b58 	.inst	0xbe2b5b58 ; undefined
    61000284:	baea46ef 	.inst	0xbaea46ef ; undefined
    61000288:	b7a96036 	tbnz	x22, #53, 61002e8c <out_buf+0x26ac>
    6100028c:	b3687d81 	bfi	x1, x12, #24, #32
    61000290:	ad2f2d84 	stp	q4, q11, [x12, #-544]
    61000294:	a9ee3033 	ldp	x19, x12, [x1, #-288]!
    61000298:	a4ad16ea 	ld1roh	{z10.h}, p5/z, [x23, x13, lsl #1]
    6100029c:	a06c0b5d 	.inst	0xa06c0b5d ; undefined
    610002a0:	d4326d90 	.inst	0xd4326d90 ; undefined
    610002a4:	d0f37027 	adrp	x7, 47e06000 <_start-0x191fa000>
    610002a8:	ddb056fe 	.inst	0xddb056fe ; undefined
    610002ac:	d9714b49 	stzg	x9, [x26, #-3776]
    610002b0:	c7361b4c 	.inst	0xc7361b4c ; undefined
    610002b4:	c3f706fb 	.inst	0xc3f706fb ; undefined
    610002b8:	ceb42022 	.inst	0xceb42022 ; undefined
    610002bc:	ca753d95 	eon	x21, x12, x21, lsr #15
    610002c0:	f23a8028 	ands	x8, x1, #0x40004000400040
    610002c4:	f6fb9d9f 	.inst	0xf6fb9d9f ; undefined
    610002c8:	fbb8bb46 	.inst	0xfbb8bb46 ; undefined
    610002cc:	ff79a6f1 	.inst	0xff79a6f1 ; undefined
    610002d0:	e13ef6f4 	.inst	0xe13ef6f4 ; undefined
    610002d4:	e5ffeb43 	st4d	{z3.d-z6.d}, p2, [x26, #-4, mul vl]
    610002d8:	e8bccd9a 	.inst	0xe8bccd9a ; undefined
    610002dc:	ec7dd02d 	.inst	0xec7dd02d ; undefined
    610002e0:	34867077 	cbz	w23, 60f0d0ec <_start-0xf2f14>
    610002e4:	30476dc0 	adr	x0, 6108f09d <out_buf+0x8e8bd>
    610002e8:	3d044b19 	str	b25, [x24, #274]
    610002ec:	39c556ae 	ldrsb	w14, [x21, #341]
    610002f0:	278206ab 	.inst	0x278206ab ; undefined
    610002f4:	23431b1c 	.inst	0x23431b1c ; undefined
    610002f8:	2e003dc5 	.inst	0x2e003dc5 ; undefined
    610002fc:	2ac12072 	orr	w18, w3, w1, ror #8
    61000300:	128e9dcf 	mov	w15, #0xffff8b11            	// #-29935
    61000304:	164f8078 	b	5a3e04e4 <_start-0x6c1fb1c>
    61000308:	1b0ca6a1 	msub	w1, w21, w12, w9
    6100030c:	1fcdbb16 	fmsub	h22, h24, h13, h14
    61000310:	018aeb13 	.inst	0x018aeb13 ; undefined
    61000314:	054bf6a4 	.inst	0x054bf6a4 ; undefined
    61000318:	0808d07d 	stlxrb	w8, w29, [x3]
    6100031c:	0cc9cdca 	.inst	0x0cc9cdca ; undefined
    61000320:	7897ab07 	ldtrsh	x7, [x24, #-134]
    61000324:	7c56b6b0 	ldr	h16, [x21], #-149
    61000328:	71159069 	subs	w9, w3, #0x564
    6100032c:	75d48dde 	.inst	0x75d48dde ; undefined
    61000330:	6b93dddb 	.inst	0x6b93dddb ; undefined
    61000334:	6f52c06c 	.inst	0x6f52c06c ; undefined
    61000338:	6211e6b5 	.inst	0x6211e6b5 ; undefined
    6100033c:	66d0fb02 	.inst	0x66d0fb02 ; undefined
    61000340:	5e9f46bf 	.inst	0x5e9f46bf ; undefined
    61000344:	5a5e5b08 	.inst	0x5a5e5b08 ; undefined
    61000348:	571d7dd1 	.inst	0x571d7dd1 ; undefined
    6100034c:	53dc6066 	.inst	0x53dc6066 ; undefined
    61000350:	4d9b3063 	st3	{v3.b-v5.b}[12], [x3], x27
    61000354:	495a2dd4 	.inst	0x495a2dd4 ; undefined
    61000358:	44190b0d 	.inst	0x44190b0d ; undefined
    6100035c:	40d816ba 	.inst	0x40d816ba ; undefined
    61000360:	aca5c697 	stp	q23, q17, [x20], #-848
    61000364:	a864db20 	ldnp	x0, x22, [x25, #-440]
    61000368:	a527fdf9 	ld2w	{z25.s, z26.s}, p7/z, [x15, #14, mul vl]
    6100036c:	a1e6e04e 	.inst	0xa1e6e04e ; undefined
    61000370:	bfa1b04b 	.inst	0xbfa1b04b ; undefined
    61000374:	bb60adfc 	.inst	0xbb60adfc ; undefined
    61000378:	b6238b25 	tbz	x5, #36, 610074dc <out_buf+0x6cfc>
    6100037c:	b2e29692 	.inst	0xb2e29692 ; undefined
    61000380:	8aad2b2f 	bic	x15, x25, x13, asr #10
    61000384:	8e6c3698 	.inst	0x8e6c3698 ; undefined
    61000388:	832f1041 	.inst	0x832f1041 ; undefined
    6100038c:	87ee0df6 	.inst	0x87ee0df6 ; undefined
    61000390:	99a95df3 	.inst	0x99a95df3 ; undefined
    61000394:	9d684044 	.inst	0x9d684044 ; undefined
    61000398:	902b669d 	adrp	x29, b7cd0000 <out_buf+0x56ccf820>
    6100039c:	94ea7b2a 	bl	64a9f044 <out_buf+0x3a9e864>
    610003a0:	e0b41de7 	.inst	0xe0b41de7 ; undefined
    610003a4:	e4750050 	.inst	0xe4750050 ; undefined
    610003a8:	e9362689 	.inst	0xe9362689 ; undefined
    610003ac:	edf73b3e 	.inst	0xedf73b3e ; undefined
    610003b0:	f3b06b3b 	.inst	0xf3b06b3b ; undefined
    610003b4:	f771768c 	.inst	0xf771768c ; undefined
    610003b8:	fa325055 	.inst	0xfa325055 ; undefined
    610003bc:	fef34de2 	.inst	0xfef34de2 ; undefined
    610003c0:	c6bcf05f 	.inst	0xc6bcf05f ; undefined
    610003c4:	c27dede8 	.inst	0xc27dede8 ; undefined
    610003c8:	cf3ecb31 	.inst	0xcf3ecb31 ; undefined
    610003cc:	cbffd686 	.inst	0xcbffd686 ; undefined
    610003d0:	d5b88683 	.inst	0xd5b88683 ; undefined
    610003d4:	d1799b34 	sub	x20, x25, #0xe66, lsl #12
    610003d8:	dc3abded 	.inst	0xdc3abded ; undefined
    610003dc:	d8fba05a 	prfm	#0x1a, 60ff77e4 <_start-0x881c>
    610003e0:	690ce0ee 	stgp	x14, x24, [x7, #400]
    610003e4:	6dcdfd59 	ldp	d25, d31, [x10, #216]!
    610003e8:	608edb80 	.inst	0x608edb80 ; undefined
    610003ec:	644fc637 	.inst	0x644fc637 ; undefined
    610003f0:	7a089632 	.inst	0x7a089632 ; undefined
    610003f4:	7ec98b85 	.inst	0x7ec98b85 ; undefined
    610003f8:	738aad5c 	.inst	0x738aad5c ; undefined
    610003fc:	774bb0eb 	.inst	0x774bb0eb ; undefined
    61000400:	4f040d56 	.inst	0x4f040d56 ; undefined
    61000404:	4bc510e1 	.inst	0x4bc510e1 ; undefined
    61000408:	46863638 	.inst	0x46863638 ; undefined
    6100040c:	42472b8f 	.inst	0x42472b8f ; undefined
    61000410:	5c007b8a 	ldr	d10, 61001380 <out_buf+0xba0>
    61000414:	58c1663d 	ldr	x29, 60f830d8 <_start-0x7cf28>
    61000418:	558240e4 	.inst	0x558240e4 ; undefined
    6100041c:	51435d53 	sub	w19, w10, #0xd7, lsl #12
    61000420:	251d3b9e 	cmple	p14.b, p6/z, z28.b, #-3
    61000424:	21dc2629 	.inst	0x21dc2629 ; undefined
    61000428:	2c9f00f0 	stp	s16, s0, [x7], #248
    6100042c:	285e1d47 	ldnp	w7, w7, [x10, #240]
    61000430:	36194d42 	tbz	w2, #3, 61002dd8 <out_buf+0x25f8>
    61000434:	32d850f5 	.inst	0x32d850f5 ; undefined
    61000438:	3f9b762c 	.inst	0x3f9b762c ; undefined
    6100043c:	3b5a6b9b 	.inst	0x3b5a6b9b ; undefined
    61000440:	0315d626 	.inst	0x0315d626 ; undefined
    61000444:	07d4cb91 	.inst	0x07d4cb91 ; undefined
    61000448:	0a97ed48 	.inst	0x0a97ed48 ; undefined
    6100044c:	0e56f0ff 	.inst	0x0e56f0ff ; undefined
    61000450:	1011a0fa 	adr	x26, 6102386c <out_buf+0x2308c>
    61000454:	14d0bd4d 	b	6442f988 <out_buf+0x342f1a8>
    61000458:	19939b94 	.inst	0x19939b94 ; undefined
    6100045c:	1d528623 	.inst	0x1d528623 ; undefined
    61000460:	f12f560e 	subs	x14, x16, #0xbd5
    61000464:	f5ee4bb9 	.inst	0xf5ee4bb9 ; undefined
    61000468:	f8ad6d60 	ldrab	x0, [x11, #1712]!
    6100046c:	fc6c70d7 	.inst	0xfc6c70d7 ; undefined
    61000470:	e22b20d2 	.inst	0xe22b20d2 ; undefined
    61000474:	e6ea3d65 	.inst	0xe6ea3d65 ; undefined
    61000478:	eba91bbc 	.inst	0xeba91bbc ; undefined
    6100047c:	ef68060b 	.inst	0xef68060b ; undefined
    61000480:	d727bbb6 	.inst	0xd727bbb6 ; undefined
    61000484:	d3e6a601 	.inst	0xd3e6a601 ; undefined
    61000488:	dea580d8 	.inst	0xdea580d8 ; undefined
    6100048c:	da649d6f 	.inst	0xda649d6f ; undefined
    61000490:	c423cd6a 	ld1b	{z10.d}, p3/z, [z11.d, #3]
    61000494:	c0e2d0dd 	.inst	0xc0e2d0dd ; undefined
    61000498:	cda1f604 	.inst	0xcda1f604 ; undefined
    6100049c:	c960ebb3 	.inst	0xc960ebb3 ; undefined
    610004a0:	bd3e8d7e 	str	s30, [x11, #16012]
    610004a4:	b9ff90c9 	.inst	0xb9ff90c9 ; undefined
    610004a8:	b4bcb610 	cbz	x16, 60f79b68 <_start-0x86498>
    610004ac:	b07daba7 	adrp	x7, 15c575000 <out_buf+0xfb574820>
    610004b0:	ae3afba2 	.inst	0xae3afba2 ; undefined
    610004b4:	aafbe615 	orn	x21, x16, x27, ror #57
    610004b8:	a7b8c0cc 	.inst	0xa7b8c0cc ; undefined
    610004bc:	a379dd7b 	.inst	0xa379dd7b ; undefined
    610004c0:	9b3660c6 	smaddl	x6, w6, w22, x24
    610004c4:	9ff77d71 	.inst	0x9ff77d71 ; undefined
    610004c8:	92b45ba8 	mov	x8, #0xffffffff5d22ffff    	// #-2732392449
    610004cc:	9675461f 	bl	5ad51d48 <_start-0x62ae2b8>
    610004d0:	8832161a 	stxp	w18, w26, w5, [x16]
    610004d4:	8cf30bad 	.inst	0x8cf30bad ; undefined
    610004d8:	81b02d74 	.inst	0x81b02d74 ; undefined
    610004dc:	857130c3 	.inst	0x857130c3 ; undefined
    610004e0:	5d8a9099 	.inst	0x5d8a9099 ; undefined
    610004e4:	594b8d2e 	.inst	0x594b8d2e ; undefined
    610004e8:	5408abf7 	.inst	0x5408abf7 ; undefined
    610004ec:	50c9b640 	adr	x0, 60f93bb6 <_start-0x6c44a>
    610004f0:	4e8ee645 	.inst	0x4e8ee645 ; undefined
    610004f4:	4a4ffbf2 	.inst	0x4a4ffbf2 ; undefined
    610004f8:	470cdd2b 	.inst	0x470cdd2b ; undefined
    610004fc:	43cdc09c 	.inst	0x43cdc09c ; undefined
    61000500:	7b827d21 	.inst	0x7b827d21 ; undefined
    61000504:	7f436096 	.inst	0x7f436096 ; undefined
    61000508:	7200464f 	ands	w15, w18, #0x3ffff
    6100050c:	76c15bf8 	.inst	0x76c15bf8 ; undefined
    61000510:	68860bfd 	stgp	x29, x2, [sp], #192
    61000514:	6c47164a 	ldnp	d10, d5, [x18, #112]
    61000518:	61043093 	.inst	0x61043093 ; undefined
    6100051c:	65c52d24 	fminnmv	d4, p3, z9.d
    61000520:	119b4be9 	.inst	0x119b4be9 ; undefined
    61000524:	155a565e 	b	66695e9c <out_buf+0x56956bc>
    61000528:	18197087 	ldr	w7, 61033338 <out_buf+0x32b58>
    6100052c:	1cd86d30 	ldr	s16, 60fb12d0 <_start-0x4ed30>
    61000530:	029f3d35 	.inst	0x029f3d35 ; undefined
    61000534:	065e2082 	.inst	0x065e2082 ; undefined
    61000538:	0b1d065b 	add	w27, w18, w29, lsl #1
    6100053c:	0fdc1bec 	.inst	0x0fdc1bec ; undefined
    61000540:	3793a651 	tbnz	w17, #18, 61007a08 <out_buf+0x7228>
    61000544:	3352bbe6 	.inst	0x3352bbe6 ; undefined
    61000548:	3e119d3f 	.inst	0x3e119d3f ; undefined
    6100054c:	3ad08088 	.inst	0x3ad08088 ; undefined
    61000550:	2497d08d 	cmphs	p13.s, p4/z, z4.s, z23.d
    61000554:	2056cd3a 	.inst	0x2056cd3a ; undefined
    61000558:	2d15ebe3 	stp	s3, s26, [sp, #172]
    6100055c:	29d4f654 	ldp	w20, w29, [x18, #164]!
    61000560:	c5a92679 	.inst	0xc5a92679 ; undefined
    61000564:	c1683bce 	.inst	0xc1683bce ; undefined
    61000568:	cc2b1d17 	.inst	0xcc2b1d17 ; undefined
    6100056c:	c8ea00a0 	.inst	0xc8ea00a0 ; undefined
    61000570:	d6ad50a5 	.inst	0xd6ad50a5 ; undefined
    61000574:	d26c4d12 	eor	x18, x8, #0xfffff00000
    61000578:	df2f6bcb 	.inst	0xdf2f6bcb ; undefined
    6100057c:	dbee767c 	.inst	0xdbee767c ; undefined
    61000580:	e3a1cbc1 	.inst	0xe3a1cbc1 ; undefined
    61000584:	e760d676 	.inst	0xe760d676 ; undefined
    61000588:	ea23f0af 	bics	x15, x5, x3, lsl #60
    6100058c:	eee2ed18 	.inst	0xeee2ed18 ; undefined
    61000590:	f0a5bd1d 	adrp	x29, ffffffffac7a3000 <out_buf+0xffffffff4b7a2820>
    61000594:	f464a0aa 	.inst	0xf464a0aa ; undefined
    61000598:	f9278673 	str	x19, [x19, #20232]
    6100059c:	fde69bc4 	.inst	0xfde69bc4 ; undefined
    610005a0:	89b8fd09 	.inst	0x89b8fd09 ; undefined
    610005a4:	8d79e0be 	.inst	0x8d79e0be ; undefined
    610005a8:	803ac667 	.inst	0x803ac667 ; NYI
    610005ac:	84fbdbd0 	ld1rh	{z16.s}, p6/z, [x30, #118]
    610005b0:	9abc8bd5 	.inst	0x9abc8bd5 ; undefined
    610005b4:	9e7d9662 	.inst	0x9e7d9662 ; undefined
    610005b8:	933eb0bb 	.inst	0x933eb0bb ; undefined
    610005bc:	97ffad0c 	bl	60feb9ec <_start-0x14614>
    610005c0:	afb010b1 	.inst	0xafb010b1 ; undefined
    610005c4:	ab710d06 	.inst	0xab710d06 ; undefined
    610005c8:	a6322bdf 	.inst	0xa6322bdf ; undefined
    610005cc:	a2f33668 	.inst	0xa2f33668 ; undefined
    610005d0:	bcb4666d 	.inst	0xbcb4666d ; undefined
    610005d4:	b8757bda 	ldr	w26, [x30, x21, lsl #2]
    610005d8:	b5365d03 	cbnz	x3, 6106d178 <out_buf+0x6c998>
    610005dc:	b1f740b4 	.inst	0xb1f740b4 ; undefined

Disassembly of section .data:

00000000610005e0 <stack>:
    610005e0:	08675309 	.inst	0x08675309 ; undefined
    610005e4:	00000000 	udf	#0
    610005e8:	08675309 	.inst	0x08675309 ; undefined
    610005ec:	00000000 	udf	#0
    610005f0:	08675309 	.inst	0x08675309 ; undefined
    610005f4:	00000000 	udf	#0
    610005f8:	08675309 	.inst	0x08675309 ; undefined
    610005fc:	00000000 	udf	#0
    61000600:	08675309 	.inst	0x08675309 ; undefined
    61000604:	00000000 	udf	#0
    61000608:	08675309 	.inst	0x08675309 ; undefined
    6100060c:	00000000 	udf	#0
    61000610:	08675309 	.inst	0x08675309 ; undefined
    61000614:	00000000 	udf	#0
    61000618:	08675309 	.inst	0x08675309 ; undefined
    6100061c:	00000000 	udf	#0
    61000620:	08675309 	.inst	0x08675309 ; undefined
    61000624:	00000000 	udf	#0
    61000628:	08675309 	.inst	0x08675309 ; undefined
    6100062c:	00000000 	udf	#0
    61000630:	08675309 	.inst	0x08675309 ; undefined
    61000634:	00000000 	udf	#0
    61000638:	08675309 	.inst	0x08675309 ; undefined
    6100063c:	00000000 	udf	#0
    61000640:	08675309 	.inst	0x08675309 ; undefined
    61000644:	00000000 	udf	#0
    61000648:	08675309 	.inst	0x08675309 ; undefined
    6100064c:	00000000 	udf	#0
    61000650:	08675309 	.inst	0x08675309 ; undefined
    61000654:	00000000 	udf	#0
    61000658:	08675309 	.inst	0x08675309 ; undefined
    6100065c:	00000000 	udf	#0
    61000660:	08675309 	.inst	0x08675309 ; undefined
    61000664:	00000000 	udf	#0
    61000668:	08675309 	.inst	0x08675309 ; undefined
    6100066c:	00000000 	udf	#0
    61000670:	08675309 	.inst	0x08675309 ; undefined
    61000674:	00000000 	udf	#0
    61000678:	08675309 	.inst	0x08675309 ; undefined
    6100067c:	00000000 	udf	#0
    61000680:	08675309 	.inst	0x08675309 ; undefined
    61000684:	00000000 	udf	#0
    61000688:	08675309 	.inst	0x08675309 ; undefined
    6100068c:	00000000 	udf	#0
    61000690:	08675309 	.inst	0x08675309 ; undefined
    61000694:	00000000 	udf	#0
    61000698:	08675309 	.inst	0x08675309 ; undefined
    6100069c:	00000000 	udf	#0
    610006a0:	08675309 	.inst	0x08675309 ; undefined
    610006a4:	00000000 	udf	#0
    610006a8:	08675309 	.inst	0x08675309 ; undefined
    610006ac:	00000000 	udf	#0
    610006b0:	08675309 	.inst	0x08675309 ; undefined
    610006b4:	00000000 	udf	#0
    610006b8:	08675309 	.inst	0x08675309 ; undefined
    610006bc:	00000000 	udf	#0
    610006c0:	08675309 	.inst	0x08675309 ; undefined
    610006c4:	00000000 	udf	#0
    610006c8:	08675309 	.inst	0x08675309 ; undefined
    610006cc:	00000000 	udf	#0
    610006d0:	08675309 	.inst	0x08675309 ; undefined
    610006d4:	00000000 	udf	#0
    610006d8:	08675309 	.inst	0x08675309 ; undefined
    610006dc:	00000000 	udf	#0
    610006e0:	08675309 	.inst	0x08675309 ; undefined
    610006e4:	00000000 	udf	#0
    610006e8:	08675309 	.inst	0x08675309 ; undefined
    610006ec:	00000000 	udf	#0
    610006f0:	08675309 	.inst	0x08675309 ; undefined
    610006f4:	00000000 	udf	#0
    610006f8:	08675309 	.inst	0x08675309 ; undefined
    610006fc:	00000000 	udf	#0
    61000700:	08675309 	.inst	0x08675309 ; undefined
    61000704:	00000000 	udf	#0
    61000708:	08675309 	.inst	0x08675309 ; undefined
    6100070c:	00000000 	udf	#0
    61000710:	08675309 	.inst	0x08675309 ; undefined
    61000714:	00000000 	udf	#0
    61000718:	08675309 	.inst	0x08675309 ; undefined
    6100071c:	00000000 	udf	#0
    61000720:	08675309 	.inst	0x08675309 ; undefined
    61000724:	00000000 	udf	#0
    61000728:	08675309 	.inst	0x08675309 ; undefined
    6100072c:	00000000 	udf	#0
    61000730:	08675309 	.inst	0x08675309 ; undefined
    61000734:	00000000 	udf	#0
    61000738:	08675309 	.inst	0x08675309 ; undefined
    6100073c:	00000000 	udf	#0
    61000740:	08675309 	.inst	0x08675309 ; undefined
    61000744:	00000000 	udf	#0
    61000748:	08675309 	.inst	0x08675309 ; undefined
    6100074c:	00000000 	udf	#0
    61000750:	08675309 	.inst	0x08675309 ; undefined
    61000754:	00000000 	udf	#0
    61000758:	08675309 	.inst	0x08675309 ; undefined
    6100075c:	00000000 	udf	#0
    61000760:	08675309 	.inst	0x08675309 ; undefined
    61000764:	00000000 	udf	#0
    61000768:	08675309 	.inst	0x08675309 ; undefined
    6100076c:	00000000 	udf	#0
    61000770:	08675309 	.inst	0x08675309 ; undefined
    61000774:	00000000 	udf	#0
    61000778:	08675309 	.inst	0x08675309 ; undefined
    6100077c:	00000000 	udf	#0
    61000780:	08675309 	.inst	0x08675309 ; undefined
    61000784:	00000000 	udf	#0
    61000788:	08675309 	.inst	0x08675309 ; undefined
    6100078c:	00000000 	udf	#0
    61000790:	08675309 	.inst	0x08675309 ; undefined
    61000794:	00000000 	udf	#0
    61000798:	08675309 	.inst	0x08675309 ; undefined
    6100079c:	00000000 	udf	#0
    610007a0:	08675309 	.inst	0x08675309 ; undefined
    610007a4:	00000000 	udf	#0
    610007a8:	08675309 	.inst	0x08675309 ; undefined
    610007ac:	00000000 	udf	#0
    610007b0:	08675309 	.inst	0x08675309 ; undefined
    610007b4:	00000000 	udf	#0
    610007b8:	08675309 	.inst	0x08675309 ; undefined
    610007bc:	00000000 	udf	#0
    610007c0:	08675309 	.inst	0x08675309 ; undefined
    610007c4:	00000000 	udf	#0
    610007c8:	08675309 	.inst	0x08675309 ; undefined
    610007cc:	00000000 	udf	#0
    610007d0:	08675309 	.inst	0x08675309 ; undefined
    610007d4:	00000000 	udf	#0
    610007d8:	08675309 	.inst	0x08675309 ; undefined
    610007dc:	00000000 	udf	#0

Disassembly of section .bss:

00000000610007e0 <out_buf>:
	...

Disassembly of section .debug_line:

0000000000000000 <.debug_line>:
   0:	00000097 	udf	#151
   4:	007d0003 	.inst	0x007d0003 ; undefined
   8:	01040000 	.inst	0x01040000 ; undefined
   c:	000d0efb 	.inst	0x000d0efb ; undefined
  10:	01010101 	.inst	0x01010101 ; undefined
  14:	01000000 	.inst	0x01000000 ; undefined
  18:	44010000 	.inst	0x44010000 ; undefined
  1c:	6f772f3a 	.inst	0x6f772f3a ; undefined
  20:	322f6b72 	orr	w18, w27, #0xfffe0fff
  24:	2f323230 	.inst	0x2f323230 ; undefined
  28:	30396c61 	adr	x1, 72db5 <_start-0x60f8d24b>
  2c:	732f3030 	.inst	0x732f3030 ; undefined
  30:	65642f77 	fmls	z23.h, p3/m, z27.h, z4.h
  34:	67677562 	.inst	0x67677562 ; undefined
  38:	612f7265 	.inst	0x612f7265 ; undefined
  3c:	706f5f6c 	adr	x12, dec2b <_start-0x60f213d5>
  40:	636f6e65 	.inst	0x636f6e65 ; undefined
  44:	6f632f64 	.inst	0x6f632f64 ; undefined
  48:	6972746e 	ldpsw	x14, x29, [x3, #-112]
  4c:	6f6c2f62 	.inst	0x6f6c2f62 ; undefined
  50:	72656461 	.inst	0x72656461 ; undefined
  54:	6c662f73 	ldnp	d19, d11, [x27, #-416]
  58:	2f687361 	fcmla	v1.4h, v27.4h, v8.h[1], #270
  5c:	636d6d65 	.inst	0x636d6d65 ; undefined
  60:	6377642f 	.inst	0x6377642f ; undefined
  64:	6368736d 	.inst	0x6368736d ; undefined
  68:	6372732f 	.inst	0x6372732f ; undefined
  6c:	6f6f622f 	umlsl2	v15.4s, v17.8h, v15.h[2]
  70:	61612f74 	.inst	0x61612f74 ; undefined
  74:	00686372 	.inst	0x00686372 ; undefined
  78:	61727700 	.inst	0x61727700 ; undefined
  7c:	72657070 	.inst	0x72657070 ; undefined
  80:	0100532e 	.inst	0x0100532e ; undefined
  84:	00000000 	udf	#0
  88:	00000209 	udf	#521
  8c:	00006100 	udf	#24832
  90:	21180000 	.inst	0x21180000 ; undefined
  94:	03022221 	.inst	0x03022221 ; undefined
  98:	b6010100 	tbz	x0, #32, 20b8 <_start-0x60ffdf48>
  9c:	03000002 	.inst	0x03000002 ; undefined
  a0:	00014300 	.inst	0x00014300 ; undefined
  a4:	fb010400 	.inst	0xfb010400 ; undefined
  a8:	01000d0e 	.inst	0x01000d0e ; undefined
  ac:	00010101 	.inst	0x00010101 ; undefined
  b0:	00010000 	.inst	0x00010000 ; undefined
  b4:	3a440100 	ccmn	w8, w4, #0x0, eq  // eq = none
  b8:	726f772f 	.inst	0x726f772f ; undefined
  bc:	30322f6b 	adr	x11, 646a9 <_start-0x60f9b957>
  c0:	612f3232 	.inst	0x612f3232 ; undefined
  c4:	3030396c 	adr	x12, 607f1 <_start-0x60f9f80f>
  c8:	77732f30 	.inst	0x77732f30 ; undefined
  cc:	6265642f 	.inst	0x6265642f ; undefined
  d0:	65676775 	fnmls	z21.h, p1/m, z27.h, z7.h
  d4:	6c612f72 	ldnp	d18, d11, [x27, #-496]
  d8:	65706f5f 	fnmls	z31.h, p3/m, z26.h, z16.h
  dc:	64636f6e 	.inst	0x64636f6e ; undefined
  e0:	6e6f632f 	rsubhn2	v15.8h, v25.4s, v15.4s
  e4:	62697274 	.inst	0x62697274 ; undefined
  e8:	616f6c2f 	.inst	0x616f6c2f ; undefined
  ec:	73726564 	.inst	0x73726564 ; undefined
  f0:	616c662f 	.inst	0x616c662f ; undefined
  f4:	652f6873 	.inst	0x652f6873 ; undefined
  f8:	2f636d6d 	.inst	0x2f636d6d ; undefined
  fc:	6d637764 	ldp	d4, d29, [x27, #-464]
 100:	2f636873 	umlsl	v19.4s, v3.4h, v3.h[6]
 104:	00637273 	.inst	0x00637273 ; undefined
 108:	775c3a64 	.inst	0x775c3a64 ; undefined
 10c:	5c6b726f 	ldr	d15, d6f58 <_start-0x60f290a8>
 110:	32323032 	orr	w18, w1, #0x7ffc000
 114:	396c615c 	ldrb	w28, [x10, #2840]
 118:	5c303030 	ldr	d16, 6071c <_start-0x60f9f8e4>
 11c:	735c7773 	.inst	0x735c7773 ; undefined
 120:	735c6b64 	.inst	0x735c6b64 ; undefined
 124:	612d636f 	.inst	0x612d636f ; undefined
 128:	732d7570 	.inst	0x732d7570 ; undefined
 12c:	745c6b64 	.inst	0x745c6b64 ; undefined
 130:	736c6f6f 	.inst	0x736c6f6f ; undefined
 134:	6e69775c 	uabd	v28.8h, v26.8h, v9.8h
 138:	7261615c 	.inst	0x7261615c ; undefined
 13c:	34366863 	cbz	w3, 6ce48 <_start-0x60f931b8>
 140:	7261615c 	.inst	0x7261615c ; undefined
 144:	34366863 	cbz	w3, 6ce50 <_start-0x60f931b0>
 148:	6e6f6e2d 	umin	v13.8h, v17.8h, v15.8h
 14c:	6c652d65 	ldnp	d5, d11, [x11, #-432]
 150:	6e695c66 	uqrshl	v6.8h, v3.8h, v9.8h
 154:	64756c63 	.inst	0x64756c63 ; undefined
 158:	616d5c65 	.inst	0x616d5c65 ; undefined
 15c:	6e696863 	.inst	0x6e696863 ; undefined
 160:	3a640065 	.inst	0x3a640065 ; undefined
 164:	726f775c 	.inst	0x726f775c ; undefined
 168:	30325c6b 	adr	x11, 64cf5 <_start-0x60f9b30b>
 16c:	615c3232 	.inst	0x615c3232 ; undefined
 170:	3030396c 	adr	x12, 6089d <_start-0x60f9f763>
 174:	77735c30 	.inst	0x77735c30 ; undefined
 178:	6b64735c 	.inst	0x6b64735c ; undefined
 17c:	636f735c 	.inst	0x636f735c ; undefined
 180:	7570612d 	.inst	0x7570612d ; undefined
 184:	6b64732d 	.inst	0x6b64732d ; undefined
 188:	6f6f745c 	uqshl	v28.2d, v2.2d, #47
 18c:	775c736c 	.inst	0x775c736c ; undefined
 190:	615c6e69 	.inst	0x615c6e69 ; undefined
 194:	68637261 	.inst	0x68637261 ; undefined
 198:	615c3436 	.inst	0x615c3436 ; undefined
 19c:	68637261 	.inst	0x68637261 ; undefined
 1a0:	6e2d3436 	cmhi	v22.16b, v1.16b, v13.16b
 1a4:	2d656e6f 	ldp	s15, s27, [x19, #-216]
 1a8:	5c666c65 	ldr	d5, ccf34 <_start-0x60f330cc>
 1ac:	6c636e69 	ldnp	d9, d27, [x19, #-464]
 1b0:	5c656475 	ldr	d21, cae3c <_start-0x60f351c4>
 1b4:	00737973 	.inst	0x00737973 ; undefined
 1b8:	63776400 	.inst	0x63776400 ; undefined
 1bc:	6368736d 	.inst	0x6368736d ; undefined
 1c0:	0100632e 	.inst	0x0100632e ; undefined
 1c4:	645f0000 	fcmla	z0.h, p0/m, z0.h, z31.h, #0
 1c8:	75616665 	.inst	0x75616665 ; undefined
 1cc:	745f746c 	.inst	0x745f746c ; undefined
 1d0:	73657079 	.inst	0x73657079 ; undefined
 1d4:	0200682e 	.inst	0x0200682e ; undefined
 1d8:	735f0000 	.inst	0x735f0000 ; undefined
 1dc:	6e696474 	umax	v20.8h, v3.8h, v9.8h
 1e0:	00682e74 	.inst	0x00682e74 ; undefined
 1e4:	00000003 	udf	#3
 1e8:	09000105 	.inst	0x09000105 ; undefined
 1ec:	00001802 	udf	#6146
 1f0:	00000061 	udf	#97
 1f4:	01061500 	.inst	0x01061500 ; undefined
 1f8:	21060505 	.inst	0x21060505 ; undefined
 1fc:	010a0513 	.inst	0x010a0513 ; undefined
 200:	05140905 	mov	z5.b, p4/z, #72
 204:	0501060c 	orr	z12.b, z12.b, #0x1
 208:	05210609 	ext	z9.b, z9.b, z16.b, #9
 20c:	0501060c 	orr	z12.b, z12.b, #0x1
 210:	051d060a 	mov	z10.b, p13/z, #48
 214:	01053305 	.inst	0x01053305 ; undefined
 218:	23061306 	.inst	0x23061306 ; undefined
 21c:	13130505 	sbfiz	w5, w8, #13, #2
 220:	01050106 	.inst	0x01050106 ; undefined
 224:	0605051e 	.inst	0x0605051e ; undefined
 228:	14090531 	b	2416ec <_start-0x60dbe914>
 22c:	01061105 	.inst	0x01061105 ; undefined
 230:	21060905 	.inst	0x21060905 ; undefined
 234:	060b0513 	.inst	0x060b0513 ; undefined
 238:	00160501 	.inst	0x00160501 ; undefined
 23c:	2e010402 	.inst	0x2e010402 ; undefined
 240:	31060505 	adds	w5, w8, #0x181
 244:	01061c05 	.inst	0x01061c05 ; undefined
 248:	052e0f05 	ext	z5.b, z5.b, z24.b, #115
 24c:	01210605 	.inst	0x01210605 ; undefined
 250:	01051320 	.inst	0x01051320 ; undefined
 254:	06201306 	.inst	0x06201306 ; undefined
 258:	13050523 	sbfiz	w3, w9, #27, #2
 25c:	20011413 	.inst	0x20011413 ; undefined
 260:	20060113 	.inst	0x20060113 ; undefined
 264:	01040200 	.inst	0x01040200 ; undefined
 268:	02002e06 	.inst	0x02002e06 ; undefined
 26c:	05130104 	mov	z4.b, p3/z, #8
 270:	04020009 	.inst	0x04020009 ; undefined
 274:	11051401 	add	w1, w0, #0x145
 278:	01040200 	.inst	0x01040200 ; undefined
 27c:	09050106 	.inst	0x09050106 ; undefined
 280:	01040200 	.inst	0x01040200 ; undefined
 284:	02002106 	.inst	0x02002106 ; undefined
 288:	05130104 	mov	z4.b, p3/z, #8
 28c:	0402000b 	.inst	0x0402000b ; undefined
 290:	05010601 	orr	z1.b, z1.b, #0x1
 294:	04020016 	.inst	0x04020016 ; undefined
 298:	09052001 	.inst	0x09052001 ; undefined
 29c:	03040200 	.inst	0x03040200 ; undefined
 2a0:	02004006 	.inst	0x02004006 ; undefined
 2a4:	05010304 	orr	z4.s, z4.s, #0x1ffffff
 2a8:	04020005 	.inst	0x04020005 ; undefined
 2ac:	051f0603 	mov	z3.b, p15/z, #48
 2b0:	04020009 	.inst	0x04020009 ; undefined
 2b4:	02002103 	.inst	0x02002103 ; undefined
 2b8:	20060304 	.inst	0x20060304 ; undefined
 2bc:	02002605 	.inst	0x02002605 ; undefined
 2c0:	05110304 	mov	z4.b, p1/z, #24
 2c4:	0402000e 	.inst	0x0402000e ; undefined
 2c8:	05050103 	.inst	0x05050103 ; undefined
 2cc:	03040200 	.inst	0x03040200 ; undefined
 2d0:	30060106 	adr	x6, c2f1 <_start-0x60ff3d0f>
 2d4:	05140905 	mov	z5.b, p4/z, #72
 2d8:	05010611 	orr	z17.b, z17.b, #0x1
 2dc:	13210609 	.inst	0x13210609 ; undefined
 2e0:	01060b05 	.inst	0x01060b05 ; undefined
 2e4:	02001605 	.inst	0x02001605 ; undefined
 2e8:	05200104 	ext	z4.b, z4.b, z8.b, #0
 2ec:	01310605 	.inst	0x01310605 ; undefined
 2f0:	20062006 	.inst	0x20062006 ; undefined
 2f4:	14060105 	b	180708 <_start-0x60e7f8f8>
 2f8:	05052306 	.inst	0x05052306 ; undefined
 2fc:	05011413 	orr	z19.h, z19.h, #0x4000
 300:	660f0601 	.inst	0x660f0601 ; undefined
 304:	1f240505 	fnmadd	s5, s8, s4, s1
 308:	01132006 	.inst	0x01132006 ; undefined
 30c:	0630132e 	.inst	0x0630132e ; undefined
 310:	000e0501 	.inst	0x000e0501 ; undefined
 314:	06010402 	.inst	0x06010402 ; undefined
 318:	00050520 	.inst	0x00050520 ; undefined
 31c:	06010402 	.inst	0x06010402 ; undefined
 320:	06300601 	.inst	0x06300601 ; undefined
 324:	21010520 	.inst	0x21010520 ; undefined
 328:	05052020 	.inst	0x05052020 ; undefined
 32c:	0009051f 	.inst	0x0009051f ; undefined
 330:	06030402 	.inst	0x06030402 ; undefined
 334:	0017051f 	.inst	0x0017051f ; undefined
 338:	06030402 	.inst	0x06030402 ; undefined
 33c:	001c0501 	.inst	0x001c0501 ; undefined
 340:	06030402 	.inst	0x06030402 ; undefined
 344:	0402002d 	.inst	0x0402002d ; undefined
 348:	00010603 	.inst	0x00010603 ; undefined
 34c:	20030402 	.inst	0x20030402 ; undefined
 350:	01000102 	.inst	0x01000102 ; undefined
 354:	0001f901 	.inst	0x0001f901 ; undefined
 358:	54000300 	b.eq	3b8 <_start-0x60fffc48>  // b.none
 35c:	04000001 	add	z1.b, p0/m, z1.b, z0.b
 360:	0d0efb01 	.inst	0x0d0efb01 ; undefined
 364:	01010100 	.inst	0x01010100 ; undefined
 368:	00000001 	udf	#1
 36c:	01000001 	.inst	0x01000001 ; undefined
 370:	772f3a44 	.inst	0x772f3a44 ; undefined
 374:	2f6b726f 	fcmla	v15.4h, v19.4h, v11.h[1], #270
 378:	32323032 	orr	w18, w1, #0x7ffc000
 37c:	396c612f 	ldrb	w15, [x9, #2840]
 380:	2f303030 	.inst	0x2f303030 ; undefined
 384:	642f7773 	.inst	0x642f7773 ; undefined
 388:	67756265 	.inst	0x67756265 ; undefined
 38c:	2f726567 	.inst	0x2f726567 ; undefined
 390:	6f5f6c61 	.inst	0x6f5f6c61 ; undefined
 394:	6f6e6570 	sqshlu	v16.2d, v11.2d, #46
 398:	632f6463 	.inst	0x632f6463 ; undefined
 39c:	72746e6f 	.inst	0x72746e6f ; undefined
 3a0:	6c2f6269 	stnp	d9, d24, [x19, #-272]
 3a4:	6564616f 	fnmls	z15.h, p0/m, z11.h, z4.h
 3a8:	662f7372 	.inst	0x662f7372 ; undefined
 3ac:	6873616c 	.inst	0x6873616c ; undefined
 3b0:	6d6d652f 	ldp	d15, d25, [x9, #-304]
 3b4:	77642f63 	.inst	0x77642f63 ; undefined
 3b8:	68736d63 	.inst	0x68736d63 ; undefined
 3bc:	72732f63 	.inst	0x72732f63 ; undefined
 3c0:	3a640063 	.inst	0x3a640063 ; undefined
 3c4:	726f775c 	.inst	0x726f775c ; undefined
 3c8:	30325c6b 	adr	x11, 64f55 <_start-0x60f9b0ab>
 3cc:	615c3232 	.inst	0x615c3232 ; undefined
 3d0:	3030396c 	adr	x12, 60afd <_start-0x60f9f503>
 3d4:	77735c30 	.inst	0x77735c30 ; undefined
 3d8:	6b64735c 	.inst	0x6b64735c ; undefined
 3dc:	636f735c 	.inst	0x636f735c ; undefined
 3e0:	7570612d 	.inst	0x7570612d ; undefined
 3e4:	6b64732d 	.inst	0x6b64732d ; undefined
 3e8:	6f6f745c 	uqshl	v28.2d, v2.2d, #47
 3ec:	775c736c 	.inst	0x775c736c ; undefined
 3f0:	615c6e69 	.inst	0x615c6e69 ; undefined
 3f4:	68637261 	.inst	0x68637261 ; undefined
 3f8:	615c3436 	.inst	0x615c3436 ; undefined
 3fc:	68637261 	.inst	0x68637261 ; undefined
 400:	6e2d3436 	cmhi	v22.16b, v1.16b, v13.16b
 404:	2d656e6f 	ldp	s15, s27, [x19, #-216]
 408:	5c666c65 	ldr	d5, cd194 <_start-0x60f32e6c>
 40c:	6c636e69 	ldnp	d9, d27, [x19, #-464]
 410:	5c656475 	ldr	d21, cb09c <_start-0x60f34f64>
 414:	6863616d 	.inst	0x6863616d ; undefined
 418:	00656e69 	.inst	0x00656e69 ; undefined
 41c:	775c3a64 	.inst	0x775c3a64 ; undefined
 420:	5c6b726f 	ldr	d15, d726c <_start-0x60f28d94>
 424:	32323032 	orr	w18, w1, #0x7ffc000
 428:	396c615c 	ldrb	w28, [x10, #2840]
 42c:	5c303030 	ldr	d16, 60a30 <_start-0x60f9f5d0>
 430:	735c7773 	.inst	0x735c7773 ; undefined
 434:	735c6b64 	.inst	0x735c6b64 ; undefined
 438:	612d636f 	.inst	0x612d636f ; undefined
 43c:	732d7570 	.inst	0x732d7570 ; undefined
 440:	745c6b64 	.inst	0x745c6b64 ; undefined
 444:	736c6f6f 	.inst	0x736c6f6f ; undefined
 448:	6e69775c 	uabd	v28.8h, v26.8h, v9.8h
 44c:	7261615c 	.inst	0x7261615c ; undefined
 450:	34366863 	cbz	w3, 6d15c <_start-0x60f92ea4>
 454:	7261615c 	.inst	0x7261615c ; undefined
 458:	34366863 	cbz	w3, 6d164 <_start-0x60f92e9c>
 45c:	6e6f6e2d 	umin	v13.8h, v17.8h, v15.8h
 460:	6c652d65 	ldnp	d5, d11, [x11, #-432]
 464:	6e695c66 	uqrshl	v6.8h, v3.8h, v9.8h
 468:	64756c63 	.inst	0x64756c63 ; undefined
 46c:	79735c65 	ldrh	w5, [x3, #6574]
 470:	64000073 	.inst	0x64000073 ; undefined
 474:	736d6377 	.inst	0x736d6377 ; undefined
 478:	635f6368 	.inst	0x635f6368 ; undefined
 47c:	632e6372 	.inst	0x632e6372 ; undefined
 480:	00000100 	udf	#256
 484:	6665645f 	.inst	0x6665645f ; undefined
 488:	746c7561 	.inst	0x746c7561 ; undefined
 48c:	7079745f 	adr	xzr, f3317 <_start-0x60f0cce9>
 490:	682e7365 	.inst	0x682e7365 ; undefined
 494:	00000200 	udf	#512
 498:	6474735f 	.inst	0x6474735f ; undefined
 49c:	2e746e69 	umin	v9.4h, v19.4h, v20.4h
 4a0:	00030068 	.inst	0x00030068 ; undefined
 4a4:	63776400 	.inst	0x63776400 ; undefined
 4a8:	6368736d 	.inst	0x6368736d ; undefined
 4ac:	0100682e 	.inst	0x0100682e ; undefined
 4b0:	05000000 	orr	z0.s, z0.s, #0x1
 4b4:	02090001 	.inst	0x02090001 ; undefined
 4b8:	61000124 	.inst	0x61000124 ; undefined
 4bc:	00000000 	udf	#0
 4c0:	0100c703 	.inst	0x0100c703 ; undefined
 4c4:	13130505 	sbfiz	w5, w8, #13, #2
 4c8:	01051313 	.inst	0x01051313 ; undefined
 4cc:	0e050e06 	dup	v6.8b, w16
 4d0:	1f010575 	fmadd	s21, s11, s1, s1
 4d4:	0d030905 	.inst	0x0d030905 ; undefined
 4d8:	03010520 	.inst	0x03010520 ; undefined
 4dc:	09052073 	.inst	0x09052073 ; undefined
 4e0:	05200d03 	ext	z3.b, z3.b, z8.b, #3
 4e4:	20730301 	.inst	0x20730301 ; undefined
 4e8:	10032b05 	adr	x5, 6a48 <_start-0x60ff95b8>
 4ec:	03010520 	.inst	0x03010520 ; undefined
 4f0:	50052e70 	adr	x16, aabe <_start-0x60ff5542>
 4f4:	05200d03 	ext	z3.b, z3.b, z8.b, #3
 4f8:	7703060a 	.inst	0x7703060a ; undefined
 4fc:	03050520 	.inst	0x03050520 ; undefined
 500:	01052e13 	.inst	0x01052e13 ; undefined
 504:	202e1406 	.inst	0x202e1406 ; undefined
 508:	06090520 	.inst	0x06090520 ; undefined
 50c:	064a6d03 	.inst	0x064a6d03 ; undefined
 510:	132b0627 	.inst	0x132b0627 ; undefined
 514:	13054b14 	sbfx	w20, w24, #5, #14
 518:	0e051c06 	.inst	0x0e051c06 ; undefined
 51c:	19052406 	.inst	0x19052406 ; undefined
 520:	0e050106 	tbl	v6.8b, {v8.16b}, v5.8b
 524:	06090520 	.inst	0x06090520 ; undefined
 528:	060f0534 	.inst	0x060f0534 ; undefined
 52c:	06090501 	.inst	0x06090501 ; undefined
 530:	06140521 	.inst	0x06140521 ; undefined
 534:	0d052001 	.inst	0x0d052001 ; undefined
 538:	42051b06 	.inst	0x42051b06 ; undefined
 53c:	39050106 	strb	w6, [x8, #320]
 540:	202b0520 	.inst	0x202b0520 ; undefined
 544:	05201105 	ext	z5.b, z5.b, z8.b, #4
 548:	0621060d 	.inst	0x0621060d ; undefined
 54c:	00010201 	.inst	0x00010201 ; undefined
 550:	Address 0x0000000000000550 is out of bounds.


Disassembly of section .debug_info:

0000000000000000 <.debug_info>:
   0:	0000002a 	udf	#42
   4:	00000002 	udf	#2
   8:	01080000 	.inst	0x01080000 ; undefined
   c:	00000000 	udf	#0
  10:	61000000 	.inst	0x61000000 ; undefined
  14:	00000000 	udf	#0
  18:	61000018 	.inst	0x61000018 ; undefined
	...
  24:	00000067 	udf	#103
  28:	000000b5 	udf	#181
  2c:	02cf8001 	.inst	0x02cf8001 ; undefined
  30:	00040000 	.inst	0x00040000 ; undefined
  34:	00000014 	udf	#20
  38:	00e70108 	.inst	0x00e70108 ; undefined
  3c:	c20c0000 	.inst	0xc20c0000 ; undefined
  40:	67000001 	.inst	0x67000001 ; undefined
  44:	18000000 	ldr	w0, 44 <_start-0x60ffffbc>
  48:	00610000 	.inst	0x00610000 ; undefined
  4c:	0c000000 	st4	{v0.8b-v3.8b}, [x0]
  50:	00000001 	udf	#1
  54:	9b000000 	madd	x0, x0, x0, x0
  58:	02000000 	.inst	0x02000000 ; undefined
  5c:	01450601 	.inst	0x01450601 ; undefined
  60:	cd030000 	.inst	0xcd030000 ; undefined
  64:	02000000 	.inst	0x02000000 ; undefined
  68:	0040182b 	.inst	0x0040182b ; undefined
  6c:	01020000 	.inst	0x01020000 ; undefined
  70:	00014308 	.inst	0x00014308 ; undefined
  74:	05020200 	orr	z0.d, z0.d, #0x1ffff
  78:	00000227 	udf	#551
  7c:	63070202 	.inst	0x63070202 ; undefined
  80:	04000001 	add	z1.b, p0/m, z1.b, z0.b
  84:	6e690504 	uhadd	v4.8h, v8.8h, v9.8h
  88:	9e030074 	ucvtf	s20, x3, #64
  8c:	02000001 	.inst	0x02000001 ; undefined
  90:	0068194f 	.inst	0x0068194f ; undefined
  94:	04020000 	.inst	0x04020000 ; undefined
  98:	00015607 	.inst	0x00015607 ; undefined
  9c:	05080200 	.inst	0x05080200 ; undefined
  a0:	00000238 	udf	#568
  a4:	51070802 	sub	w2, w0, #0x1c2
  a8:	03000001 	.inst	0x03000001 ; undefined
  ac:	000000cf 	udf	#207
  b0:	34131803 	cbz	w3, 263b0 <_start-0x60fd9c50>
  b4:	03000000 	.inst	0x03000000 ; undefined
  b8:	000001a0 	udf	#416
  bc:	5c143003 	ldr	d3, 286bc <_start-0x60fd7944>
  c0:	05000000 	orr	z0.s, z0.s, #0x1
  c4:	00000089 	udf	#137
  c8:	00008906 	udf	#35078
  cc:	00d70700 	.inst	0x00d70700 ; undefined
  d0:	39010000 	strb	w0, [x0, #64]
  d4:	0000bc06 	udf	#48134
  d8:	00000061 	udf	#97
  dc:	00006800 	udf	#26624
  e0:	00000000 	udf	#0
  e4:	579c0100 	.inst	0x579c0100 ; undefined
  e8:	08000001 	stxrb	w0, w1, [x0]
  ec:	000000c3 	udf	#195
  f0:	57293901 	.inst	0x57293901 ; undefined
  f4:	0a000001 	and	w1, w0, w0
  f8:	00000000 	udf	#0
  fc:	08000000 	stxrb	w0, w0, [x0]
 100:	00000231 	udf	#561
 104:	5d3e3901 	.inst	0x5d3e3901 ; undefined
 108:	84000001 	ld1sb	{z1.s}, p0/z, [x0, z0.s, uxtw]
 10c:	7c000000 	stur	h0, [x0]
 110:	08000000 	stxrb	w0, w0, [x0]
 114:	00000250 	udf	#592
 118:	894f3901 	.inst	0x894f3901 ; undefined
 11c:	e7000000 	.inst	0xe7000000 ; undefined
 120:	e3000000 	.inst	0xe3000000 ; undefined
 124:	08000000 	stxrb	w0, w0, [x0]
 128:	0000021e 	udf	#542
 12c:	89603901 	.inst	0x89603901 ; undefined
 130:	28000000 	stnp	w0, w0, [x0]
 134:	20000001 	.inst	0x20000001 ; undefined
 138:	09000001 	.inst	0x09000001 ; undefined
 13c:	3b010069 	.inst	0x3b010069 ; undefined
 140:	0000890e 	udf	#35086
 144:	00018f00 	.inst	0x00018f00 ; undefined
 148:	00018700 	.inst	0x00018700 ; undefined
 14c:	00f00a00 	.inst	0x00f00a00 ; undefined
 150:	00006100 	udf	#24832
 154:	01f20000 	.inst	0x01f20000 ; undefined
 158:	013c0000 	.inst	0x013c0000 ; undefined
 15c:	010b0000 	.inst	0x010b0000 ; undefined
 160:	00830250 	.inst	0x00830250 ; undefined
 164:	0151010b 	.inst	0x0151010b ; undefined
 168:	140c0035 	b	30023c <_start-0x60cffdc4>
 16c:	00610001 	.inst	0x00610001 ; undefined
 170:	f2000000 	ands	x0, x0, #0x100000001
 174:	0b000001 	add	w1, w0, w0
 178:	f3035001 	.inst	0xf3035001 ; undefined
 17c:	010b5001 	.inst	0x010b5001 ; undefined
 180:	00310151 	.inst	0x00310151 ; NYI
 184:	95080d00 	bl	4203584 <_start-0x5cdfca7c>
 188:	0d000000 	st1	{v0.b}[0], [x0]
 18c:	00008908 	udf	#35080
 190:	01a90700 	.inst	0x01a90700 ; undefined
 194:	1e010000 	.inst	0x1e010000 ; undefined
 198:	00006806 	udf	#26630
 19c:	00000061 	udf	#97
 1a0:	00005400 	udf	#21504
 1a4:	00000000 	udf	#0
 1a8:	ec9c0100 	.inst	0xec9c0100 ; undefined
 1ac:	0e000001 	tbl	v1.8b, {v0.16b}, v0.8b
 1b0:	000000c3 	udf	#195
 1b4:	572a1e01 	.inst	0x572a1e01 ; undefined
 1b8:	01000001 	.inst	0x01000001 ; undefined
 1bc:	02500850 	.inst	0x02500850 ; undefined
 1c0:	1e010000 	.inst	0x1e010000 ; undefined
 1c4:	0000893e 	udf	#35134
 1c8:	0001f400 	.inst	0x0001f400 ; undefined
 1cc:	0001ee00 	.inst	0x0001ee00 ; undefined
 1d0:	02310e00 	.inst	0x02310e00 ; undefined
 1d4:	1e010000 	.inst	0x1e010000 ; undefined
 1d8:	0001ec56 	.inst	0x0001ec56 ; undefined
 1dc:	09520100 	.inst	0x09520100 ; undefined
 1e0:	20010069 	.inst	0x20010069 ; undefined
 1e4:	0000890e 	udf	#35086
 1e8:	00024300 	.inst	0x00024300 ; undefined
 1ec:	00024100 	.inst	0x00024100 ; undefined
 1f0:	01ba0f00 	.inst	0x01ba0f00 ; undefined
 1f4:	20010000 	.inst	0x20010000 ; undefined
 1f8:	00008911 	udf	#35089
 1fc:	00026a00 	.inst	0x00026a00 ; undefined
 200:	00026600 	.inst	0x00026600 ; undefined
 204:	01760f00 	.inst	0x01760f00 ; undefined
 208:	21010000 	.inst	0x21010000 ; undefined
 20c:	00007d0d 	udf	#32013
 210:	0002a400 	.inst	0x0002a400 ; undefined
 214:	0002a000 	.inst	0x0002a000 ; undefined
 218:	080d0000 	stxrb	w13, w0, [x0]
 21c:	0000009a 	udf	#154
 220:	00018010 	.inst	0x00018010 ; undefined
 224:	050e0100 	.inst	0x050e0100 ; undefined
 228:	00000055 	udf	#85
 22c:	61000030 	.inst	0x61000030 ; undefined
 230:	00000000 	udf	#0
 234:	00000038 	udf	#56
 238:	00000000 	udf	#0
 23c:	02799c01 	.inst	0x02799c01 ; undefined
 240:	c3080000 	.inst	0xc3080000 ; undefined
 244:	01000000 	.inst	0x01000000 ; undefined
 248:	0157260e 	.inst	0x0157260e ; undefined
 24c:	02ea0000 	.inst	0x02ea0000 ; undefined
 250:	02e60000 	.inst	0x02e60000 ; undefined
 254:	4b080000 	sub	w0, w0, w8
 258:	01000002 	.inst	0x01000002 ; undefined
 25c:	007d390e 	.inst	0x007d390e ; undefined
 260:	03270000 	.inst	0x03270000 ; undefined
 264:	03230000 	.inst	0x03230000 ; undefined
 268:	ba0f0000 	adcs	x0, x0, x15
 26c:	01000001 	.inst	0x01000001 ; undefined
 270:	00890e10 	.inst	0x00890e10 ; undefined
 274:	035f0000 	.inst	0x035f0000 ; undefined
 278:	035d0000 	.inst	0x035d0000 ; undefined
 27c:	410f0000 	.inst	0x410f0000 ; undefined
 280:	01000002 	.inst	0x01000002 ; undefined
 284:	00891710 	.inst	0x00891710 ; undefined
 288:	03840000 	.inst	0x03840000 ; undefined
 28c:	03820000 	.inst	0x03820000 ; undefined
 290:	760f0000 	.inst	0x760f0000 ; undefined
 294:	01000001 	.inst	0x01000001 ; undefined
 298:	007d0d11 	.inst	0x007d0d11 ; undefined
 29c:	03ab0000 	.inst	0x03ab0000 ; undefined
 2a0:	03a70000 	.inst	0x03a70000 ; undefined
 2a4:	11000000 	add	w0, w0, #0x0
 2a8:	00000134 	udf	#308
 2ac:	890b0301 	.inst	0x890b0301 ; undefined
 2b0:	18000000 	ldr	w0, 2b0 <_start-0x60fffd50>
 2b4:	00610000 	.inst	0x00610000 ; undefined
 2b8:	18000000 	ldr	w0, 2b8 <_start-0x60fffd48>
 2bc:	00000000 	udf	#0
 2c0:	01000000 	.inst	0x01000000 ; undefined
 2c4:	018e089c 	.inst	0x018e089c ; undefined
 2c8:	03010000 	.inst	0x03010000 ; undefined
 2cc:	00015d24 	.inst	0x00015d24 ; undefined
 2d0:	0003ed00 	.inst	0x0003ed00 ; undefined
 2d4:	0003e900 	.inst	0x0003e900 ; undefined
 2d8:	70770900 	adr	x0, ee3fb <_start-0x60f11c05>
 2dc:	0e050100 	tbl	v0.8b, {v8.16b}, v5.8b
 2e0:	00000089 	udf	#137
 2e4:	00000427 	udf	#1063
 2e8:	00000423 	udf	#1059
 2ec:	00707209 	.inst	0x00707209 ; undefined
 2f0:	89160501 	.inst	0x89160501 ; undefined
 2f4:	62000000 	.inst	0x62000000 ; undefined
 2f8:	5e000004 	sha1c	q4, s0, v0.4s
 2fc:	00000004 	udf	#4
 300:	0001ff00 	.inst	0x0001ff00 ; undefined
 304:	2e000400 	.inst	0x2e000400 ; undefined
 308:	08000001 	stxrb	w0, w1, [x0]
 30c:	0000e701 	udf	#59137
 310:	02810c00 	.inst	0x02810c00 ; undefined
 314:	00670000 	.inst	0x00670000 ; undefined
 318:	01240000 	.inst	0x01240000 ; undefined
 31c:	00006100 	udf	#24832
 320:	00bc0000 	.inst	0x00bc0000 ; undefined
 324:	00000000 	udf	#0
 328:	03550000 	.inst	0x03550000 ; undefined
 32c:	01020000 	.inst	0x01020000 ; undefined
 330:	00014506 	.inst	0x00014506 ; undefined
 334:	00cd0300 	.inst	0x00cd0300 ; undefined
 338:	2b020000 	adds	w0, w0, w2
 33c:	00004018 	udf	#16408
 340:	08010200 	stxrb	w1, w0, [x16]
 344:	00000143 	udf	#323
 348:	27050202 	.inst	0x27050202 ; undefined
 34c:	02000002 	.inst	0x02000002 ; undefined
 350:	01630702 	.inst	0x01630702 ; undefined
 354:	57030000 	.inst	0x57030000 ; undefined
 358:	02000002 	.inst	0x02000002 ; undefined
 35c:	0061184d 	.inst	0x0061184d ; undefined
 360:	04040000 	.inst	0x04040000 ; undefined
 364:	746e6905 	.inst	0x746e6905 ; undefined
 368:	019e0300 	.inst	0x019e0300 ; undefined
 36c:	4f020000 	.inst	0x4f020000 ; undefined
 370:	00007419 	udf	#29721
 374:	07040200 	.inst	0x07040200 ; undefined
 378:	00000156 	udf	#342
 37c:	00007405 	udf	#29701
 380:	05080200 	.inst	0x05080200 ; undefined
 384:	00000238 	udf	#568
 388:	51070802 	sub	w2, w0, #0x1c2
 38c:	03000001 	.inst	0x03000001 ; undefined
 390:	000000cf 	udf	#207
 394:	34131803 	cbz	w3, 26694 <_start-0x60fd996c>
 398:	03000000 	.inst	0x03000000 ; undefined
 39c:	00000259 	udf	#601
 3a0:	55132c03 	.inst	0x55132c03 ; undefined
 3a4:	03000000 	.inst	0x03000000 ; undefined
 3a8:	000001a0 	udf	#416
 3ac:	68143003 	.inst	0x68143003 ; undefined
 3b0:	06000000 	.inst	0x06000000 ; undefined
 3b4:	000000a6 	udf	#166
 3b8:	00007b07 	udf	#31495
 3bc:	0000c700 	udf	#50944
 3c0:	00870800 	.inst	0x00870800 ; undefined
 3c4:	00ff0000 	.inst	0x00ff0000 ; undefined
 3c8:	0000b705 	udf	#46853
 3cc:	02eb0900 	.inst	0x02eb0900 ; undefined
 3d0:	03010000 	.inst	0x03010000 ; undefined
 3d4:	0000c71b 	udf	#50971
 3d8:	e0030900 	.inst	0xe0030900 ; undefined
 3dc:	00610001 	.inst	0x00610001 ; undefined
 3e0:	07000000 	.inst	0x07000000 ; undefined
 3e4:	0000008e 	udf	#142
 3e8:	000000f3 	udf	#243
 3ec:	0000870a 	udf	#34570
 3f0:	0001ff00 	.inst	0x0001ff00 ; undefined
 3f4:	0002f709 	.inst	0x0002f709 ; undefined
 3f8:	10460100 	adr	x0, 8c418 <_start-0x60f73be8>
 3fc:	000000e2 	udf	#226
 400:	07e00309 	.inst	0x07e00309 ; undefined
 404:	00006100 	udf	#24832
 408:	ff0b0000 	.inst	0xff0b0000 ; undefined
 40c:	01000002 	.inst	0x01000002 ; undefined
 410:	00610547 	.inst	0x00610547 ; undefined
 414:	01240000 	.inst	0x01240000 ; undefined
 418:	00006100 	udf	#24832
 41c:	00bc0000 	.inst	0x00bc0000 ; undefined
 420:	00000000 	udf	#0
 424:	9c010000 	ldr	q0, 2424 <_start-0x60ffdbdc>
 428:	000001f0 	udf	#496
 42c:	0000c30c 	udf	#49932
 430:	25470100 	cmpge	p0.h, p0/z, z8.h, #7
 434:	000001f0 	udf	#496
 438:	000004a1 	udf	#1185
 43c:	00000499 	udf	#1177
 440:	0002760c 	.inst	0x0002760c ; undefined
 444:	38470100 	ldurb	w0, [x8, #112]
 448:	0000009a 	udf	#154
 44c:	00000508 	udf	#1288
 450:	00000500 	udf	#1280
 454:	0002e50c 	.inst	0x0002e50c ; undefined
 458:	48470100 	ldxrh	w0, [x8]
 45c:	00000061 	udf	#97
 460:	0000056d 	udf	#1389
 464:	00000567 	udf	#1383
 468:	0002610c 	.inst	0x0002610c ; undefined
 46c:	53470100 	.inst	0x53470100 ; undefined
 470:	00000061 	udf	#97
 474:	000005bc 	udf	#1468
 478:	000005b6 	udf	#1462
 47c:	6372630d 	.inst	0x6372630d ; undefined
 480:	0e490100 	.inst	0x0e490100 ; undefined
 484:	000000a6 	udf	#166
 488:	0000060d 	udf	#1549
 48c:	00000605 	udf	#1541
 490:	00026c0e 	.inst	0x00026c0e ; undefined
 494:	094a0100 	.inst	0x094a0100 ; undefined
 498:	00000061 	udf	#97
 49c:	0000066f 	udf	#1647
 4a0:	0000066b 	udf	#1643
 4a4:	0002e10e 	.inst	0x0002e10e ; undefined
 4a8:	184a0100 	ldr	w0, 944c8 <_start-0x60f6bb38>
 4ac:	00000061 	udf	#97
 4b0:	000006ac 	udf	#1708
 4b4:	000006a6 	udf	#1702
 4b8:	0100690d 	.inst	0x0100690d ; undefined
 4bc:	0061094b 	.inst	0x0061094b ; undefined
 4c0:	06fe0000 	.inst	0x06fe0000 ; undefined
 4c4:	06f60000 	.inst	0x06f60000 ; undefined
 4c8:	ac0f0000 	stnp	q0, q0, [x0, #480]
 4cc:	00610001 	.inst	0x00610001 ; undefined
 4d0:	f6000000 	.inst	0xf6000000 ; undefined
 4d4:	10000001 	adr	x1, 4d4 <_start-0x60fffb2c>
 4d8:	88025001 	stxr	w2, w1, [x0]
 4dc:	51011000 	sub	w0, w0, #0x44
 4e0:	10008702 	adr	x2, 15c0 <_start-0x60ffea40>
 4e4:	85025201 	ld1w	{z1.s}, p4/z, [x16, z2.s, uxtw]
 4e8:	53011000 	ubfx	w0, w0, #1, #4
 4ec:	00008b02 	udf	#35586
 4f0:	b2081100 	orr	x0, x8, #0x1f0000001f000000
 4f4:	12000000 	and	w0, w0, #0x1
 4f8:	000000d7 	udf	#215
 4fc:	000000d7 	udf	#215
 500:	00063004 	.inst	0x00063004 ; undefined

Disassembly of section .debug_abbrev:

0000000000000000 <.debug_abbrev>:
   0:	10001101 	adr	x1, 220 <_start-0x60fffde0>
   4:	12011106 	and	w6, w8, #0x8000000f
   8:	1b0e0301 	madd	w1, w24, w14, w0
   c:	130e250e 	sbfiz	w14, w8, #18, #10
  10:	00000005 	udf	#5
  14:	25011101 	cmpge	p1.b, p4/z, z8.b, #1
  18:	030b130e 	.inst	0x030b130e ; undefined
  1c:	110e1b0e 	add	w14, w24, #0x386
  20:	10071201 	adr	x1, e260 <_start-0x60ff1da0>
  24:	02000017 	.inst	0x02000017 ; undefined
  28:	0b0b0024 	add	w4, w1, w11
  2c:	0e030b3e 	.inst	0x0e030b3e ; undefined
  30:	16030000 	b	fffffffff80c0030 <out_buf+0xffffffff970bf850>
  34:	3a0e0300 	adcs	w0, w24, w14
  38:	390b3b0b 	strb	w11, [x24, #718]
  3c:	0013490b 	.inst	0x0013490b ; undefined
  40:	00240400 	.inst	0x00240400 ; NYI
  44:	0b3e0b0b 	add	w11, w24, w30, uxtb #2
  48:	00000803 	udf	#2051
  4c:	49003505 	.inst	0x49003505 ; undefined
  50:	06000013 	.inst	0x06000013 ; undefined
  54:	13490026 	.inst	0x13490026 ; undefined
  58:	2e070000 	ext	v0.8b, v0.8b, v7.8b, #0
  5c:	03193f01 	.inst	0x03193f01 ; undefined
  60:	3b0b3a0e 	.inst	0x3b0b3a0e ; undefined
  64:	270b390b 	.inst	0x270b390b ; undefined
  68:	12011119 	and	w25, w8, #0x8000000f
  6c:	97184007 	bl	fffffffffc610088 <out_buf+0xffffffff9b60f8a8>
  70:	13011942 	sbfx	w2, w10, #1, #6
  74:	05080000 	.inst	0x05080000 ; undefined
  78:	3a0e0300 	adcs	w0, w24, w14
  7c:	390b3b0b 	strb	w11, [x24, #718]
  80:	0213490b 	.inst	0x0213490b ; undefined
  84:	1742b717 	b	fffffffffd0adce0 <out_buf+0xffffffff9c0ad500>
  88:	34090000 	cbz	w0, 12088 <_start-0x60fedf78>
  8c:	3a080300 	adcs	w0, w24, w8
  90:	390b3b0b 	strb	w11, [x24, #718]
  94:	0213490b 	.inst	0x0213490b ; undefined
  98:	1742b717 	b	fffffffffd0adcf4 <out_buf+0xffffffff9c0ad514>
  9c:	890a0000 	.inst	0x890a0000 ; undefined
  a0:	11010182 	add	w2, w12, #0x40
  a4:	01133101 	.inst	0x01133101 ; undefined
  a8:	0b000013 	add	w19, w0, w0
  ac:	0001828a 	.inst	0x0001828a ; undefined
  b0:	42911802 	.inst	0x42911802 ; undefined
  b4:	0c000018 	st4	{v24.8b-v27.8b}, [x0]
  b8:	01018289 	.inst	0x01018289 ; undefined
  bc:	42950111 	.inst	0x42950111 ; undefined
  c0:	00133119 	.inst	0x00133119 ; undefined
  c4:	000f0d00 	.inst	0x000f0d00 ; undefined
  c8:	13490b0b 	.inst	0x13490b0b ; undefined
  cc:	050e0000 	.inst	0x050e0000 ; undefined
  d0:	3a0e0300 	adcs	w0, w24, w14
  d4:	390b3b0b 	strb	w11, [x24, #718]
  d8:	0213490b 	.inst	0x0213490b ; undefined
  dc:	0f000018 	.inst	0x0f000018 ; undefined
  e0:	0e030034 	tbl	v20.8b, {v1.16b}, v3.8b
  e4:	0b3b0b3a 	add	w26, w25, w27, uxtb #2
  e8:	13490b39 	.inst	0x13490b39 ; undefined
  ec:	42b71702 	.inst	0x42b71702 ; undefined
  f0:	10000017 	adr	x23, f0 <_start-0x60ffff10>
  f4:	193f012e 	.inst	0x193f012e ; undefined
  f8:	0b3a0e03 	add	w3, w16, w26, uxtb #3
  fc:	0b390b3b 	add	w27, w25, w25, uxtb #2
 100:	13491927 	.inst	0x13491927 ; undefined
 104:	07120111 	.inst	0x07120111 ; undefined
 108:	42971840 	.inst	0x42971840 ; undefined
 10c:	00130119 	.inst	0x00130119 ; undefined
 110:	012e1100 	.inst	0x012e1100 ; undefined
 114:	0e03193f 	uzp1	v31.8b, v9.8b, v3.8b
 118:	0b3b0b3a 	add	w26, w25, w27, uxtb #2
 11c:	19270b39 	.inst	0x19270b39 ; undefined
 120:	01111349 	.inst	0x01111349 ; undefined
 124:	18400712 	ldr	w18, 80204 <_start-0x60f7fdfc>
 128:	00194297 	.inst	0x00194297 ; undefined
 12c:	11010000 	add	w0, w0, #0x40
 130:	130e2501 	sbfiz	w1, w8, #18, #10
 134:	1b0e030b 	madd	w11, w24, w14, w0
 138:	1201110e 	and	w14, w8, #0x8000000f
 13c:	00171007 	.inst	0x00171007 ; undefined
 140:	00240200 	.inst	0x00240200 ; NYI
 144:	0b3e0b0b 	add	w11, w24, w30, uxtb #2
 148:	00000e03 	udf	#3587
 14c:	03001603 	.inst	0x03001603 ; undefined
 150:	3b0b3a0e 	.inst	0x3b0b3a0e ; undefined
 154:	490b390b 	.inst	0x490b390b ; undefined
 158:	04000013 	add	z19.b, p0/m, z19.b, z0.b
 15c:	0b0b0024 	add	w4, w1, w11
 160:	08030b3e 	stxrb	w3, w30, [x25]
 164:	26050000 	.inst	0x26050000 ; undefined
 168:	00134900 	.inst	0x00134900 ; undefined
 16c:	00350600 	.inst	0x00350600 ; NYI
 170:	00001349 	udf	#4937
 174:	49010107 	.inst	0x49010107 ; undefined
 178:	00130113 	.inst	0x00130113 ; undefined
 17c:	00210800 	.inst	0x00210800 ; NYI
 180:	0b2f1349 	add	w9, w26, w15, uxtb #4
 184:	34090000 	cbz	w0, 12184 <_start-0x60fede7c>
 188:	3a0e0300 	adcs	w0, w24, w14
 18c:	390b3b0b 	strb	w11, [x24, #718]
 190:	0213490b 	.inst	0x0213490b ; undefined
 194:	0a000018 	and	w24, w0, w0
 198:	13490021 	.inst	0x13490021 ; undefined
 19c:	0000052f 	udf	#1327
 1a0:	3f012e0b 	.inst	0x3f012e0b ; undefined
 1a4:	3a0e0319 	adcs	w25, w24, w14
 1a8:	390b3b0b 	strb	w11, [x24, #718]
 1ac:	4919270b 	.inst	0x4919270b ; undefined
 1b0:	12011113 	and	w19, w8, #0x8000000f
 1b4:	97184007 	bl	fffffffffc6101d0 <out_buf+0xffffffff9b60f9f0>
 1b8:	13011942 	sbfx	w2, w10, #1, #6
 1bc:	050c0000 	.inst	0x050c0000 ; undefined
 1c0:	3a0e0300 	adcs	w0, w24, w14
 1c4:	390b3b0b 	strb	w11, [x24, #718]
 1c8:	0213490b 	.inst	0x0213490b ; undefined
 1cc:	1742b717 	b	fffffffffd0ade28 <out_buf+0xffffffff9c0ad648>
 1d0:	340d0000 	cbz	w0, 1a1d0 <_start-0x60fe5e30>
 1d4:	3a080300 	adcs	w0, w24, w8
 1d8:	390b3b0b 	strb	w11, [x24, #718]
 1dc:	0213490b 	.inst	0x0213490b ; undefined
 1e0:	1742b717 	b	fffffffffd0ade3c <out_buf+0xffffffff9c0ad65c>
 1e4:	340e0000 	cbz	w0, 1c1e4 <_start-0x60fe3e1c>
 1e8:	3a0e0300 	adcs	w0, w24, w14
 1ec:	390b3b0b 	strb	w11, [x24, #718]
 1f0:	0213490b 	.inst	0x0213490b ; undefined
 1f4:	1742b717 	b	fffffffffd0ade50 <out_buf+0xffffffff9c0ad670>
 1f8:	890f0000 	.inst	0x890f0000 ; undefined
 1fc:	11010182 	add	w2, w12, #0x40
 200:	00133101 	.inst	0x00133101 ; undefined
 204:	828a1000 	.inst	0x828a1000 ; undefined
 208:	18020001 	ldr	w1, 4208 <_start-0x60ffbdf8>
 20c:	00184291 	.inst	0x00184291 ; undefined
 210:	000f1100 	.inst	0x000f1100 ; undefined
 214:	13490b0b 	.inst	0x13490b0b ; undefined
 218:	2e120000 	ext	v0.8b, v0.8b, v18.8b, #0
 21c:	3c193f00 	str	b0, [x24, #-109]!
 220:	030e6e19 	.inst	0x030e6e19 ; undefined
 224:	3b0b3a0e 	.inst	0x3b0b3a0e ; undefined
 228:	000b390b 	.inst	0x000b390b ; undefined
	...

Disassembly of section .debug_aranges:

0000000000000000 <.debug_aranges>:
   0:	0000002c 	udf	#44
   4:	00000002 	udf	#2
   8:	00080000 	.inst	0x00080000 ; undefined
   c:	00000000 	udf	#0
  10:	61000000 	.inst	0x61000000 ; undefined
  14:	00000000 	udf	#0
  18:	00000018 	udf	#24
	...
  30:	0000002c 	udf	#44
  34:	002e0002 	.inst	0x002e0002 ; NYI
  38:	00080000 	.inst	0x00080000 ; undefined
  3c:	00000000 	udf	#0
  40:	61000018 	.inst	0x61000018 ; undefined
  44:	00000000 	udf	#0
  48:	0000010c 	udf	#268
	...
  60:	0000002c 	udf	#44
  64:	03010002 	.inst	0x03010002 ; undefined
  68:	00080000 	.inst	0x00080000 ; undefined
  6c:	00000000 	udf	#0
  70:	61000124 	.inst	0x61000124 ; undefined
  74:	00000000 	udf	#0
  78:	000000bc 	udf	#188
	...

Disassembly of section .debug_str:

0000000000000000 <.debug_str>:
   0:	772f3a44 	.inst	0x772f3a44 ; undefined
   4:	2f6b726f 	fcmla	v15.4h, v19.4h, v11.h[1], #270
   8:	32323032 	orr	w18, w1, #0x7ffc000
   c:	396c612f 	ldrb	w15, [x9, #2840]
  10:	2f303030 	.inst	0x2f303030 ; undefined
  14:	642f7773 	.inst	0x642f7773 ; undefined
  18:	67756265 	.inst	0x67756265 ; undefined
  1c:	2f726567 	.inst	0x2f726567 ; undefined
  20:	6f5f6c61 	.inst	0x6f5f6c61 ; undefined
  24:	6f6e6570 	sqshlu	v16.2d, v11.2d, #46
  28:	632f6463 	.inst	0x632f6463 ; undefined
  2c:	72746e6f 	.inst	0x72746e6f ; undefined
  30:	6c2f6269 	stnp	d9, d24, [x19, #-272]
  34:	6564616f 	fnmls	z15.h, p0/m, z11.h, z4.h
  38:	662f7372 	.inst	0x662f7372 ; undefined
  3c:	6873616c 	.inst	0x6873616c ; undefined
  40:	6d6d652f 	ldp	d15, d25, [x9, #-304]
  44:	77642f63 	.inst	0x77642f63 ; undefined
  48:	68736d63 	.inst	0x68736d63 ; undefined
  4c:	72732f63 	.inst	0x72732f63 ; undefined
  50:	6f622f63 	.inst	0x6f622f63 ; undefined
  54:	612f746f 	.inst	0x612f746f ; undefined
  58:	68637261 	.inst	0x68637261 ; undefined
  5c:	6172775c 	.inst	0x6172775c ; undefined
  60:	72657070 	.inst	0x72657070 ; undefined
  64:	4400532e 	.inst	0x4400532e ; undefined
  68:	6f775c3a 	.inst	0x6f775c3a ; undefined
  6c:	325c6b72 	.inst	0x325c6b72 ; undefined
  70:	5c323230 	ldr	d16, 646b4 <_start-0x60f9b94c>
  74:	30396c61 	adr	x1, 72e01 <_start-0x60f8d1ff>
  78:	735c3030 	.inst	0x735c3030 ; undefined
  7c:	65645c77 	fnmla	z23.h, p7/m, z3.h, z4.h
  80:	67677562 	.inst	0x67677562 ; undefined
  84:	615c7265 	.inst	0x615c7265 ; undefined
  88:	706f5f6c 	adr	x12, dec77 <_start-0x60f21389>
  8c:	636f6e65 	.inst	0x636f6e65 ; undefined
  90:	6f635c64 	.inst	0x6f635c64 ; undefined
  94:	6972746e 	ldpsw	x14, x29, [x3, #-112]
  98:	6f6c5c62 	.inst	0x6f6c5c62 ; undefined
  9c:	72656461 	.inst	0x72656461 ; undefined
  a0:	6c665c73 	ldnp	d19, d23, [x3, #-416]
  a4:	5c687361 	ldr	d1, d0f10 <_start-0x60f2f0f0>
  a8:	636d6d65 	.inst	0x636d6d65 ; undefined
  ac:	6377645c 	.inst	0x6377645c ; undefined
  b0:	6368736d 	.inst	0x6368736d ; undefined
  b4:	554e4700 	.inst	0x554e4700 ; undefined
  b8:	20534120 	.inst	0x20534120 ; undefined
  bc:	36332e32 	tbz	w18, #6, 6680 <_start-0x60ff9980>
  c0:	6300312e 	.inst	0x6300312e ; undefined
  c4:	5f6c7274 	sqdmlsl	s20, h19, v12.h[2]
  c8:	65736162 	fnmls	z2.h, p0/m, z11.h, z19.h
  cc:	755f5f00 	.inst	0x755f5f00 ; undefined
  d0:	38746e69 	.inst	0x38746e69 ; undefined
  d4:	6500745f 	.inst	0x6500745f ; undefined
  d8:	5f636d6d 	.inst	0x5f636d6d ; undefined
  dc:	64616572 	.inst	0x64616572 ; undefined
  e0:	6f6c625f 	umlsl2	v31.4s, v18.8h, v12.h[2]
  e4:	47006b63 	.inst	0x47006b63 ; undefined
  e8:	4320554e 	.inst	0x4320554e ; undefined
  ec:	31203731 	adds	w17, w25, #0x80d
  f0:	2e332e30 	uqsub	v16.8b, v17.8b, v19.8b
  f4:	30322031 	adr	x17, 644f9 <_start-0x60f9bb07>
  f8:	36303132 	tbz	w18, #6, 71c <_start-0x60fff8e4>
  fc:	2d203132 	stp	s18, s12, [x9, #-256]
 100:	6962616d 	ldpsw	x13, x24, [x11, #-240]
 104:	36706c3d 	tbz	w29, #14, e88 <_start-0x60fff178>
 108:	6d2d2034 	stp	d20, d8, [x1, #-304]
 10c:	7474696c 	.inst	0x7474696c ; undefined
 110:	652d656c 	.inst	0x652d656c ; undefined
 114:	6169646e 	.inst	0x6169646e ; undefined
 118:	672d206e 	.inst	0x672d206e ; undefined
 11c:	734f2d20 	.inst	0x734f2d20 ; undefined
 120:	6e662d20 	uqsub	v0.8h, v9.8h, v6.8h
 124:	75622d6f 	.inst	0x75622d6f ; undefined
 128:	69746c69 	ldpsw	x9, x27, [x3, #-96]
 12c:	662d206e 	.inst	0x662d206e ; undefined
 130:	00434950 	.inst	0x00434950 ; undefined
 134:	636d6d65 	.inst	0x636d6d65 ; undefined
 138:	6961775f 	ldpsw	xzr, x29, [x26, #-248]
 13c:	69665f74 	ldpsw	x20, x23, [x27, #-208]
 140:	75006f66 	.inst	0x75006f66 ; undefined
 144:	6769736e 	.inst	0x6769736e ; undefined
 148:	2064656e 	.inst	0x2064656e ; undefined
 14c:	72616863 	.inst	0x72616863 ; undefined
 150:	6e6f6c00 	umin	v0.8h, v0.8h, v15.8h
 154:	6e752067 	usubl2	v7.4s, v3.8h, v21.8h
 158:	6e676973 	.inst	0x6e676973 ; undefined
 15c:	69206465 	stgp	x5, x25, [x3, #-1024]
 160:	7300746e 	.inst	0x7300746e ; undefined
 164:	74726f68 	.inst	0x74726f68 ; undefined
 168:	736e7520 	.inst	0x736e7520 ; undefined
 16c:	656e6769 	fnmls	z9.h, p1/m, z27.h, z14.h
 170:	6e692064 	usubl2	v4.4s, v3.8h, v9.8h
 174:	6f640074 	mla	v20.8h, v3.8h, v4.h[2]
 178:	665f656e 	.inst	0x665f656e ; undefined
 17c:	0067616c 	.inst	0x0067616c ; undefined
 180:	636d6d65 	.inst	0x636d6d65 ; undefined
 184:	6c6f705f 	ldnp	d31, d28, [x2, #-272]
 188:	6e695f6c 	uqrshl	v12.8h, v27.8h, v9.8h
 18c:	6f770074 	mla	v20.8h, v3.8h, v7.h[3]
 190:	615f6b72 	.inst	0x615f6b72 ; undefined
 194:	5f616572 	.inst	0x5f616572 ; undefined
 198:	72617473 	.inst	0x72617473 ; undefined
 19c:	5f5f0074 	.inst	0x5f5f0074 ; undefined
 1a0:	746e6975 	.inst	0x746e6975 ; undefined
 1a4:	745f3233 	.inst	0x745f3233 ; undefined
 1a8:	6d6d6500 	ldp	d0, d25, [x8, #-304]
 1ac:	72775f63 	.inst	0x72775f63 ; undefined
 1b0:	5f657469 	sqshl	d9, d3, #37
 1b4:	636f6c62 	.inst	0x636f6c62 ; undefined
 1b8:	6e69006b 	uaddl2	v11.4s, v3.8h, v9.8h
 1bc:	61765f74 	.inst	0x61765f74 ; undefined
 1c0:	3a44006c 	ccmn	w3, w4, #0xc, eq  // eq = none
 1c4:	726f772f 	.inst	0x726f772f ; undefined
 1c8:	30322f6b 	adr	x11, 647b5 <_start-0x60f9b84b>
 1cc:	612f3232 	.inst	0x612f3232 ; undefined
 1d0:	3030396c 	adr	x12, 608fd <_start-0x60f9f703>
 1d4:	77732f30 	.inst	0x77732f30 ; undefined
 1d8:	6265642f 	.inst	0x6265642f ; undefined
 1dc:	65676775 	fnmls	z21.h, p1/m, z27.h, z7.h
 1e0:	6c612f72 	ldnp	d18, d11, [x27, #-496]
 1e4:	65706f5f 	fnmls	z31.h, p3/m, z26.h, z16.h
 1e8:	64636f6e 	.inst	0x64636f6e ; undefined
 1ec:	6e6f632f 	rsubhn2	v15.8h, v25.4s, v15.4s
 1f0:	62697274 	.inst	0x62697274 ; undefined
 1f4:	616f6c2f 	.inst	0x616f6c2f ; undefined
 1f8:	73726564 	.inst	0x73726564 ; undefined
 1fc:	616c662f 	.inst	0x616c662f ; undefined
 200:	652f6873 	.inst	0x652f6873 ; undefined
 204:	2f636d6d 	.inst	0x2f636d6d ; undefined
 208:	6d637764 	ldp	d4, d29, [x27, #-464]
 20c:	2f636873 	umlsl	v19.4s, v3.4h, v3.h[6]
 210:	2f637273 	fcmla	v19.4h, v19.4h, v3.h[1], #270
 214:	6d637764 	ldp	d4, d29, [x27, #-464]
 218:	2e636873 	.inst	0x2e636873 ; undefined
 21c:	6f770063 	mla	v3.8h, v3.8h, v7.h[3]
 220:	635f6472 	.inst	0x635f6472 ; undefined
 224:	7300746e 	.inst	0x7300746e ; undefined
 228:	74726f68 	.inst	0x74726f68 ; undefined
 22c:	746e6920 	.inst	0x746e6920 ; undefined
 230:	66756200 	.inst	0x66756200 ; undefined
 234:	00726566 	.inst	0x00726566 ; undefined
 238:	676e6f6c 	.inst	0x676e6f6c ; undefined
 23c:	746e6920 	.inst	0x746e6920 ; undefined
 240:	656c6300 	fnmls	z0.h, p0/m, z24.h, z12.h
 244:	725f7261 	.inst	0x725f7261 ; undefined
 248:	66006765 	.inst	0x66006765 ; undefined
 24c:	5f67616c 	.inst	0x5f67616c ; undefined
 250:	7366666f 	.inst	0x7366666f ; undefined
 254:	5f007465 	.inst	0x5f007465 ; undefined
 258:	746e695f 	.inst	0x746e695f ; undefined
 25c:	745f3233 	.inst	0x745f3233 ; undefined
 260:	6f6c6200 	umlsl2	v0.4s, v16.8h, v12.h[2]
 264:	615f6b63 	.inst	0x615f6b63 ; undefined
 268:	00726464 	.inst	0x00726464 ; undefined
 26c:	5f727563 	sqshl	d3, d11, #50
 270:	6e756f63 	umin	v3.8h, v27.8h, v21.8h
 274:	6c620074 	ldnp	d20, d0, [x3, #-480]
 278:	5f6b636f 	.inst	0x5f6b636f ; undefined
 27c:	657a6973 	fnmls	z19.h, p2/m, z11.h, z26.h
 280:	2f3a4400 	sri	v0.2s, v0.2s, #6
 284:	6b726f77 	.inst	0x6b726f77 ; undefined
 288:	3230322f 	orr	w15, w17, #0x1fff0000
 28c:	6c612f32 	ldnp	d18, d11, [x25, #-496]
 290:	30303039 	adr	x25, 60895 <_start-0x60f9f76b>
 294:	2f77732f 	fcmla	v15.4h, v25.4h, v23.h[1], #270
 298:	75626564 	.inst	0x75626564 ; undefined
 29c:	72656767 	.inst	0x72656767 ; undefined
 2a0:	5f6c612f 	.inst	0x5f6c612f ; undefined
 2a4:	6e65706f 	uabdl2	v15.4s, v3.8h, v5.8h
 2a8:	2f64636f 	umlsl	v15.4s, v27.4h, v4.h[2]
 2ac:	746e6f63 	.inst	0x746e6f63 ; undefined
 2b0:	2f626972 	umlsl	v18.4s, v11.4h, v2.h[6]
 2b4:	64616f6c 	.inst	0x64616f6c ; undefined
 2b8:	2f737265 	fcmla	v5.4h, v19.4h, v19.h[1], #270
 2bc:	73616c66 	.inst	0x73616c66 ; undefined
 2c0:	6d652f68 	ldp	d8, d11, [x27, #-432]
 2c4:	642f636d 	.inst	0x642f636d ; undefined
 2c8:	736d6377 	.inst	0x736d6377 ; undefined
 2cc:	732f6368 	.inst	0x732f6368 ; undefined
 2d0:	642f6372 	.inst	0x642f6372 ; undefined
 2d4:	736d6377 	.inst	0x736d6377 ; undefined
 2d8:	635f6368 	.inst	0x635f6368 ; undefined
 2dc:	632e6372 	.inst	0x632e6372 ; undefined
 2e0:	63726300 	.inst	0x63726300 ; undefined
 2e4:	756f635f 	.inst	0x756f635f ; undefined
 2e8:	6300746e 	.inst	0x6300746e ; undefined
 2ec:	32336372 	orr	w18, w27, #0xffffe03f
 2f0:	6261745f 	.inst	0x6261745f ; undefined
 2f4:	6f00656c 	mvni	v12.4s, #0xb, lsl #24
 2f8:	625f7475 	.inst	0x625f7475 ; undefined
 2fc:	65006675 	.inst	0x65006675 ; undefined
 300:	5f636d6d 	.inst	0x5f636d6d ; undefined
 304:	6d637764 	ldp	d4, d29, [x27, #-464]
 308:	00636873 	.inst	0x00636873 ; undefined

Disassembly of section .debug_loc:

0000000000000000 <.debug_loc>:
	...
   8:	00a40000 	.inst	0x00a40000 ; undefined
   c:	00000000 	udf	#0
  10:	00d70000 	.inst	0x00d70000 ; undefined
  14:	00000000 	udf	#0
  18:	00010000 	.inst	0x00010000 ; undefined
  1c:	0000d750 	udf	#55120
  20:	00000000 	udf	#0
  24:	0000f000 	udf	#61440
  28:	00000000 	udf	#0
  2c:	63000100 	.inst	0x63000100 ; undefined
  30:	000000f0 	udf	#240
  34:	00000000 	udf	#0
  38:	000000fb 	udf	#251
  3c:	00000000 	udf	#0
  40:	fb500001 	.inst	0xfb500001 ; undefined
  44:	00000000 	udf	#0
  48:	fc000000 	stur	d0, [x0]
  4c:	00000000 	udf	#0
  50:	04000000 	add	z0.b, p0/m, z0.b, z0.b
  54:	5001f300 	adr	x0, 3eb6 <_start-0x60ffc14a>
  58:	0000fc9f 	udf	#64671
  5c:	00000000 	udf	#0
  60:	00010c00 	.inst	0x00010c00 ; undefined
  64:	00000000 	udf	#0
  68:	63000100 	.inst	0x63000100 ; undefined
	...
  84:	000000a4 	udf	#164
  88:	00000000 	udf	#0
  8c:	000000c4 	udf	#196
  90:	00000000 	udf	#0
  94:	c4510001 	ld1sb	{z1.d}, p0/z, [x0, z17.d, sxtw]
  98:	00000000 	udf	#0
  9c:	f4000000 	.inst	0xf4000000 ; undefined
  a0:	00000000 	udf	#0
  a4:	01000000 	.inst	0x01000000 ; undefined
  a8:	00f46500 	.inst	0x00f46500 ; undefined
  ac:	00000000 	udf	#0
  b0:	00fc0000 	.inst	0x00fc0000 ; undefined
  b4:	00000000 	udf	#0
  b8:	00040000 	.inst	0x00040000 ; undefined
  bc:	9f5101f3 	.inst	0x9f5101f3 ; undefined
  c0:	000000fc 	udf	#252
  c4:	00000000 	udf	#0
  c8:	0000010c 	udf	#268
  cc:	00000000 	udf	#0
  d0:	00650001 	.inst	0x00650001 ; undefined
	...
  e4:	a4000000 	ld1rqb	{z0.b}, p0/z, [x0, x0]
  e8:	00000000 	udf	#0
  ec:	d7000000 	.inst	0xd7000000 ; undefined
  f0:	00000000 	udf	#0
  f4:	01000000 	.inst	0x01000000 ; undefined
  f8:	00d75200 	.inst	0x00d75200 ; undefined
  fc:	00000000 	udf	#0
 100:	010c0000 	.inst	0x010c0000 ; undefined
 104:	00000000 	udf	#0
 108:	00040000 	.inst	0x00040000 ; undefined
 10c:	9f5201f3 	.inst	0x9f5201f3 ; undefined
	...
 128:	000000a4 	udf	#164
 12c:	00000000 	udf	#0
 130:	000000d7 	udf	#215
 134:	00000000 	udf	#0
 138:	d7530001 	.inst	0xd7530001 ; undefined
 13c:	00000000 	udf	#0
 140:	f0000000 	adrp	x0, 3000 <_start-0x60ffd000>
 144:	00000000 	udf	#0
 148:	01000000 	.inst	0x01000000 ; undefined
 14c:	00f06400 	.inst	0x00f06400 ; undefined
 150:	00000000 	udf	#0
 154:	00fc0000 	.inst	0x00fc0000 ; undefined
 158:	00000000 	udf	#0
 15c:	00040000 	.inst	0x00040000 ; undefined
 160:	9f5301f3 	.inst	0x9f5301f3 ; undefined
 164:	000000fc 	udf	#252
 168:	00000000 	udf	#0
 16c:	0000010c 	udf	#268
 170:	00000000 	udf	#0
 174:	00640001 	.inst	0x00640001 ; undefined
	...
 184:	01000000 	.inst	0x01000000 ; undefined
 188:	00000000 	udf	#0
 18c:	d8000101 	prfm	pldl1strm, 1ac <_start-0x60fffe54>
 190:	00000000 	udf	#0
 194:	dc000000 	.inst	0xdc000000 ; undefined
 198:	00000000 	udf	#0
 19c:	02000000 	.inst	0x02000000 ; undefined
 1a0:	dc9f3000 	.inst	0xdc9f3000 ; undefined
 1a4:	00000000 	udf	#0
 1a8:	e8000000 	.inst	0xe8000000 ; undefined
 1ac:	00000000 	udf	#0
 1b0:	01000000 	.inst	0x01000000 ; undefined
 1b4:	00fc5000 	.inst	0x00fc5000 ; undefined
 1b8:	00000000 	udf	#0
 1bc:	01040000 	.inst	0x01040000 ; undefined
 1c0:	00000000 	udf	#0
 1c4:	00010000 	.inst	0x00010000 ; undefined
 1c8:	00010450 	.inst	0x00010450 ; undefined
 1cc:	00000000 	udf	#0
 1d0:	00010800 	.inst	0x00010800 ; undefined
 1d4:	00000000 	udf	#0
 1d8:	70000300 	adr	x0, 23b <_start-0x60fffdc5>
 1dc:	00009f01 	udf	#40705
	...
 1f4:	00000050 	udf	#80
 1f8:	00000000 	udf	#0
 1fc:	00000058 	udf	#88
 200:	00000000 	udf	#0
 204:	58510001 	ldr	x1, a2204 <_start-0x60f5ddfc>
 208:	00000000 	udf	#0
 20c:	60000000 	.inst	0x60000000 ; undefined
 210:	00000000 	udf	#0
 214:	02000000 	.inst	0x02000000 ; undefined
 218:	60087000 	.inst	0x60087000 ; undefined
 21c:	00000000 	udf	#0
 220:	a4000000 	ld1rqb	{z0.b}, p0/z, [x0, x0]
 224:	00000000 	udf	#0
 228:	04000000 	add	z0.b, p0/m, z0.b, z0.b
 22c:	5101f300 	sub	w0, w24, #0x7c
 230:	0000009f 	udf	#159
	...
 240:	74000000 	.inst	0x74000000 ; undefined
 244:	00000000 	udf	#0
 248:	7c000000 	stur	h0, [x0]
 24c:	00000000 	udf	#0
 250:	01000000 	.inst	0x01000000 ; undefined
 254:	00005100 	udf	#20736
	...
 268:	00640000 	.inst	0x00640000 ; undefined
 26c:	00000000 	udf	#0
 270:	00740000 	.inst	0x00740000 ; undefined
 274:	00000000 	udf	#0
 278:	00010000 	.inst	0x00010000 ; undefined
 27c:	00008c51 	udf	#35921
 280:	00000000 	udf	#0
 284:	00009c00 	udf	#39936
 288:	00000000 	udf	#0
 28c:	51000100 	sub	w0, w8, #0x0
	...
 2a0:	00010001 	.inst	0x00010001 ; undefined
 2a4:	00000064 	udf	#100
 2a8:	00000000 	udf	#0
 2ac:	00000074 	udf	#116
 2b0:	00000000 	udf	#0
 2b4:	00710007 	.inst	0x00710007 ; undefined
 2b8:	1a312534 	.inst	0x1a312534 ; undefined
 2bc:	00008c9f 	udf	#35999
 2c0:	00000000 	udf	#0
 2c4:	00009c00 	udf	#39936
 2c8:	00000000 	udf	#0
 2cc:	71000700 	subs	w0, w24, #0x1
 2d0:	31253100 	adds	w0, w8, #0x94c
 2d4:	00009f1a 	udf	#40730
	...
 2e8:	00180000 	.inst	0x00180000 ; undefined
 2ec:	00000000 	udf	#0
 2f0:	004c0000 	.inst	0x004c0000 ; undefined
 2f4:	00000000 	udf	#0
 2f8:	00010000 	.inst	0x00010000 ; undefined
 2fc:	00004c50 	udf	#19536
 300:	00000000 	udf	#0
 304:	00005000 	udf	#20480
 308:	00000000 	udf	#0
 30c:	f3000400 	.inst	0xf3000400 ; undefined
 310:	009f5001 	.inst	0x009f5001 ; undefined
	...
 324:	18000000 	ldr	w0, 324 <_start-0x60fffcdc>
 328:	00000000 	udf	#0
 32c:	24000000 	cmphs	p0.b, p0/z, z0.b, z0.b
 330:	00000000 	udf	#0
 334:	01000000 	.inst	0x01000000 ; undefined
 338:	00245100 	.inst	0x00245100 ; NYI
 33c:	00000000 	udf	#0
 340:	00500000 	.inst	0x00500000 ; undefined
 344:	00000000 	udf	#0
 348:	00010000 	.inst	0x00010000 ; undefined
 34c:	00000053 	udf	#83
	...
 35c:	28000000 	stnp	w0, w0, [x0]
 360:	00000000 	udf	#0
 364:	50000000 	adr	x0, 366 <_start-0x60fffc9a>
 368:	00000000 	udf	#0
 36c:	01000000 	.inst	0x01000000 ; undefined
 370:	00005200 	udf	#20992
	...
 384:	00000044 	udf	#68
 388:	00000000 	udf	#0
 38c:	00000050 	udf	#80
 390:	00000000 	udf	#0
 394:	00510001 	.inst	0x00510001 ; undefined
	...
 3a4:	03000000 	.inst	0x03000000 ; undefined
 3a8:	18000100 	ldr	w0, 3c8 <_start-0x60fffc38>
 3ac:	00000000 	udf	#0
 3b0:	24000000 	cmphs	p0.b, p0/z, z0.b, z0.b
 3b4:	00000000 	udf	#0
 3b8:	02000000 	.inst	0x02000000 ; undefined
 3bc:	289f3000 	stp	w0, w12, [x0], #248
 3c0:	00000000 	udf	#0
 3c4:	50000000 	adr	x0, 3c6 <_start-0x60fffc3a>
 3c8:	00000000 	udf	#0
 3cc:	08000000 	stxrb	w0, w0, [x0]
 3d0:	73007200 	.inst	0x73007200 ; undefined
 3d4:	1a312500 	.inst	0x1a312500 ; undefined
 3d8:	0000009f 	udf	#159
	...
 3e8:	03030000 	.inst	0x03030000 ; undefined
	...
 3f4:	00000400 	udf	#1024
 3f8:	00000000 	udf	#0
 3fc:	50000100 	adr	x0, 41e <_start-0x60fffbe2>
 400:	00000004 	udf	#4
 404:	00000000 	udf	#0
 408:	00000018 	udf	#24
 40c:	00000000 	udf	#0
 410:	00510001 	.inst	0x00510001 ; undefined
	...
 420:	01000000 	.inst	0x01000000 ; undefined
 424:	04000000 	add	z0.b, p0/m, z0.b, z0.b
 428:	00000000 	udf	#0
 42c:	08000000 	stxrb	w0, w0, [x0]
 430:	00000000 	udf	#0
 434:	02000000 	.inst	0x02000000 ; undefined
 438:	089f3000 	stllrb	w0, [x0]
 43c:	00000000 	udf	#0
 440:	18000000 	ldr	w0, 440 <_start-0x60fffbc0>
 444:	00000000 	udf	#0
 448:	01000000 	.inst	0x01000000 ; undefined
 44c:	00005200 	udf	#20992
	...
 45c:	00010000 	.inst	0x00010000 ; undefined
 460:	00040000 	.inst	0x00040000 ; undefined
 464:	00000000 	udf	#0
 468:	000c0000 	.inst	0x000c0000 ; undefined
 46c:	00000000 	udf	#0
 470:	00020000 	.inst	0x00020000 ; undefined
 474:	000c9f30 	.inst	0x000c9f30 ; undefined
 478:	00000000 	udf	#0
 47c:	00180000 	.inst	0x00180000 ; undefined
 480:	00000000 	udf	#0
 484:	00010000 	.inst	0x00010000 ; undefined
 488:	00000050 	udf	#80
	...
 4a8:	00004400 	udf	#17408
 4ac:	00000000 	udf	#0
 4b0:	50000100 	adr	x0, 4d2 <_start-0x60fffb2e>
 4b4:	00000044 	udf	#68
 4b8:	00000000 	udf	#0
 4bc:	0000005c 	udf	#92
 4c0:	00000000 	udf	#0
 4c4:	5c680001 	ldr	d1, d04c4 <_start-0x60f2fb3c>
 4c8:	00000000 	udf	#0
 4cc:	6c000000 	stnp	d0, d0, [x0]
 4d0:	00000000 	udf	#0
 4d4:	04000000 	add	z0.b, p0/m, z0.b, z0.b
 4d8:	5001f300 	adr	x0, 433a <_start-0x60ffbcc6>
 4dc:	00006c9f 	udf	#27807
 4e0:	00000000 	udf	#0
 4e4:	0000bc00 	udf	#48128
 4e8:	00000000 	udf	#0
 4ec:	68000100 	.inst	0x68000100 ; undefined
	...
 510:	00000044 	udf	#68
 514:	00000000 	udf	#0
 518:	44510001 	.inst	0x44510001 ; undefined
 51c:	00000000 	udf	#0
 520:	54000000 	b.eq	520 <_start-0x60fffae0>  // b.none
 524:	00000000 	udf	#0
 528:	01000000 	.inst	0x01000000 ; undefined
 52c:	00546400 	.inst	0x00546400 ; undefined
 530:	00000000 	udf	#0
 534:	006c0000 	.inst	0x006c0000 ; undefined
 538:	00000000 	udf	#0
 53c:	00040000 	.inst	0x00040000 ; undefined
 540:	9f5101f3 	.inst	0x9f5101f3 ; undefined
 544:	0000006c 	udf	#108
 548:	00000000 	udf	#0
 54c:	000000bc 	udf	#188
 550:	00000000 	udf	#0
 554:	00640001 	.inst	0x00640001 ; undefined
	...
 574:	00004400 	udf	#17408
 578:	00000000 	udf	#0
 57c:	52000100 	eor	w0, w8, #0x1
 580:	00000044 	udf	#68
 584:	00000000 	udf	#0
 588:	00000054 	udf	#84
 58c:	00000000 	udf	#0
 590:	6c630001 	ldnp	d1, d0, [x0, #-464]
 594:	00000000 	udf	#0
 598:	bc000000 	stur	s0, [x0]
 59c:	00000000 	udf	#0
 5a0:	01000000 	.inst	0x01000000 ; undefined
 5a4:	00006300 	udf	#25344
	...
 5c4:	00000044 	udf	#68
 5c8:	00000000 	udf	#0
 5cc:	44530001 	.inst	0x44530001 ; undefined
 5d0:	00000000 	udf	#0
 5d4:	58000000 	ldr	x0, 5d4 <_start-0x60fffa2c>
 5d8:	00000000 	udf	#0
 5dc:	01000000 	.inst	0x01000000 ; undefined
 5e0:	006c6500 	.inst	0x006c6500 ; undefined
 5e4:	00000000 	udf	#0
 5e8:	00bc0000 	.inst	0x00bc0000 ; undefined
 5ec:	00000000 	udf	#0
 5f0:	00010000 	.inst	0x00010000 ; undefined
 5f4:	00000065 	udf	#101
	...
 604:	00000200 	udf	#512
	...
 614:	00004400 	udf	#17408
 618:	00000000 	udf	#0
 61c:	09000300 	.inst	0x09000300 ; undefined
 620:	00449fff 	.inst	0x00449fff ; undefined
 624:	00000000 	udf	#0
 628:	00580000 	.inst	0x00580000 ; undefined
 62c:	00000000 	udf	#0
 630:	00010000 	.inst	0x00010000 ; undefined
 634:	00005866 	udf	#22630
 638:	00000000 	udf	#0
 63c:	00006c00 	udf	#27648
 640:	00000000 	udf	#0
 644:	50000100 	adr	x0, 666 <_start-0x60fff99a>
 648:	0000006c 	udf	#108
 64c:	00000000 	udf	#0
 650:	000000bc 	udf	#188
 654:	00000000 	udf	#0
 658:	00660001 	.inst	0x00660001 ; undefined
	...
 668:	03000000 	.inst	0x03000000 ; undefined
	...
 674:	44000000 	.inst	0x44000000 ; undefined
 678:	00000000 	udf	#0
 67c:	02000000 	.inst	0x02000000 ; undefined
 680:	789f3000 	ldursh	x0, [x0, #-13]
 684:	00000000 	udf	#0
 688:	bc000000 	stur	s0, [x0]
 68c:	00000000 	udf	#0
 690:	01000000 	.inst	0x01000000 ; undefined
 694:	00006a00 	udf	#27136
	...
 6a4:	00030000 	.inst	0x00030000 ; undefined
 6a8:	00000001 	udf	#1
	...
 6b4:	00000044 	udf	#68
 6b8:	00000000 	udf	#0
 6bc:	9f300002 	.inst	0x9f300002 ; undefined
 6c0:	00000078 	udf	#120
 6c4:	00000000 	udf	#0
 6c8:	00000090 	udf	#144
 6cc:	00000000 	udf	#0
 6d0:	906a0001 	adrp	x1, d4000000 <out_buf+0x72fff820>
 6d4:	00000000 	udf	#0
 6d8:	bc000000 	stur	s0, [x0]
 6dc:	00000000 	udf	#0
 6e0:	01000000 	.inst	0x01000000 ; undefined
 6e4:	00005000 	udf	#20480
	...
 6f4:	00040000 	.inst	0x00040000 ; undefined
 6f8:	00000002 	udf	#2
 6fc:	00000001 	udf	#1
 700:	00000000 	udf	#0
 704:	00440000 	.inst	0x00440000 ; undefined
 708:	00000000 	udf	#0
 70c:	00020000 	.inst	0x00020000 ; undefined
 710:	00789f30 	.inst	0x00789f30 ; undefined
 714:	00000000 	udf	#0
 718:	00900000 	.inst	0x00900000 ; undefined
 71c:	00000000 	udf	#0
 720:	00020000 	.inst	0x00020000 ; undefined
 724:	00909f30 	.inst	0x00909f30 ; undefined
 728:	00000000 	udf	#0
 72c:	00ac0000 	.inst	0x00ac0000 ; undefined
 730:	00000000 	udf	#0
 734:	000d0000 	.inst	0x000d0000 ; undefined
 738:	e0030071 	.inst	0xe0030071 ; undefined
 73c:	00610007 	.inst	0x00610007 ; undefined
 740:	1c000000 	ldr	s0, 740 <_start-0x60fff8c0>
 744:	0000b89f 	udf	#47263
 748:	00000000 	udf	#0
 74c:	0000bc00 	udf	#48128
 750:	00000000 	udf	#0
 754:	71000d00 	subs	w0, w8, #0x3
 758:	07e00300 	.inst	0x07e00300 ; undefined
 75c:	00006100 	udf	#24832
 760:	9f1c0000 	.inst	0x9f1c0000 ; undefined
	...

Disassembly of section .comment:

0000000000000000 <.comment>:
   0:	3a434347 	ccmn	w26, w3, #0x7, mi  // mi = first
   4:	4e472820 	trn1	v0.8h, v1.8h, v7.8h
   8:	6f542055 	umlal2	v21.4s, v2.8h, v4.h[1]
   c:	68636c6f 	.inst	0x68636c6f ; undefined
  10:	206e6961 	.inst	0x206e6961 ; undefined
  14:	20726f66 	.inst	0x20726f66 ; undefined
  18:	20656874 	.inst	0x20656874 ; undefined
  1c:	72702d41 	.inst	0x72702d41 ; undefined
  20:	6c69666f 	ldnp	d15, d25, [x19, #-368]
  24:	72412065 	.inst	0x72412065 ; undefined
  28:	74696863 	.inst	0x74696863 ; undefined
  2c:	75746365 	.inst	0x75746365 ; undefined
  30:	31206572 	adds	w18, w11, #0x819
  34:	2d332e30 	stp	s16, s11, [x17, #-104]
  38:	31323032 	adds	w18, w1, #0xc8c
  3c:	2037302e 	.inst	0x2037302e ; undefined
  40:	6d726128 	ldp	d8, d24, [x9, #-224]
  44:	2e30312d 	usubw	v13.8h, v9.8h, v16.8b
  48:	29293932 	stp	w18, w14, [x9, #-184]
  4c:	2e303120 	usubw	v0.8h, v9.8h, v16.8b
  50:	20312e33 	.inst	0x20312e33 ; undefined
  54:	31323032 	adds	w18, w1, #0xc8c
  58:	31323630 	adds	w16, w17, #0xc8d
	...

Disassembly of section .debug_frame:

0000000000000000 <.debug_frame>:
   0:	0000000c 	udf	#12
   4:	ffffffff 	.inst	0xffffffff ; undefined
   8:	78040001 	sturh	w1, [x0, #64]
   c:	001f0c1e 	.inst	0x001f0c1e ; undefined
  10:	00000014 	udf	#20
  14:	00000000 	udf	#0
  18:	61000018 	.inst	0x61000018 ; undefined
  1c:	00000000 	udf	#0
  20:	00000018 	udf	#24
  24:	00000000 	udf	#0
  28:	00000014 	udf	#20
  2c:	00000000 	udf	#0
  30:	61000030 	.inst	0x61000030 ; undefined
  34:	00000000 	udf	#0
  38:	00000038 	udf	#56
  3c:	00000000 	udf	#0
  40:	00000014 	udf	#20
  44:	00000000 	udf	#0
  48:	61000068 	.inst	0x61000068 ; undefined
  4c:	00000000 	udf	#0
  50:	00000054 	udf	#84
  54:	00000000 	udf	#0
  58:	00000034 	udf	#52
  5c:	00000000 	udf	#0
  60:	610000bc 	.inst	0x610000bc ; undefined
  64:	00000000 	udf	#0
  68:	00000068 	udf	#104
  6c:	00000000 	udf	#0
  70:	9d300e41 	.inst	0x9d300e41 ; undefined
  74:	42059e06 	.inst	0x42059e06 ; undefined
  78:	03940493 	.inst	0x03940493 ; undefined
  7c:	4f029543 	orr	v3.8h, #0x4a
  80:	d5ddde0a 	.inst	0xd5ddde0a ; undefined
  84:	000ed4d3 	.inst	0x000ed4d3 ; undefined
  88:	00000b41 	udf	#2881
  8c:	00000000 	udf	#0
  90:	0000000c 	udf	#12
  94:	ffffffff 	.inst	0xffffffff ; undefined
  98:	78040001 	sturh	w1, [x0, #64]
  9c:	001f0c1e 	.inst	0x001f0c1e ; undefined
  a0:	00000044 	udf	#68
  a4:	00000090 	udf	#144
  a8:	61000124 	.inst	0x61000124 ; undefined
  ac:	00000000 	udf	#0
  b0:	000000bc 	udf	#188
  b4:	00000000 	udf	#0
  b8:	9d600e41 	.inst	0x9d600e41 ; undefined
  bc:	420b9e0c 	.inst	0x420b9e0c ; undefined
  c0:	09940a93 	.inst	0x09940a93 ; undefined
  c4:	96089543 	bl	fffffffff82255d0 <out_buf+0xffffffff97224df0>
  c8:	06974307 	.inst	0x06974307 ; undefined
  cc:	99440598 	.inst	0x99440598 ; undefined
  d0:	43039a04 	.inst	0x43039a04 ; undefined
  d4:	0a4a029b 	and	w27, w20, w10, lsr #0
  d8:	d9dbddde 	.inst	0xd9dbddde ; undefined
  dc:	d5d8d7da 	.inst	0xd5d8d7da ; undefined
  e0:	0ed4d3d6 	.inst	0x0ed4d3d6 ; undefined
  e4:	000b4100 	.inst	0x000b4100 ; undefined
