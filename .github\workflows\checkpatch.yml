on: pull_request

name: Check Code Style (checkpatch)

jobs:
  check:
    runs-on: ubuntu-latest
    env:
      DL_DIR: ../downloads
      BUILD_DIR: ../build
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2
        with:
            fetch-depth: 50
      - name: Install required packages (apt-get)
        run: |
            sudo apt-get update
            sudo apt-get install patchutils
      - name: Run checkpatch
        run: |
            git diff -U20 HEAD~40 \
            | filterdiff \
                -x "a/src/jtag/drivers/libjaylink/*" \
                -x "a/tools/git2cl/*" \
                -x "a/.github/*" \
            | ./tools/scripts/checkpatch.pl --no-signoff -
