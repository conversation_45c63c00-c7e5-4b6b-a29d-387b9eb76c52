/* SPDX-License-Identifier: GPL-2.0-or-later */

/*
 * Copyright (C) 2021 by Nuvoton Technology Corporation
 * <PERSON><PERSON> <<EMAIL>>
 * Wealian Liao <<EMAIL>>
 */

#ifndef OPENOCD_LOADERS_FLASH_NPCX_NPCX_FLASH_CONFIG_H
#define OPENOCD_LOADERS_FLASH_NPCX_NPCX_FLASH_CONFIG_H

#define NPCX_FLASH_ABORT_TIMEOUT 0xFFFFFF

/* NPCX chip information */
#define NPCX_FLASH_WRITE_SIZE 256L   /* One page size for write */
#define NPCX_FLASH_ERASE_SIZE 0x1000

/* NPCX flash loader information */
#define NPCX_FLASH_LOADER_WORKING_ADDR 0x200C0000
#define NPCX_FLASH_LOADER_PARAMS_ADDR NPCX_FLASH_LOADER_WORKING_ADDR
#define NPCX_FLASH_LOADER_PARAMS_SIZE 16
#define NPCX_FLASH_LOADER_BUFFER_ADDR (NPCX_FLASH_LOADER_PARAMS_ADDR + NPCX_FLASH_LOADER_PARAMS_SIZE)
#define NPCX_FLASH_LOADER_BUFFER_SIZE NPCX_FLASH_ERASE_SIZE
#define NPCX_FLASH_LOADER_PROGRAM_ADDR (NPCX_FLASH_LOADER_BUFFER_ADDR + NPCX_FLASH_LOADER_BUFFER_SIZE)
#define NPCX_FLASH_LOADER_PROGRAM_SIZE 0x1000

/* Stack size in byte. 4 byte size alignment */
#define NPCX_FLASH_LOADER_STACK_SIZE 400


#endif /* OPENOCD_LOADERS_FLASH_NPCX_NPCX_FLASH_CONFIG_H */
