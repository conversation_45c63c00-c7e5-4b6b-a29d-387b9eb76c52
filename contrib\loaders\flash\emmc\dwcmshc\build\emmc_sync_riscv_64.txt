
D:/work/2022/al9000/sw/debugger/al_openocd/contrib/loaders/flash/emmc/dwcmshc/build/emmc_sync_riscv_64.elf:     file format elf64-littleriscv


Disassembly of section .text:

0000000061000000 <_start>:
    61000000:	00000117          	auipc	sp,0x0
    61000004:	40810113          	addi	sp,sp,1032 # 61000408 <block_addr_shift.0>
    61000008:	148000ef          	jal	ra,61000150 <emmc_dwcmshc>
    6100000c:	00100073          	ebreak

0000000061000010 <emmc_wait_fifo>:
    61000010:	00050713          	mv	a4,a0
    61000014:	00072683          	lw	a3,0(a4)
    61000018:	00472783          	lw	a5,4(a4)
    6100001c:	0006869b          	sext.w	a3,a3
    61000020:	0007851b          	sext.w	a0,a5
    61000024:	fea688e3          	beq	a3,a0,61000014 <emmc_wait_fifo+0x4>
    61000028:	00008067          	ret

000000006100002c <emmc_poll_int>:
    6100002c:	00100693          	li	a3,1
    61000030:	00b696bb          	sllw	a3,a3,a1
    61000034:	03052703          	lw	a4,48(a0)
    61000038:	0007071b          	sext.w	a4,a4
    6100003c:	00e6f7b3          	and	a5,a3,a4
    61000040:	00070613          	mv	a2,a4
    61000044:	fe0788e3          	beqz	a5,61000034 <emmc_poll_int+0x8>
    61000048:	0107571b          	srliw	a4,a4,0x10
    6100004c:	fe0714e3          	bnez	a4,61000034 <emmc_poll_int+0x8>
    61000050:	00100793          	li	a5,1
    61000054:	00b797bb          	sllw	a5,a5,a1
    61000058:	00c7e7b3          	or	a5,a5,a2
    6100005c:	0007879b          	sext.w	a5,a5
    61000060:	02f52823          	sw	a5,48(a0)
    61000064:	00000513          	li	a0,0
    61000068:	00008067          	ret

000000006100006c <emmc_write_block>:
    6100006c:	183a07b7          	lui	a5,0x183a0
    61000070:	00b52423          	sw	a1,8(a0)
    61000074:	08278793          	addi	a5,a5,130 # 183a0082 <_start-0x48c5ff7e>
    61000078:	00f52623          	sw	a5,12(a0)
    6100007c:	03052783          	lw	a5,48(a0)
    61000080:	0007871b          	sext.w	a4,a5
    61000084:	0107f793          	andi	a5,a5,16
    61000088:	fe078ae3          	beqz	a5,6100007c <emmc_write_block+0x10>
    6100008c:	0107579b          	srliw	a5,a4,0x10
    61000090:	fe0796e3          	bnez	a5,6100007c <emmc_write_block+0x10>
    61000094:	20060793          	addi	a5,a2,512
    61000098:	00062703          	lw	a4,0(a2)
    6100009c:	00460613          	addi	a2,a2,4
    610000a0:	02e52023          	sw	a4,32(a0)
    610000a4:	fec79ae3          	bne	a5,a2,61000098 <emmc_write_block+0x2c>
    610000a8:	03052783          	lw	a5,48(a0)
    610000ac:	0007879b          	sext.w	a5,a5
    610000b0:	0027f693          	andi	a3,a5,2
    610000b4:	00078713          	mv	a4,a5
    610000b8:	fe0688e3          	beqz	a3,610000a8 <emmc_write_block+0x3c>
    610000bc:	0107d79b          	srliw	a5,a5,0x10
    610000c0:	fe0794e3          	bnez	a5,610000a8 <emmc_write_block+0x3c>
    610000c4:	00276793          	ori	a5,a4,2
    610000c8:	0007879b          	sext.w	a5,a5
    610000cc:	02f52823          	sw	a5,48(a0)
    610000d0:	00008067          	ret

00000000610000d4 <emmc_read_block>:
    610000d4:	fe010113          	addi	sp,sp,-32
    610000d8:	00813823          	sd	s0,16(sp)
    610000dc:	00913423          	sd	s1,8(sp)
    610000e0:	01213023          	sd	s2,0(sp)
    610000e4:	00113c23          	sd	ra,24(sp)
    610000e8:	113a07b7          	lui	a5,0x113a0
    610000ec:	09278793          	addi	a5,a5,146 # 113a0092 <_start-0x4fc5ff6e>
    610000f0:	00c52423          	sw	a2,8(a0)
    610000f4:	00f52623          	sw	a5,12(a0)
    610000f8:	00058913          	mv	s2,a1
    610000fc:	00500593          	li	a1,5
    61000100:	00050413          	mv	s0,a0
    61000104:	00068493          	mv	s1,a3
    61000108:	f25ff0ef          	jal	ra,6100002c <emmc_poll_int>
    6100010c:	00000793          	li	a5,0
    61000110:	0007871b          	sext.w	a4,a5
    61000114:	02976263          	bltu	a4,s1,61000138 <emmc_read_block+0x64>
    61000118:	00040513          	mv	a0,s0
    6100011c:	01013403          	ld	s0,16(sp)
    61000120:	01813083          	ld	ra,24(sp)
    61000124:	00813483          	ld	s1,8(sp)
    61000128:	00013903          	ld	s2,0(sp)
    6100012c:	00100593          	li	a1,1
    61000130:	02010113          	addi	sp,sp,32
    61000134:	ef9ff06f          	j	6100002c <emmc_poll_int>
    61000138:	02042683          	lw	a3,32(s0)
    6100013c:	00279713          	slli	a4,a5,0x2
    61000140:	00e90733          	add	a4,s2,a4
    61000144:	00d72023          	sw	a3,0(a4)
    61000148:	00178793          	addi	a5,a5,1
    6100014c:	fc5ff06f          	j	61000110 <emmc_read_block+0x3c>

0000000061000150 <emmc_dwcmshc>:
    61000150:	fc010113          	addi	sp,sp,-64
    61000154:	02913423          	sd	s1,40(sp)
    61000158:	00070493          	mv	s1,a4
    6100015c:	00000717          	auipc	a4,0x0
    61000160:	2ac70713          	addi	a4,a4,684 # 61000408 <block_addr_shift.0>
    61000164:	00072783          	lw	a5,0(a4)
    61000168:	02813823          	sd	s0,48(sp)
    6100016c:	03213023          	sd	s2,32(sp)
    61000170:	01313c23          	sd	s3,24(sp)
    61000174:	01413823          	sd	s4,16(sp)
    61000178:	02113c23          	sd	ra,56(sp)
    6100017c:	01513423          	sd	s5,8(sp)
    61000180:	00050a13          	mv	s4,a0
    61000184:	00058913          	mv	s2,a1
    61000188:	00060413          	mv	s0,a2
    6100018c:	00068993          	mv	s3,a3
    61000190:	02079063          	bnez	a5,610001b0 <emmc_dwcmshc+0x60>
    61000194:	02000613          	li	a2,32
    61000198:	00f956bb          	srlw	a3,s2,a5
    6100019c:	0016f693          	andi	a3,a3,1
    610001a0:	00069663          	bnez	a3,610001ac <emmc_dwcmshc+0x5c>
    610001a4:	0017879b          	addiw	a5,a5,1
    610001a8:	fec798e3          	bne	a5,a2,61000198 <emmc_dwcmshc+0x48>
    610001ac:	00f72023          	sw	a5,0(a4)
    610001b0:	00072583          	lw	a1,0(a4)
    610001b4:	02091a93          	slli	s5,s2,0x20
    610001b8:	020ada93          	srli	s5,s5,0x20
    610001bc:	00b4543b          	srlw	s0,s0,a1
    610001c0:	02904463          	bgtz	s1,610001e8 <emmc_dwcmshc+0x98>
    610001c4:	03813083          	ld	ra,56(sp)
    610001c8:	03013403          	ld	s0,48(sp)
    610001cc:	02813483          	ld	s1,40(sp)
    610001d0:	02013903          	ld	s2,32(sp)
    610001d4:	01813983          	ld	s3,24(sp)
    610001d8:	01013a03          	ld	s4,16(sp)
    610001dc:	00813a83          	ld	s5,8(sp)
    610001e0:	04010113          	addi	sp,sp,64
    610001e4:	00008067          	ret
    610001e8:	00098613          	mv	a2,s3
    610001ec:	00040593          	mv	a1,s0
    610001f0:	000a0513          	mv	a0,s4
    610001f4:	e79ff0ef          	jal	ra,6100006c <emmc_write_block>
    610001f8:	412484bb          	subw	s1,s1,s2
    610001fc:	0014041b          	addiw	s0,s0,1
    61000200:	015989b3          	add	s3,s3,s5
    61000204:	fbdff06f          	j	610001c0 <emmc_dwcmshc+0x70>

Disassembly of section .data:

0000000061000208 <stack>:
    61000208:	5309                	li	t1,-30
    6100020a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100020e:	0000                	unimp
    61000210:	5309                	li	t1,-30
    61000212:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000216:	0000                	unimp
    61000218:	5309                	li	t1,-30
    6100021a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100021e:	0000                	unimp
    61000220:	5309                	li	t1,-30
    61000222:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000226:	0000                	unimp
    61000228:	5309                	li	t1,-30
    6100022a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100022e:	0000                	unimp
    61000230:	5309                	li	t1,-30
    61000232:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000236:	0000                	unimp
    61000238:	5309                	li	t1,-30
    6100023a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100023e:	0000                	unimp
    61000240:	5309                	li	t1,-30
    61000242:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000246:	0000                	unimp
    61000248:	5309                	li	t1,-30
    6100024a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100024e:	0000                	unimp
    61000250:	5309                	li	t1,-30
    61000252:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000256:	0000                	unimp
    61000258:	5309                	li	t1,-30
    6100025a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100025e:	0000                	unimp
    61000260:	5309                	li	t1,-30
    61000262:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000266:	0000                	unimp
    61000268:	5309                	li	t1,-30
    6100026a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100026e:	0000                	unimp
    61000270:	5309                	li	t1,-30
    61000272:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000276:	0000                	unimp
    61000278:	5309                	li	t1,-30
    6100027a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100027e:	0000                	unimp
    61000280:	5309                	li	t1,-30
    61000282:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000286:	0000                	unimp
    61000288:	5309                	li	t1,-30
    6100028a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100028e:	0000                	unimp
    61000290:	5309                	li	t1,-30
    61000292:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000296:	0000                	unimp
    61000298:	5309                	li	t1,-30
    6100029a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100029e:	0000                	unimp
    610002a0:	5309                	li	t1,-30
    610002a2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610002a6:	0000                	unimp
    610002a8:	5309                	li	t1,-30
    610002aa:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610002ae:	0000                	unimp
    610002b0:	5309                	li	t1,-30
    610002b2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610002b6:	0000                	unimp
    610002b8:	5309                	li	t1,-30
    610002ba:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610002be:	0000                	unimp
    610002c0:	5309                	li	t1,-30
    610002c2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610002c6:	0000                	unimp
    610002c8:	5309                	li	t1,-30
    610002ca:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610002ce:	0000                	unimp
    610002d0:	5309                	li	t1,-30
    610002d2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610002d6:	0000                	unimp
    610002d8:	5309                	li	t1,-30
    610002da:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610002de:	0000                	unimp
    610002e0:	5309                	li	t1,-30
    610002e2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610002e6:	0000                	unimp
    610002e8:	5309                	li	t1,-30
    610002ea:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610002ee:	0000                	unimp
    610002f0:	5309                	li	t1,-30
    610002f2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610002f6:	0000                	unimp
    610002f8:	5309                	li	t1,-30
    610002fa:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610002fe:	0000                	unimp
    61000300:	5309                	li	t1,-30
    61000302:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000306:	0000                	unimp
    61000308:	5309                	li	t1,-30
    6100030a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100030e:	0000                	unimp
    61000310:	5309                	li	t1,-30
    61000312:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000316:	0000                	unimp
    61000318:	5309                	li	t1,-30
    6100031a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100031e:	0000                	unimp
    61000320:	5309                	li	t1,-30
    61000322:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000326:	0000                	unimp
    61000328:	5309                	li	t1,-30
    6100032a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100032e:	0000                	unimp
    61000330:	5309                	li	t1,-30
    61000332:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000336:	0000                	unimp
    61000338:	5309                	li	t1,-30
    6100033a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100033e:	0000                	unimp
    61000340:	5309                	li	t1,-30
    61000342:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000346:	0000                	unimp
    61000348:	5309                	li	t1,-30
    6100034a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100034e:	0000                	unimp
    61000350:	5309                	li	t1,-30
    61000352:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000356:	0000                	unimp
    61000358:	5309                	li	t1,-30
    6100035a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100035e:	0000                	unimp
    61000360:	5309                	li	t1,-30
    61000362:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000366:	0000                	unimp
    61000368:	5309                	li	t1,-30
    6100036a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100036e:	0000                	unimp
    61000370:	5309                	li	t1,-30
    61000372:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000376:	0000                	unimp
    61000378:	5309                	li	t1,-30
    6100037a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100037e:	0000                	unimp
    61000380:	5309                	li	t1,-30
    61000382:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000386:	0000                	unimp
    61000388:	5309                	li	t1,-30
    6100038a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100038e:	0000                	unimp
    61000390:	5309                	li	t1,-30
    61000392:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000396:	0000                	unimp
    61000398:	5309                	li	t1,-30
    6100039a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100039e:	0000                	unimp
    610003a0:	5309                	li	t1,-30
    610003a2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610003a6:	0000                	unimp
    610003a8:	5309                	li	t1,-30
    610003aa:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610003ae:	0000                	unimp
    610003b0:	5309                	li	t1,-30
    610003b2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610003b6:	0000                	unimp
    610003b8:	5309                	li	t1,-30
    610003ba:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610003be:	0000                	unimp
    610003c0:	5309                	li	t1,-30
    610003c2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610003c6:	0000                	unimp
    610003c8:	5309                	li	t1,-30
    610003ca:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610003ce:	0000                	unimp
    610003d0:	5309                	li	t1,-30
    610003d2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610003d6:	0000                	unimp
    610003d8:	5309                	li	t1,-30
    610003da:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610003de:	0000                	unimp
    610003e0:	5309                	li	t1,-30
    610003e2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610003e6:	0000                	unimp
    610003e8:	5309                	li	t1,-30
    610003ea:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610003ee:	0000                	unimp
    610003f0:	5309                	li	t1,-30
    610003f2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610003f6:	0000                	unimp
    610003f8:	5309                	li	t1,-30
    610003fa:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610003fe:	0000                	unimp
    61000400:	5309                	li	t1,-30
    61000402:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
	...

Disassembly of section .bss:

0000000061000408 <block_addr_shift.0>:
    61000408:	0000                	unimp
	...

Disassembly of section .riscv.attributes:

0000000000000000 <.riscv.attributes>:
   0:	1b41                	addi	s6,s6,-16
   2:	0000                	unimp
   4:	7200                	ld	s0,32(a2)
   6:	7369                	lui	t1,0xffffa
   8:	01007663          	bgeu	zero,a6,14 <_start-0x60ffffec>
   c:	0011                	c.nop	4
   e:	0000                	unimp
  10:	1004                	addi	s1,sp,32
  12:	7205                	lui	tp,0xfffe1
  14:	3676                	fld	fa2,376(sp)
  16:	6934                	ld	a3,80(a0)
  18:	7032                	0x7032
  1a:	0030                	addi	a2,sp,8

Disassembly of section .comment:

0000000000000000 <.comment>:
   0:	3a434347          	fmsub.d	ft6,ft6,ft4,ft7,rmm
   4:	2820                	fld	fs0,80(s0)
   6:	29554e47          	fmsub.s	ft8,fa0,fs5,ft5,rmm
   a:	3120                	fld	fs0,96(a0)
   c:	2e30                	fld	fa2,88(a2)
   e:	2e32                	fld	ft8,264(sp)
  10:	0030                	addi	a2,sp,8

Disassembly of section .debug_line:

0000000000000000 <.debug_line>:
   0:	000000a3          	sb	zero,1(zero) # 1 <_start-0x60ffffff>
   4:	007d0003          	lb	zero,7(s10)
   8:	0000                	unimp
   a:	0101                	addi	sp,sp,0
   c:	000d0efb          	dkhmx8	t4,s10,zero
  10:	0101                	addi	sp,sp,0
  12:	0101                	addi	sp,sp,0
  14:	0000                	unimp
  16:	0100                	addi	s0,sp,128
  18:	0000                	unimp
  1a:	4401                	li	s0,0
  1c:	2f3a                	fld	ft10,392(sp)
  1e:	6b726f77          	0x6b726f77
  22:	3230322f          	0x3230322f
  26:	2f32                	fld	ft10,264(sp)
  28:	6c61                	lui	s8,0x18
  2a:	3039                	0x3039
  2c:	3030                	fld	fa2,96(s0)
  2e:	2f77732f          	0x2f77732f
  32:	6564                	ld	s1,200(a0)
  34:	7562                	ld	a0,56(sp)
  36:	72656767          	0x72656767
  3a:	5f6c612f          	0x5f6c612f
  3e:	6e65706f          	j	57724 <_start-0x60fa88dc>
  42:	2f64636f          	jal	t1,46338 <_start-0x60fb9cc8>
  46:	746e6f63          	bltu	t3,t1,7a4 <_start-0x60fff85c>
  4a:	6972                	ld	s2,280(sp)
  4c:	2f62                	fld	ft10,24(sp)
  4e:	6f6c                	ld	a1,216(a4)
  50:	6461                	lui	s0,0x18
  52:	7265                	lui	tp,0xffff9
  54:	6c662f73          	csrrs	t5,0x6c6,a2
  58:	7361                	lui	t1,0xffff8
  5a:	2f68                	fld	fa0,216(a4)
  5c:	6d65                	lui	s10,0x19
  5e:	636d                	lui	t1,0x1b
  60:	6377642f          	0x6377642f
  64:	736d                	lui	t1,0xffffb
  66:	6368                	ld	a0,192(a4)
  68:	6372732f          	0x6372732f
  6c:	6f6f622f          	0x6f6f622f
  70:	2f74                	fld	fa3,216(a4)
  72:	6972                	ld	s2,280(sp)
  74:	00766373          	csrrsi	t1,0x7,12
  78:	7700                	ld	s0,40(a4)
  7a:	6172                	ld	sp,280(sp)
  7c:	7070                	ld	a2,224(s0)
  7e:	7265                	lui	tp,0xffff9
  80:	532e                	lw	t1,232(sp)
  82:	0100                	addi	s0,sp,128
  84:	0000                	unimp
  86:	0000                	unimp
  88:	0209                	addi	tp,tp,2
  8a:	0000                	unimp
  8c:	6100                	ld	s0,0(a0)
  8e:	0000                	unimp
  90:	0000                	unimp
  92:	03010d03          	lb	s10,48(sp)
  96:	0901                	addi	s2,s2,0
  98:	0008                	0x8
  9a:	0301                	addi	t1,t1,0
  9c:	0901                	addi	s2,s2,0
  9e:	0004                	0x4
  a0:	0901                	addi	s2,s2,0
  a2:	0004                	0x4
  a4:	0100                	addi	s0,sp,128
  a6:	7f01                	lui	t5,0xfffe0
  a8:	0004                	0x4
  aa:	0300                	addi	s0,sp,384
  ac:	3f00                	fld	fs0,56(a4)
  ae:	0001                	nop
  b0:	0100                	addi	s0,sp,128
  b2:	fb01                	bnez	a4,ffffffffffffffc2 <block_addr_shift.0+0xffffffff9efffbba>
  b4:	0d0e                	slli	s10,s10,0x3
  b6:	0100                	addi	s0,sp,128
  b8:	0101                	addi	sp,sp,0
  ba:	0001                	nop
  bc:	0000                	unimp
  be:	0001                	nop
  c0:	0100                	addi	s0,sp,128
  c2:	3a44                	fld	fs1,176(a2)
  c4:	726f772f          	0x726f772f
  c8:	30322f6b          	0x30322f6b
  cc:	3232                	fld	ft4,296(sp)
  ce:	396c612f          	0x396c612f
  d2:	3030                	fld	fa2,96(s0)
  d4:	2f30                	fld	fa2,88(a4)
  d6:	642f7773          	csrrci	a4,0x642,30
  da:	6265                	lui	tp,0x19
  dc:	6775                	lui	a4,0x1d
  de:	2f726567          	0x2f726567
  e2:	6c61                	lui	s8,0x18
  e4:	6f5f 6570 6f6e      	0x6f6e65706f5f
  ea:	632f6463          	bltu	t5,s2,712 <_start-0x60fff8ee>
  ee:	72746e6f          	jal	t3,47014 <_start-0x60fb8fec>
  f2:	6269                	lui	tp,0x1a
  f4:	616f6c2f          	0x616f6c2f
  f8:	6564                	ld	s1,200(a0)
  fa:	7372                	ld	t1,312(sp)
  fc:	616c662f          	0x616c662f
 100:	652f6873          	csrrsi	a6,0x652,30
 104:	6d6d                	lui	s10,0x1b
 106:	77642f63          	0x77642f63
 10a:	68736d63          	bltu	t1,t2,7a4 <_start-0x60fff85c>
 10e:	72732f63          	0x72732f63
 112:	3a640063          	beq	s0,t1,4b2 <_start-0x60fffb4e>
 116:	775c                	ld	a5,168(a4)
 118:	5c6b726f          	jal	tp,b76de <_start-0x60f48922>
 11c:	3032                	fld	ft0,296(sp)
 11e:	3232                	fld	ft4,296(sp)
 120:	615c                	ld	a5,128(a0)
 122:	396c                	fld	fa1,240(a0)
 124:	3030                	fld	fa2,96(s0)
 126:	5c30                	lw	a2,120(s0)
 128:	735c7773          	csrrci	a4,0x735,24
 12c:	6b64                	ld	s1,208(a4)
 12e:	735c                	ld	a5,160(a4)
 130:	612d636f          	jal	t1,d6742 <_start-0x60f298be>
 134:	7570                	ld	a2,232(a0)
 136:	732d                	lui	t1,0xfffeb
 138:	6b64                	ld	s1,208(a4)
 13a:	745c                	ld	a5,168(s0)
 13c:	736c6f6f          	jal	t5,c6872 <_start-0x60f3978e>
 140:	775c                	ld	a5,168(a4)
 142:	6e69                	lui	t3,0x1a
 144:	725c                	ld	a5,160(a2)
 146:	7369                	lui	t1,0xffffa
 148:	725c7663          	bgeu	s8,t0,874 <_start-0x60fff78c>
 14c:	7369                	lui	t1,0xffffa
 14e:	6e2d7663          	bgeu	s10,sp,83a <_start-0x60fff7c6>
 152:	6375                	lui	t1,0x1d
 154:	656c                	ld	a1,200(a0)
 156:	2d69                	addiw	s10,s10,26
 158:	6c65                	lui	s8,0x19
 15a:	5c66                	lw	s8,120(sp)
 15c:	6e69                	lui	t3,0x1a
 15e:	64756c63          	bltu	a0,t2,7b6 <_start-0x60fff84a>
 162:	5c65                	li	s8,-7
 164:	616d                	addi	sp,sp,240
 166:	6e696863          	bltu	s2,t1,856 <_start-0x60fff7aa>
 16a:	0065                	c.nop	25
 16c:	3a64                	fld	fs1,240(a2)
 16e:	775c                	ld	a5,168(a4)
 170:	5c6b726f          	jal	tp,b7736 <_start-0x60f488ca>
 174:	3032                	fld	ft0,296(sp)
 176:	3232                	fld	ft4,296(sp)
 178:	615c                	ld	a5,128(a0)
 17a:	396c                	fld	fa1,240(a0)
 17c:	3030                	fld	fa2,96(s0)
 17e:	5c30                	lw	a2,120(s0)
 180:	735c7773          	csrrci	a4,0x735,24
 184:	6b64                	ld	s1,208(a4)
 186:	735c                	ld	a5,160(a4)
 188:	612d636f          	jal	t1,d679a <_start-0x60f29866>
 18c:	7570                	ld	a2,232(a0)
 18e:	732d                	lui	t1,0xfffeb
 190:	6b64                	ld	s1,208(a4)
 192:	745c                	ld	a5,168(s0)
 194:	736c6f6f          	jal	t5,c68ca <_start-0x60f39736>
 198:	775c                	ld	a5,168(a4)
 19a:	6e69                	lui	t3,0x1a
 19c:	725c                	ld	a5,160(a2)
 19e:	7369                	lui	t1,0xffffa
 1a0:	725c7663          	bgeu	s8,t0,8cc <_start-0x60fff734>
 1a4:	7369                	lui	t1,0xffffa
 1a6:	6e2d7663          	bgeu	s10,sp,892 <_start-0x60fff76e>
 1aa:	6375                	lui	t1,0x1d
 1ac:	656c                	ld	a1,200(a0)
 1ae:	2d69                	addiw	s10,s10,26
 1b0:	6c65                	lui	s8,0x19
 1b2:	5c66                	lw	s8,120(sp)
 1b4:	6e69                	lui	t3,0x1a
 1b6:	64756c63          	bltu	a0,t2,80e <_start-0x60fff7f2>
 1ba:	5c65                	li	s8,-7
 1bc:	00737973          	csrrci	s2,0x7,6
 1c0:	6400                	ld	s0,8(s0)
 1c2:	736d6377          	0x736d6377
 1c6:	6368                	ld	a0,192(a4)
 1c8:	632e                	ld	t1,200(sp)
 1ca:	0100                	addi	s0,sp,128
 1cc:	0000                	unimp
 1ce:	645f 6665 7561      	0x75616665645f
 1d4:	746c                	ld	a1,232(s0)
 1d6:	745f 7079 7365      	0x73657079745f
 1dc:	682e                	ld	a6,200(sp)
 1de:	0200                	addi	s0,sp,256
 1e0:	0000                	unimp
 1e2:	735f 6474 6e69      	0x6e696474735f
 1e8:	2e74                	fld	fa3,216(a2)
 1ea:	0068                	addi	a0,sp,12
 1ec:	00000003          	lb	zero,0(zero) # 0 <_start-0x61000000>
 1f0:	0105                	addi	sp,sp,1
 1f2:	0900                	addi	s0,sp,144
 1f4:	1002                	c.slli	zero,0x20
 1f6:	0000                	unimp
 1f8:	0061                	c.nop	24
 1fa:	0000                	unimp
 1fc:	1500                	addi	s0,sp,672
 1fe:	0505                	addi	a0,a0,1
 200:	04090103          	lb	sp,64(s2)
 204:	0100                	addi	s0,sp,128
 206:	00090103          	lb	sp,0(s2)
 20a:	0100                	addi	s0,sp,128
 20c:	0a05                	addi	s4,s4,1
 20e:	00090003          	lb	zero,0(s2)
 212:	0100                	addi	s0,sp,128
 214:	0905                	addi	s2,s2,1
 216:	00090203          	lb	tp,0(s2)
 21a:	0100                	addi	s0,sp,128
 21c:	0c05                	addi	s8,s8,1
 21e:	0306                	slli	t1,t1,0x1
 220:	0900                	addi	s0,sp,144
 222:	0000                	unimp
 224:	0301                	addi	t1,t1,0
 226:	0901                	addi	s2,s2,0
 228:	0004                	0x4
 22a:	0301                	addi	t1,t1,0
 22c:	0004097f          	radd16	s2,s0,zero
 230:	0501                	addi	a0,a0,0
 232:	0609                	addi	a2,a2,2
 234:	04090103          	lb	sp,64(s2)
 238:	0100                	addi	s0,sp,128
 23a:	0c05                	addi	s8,s8,1
 23c:	0306                	slli	t1,t1,0x1
 23e:	0900                	addi	s0,sp,144
 240:	0000                	unimp
 242:	0501                	addi	a0,a0,0
 244:	060a                	slli	a2,a2,0x2
 246:	04097d03          	0x4097d03
 24a:	0100                	addi	s0,sp,128
 24c:	0505                	addi	a0,a0,1
 24e:	04090503          	lb	a0,64(s2)
 252:	0100                	addi	s0,sp,128
 254:	0105                	addi	sp,sp,1
 256:	0306                	slli	t1,t1,0x1
 258:	0901                	addi	s2,s2,0
 25a:	0000                	unimp
 25c:	0601                	addi	a2,a2,0
 25e:	04090303          	lb	t1,64(s2)
 262:	0100                	addi	s0,sp,128
 264:	0505                	addi	a0,a0,1
 266:	00090103          	lb	sp,0(s2)
 26a:	0100                	addi	s0,sp,128
 26c:	00090103          	lb	sp,0(s2)
 270:	0100                	addi	s0,sp,128
 272:	08090103          	lb	sp,128(s2)
 276:	0100                	addi	s0,sp,128
 278:	0905                	addi	s2,s2,1
 27a:	00090203          	lb	tp,0(s2)
 27e:	0100                	addi	s0,sp,128
 280:	1105                	addi	sp,sp,-31
 282:	0306                	slli	t1,t1,0x1
 284:	0900                	addi	s0,sp,144
 286:	0000                	unimp
 288:	0501                	addi	a0,a0,0
 28a:	0609                	addi	a2,a2,2
 28c:	08090103          	lb	sp,128(s2)
 290:	0100                	addi	s0,sp,128
 292:	00090103          	lb	sp,0(s2)
 296:	0100                	addi	s0,sp,128
 298:	0b05                	addi	s6,s6,1
 29a:	0306                	slli	t1,t1,0x1
 29c:	0900                	addi	s0,sp,144
 29e:	0000                	unimp
 2a0:	0501                	addi	a0,a0,0
 2a2:	0016                	c.slli	zero,0x5
 2a4:	0402                	c.slli64	s0
 2a6:	0301                	addi	t1,t1,0
 2a8:	0900                	addi	s0,sp,144
 2aa:	000c                	0xc
 2ac:	0501                	addi	a0,a0,0
 2ae:	0605                	addi	a2,a2,1
 2b0:	08090303          	lb	t1,128(s2)
 2b4:	0100                	addi	s0,sp,128
 2b6:	1c05                	addi	s8,s8,-31
 2b8:	0306                	slli	t1,t1,0x1
 2ba:	0900                	addi	s0,sp,144
 2bc:	0000                	unimp
 2be:	0501                	addi	a0,a0,0
 2c0:	0900030f          	0x900030f
 2c4:	0008                	0x8
 2c6:	0501                	addi	a0,a0,0
 2c8:	0605                	addi	a2,a2,1
 2ca:	08090103          	lb	sp,128(s2)
 2ce:	0100                	addi	s0,sp,128
 2d0:	00090003          	lb	zero,0(s2)
 2d4:	0100                	addi	s0,sp,128
 2d6:	04090003          	lb	zero,64(s2)
 2da:	0100                	addi	s0,sp,128
 2dc:	00090103          	lb	sp,0(s2)
 2e0:	0100                	addi	s0,sp,128
 2e2:	0105                	addi	sp,sp,1
 2e4:	0306                	slli	t1,t1,0x1
 2e6:	0901                	addi	s2,s2,0
 2e8:	0000                	unimp
 2ea:	0601                	addi	a2,a2,0
 2ec:	08090303          	lb	t1,128(s2)
 2f0:	0100                	addi	s0,sp,128
 2f2:	0505                	addi	a0,a0,1
 2f4:	00090103          	lb	sp,0(s2)
 2f8:	0100                	addi	s0,sp,128
 2fa:	00090103          	lb	sp,0(s2)
 2fe:	0100                	addi	s0,sp,128
 300:	00090203          	lb	tp,0(s2)
 304:	0100                	addi	s0,sp,128
 306:	00090003          	lb	zero,0(s2)
 30a:	0100                	addi	s0,sp,128
 30c:	0306                	slli	t1,t1,0x1
 30e:	0901                	addi	s2,s2,0
 310:	0000                	unimp
 312:	0301                	addi	t1,t1,0
 314:	0004097f          	radd16	s2,s0,zero
 318:	0601                	addi	a2,a2,0
 31a:	04090003          	lb	zero,64(s2)
 31e:	0100                	addi	s0,sp,128
 320:	00090103          	lb	sp,0(s2)
 324:	0100                	addi	s0,sp,128
 326:	00090003          	lb	zero,0(s2)
 32a:	0100                	addi	s0,sp,128
 32c:	0200                	addi	s0,sp,256
 32e:	0104                	addi	s1,sp,128
 330:	08090003          	lb	zero,128(s2)
 334:	0100                	addi	s0,sp,128
 336:	0200                	addi	s0,sp,256
 338:	0104                	addi	s1,sp,128
 33a:	00090103          	lb	sp,0(s2)
 33e:	0100                	addi	s0,sp,128
 340:	0905                	addi	s2,s2,1
 342:	0200                	addi	s0,sp,256
 344:	0104                	addi	s1,sp,128
 346:	00090203          	lb	tp,0(s2)
 34a:	0100                	addi	s0,sp,128
 34c:	1105                	addi	sp,sp,-31
 34e:	0200                	addi	s0,sp,256
 350:	0104                	addi	s1,sp,128
 352:	0306                	slli	t1,t1,0x1
 354:	0900                	addi	s0,sp,144
 356:	0000                	unimp
 358:	0501                	addi	a0,a0,0
 35a:	0009                	c.nop	2
 35c:	0402                	c.slli64	s0
 35e:	0601                	addi	a2,a2,0
 360:	08090103          	lb	sp,128(s2)
 364:	0100                	addi	s0,sp,128
 366:	0200                	addi	s0,sp,256
 368:	0104                	addi	s1,sp,128
 36a:	00090103          	lb	sp,0(s2)
 36e:	0100                	addi	s0,sp,128
 370:	0b05                	addi	s6,s6,1
 372:	0200                	addi	s0,sp,256
 374:	0104                	addi	s1,sp,128
 376:	0306                	slli	t1,t1,0x1
 378:	0900                	addi	s0,sp,144
 37a:	0000                	unimp
 37c:	0501                	addi	a0,a0,0
 37e:	0016                	c.slli	zero,0x5
 380:	0402                	c.slli64	s0
 382:	0301                	addi	t1,t1,0
 384:	0900                	addi	s0,sp,144
 386:	0008                	0x8
 388:	0501                	addi	a0,a0,0
 38a:	0009                	c.nop	2
 38c:	0402                	c.slli64	s0
 38e:	04030603          	lb	a2,64(t1) # 1d040 <_start-0x60fe2fc0>
 392:	0c09                	addi	s8,s8,2
 394:	0100                	addi	s0,sp,128
 396:	0200                	addi	s0,sp,256
 398:	0304                	addi	s1,sp,384
 39a:	00090003          	lb	zero,0(s2)
 39e:	0100                	addi	s0,sp,128
 3a0:	0505                	addi	a0,a0,1
 3a2:	0200                	addi	s0,sp,256
 3a4:	0304                	addi	s1,sp,384
 3a6:	0306                	slli	t1,t1,0x1
 3a8:	0004097f          	radd16	s2,s0,zero
 3ac:	0501                	addi	a0,a0,0
 3ae:	0009                	c.nop	2
 3b0:	0402                	c.slli64	s0
 3b2:	09010303          	lb	t1,144(sp)
 3b6:	0004                	0x4
 3b8:	0001                	nop
 3ba:	0402                	c.slli64	s0
 3bc:	00030603          	lb	a2,0(t1)
 3c0:	0409                	addi	s0,s0,2
 3c2:	0100                	addi	s0,sp,128
 3c4:	2605                	addiw	a2,a2,1
 3c6:	0200                	addi	s0,sp,256
 3c8:	0304                	addi	s1,sp,384
 3ca:	00097f03          	0x97f03
 3ce:	0100                	addi	s0,sp,128
 3d0:	0e05                	addi	t3,t3,1
 3d2:	0200                	addi	s0,sp,256
 3d4:	0304                	addi	s1,sp,384
 3d6:	00090003          	lb	zero,0(s2)
 3da:	0100                	addi	s0,sp,128
 3dc:	0505                	addi	a0,a0,1
 3de:	0200                	addi	s0,sp,256
 3e0:	0304                	addi	s1,sp,384
 3e2:	0306                	slli	t1,t1,0x1
 3e4:	0900                	addi	s0,sp,144
 3e6:	0000                	unimp
 3e8:	0601                	addi	a2,a2,0
 3ea:	04090203          	lb	tp,64(s2)
 3ee:	0100                	addi	s0,sp,128
 3f0:	0905                	addi	s2,s2,1
 3f2:	00090203          	lb	tp,0(s2)
 3f6:	0100                	addi	s0,sp,128
 3f8:	1105                	addi	sp,sp,-31
 3fa:	0306                	slli	t1,t1,0x1
 3fc:	0900                	addi	s0,sp,144
 3fe:	0000                	unimp
 400:	0501                	addi	a0,a0,0
 402:	0609                	addi	a2,a2,2
 404:	08090103          	lb	sp,128(s2)
 408:	0100                	addi	s0,sp,128
 40a:	00090103          	lb	sp,0(s2)
 40e:	0100                	addi	s0,sp,128
 410:	0b05                	addi	s6,s6,1
 412:	0306                	slli	t1,t1,0x1
 414:	0900                	addi	s0,sp,144
 416:	0000                	unimp
 418:	0501                	addi	a0,a0,0
 41a:	0016                	c.slli	zero,0x5
 41c:	0402                	c.slli64	s0
 41e:	0301                	addi	t1,t1,0
 420:	0900                	addi	s0,sp,144
 422:	000c                	0xc
 424:	0501                	addi	a0,a0,0
 426:	0605                	addi	a2,a2,1
 428:	08090303          	lb	t1,128(s2)
 42c:	0100                	addi	s0,sp,128
 42e:	00090003          	lb	zero,0(s2)
 432:	0100                	addi	s0,sp,128
 434:	0c090003          	lb	zero,192(s2)
 438:	0100                	addi	s0,sp,128
 43a:	0105                	addi	sp,sp,1
 43c:	0306                	slli	t1,t1,0x1
 43e:	0902                	c.slli64	s2
 440:	0000                	unimp
 442:	0601                	addi	a2,a2,0
 444:	04090303          	lb	t1,64(s2)
 448:	0100                	addi	s0,sp,128
 44a:	0505                	addi	a0,a0,1
 44c:	00090103          	lb	sp,0(s2)
 450:	0100                	addi	s0,sp,128
 452:	00090203          	lb	tp,0(s2)
 456:	0100                	addi	s0,sp,128
 458:	00090003          	lb	zero,0(s2)
 45c:	0100                	addi	s0,sp,128
 45e:	0105                	addi	sp,sp,1
 460:	0306                	slli	t1,t1,0x1
 462:	097d                	addi	s2,s2,31
 464:	0000                	unimp
 466:	0501                	addi	a0,a0,0
 468:	0305                	addi	t1,t1,1
 46a:	0904                	addi	s1,sp,144
 46c:	0014                	0x14
 46e:	0301                	addi	t1,t1,0
 470:	0008097f          	radd16	s2,a6,zero
 474:	0601                	addi	a2,a2,0
 476:	04090003          	lb	zero,64(s2)
 47a:	0100                	addi	s0,sp,128
 47c:	00090103          	lb	sp,0(s2)
 480:	0100                	addi	s0,sp,128
 482:	00090003          	lb	zero,0(s2)
 486:	0100                	addi	s0,sp,128
 488:	04090003          	lb	zero,64(s2)
 48c:	0100                	addi	s0,sp,128
 48e:	00090103          	lb	sp,0(s2)
 492:	0100                	addi	s0,sp,128
 494:	0105                	addi	sp,sp,1
 496:	0306                	slli	t1,t1,0x1
 498:	0000097b          	dkhmx8	s2,zero,zero
 49c:	0501                	addi	a0,a0,0
 49e:	0305                	addi	t1,t1,1
 4a0:	0905                	addi	s2,s2,1
 4a2:	0004                	0x4
 4a4:	0501                	addi	a0,a0,0
 4a6:	0301                	addi	t1,t1,0
 4a8:	0004097b          	dkhmx8	s2,s0,zero
 4ac:	0501                	addi	a0,a0,0
 4ae:	0305                	addi	t1,t1,1
 4b0:	0905                	addi	s2,s2,1
 4b2:	0008                	0x8
 4b4:	0601                	addi	a2,a2,0
 4b6:	04090203          	lb	tp,64(s2)
 4ba:	0100                	addi	s0,sp,128
 4bc:	0e05                	addi	t3,t3,1
 4be:	0200                	addi	s0,sp,256
 4c0:	0104                	addi	s1,sp,128
 4c2:	04090003          	lb	zero,64(s2)
 4c6:	0100                	addi	s0,sp,128
 4c8:	0505                	addi	a0,a0,1
 4ca:	0200                	addi	s0,sp,256
 4cc:	0104                	addi	s1,sp,128
 4ce:	0306                	slli	t1,t1,0x1
 4d0:	0900                	addi	s0,sp,144
 4d2:	0000                	unimp
 4d4:	0601                	addi	a2,a2,0
 4d6:	08090203          	lb	tp,128(s2)
 4da:	0100                	addi	s0,sp,128
 4dc:	0105                	addi	sp,sp,1
 4de:	0306                	slli	t1,t1,0x1
 4e0:	0901                	addi	s2,s2,0
 4e2:	0004                	0x4
 4e4:	0501                	addi	a0,a0,0
 4e6:	0305                	addi	t1,t1,1
 4e8:	0010097f          	radd16	s2,zero,ra
 4ec:	0501                	addi	a0,a0,0
 4ee:	0301                	addi	t1,t1,0
 4f0:	0901                	addi	s2,s2,0
 4f2:	0004                	0x4
 4f4:	0501                	addi	a0,a0,0
 4f6:	0305                	addi	t1,t1,1
 4f8:	0004097f          	radd16	s2,s0,zero
 4fc:	0501                	addi	a0,a0,0
 4fe:	0009                	c.nop	2
 500:	0402                	c.slli64	s0
 502:	7f030603          	lb	a2,2032(t1)
 506:	0409                	addi	s0,s0,2
 508:	0100                	addi	s0,sp,128
 50a:	1705                	addi	a4,a4,-31
 50c:	0200                	addi	s0,sp,256
 50e:	0304                	addi	s1,sp,384
 510:	0306                	slli	t1,t1,0x1
 512:	0900                	addi	s0,sp,144
 514:	0000                	unimp
 516:	0501                	addi	a0,a0,0
 518:	001c                	0x1c
 51a:	0402                	c.slli64	s0
 51c:	7f030603          	lb	a2,2032(t1)
 520:	1009                	c.nop	-30
 522:	0100                	addi	s0,sp,128
 524:	0809                	addi	a6,a6,2
 526:	0000                	unimp
 528:	0101                	addi	sp,sp,0
 52a:	027c                	addi	a5,sp,268
 52c:	0000                	unimp
 52e:	01510003          	lb	zero,21(sp)
 532:	0000                	unimp
 534:	0101                	addi	sp,sp,0
 536:	000d0efb          	dkhmx8	t4,s10,zero
 53a:	0101                	addi	sp,sp,0
 53c:	0101                	addi	sp,sp,0
 53e:	0000                	unimp
 540:	0100                	addi	s0,sp,128
 542:	0000                	unimp
 544:	4401                	li	s0,0
 546:	2f3a                	fld	ft10,392(sp)
 548:	6b726f77          	0x6b726f77
 54c:	3230322f          	0x3230322f
 550:	2f32                	fld	ft10,264(sp)
 552:	6c61                	lui	s8,0x18
 554:	3039                	0x3039
 556:	3030                	fld	fa2,96(s0)
 558:	2f77732f          	0x2f77732f
 55c:	6564                	ld	s1,200(a0)
 55e:	7562                	ld	a0,56(sp)
 560:	72656767          	0x72656767
 564:	5f6c612f          	0x5f6c612f
 568:	6e65706f          	j	57c4e <_start-0x60fa83b2>
 56c:	2f64636f          	jal	t1,46862 <_start-0x60fb979e>
 570:	746e6f63          	bltu	t3,t1,cce <_start-0x60fff332>
 574:	6972                	ld	s2,280(sp)
 576:	2f62                	fld	ft10,24(sp)
 578:	6f6c                	ld	a1,216(a4)
 57a:	6461                	lui	s0,0x18
 57c:	7265                	lui	tp,0xffff9
 57e:	6c662f73          	csrrs	t5,0x6c6,a2
 582:	7361                	lui	t1,0xffff8
 584:	2f68                	fld	fa0,216(a4)
 586:	6d65                	lui	s10,0x19
 588:	636d                	lui	t1,0x1b
 58a:	6377642f          	0x6377642f
 58e:	736d                	lui	t1,0xffffb
 590:	6368                	ld	a0,192(a4)
 592:	6372732f          	0x6372732f
 596:	6400                	ld	s0,8(s0)
 598:	5c3a                	lw	s8,172(sp)
 59a:	6b726f77          	0x6b726f77
 59e:	325c                	fld	fa5,160(a2)
 5a0:	3230                	fld	fa2,96(a2)
 5a2:	5c32                	lw	s8,44(sp)
 5a4:	6c61                	lui	s8,0x18
 5a6:	3039                	0x3039
 5a8:	3030                	fld	fa2,96(s0)
 5aa:	735c                	ld	a5,160(a4)
 5ac:	64735c77          	0x64735c77
 5b0:	6f735c6b          	0x6f735c6b
 5b4:	70612d63          	0x70612d63
 5b8:	2d75                	addiw	s10,s10,29
 5ba:	5c6b6473          	csrrsi	s0,0x5c6,22
 5be:	6f74                	ld	a3,216(a4)
 5c0:	5c736c6f          	jal	s8,37386 <_start-0x60fc8c7a>
 5c4:	5c6e6977          	0x5c6e6977
 5c8:	6972                	ld	s2,280(sp)
 5ca:	5c766373          	csrrsi	t1,0x5c7,12
 5ce:	6972                	ld	s2,280(sp)
 5d0:	2d766373          	csrrsi	t1,0x2d7,12
 5d4:	756e                	ld	a0,248(sp)
 5d6:	69656c63          	bltu	a0,s6,c6e <_start-0x60fff392>
 5da:	652d                	lui	a0,0xb
 5dc:	666c                	ld	a1,200(a2)
 5de:	695c                	ld	a5,144(a0)
 5e0:	636e                	ld	t1,216(sp)
 5e2:	756c                	ld	a1,232(a0)
 5e4:	6564                	ld	s1,200(a0)
 5e6:	6d5c                	ld	a5,152(a0)
 5e8:	6361                	lui	t1,0x18
 5ea:	6968                	ld	a0,208(a0)
 5ec:	656e                	ld	a0,216(sp)
 5ee:	6400                	ld	s0,8(s0)
 5f0:	5c3a                	lw	s8,172(sp)
 5f2:	6b726f77          	0x6b726f77
 5f6:	325c                	fld	fa5,160(a2)
 5f8:	3230                	fld	fa2,96(a2)
 5fa:	5c32                	lw	s8,44(sp)
 5fc:	6c61                	lui	s8,0x18
 5fe:	3039                	0x3039
 600:	3030                	fld	fa2,96(s0)
 602:	735c                	ld	a5,160(a4)
 604:	64735c77          	0x64735c77
 608:	6f735c6b          	0x6f735c6b
 60c:	70612d63          	0x70612d63
 610:	2d75                	addiw	s10,s10,29
 612:	5c6b6473          	csrrsi	s0,0x5c6,22
 616:	6f74                	ld	a3,216(a4)
 618:	5c736c6f          	jal	s8,373de <_start-0x60fc8c22>
 61c:	5c6e6977          	0x5c6e6977
 620:	6972                	ld	s2,280(sp)
 622:	5c766373          	csrrsi	t1,0x5c7,12
 626:	6972                	ld	s2,280(sp)
 628:	2d766373          	csrrsi	t1,0x2d7,12
 62c:	756e                	ld	a0,248(sp)
 62e:	69656c63          	bltu	a0,s6,cc6 <_start-0x60fff33a>
 632:	652d                	lui	a0,0xb
 634:	666c                	ld	a1,200(a2)
 636:	695c                	ld	a5,144(a0)
 638:	636e                	ld	t1,216(sp)
 63a:	756c                	ld	a1,232(a0)
 63c:	6564                	ld	s1,200(a0)
 63e:	735c                	ld	a5,160(a4)
 640:	7379                	lui	t1,0xffffe
 642:	0000                	unimp
 644:	7764                	ld	s1,232(a4)
 646:	68736d63          	bltu	t1,t2,ce0 <_start-0x60fff320>
 64a:	79735f63          	bge	t1,s7,de8 <_start-0x60fff218>
 64e:	636e                	ld	t1,216(sp)
 650:	632e                	ld	t1,200(sp)
 652:	0100                	addi	s0,sp,128
 654:	0000                	unimp
 656:	645f 6665 7561      	0x75616665645f
 65c:	746c                	ld	a1,232(s0)
 65e:	745f 7079 7365      	0x73657079745f
 664:	682e                	ld	a6,200(sp)
 666:	0200                	addi	s0,sp,256
 668:	0000                	unimp
 66a:	735f 6474 6e69      	0x6e696474735f
 670:	2e74                	fld	fa3,216(a2)
 672:	0068                	addi	a0,sp,12
 674:	64000003          	lb	zero,1600(zero) # 640 <_start-0x60fff9c0>
 678:	736d6377          	0x736d6377
 67c:	6368                	ld	a0,192(a4)
 67e:	682e                	ld	a6,200(sp)
 680:	0100                	addi	s0,sp,128
 682:	0000                	unimp
 684:	0500                	addi	s0,sp,640
 686:	0001                	nop
 688:	0209                	addi	tp,tp,2
 68a:	0150                	addi	a2,sp,132
 68c:	6100                	ld	s0,0(a0)
 68e:	0000                	unimp
 690:	0000                	unimp
 692:	0515                	addi	a0,a0,5
 694:	0305                	addi	t1,t1,1
 696:	0901                	addi	s2,s2,0
 698:	0000                	unimp
 69a:	0301                	addi	t1,t1,0
 69c:	0901                	addi	s2,s2,0
 69e:	0000                	unimp
 6a0:	0301                	addi	t1,t1,0
 6a2:	0901                	addi	s2,s2,0
 6a4:	0000                	unimp
 6a6:	0501                	addi	a0,a0,0
 6a8:	0601                	addi	a2,a2,0
 6aa:	00097d03          	0x97d03
 6ae:	0100                	addi	s0,sp,128
 6b0:	1905                	addi	s2,s2,-31
 6b2:	0c090303          	lb	t1,192(s2)
 6b6:	0100                	addi	s0,sp,128
 6b8:	0705                	addi	a4,a4,1
 6ba:	08090003          	lb	zero,128(s2)
 6be:	0100                	addi	s0,sp,128
 6c0:	0105                	addi	sp,sp,1
 6c2:	04097d03          	0x4097d03
 6c6:	0100                	addi	s0,sp,128
 6c8:	18090003          	lb	zero,384(s2)
 6cc:	0100                	addi	s0,sp,128
 6ce:	0705                	addi	a4,a4,1
 6d0:	10090303          	lb	t1,256(s2)
 6d4:	0100                	addi	s0,sp,128
 6d6:	0905                	addi	s2,s2,1
 6d8:	04090203          	lb	tp,64(s2)
 6dc:	0100                	addi	s0,sp,128
 6de:	0d05                	addi	s10,s10,1
 6e0:	0306                	slli	t1,t1,0x1
 6e2:	0902                	c.slli64	s2
 6e4:	0004                	0x4
 6e6:	0501                	addi	a0,a0,0
 6e8:	061c                	addi	a5,sp,768
 6ea:	00090003          	lb	zero,0(s2)
 6ee:	0100                	addi	s0,sp,128
 6f0:	0f05                	addi	t5,t5,1
 6f2:	04090003          	lb	zero,64(s2)
 6f6:	0100                	addi	s0,sp,128
 6f8:	1805                	addi	a6,a6,-31
 6fa:	0200                	addi	s0,sp,256
 6fc:	0204                	addi	s1,sp,256
 6fe:	0306                	slli	t1,t1,0x1
 700:	097e                	slli	s2,s2,0x1f
 702:	0008                	0x8
 704:	0501                	addi	a0,a0,0
 706:	0019                	c.nop	6
 708:	0402                	c.slli64	s0
 70a:	0602                	c.slli64	a2
 70c:	00090003          	lb	zero,0(s2)
 710:	0100                	addi	s0,sp,128
 712:	1205                	addi	tp,tp,-31
 714:	0200                	addi	s0,sp,256
 716:	0204                	addi	s1,sp,256
 718:	0306                	slli	t1,t1,0x1
 71a:	0900                	addi	s0,sp,144
 71c:	0004                	0x4
 71e:	0501                	addi	a0,a0,0
 720:	0009                	c.nop	2
 722:	0402                	c.slli64	s0
 724:	0602                	c.slli64	a2
 726:	00090003          	lb	zero,0(s2)
 72a:	0100                	addi	s0,sp,128
 72c:	0306                	slli	t1,t1,0x1
 72e:	0906                	slli	s2,s2,0x1
 730:	0004                	0x4
 732:	0501                	addi	a0,a0,0
 734:	061a                	slli	a2,a2,0x6
 736:	00090003          	lb	zero,0(s2)
 73a:	0100                	addi	s0,sp,128
 73c:	0505                	addi	a0,a0,1
 73e:	0306                	slli	t1,t1,0x1
 740:	00040903          	lb	s2,0(s0) # 18000 <_start-0x60fe8000>
 744:	0501                	addi	a0,a0,0
 746:	0610                	addi	a2,sp,768
 748:	00090003          	lb	zero,0(s2)
 74c:	0100                	addi	s0,sp,128
 74e:	04090603          	lb	a2,64(s2)
 752:	0100                	addi	s0,sp,128
 754:	08097a03          	0x8097a03
 758:	0100                	addi	s0,sp,128
 75a:	0505                	addi	a0,a0,1
 75c:	0306                	slli	t1,t1,0x1
 75e:	0901                	addi	s2,s2,0
 760:	0004                	0x4
 762:	0501                	addi	a0,a0,0
 764:	030a                	slli	t1,t1,0x2
 766:	0900                	addi	s0,sp,144
 768:	0000                	unimp
 76a:	0501                	addi	a0,a0,0
 76c:	0601                	addi	a2,a2,0
 76e:	04090703          	lb	a4,64(s2)
 772:	0100                	addi	s0,sp,128
 774:	0905                	addi	s2,s2,1
 776:	0306                	slli	t1,t1,0x1
 778:	0024097b          	dkhmx8	s2,s0,sp
 77c:	0301                	addi	t1,t1,0
 77e:	0901                	addi	s2,s2,0
 780:	0010                	0x10
 782:	0301                	addi	t1,t1,0
 784:	0901                	addi	s2,s2,0
 786:	0004                	0x4
 788:	0501                	addi	a0,a0,0
 78a:	0614                	addi	a3,sp,768
 78c:	00090003          	lb	zero,0(s2)
 790:	0100                	addi	s0,sp,128
 792:	0905                	addi	s2,s2,1
 794:	0306                	slli	t1,t1,0x1
 796:	0901                	addi	s2,s2,0
 798:	0004                	0x4
 79a:	0501                	addi	a0,a0,0
 79c:	0610                	addi	a2,sp,768
 79e:	00090003          	lb	zero,0(s2)
 7a2:	0100                	addi	s0,sp,128
 7a4:	0809                	addi	a6,a6,2
 7a6:	0000                	unimp
 7a8:	0101                	addi	sp,sp,0

Disassembly of section .debug_info:

0000000000000000 <.debug_info>:
   0:	002a                	c.slli	zero,0xa
   2:	0000                	unimp
   4:	0002                	c.slli64	zero
   6:	0000                	unimp
   8:	0000                	unimp
   a:	0108                	addi	a0,sp,128
   c:	0000                	unimp
   e:	0000                	unimp
  10:	0000                	unimp
  12:	6100                	ld	s0,0(a0)
  14:	0000                	unimp
  16:	0000                	unimp
  18:	0010                	0x10
  1a:	6100                	ld	s0,0(a0)
	...
  24:	00000067          	jr	zero # 0 <_start-0x61000000>
  28:	00b5                	addi	ra,ra,13
  2a:	0000                	unimp
  2c:	8001                	c.srli64	s0
  2e:	00000287          	vle8.v	v5,(zero),v0.t
  32:	0004                	0x4
  34:	0014                	0x14
  36:	0000                	unimp
  38:	0108                	addi	a0,sp,128
  3a:	01da                	slli	gp,gp,0x16
  3c:	0000                	unimp
  3e:	750c                	ld	a1,40(a0)
  40:	0001                	nop
  42:	6700                	ld	s0,8(a4)
  44:	0000                	unimp
  46:	1000                	addi	s0,sp,32
  48:	0000                	unimp
  4a:	0061                	c.nop	24
  4c:	0000                	unimp
  4e:	4000                	lw	s0,0(s0)
  50:	0001                	nop
  52:	0000                	unimp
  54:	0000                	unimp
  56:	a700                	fsd	fs0,8(a4)
  58:	0000                	unimp
  5a:	0200                	addi	s0,sp,256
  5c:	0601                	addi	a2,a2,0
  5e:	00f8                	addi	a4,sp,76
  60:	0000                	unimp
  62:	0000cd03          	lbu	s10,0(ra)
  66:	0200                	addi	s0,sp,256
  68:	0040182b          	0x40182b
  6c:	0000                	unimp
  6e:	0102                	c.slli64	sp
  70:	f608                	sd	a0,40(a2)
  72:	0000                	unimp
  74:	0200                	addi	s0,sp,256
  76:	0502                	c.slli64	a0
  78:	0236                	slli	tp,tp,0xd
  7a:	0000                	unimp
  7c:	0202                	c.slli64	tp
  7e:	00011607          	flh	fa2,0(sp)
  82:	0400                	addi	s0,sp,512
  84:	0504                	addi	s1,sp,640
  86:	6e69                	lui	t3,0x1a
  88:	0074                	addi	a3,sp,12
  8a:	00015103          	lhu	sp,0(sp)
  8e:	0200                	addi	s0,sp,256
  90:	0068194f          	fnmadd.s	fs2,fa6,ft6,ft0,rtz
  94:	0000                	unimp
  96:	0402                	c.slli64	s0
  98:	00010907          	vle8.v	v18,(sp),v0.t
  9c:	0200                	addi	s0,sp,256
  9e:	0508                	addi	a0,sp,640
  a0:	00000247          	fmsub.s	ft4,ft0,ft0,ft0,rne
  a4:	0802                	c.slli64	a6
  a6:	00010407          	vle8.v	v8,(sp),v0.t
  aa:	0300                	addi	s0,sp,384
  ac:	000000cf          	fnmadd.s	ft1,ft0,ft0,ft0,rne
  b0:	34131803          	lh	a6,833(t1) # ffffffffffffe341 <block_addr_shift.0+0xffffffff9effdf39>
  b4:	0000                	unimp
  b6:	0300                	addi	s0,sp,384
  b8:	00000153          	fadd.s	ft2,ft0,ft0,rne
  bc:	5c143003          	ld	zero,1473(s0)
  c0:	0000                	unimp
  c2:	0500                	addi	s0,sp,640
  c4:	0089                	addi	ra,ra,2
  c6:	0000                	unimp
  c8:	8906                	mv	s2,ra
  ca:	0000                	unimp
  cc:	0700                	addi	s0,sp,896
  ce:	000000d7          	vadd.vv	v1,v0,v0,v0.t
  d2:	3901                	addiw	s2,s2,-32
  d4:	d406                	sw	ra,40(sp)
  d6:	0000                	unimp
  d8:	0061                	c.nop	24
  da:	0000                	unimp
  dc:	7c00                	ld	s0,56(s0)
  de:	0000                	unimp
  e0:	0000                	unimp
  e2:	0000                	unimp
  e4:	0100                	addi	s0,sp,128
  e6:	439c                	lw	a5,0(a5)
  e8:	0001                	nop
  ea:	0800                	addi	s0,sp,16
  ec:	000000c3          	fmadd.s	ft1,ft0,ft0,ft0,rne
  f0:	3901                	addiw	s2,s2,-32
  f2:	4329                	li	t1,10
  f4:	0001                	nop
  f6:	0000                	unimp
  f8:	0000                	unimp
  fa:	0800                	addi	s0,sp,16
  fc:	0240                	addi	s0,sp,260
  fe:	0000                	unimp
 100:	3901                	addiw	s2,s2,-32
 102:	493e                	lw	s2,204(sp)
 104:	0001                	nop
 106:	7200                	ld	s0,32(a2)
 108:	0000                	unimp
 10a:	0800                	addi	s0,sp,16
 10c:	025f 0000 3901      	0x39010000025f
 112:	0000894f          	fnmadd.s	fs2,ft1,ft0,ft0,rne
 116:	d100                	sw	s0,32(a0)
 118:	0000                	unimp
 11a:	0800                	addi	s0,sp,16
 11c:	01d1                	addi	gp,gp,20
 11e:	0000                	unimp
 120:	3901                	addiw	s2,s2,-32
 122:	8960                	0x8960
 124:	0000                	unimp
 126:	0a00                	addi	s0,sp,272
 128:	0001                	nop
 12a:	0900                	addi	s0,sp,144
 12c:	0069                	c.nop	26
 12e:	3b01                	addiw	s6,s6,-32
 130:	890e                	mv	s2,gp
 132:	0000                	unimp
 134:	4300                	lw	s0,0(a4)
 136:	0001                	nop
 138:	0a00                	addi	s0,sp,272
 13a:	010c                	addi	a1,sp,128
 13c:	6100                	ld	s0,0(a0)
 13e:	0000                	unimp
 140:	0000                	unimp
 142:	01ce                	slli	gp,gp,0x13
 144:	0000                	unimp
 146:	0128                	addi	a0,sp,136
 148:	0000                	unimp
 14a:	025a010b          	0x25a010b
 14e:	0078                	addi	a4,sp,12
 150:	015b010b          	0x15b010b
 154:	0035                	c.nop	13
 156:	380c                	fld	fa1,48(s0)
 158:	0001                	nop
 15a:	0061                	c.nop	24
 15c:	0000                	unimp
 15e:	ce00                	sw	s0,24(a2)
 160:	0001                	nop
 162:	0b00                	addi	s0,sp,400
 164:	5a01                	li	s4,-32
 166:	5a01f303          	0x5a01f303
 16a:	015b010b          	0x15b010b
 16e:	0031                	c.nop	12
 170:	0d00                	addi	s0,sp,656
 172:	9508                	0x9508
 174:	0000                	unimp
 176:	0d00                	addi	s0,sp,656
 178:	8908                	0x8908
 17a:	0000                	unimp
 17c:	0700                	addi	s0,sp,896
 17e:	015c                	addi	a5,sp,132
 180:	0000                	unimp
 182:	1e01                	addi	t3,t3,-32
 184:	6c06                	ld	s8,64(sp)
 186:	0000                	unimp
 188:	0061                	c.nop	24
 18a:	0000                	unimp
 18c:	6800                	ld	s0,16(s0)
 18e:	0000                	unimp
 190:	0000                	unimp
 192:	0000                	unimp
 194:	0100                	addi	s0,sp,128
 196:	c89c                	sw	a5,16(s1)
 198:	0001                	nop
 19a:	0e00                	addi	s0,sp,784
 19c:	000000c3          	fmadd.s	ft1,ft0,ft0,ft0,rne
 1a0:	1e01                	addi	t3,t3,-32
 1a2:	432a                	lw	t1,136(sp)
 1a4:	0001                	nop
 1a6:	0100                	addi	s0,sp,128
 1a8:	0e5a                	slli	t3,t3,0x16
 1aa:	025f 0000 1e01      	0x1e010000025f
 1b0:	893e                	mv	s2,a5
 1b2:	0000                	unimp
 1b4:	0100                	addi	s0,sp,128
 1b6:	0240085b          	0x240085b
 1ba:	0000                	unimp
 1bc:	1e01                	addi	t3,t3,-32
 1be:	c856                	sw	s5,16(sp)
 1c0:	0001                	nop
 1c2:	a200                	fsd	fs0,0(a2)
 1c4:	0001                	nop
 1c6:	0900                	addi	s0,sp,144
 1c8:	0069                	c.nop	26
 1ca:	2001                	0x2001
 1cc:	890e                	mv	s2,gp
 1ce:	0000                	unimp
 1d0:	f100                	sd	s0,32(a0)
 1d2:	0001                	nop
 1d4:	0f00                	addi	s0,sp,912
 1d6:	016d                	addi	sp,sp,27
 1d8:	0000                	unimp
 1da:	2001                	0x2001
 1dc:	8911                	andi	a0,a0,4
 1de:	0000                	unimp
 1e0:	3b00                	fld	fs0,48(a4)
 1e2:	0002                	c.slli64	zero
 1e4:	0f00                	addi	s0,sp,912
 1e6:	0129                	addi	sp,sp,10
 1e8:	0000                	unimp
 1ea:	2101                	sext.w	sp,sp
 1ec:	7d0d                	lui	s10,0xfffe3
 1ee:	0000                	unimp
 1f0:	8400                	0x8400
 1f2:	0002                	c.slli64	zero
 1f4:	0000                	unimp
 1f6:	080d                	addi	a6,a6,3
 1f8:	009a                	slli	ra,ra,0x6
 1fa:	0000                	unimp
 1fc:	3310                	fld	fa2,32(a4)
 1fe:	0001                	nop
 200:	0100                	addi	s0,sp,128
 202:	050e                	slli	a0,a0,0x3
 204:	0055                	c.nop	21
 206:	0000                	unimp
 208:	002c                	addi	a1,sp,8
 20a:	6100                	ld	s0,0(a0)
 20c:	0000                	unimp
 20e:	0000                	unimp
 210:	0040                	addi	s0,sp,4
 212:	0000                	unimp
 214:	0000                	unimp
 216:	0000                	unimp
 218:	9c01                	subw	s0,s0,s0
 21a:	023d                	addi	tp,tp,15
 21c:	0000                	unimp
 21e:	c308                	sw	a0,0(a4)
 220:	0000                	unimp
 222:	0100                	addi	s0,sp,128
 224:	260e                	fld	fa2,192(sp)
 226:	00000143          	fmadd.s	ft2,ft0,ft0,ft0,rne
 22a:	02df 0000 5a0e      	0x5a0e000002df
 230:	0002                	c.slli64	zero
 232:	0100                	addi	s0,sp,128
 234:	390e                	fld	fs2,224(sp)
 236:	007d                	c.nop	31
 238:	0000                	unimp
 23a:	5b01                	li	s6,-32
 23c:	00016d0f          	0x16d0f
 240:	0100                	addi	s0,sp,128
 242:	0e10                	addi	a2,sp,784
 244:	0089                	addi	ra,ra,2
 246:	0000                	unimp
 248:	0318                	addi	a4,sp,384
 24a:	0000                	unimp
 24c:	5011                	c.li	zero,-28
 24e:	0002                	c.slli64	zero
 250:	0100                	addi	s0,sp,128
 252:	1710                	addi	a2,sp,928
 254:	0089                	addi	ra,ra,2
 256:	0000                	unimp
 258:	5f01                	li	t5,-32
 25a:	0001290f          	0x1290f
 25e:	0100                	addi	s0,sp,128
 260:	0d11                	addi	s10,s10,4
 262:	007d                	c.nop	31
 264:	0000                	unimp
 266:	034e                	slli	t1,t1,0x13
 268:	0000                	unimp
 26a:	1200                	addi	s0,sp,288
 26c:	000000e7          	jalr	zero # 0 <_start-0x61000000>
 270:	0301                	addi	t1,t1,0
 272:	0000890b          	0x890b
 276:	1000                	addi	s0,sp,32
 278:	0000                	unimp
 27a:	0061                	c.nop	24
 27c:	0000                	unimp
 27e:	1c00                	addi	s0,sp,560
 280:	0000                	unimp
 282:	0000                	unimp
 284:	0000                	unimp
 286:	0100                	addi	s0,sp,128
 288:	089c                	addi	a5,sp,80
 28a:	0141                	addi	sp,sp,16
 28c:	0000                	unimp
 28e:	0301                	addi	t1,t1,0
 290:	4924                	lw	s1,80(a0)
 292:	0001                	nop
 294:	a600                	fsd	fs0,8(a2)
 296:	09000003          	lb	zero,144(zero) # 90 <_start-0x60ffff70>
 29a:	01007077          	0x1007077
 29e:	0e05                	addi	t3,t3,1
 2a0:	0089                	addi	ra,ra,2
 2a2:	0000                	unimp
 2a4:	03dc                	addi	a5,sp,452
 2a6:	0000                	unimp
 2a8:	7209                	lui	tp,0xfffe2
 2aa:	0070                	addi	a2,sp,12
 2ac:	0501                	addi	a0,a0,0
 2ae:	8916                	mv	s2,t0
 2b0:	0000                	unimp
 2b2:	1300                	addi	s0,sp,416
 2b4:	0004                	0x4
 2b6:	0000                	unimp
 2b8:	7200                	ld	s0,32(a2)
 2ba:	0001                	nop
 2bc:	0400                	addi	s0,sp,512
 2be:	3600                	fld	fs0,40(a2)
 2c0:	0001                	nop
 2c2:	0800                	addi	s0,sp,16
 2c4:	da01                	beqz	a2,1d4 <_start-0x60fffe2c>
 2c6:	0001                	nop
 2c8:	0c00                	addi	s0,sp,528
 2ca:	0266                	slli	tp,tp,0x19
 2cc:	0000                	unimp
 2ce:	00000067          	jr	zero # 0 <_start-0x61000000>
 2d2:	0150                	addi	a2,sp,132
 2d4:	6100                	ld	s0,0(a0)
 2d6:	0000                	unimp
 2d8:	0000                	unimp
 2da:	00b8                	addi	a4,sp,72
 2dc:	0000                	unimp
 2de:	0000                	unimp
 2e0:	0000                	unimp
 2e2:	052a                	slli	a0,a0,0xa
 2e4:	0000                	unimp
 2e6:	0102                	c.slli64	sp
 2e8:	f806                	sd	ra,48(sp)
 2ea:	0000                	unimp
 2ec:	0300                	addi	s0,sp,384
 2ee:	00cd                	addi	ra,ra,19
 2f0:	0000                	unimp
 2f2:	2b02                	fld	fs6,0(sp)
 2f4:	4018                	lw	a4,0(s0)
 2f6:	0000                	unimp
 2f8:	0200                	addi	s0,sp,256
 2fa:	0801                	addi	a6,a6,0
 2fc:	00f6                	slli	ra,ra,0x1d
 2fe:	0000                	unimp
 300:	0202                	c.slli64	tp
 302:	3605                	addiw	a2,a2,-31
 304:	0002                	c.slli64	zero
 306:	0200                	addi	s0,sp,256
 308:	0702                	c.slli64	a4
 30a:	0116                	slli	sp,sp,0x5
 30c:	0000                	unimp
 30e:	0404                	addi	s1,sp,512
 310:	6905                	lui	s2,0x1
 312:	746e                	ld	s0,248(sp)
 314:	0300                	addi	s0,sp,384
 316:	0151                	addi	sp,sp,20
 318:	0000                	unimp
 31a:	4f02                	lw	t5,0(sp)
 31c:	6819                	lui	a6,0x6
 31e:	0000                	unimp
 320:	0200                	addi	s0,sp,256
 322:	0704                	addi	s1,sp,896
 324:	0109                	addi	sp,sp,2
 326:	0000                	unimp
 328:	0802                	c.slli64	a6
 32a:	4705                	li	a4,1
 32c:	0002                	c.slli64	zero
 32e:	0200                	addi	s0,sp,256
 330:	0708                	addi	a0,sp,896
 332:	0104                	addi	s1,sp,128
 334:	0000                	unimp
 336:	0000cf03          	lbu	t5,0(ra)
 33a:	0300                	addi	s0,sp,384
 33c:	1318                	addi	a4,sp,416
 33e:	0034                	addi	a3,sp,8
 340:	0000                	unimp
 342:	00015303          	lhu	t1,0(sp)
 346:	0300                	addi	s0,sp,384
 348:	1430                	addi	a2,sp,552
 34a:	005c                	addi	a5,sp,4
 34c:	0000                	unimp
 34e:	8905                	andi	a0,a0,1
 350:	0000                	unimp
 352:	0600                	addi	s0,sp,768
 354:	000002eb          	0x2eb
 358:	0301                	addi	t1,t1,0
 35a:	5006                	0x5006
 35c:	0001                	nop
 35e:	0061                	c.nop	24
 360:	0000                	unimp
 362:	b800                	fsd	fs0,48(s0)
 364:	0000                	unimp
 366:	0000                	unimp
 368:	0000                	unimp
 36a:	0100                	addi	s0,sp,128
 36c:	5d9c                	lw	a5,56(a1)
 36e:	0001                	nop
 370:	0700                	addi	s0,sp,896
 372:	000000c3          	fmadd.s	ft1,ft0,ft0,ft0,rne
 376:	0301                	addi	t1,t1,0
 378:	5d26                	lw	s10,104(sp)
 37a:	0001                	nop
 37c:	4a00                	lw	s0,16(a2)
 37e:	0004                	0x4
 380:	0700                	addi	s0,sp,896
 382:	02e0                	addi	s0,sp,332
 384:	0000                	unimp
 386:	0301                	addi	t1,t1,0
 388:	893a                	mv	s2,a4
 38a:	0000                	unimp
 38c:	a900                	fsd	fs0,16(a0)
 38e:	0004                	0x4
 390:	0700                	addi	s0,sp,896
 392:	000002db          	0x2db
 396:	0301                	addi	t1,t1,0
 398:	0000894f          	fnmadd.s	fs2,ft1,ft0,ft0,rne
 39c:	e200                	sd	s0,0(a2)
 39e:	0004                	0x4
 3a0:	0700                	addi	s0,sp,896
 3a2:	0240                	addi	s0,sp,260
 3a4:	0000                	unimp
 3a6:	0301                	addi	t1,t1,0
 3a8:	635e                	ld	t1,464(sp)
 3aa:	0001                	nop
 3ac:	1b00                	addi	s0,sp,432
 3ae:	0005                	c.nop	1
 3b0:	0700                	addi	s0,sp,896
 3b2:	000002c7          	fmsub.s	ft5,ft0,ft0,ft0,rne
 3b6:	0301                	addi	t1,t1,0
 3b8:	556a                	lw	a0,184(sp)
 3ba:	0000                	unimp
 3bc:	6400                	ld	s0,8(s0)
 3be:	0005                	c.nop	1
 3c0:	0800                	addi	s0,sp,16
 3c2:	02f8                	addi	a4,sp,332
 3c4:	0000                	unimp
 3c6:	0501                	addi	a0,a0,0
 3c8:	5510                	lw	a2,40(a0)
 3ca:	0000                	unimp
 3cc:	0900                	addi	s0,sp,144
 3ce:	00040803          	lb	a6,0(s0)
 3d2:	0061                	c.nop	24
 3d4:	0000                	unimp
 3d6:	0900                	addi	s0,sp,144
 3d8:	0069                	c.nop	26
 3da:	0601                	addi	a2,a2,0
 3dc:	890e                	mv	s2,gp
 3de:	0000                	unimp
 3e0:	c300                	sw	s0,0(a4)
 3e2:	0005                	c.nop	1
 3e4:	0a00                	addi	s0,sp,272
 3e6:	02d5                	addi	t0,t0,21
 3e8:	0000                	unimp
 3ea:	0601                	addi	a2,a2,0
 3ec:	8911                	andi	a0,a0,4
 3ee:	0000                	unimp
 3f0:	e600                	sd	s0,8(a2)
 3f2:	0005                	c.nop	1
 3f4:	0b00                	addi	s0,sp,400
 3f6:	01f8                	addi	a4,sp,204
 3f8:	6100                	ld	s0,0(a0)
 3fa:	0000                	unimp
 3fc:	0000                	unimp
 3fe:	0169                	addi	sp,sp,26
 400:	0000                	unimp
 402:	010c                	addi	a1,sp,128
 404:	025a                	slli	tp,tp,0x16
 406:	0084                	addi	s1,sp,64
 408:	010c                	addi	a1,sp,128
 40a:	0078025b          	0x78025b
 40e:	010c                	addi	a1,sp,128
 410:	025c                	addi	a5,sp,260
 412:	00000083          	lb	ra,0(zero) # 0 <_start-0x61000000>
 416:	080d                	addi	a6,a6,3
 418:	0095                	addi	ra,ra,5
 41a:	0000                	unimp
 41c:	080d                	addi	a6,a6,3
 41e:	007d                	c.nop	31
 420:	0000                	unimp
 422:	5c0e                	lw	s8,224(sp)
 424:	0001                	nop
 426:	5c00                	lw	s0,56(s0)
 428:	0001                	nop
 42a:	0400                	addi	s0,sp,512
 42c:	          	0x62f

Disassembly of section .debug_abbrev:

0000000000000000 <.debug_abbrev>:
   0:	1101                	addi	sp,sp,-32
   2:	1000                	addi	s0,sp,32
   4:	1106                	slli	sp,sp,0x21
   6:	1201                	addi	tp,tp,-32
   8:	0301                	addi	t1,t1,0
   a:	1b0e                	slli	s6,s6,0x23
   c:	250e                	fld	fa0,192(sp)
   e:	130e                	slli	t1,t1,0x23
  10:	0005                	c.nop	1
  12:	0000                	unimp
  14:	1101                	addi	sp,sp,-32
  16:	2501                	sext.w	a0,a0
  18:	130e                	slli	t1,t1,0x23
  1a:	1b0e030b          	0x1b0e030b
  1e:	110e                	slli	sp,sp,0x23
  20:	1201                	addi	tp,tp,-32
  22:	00171007          	flh	ft0,1(a4) # 1d001 <_start-0x60fe2fff>
  26:	0200                	addi	s0,sp,256
  28:	0024                	addi	s1,sp,8
  2a:	0b3e0b0b          	0xb3e0b0b
  2e:	00000e03          	lb	t3,0(zero) # 0 <_start-0x61000000>
  32:	03001603          	lh	a2,48(zero) # 30 <_start-0x60ffffd0>
  36:	3a0e                	fld	fs4,224(sp)
  38:	390b3b0b          	0x390b3b0b
  3c:	0013490b          	0x13490b
  40:	0400                	addi	s0,sp,512
  42:	0024                	addi	s1,sp,8
  44:	0b3e0b0b          	0xb3e0b0b
  48:	00000803          	lb	a6,0(zero) # 0 <_start-0x61000000>
  4c:	3505                	addiw	a0,a0,-31
  4e:	4900                	lw	s0,16(a0)
  50:	06000013          	li	zero,96
  54:	0026                	c.slli	zero,0x9
  56:	1349                	addi	t1,t1,-14
  58:	0000                	unimp
  5a:	3f012e07          	flw	ft8,1008(sp)
  5e:	0319                	addi	t1,t1,6
  60:	3a0e                	fld	fs4,224(sp)
  62:	390b3b0b          	0x390b3b0b
  66:	1119270b          	0x1119270b
  6a:	1201                	addi	tp,tp,-32
  6c:	97184007          	flq	ft0,-1679(a6) # 5971 <_start-0x60ffa68f>
  70:	1942                	slli	s2,s2,0x30
  72:	1301                	addi	t1,t1,-32
  74:	0000                	unimp
  76:	0508                	addi	a0,sp,640
  78:	0300                	addi	s0,sp,384
  7a:	3a0e                	fld	fs4,224(sp)
  7c:	390b3b0b          	0x390b3b0b
  80:	0213490b          	0x213490b
  84:	09000017          	auipc	zero,0x9000
  88:	0034                	addi	a3,sp,8
  8a:	0b3a0803          	lb	a6,179(s4)
  8e:	0b390b3b          	0xb390b3b
  92:	1349                	addi	t1,t1,-14
  94:	1702                	slli	a4,a4,0x20
  96:	0000                	unimp
  98:	890a                	mv	s2,sp
  9a:	0182                	c.slli64	gp
  9c:	1101                	addi	sp,sp,-32
  9e:	3101                	addiw	sp,sp,-32
  a0:	00130113          	addi	sp,t1,1
  a4:	0b00                	addi	s0,sp,400
  a6:	828a                	mv	t0,sp
  a8:	0001                	nop
  aa:	1802                	slli	a6,a6,0x20
  ac:	4291                	li	t0,4
  ae:	0018                	0x18
  b0:	0c00                	addi	s0,sp,528
  b2:	8289                	srli	a3,a3,0x2
  b4:	0101                	addi	sp,sp,0
  b6:	0111                	addi	sp,sp,4
  b8:	4295                	li	t0,5
  ba:	3119                	addiw	sp,sp,-26
  bc:	0d000013          	li	zero,208
  c0:	0b0b000f          	0xb0b000f
  c4:	1349                	addi	t1,t1,-14
  c6:	0000                	unimp
  c8:	050e                	slli	a0,a0,0x3
  ca:	0300                	addi	s0,sp,384
  cc:	3a0e                	fld	fs4,224(sp)
  ce:	390b3b0b          	0x390b3b0b
  d2:	0213490b          	0x213490b
  d6:	0018                	0x18
  d8:	0f00                	addi	s0,sp,912
  da:	0034                	addi	a3,sp,8
  dc:	0b3a0e03          	lb	t3,179(s4)
  e0:	0b390b3b          	0xb390b3b
  e4:	1349                	addi	t1,t1,-14
  e6:	1702                	slli	a4,a4,0x20
  e8:	0000                	unimp
  ea:	2e10                	fld	fa2,24(a2)
  ec:	3f01                	addiw	t5,t5,-32
  ee:	0319                	addi	t1,t1,6
  f0:	3a0e                	fld	fs4,224(sp)
  f2:	390b3b0b          	0x390b3b0b
  f6:	4919270b          	0x4919270b
  fa:	12011113          	0x12011113
  fe:	97184007          	flq	ft0,-1679(a6)
 102:	1942                	slli	s2,s2,0x30
 104:	1301                	addi	t1,t1,-32
 106:	0000                	unimp
 108:	3411                	addiw	s0,s0,-28
 10a:	0300                	addi	s0,sp,384
 10c:	3a0e                	fld	fs4,224(sp)
 10e:	390b3b0b          	0x390b3b0b
 112:	0213490b          	0x213490b
 116:	0018                	0x18
 118:	1200                	addi	s0,sp,288
 11a:	012e                	slli	sp,sp,0xb
 11c:	0e03193f 0b3b0b3a 	0xb3b0b3a0e03193f
 124:	0b39                	addi	s6,s6,14
 126:	13491927          	fsh	fs4,306(s2) # 1132 <_start-0x60ffeece>
 12a:	0111                	addi	sp,sp,4
 12c:	0712                	slli	a4,a4,0x4
 12e:	1840                	addi	s0,sp,52
 130:	00194297          	auipc	t0,0x194
 134:	0000                	unimp
 136:	1101                	addi	sp,sp,-32
 138:	2501                	sext.w	a0,a0
 13a:	130e                	slli	t1,t1,0x23
 13c:	1b0e030b          	0x1b0e030b
 140:	110e                	slli	sp,sp,0x23
 142:	1201                	addi	tp,tp,-32
 144:	00171007          	flh	ft0,1(a4)
 148:	0200                	addi	s0,sp,256
 14a:	0024                	addi	s1,sp,8
 14c:	0b3e0b0b          	0xb3e0b0b
 150:	00000e03          	lb	t3,0(zero) # 0 <_start-0x61000000>
 154:	03001603          	lh	a2,48(zero) # 30 <_start-0x60ffffd0>
 158:	3a0e                	fld	fs4,224(sp)
 15a:	390b3b0b          	0x390b3b0b
 15e:	0013490b          	0x13490b
 162:	0400                	addi	s0,sp,512
 164:	0024                	addi	s1,sp,8
 166:	0b3e0b0b          	0xb3e0b0b
 16a:	00000803          	lb	a6,0(zero) # 0 <_start-0x61000000>
 16e:	3505                	addiw	a0,a0,-31
 170:	4900                	lw	s0,16(a0)
 172:	06000013          	li	zero,96
 176:	012e                	slli	sp,sp,0xb
 178:	0e03193f 0b3b0b3a 	0xb3b0b3a0e03193f
 180:	0b39                	addi	s6,s6,14
 182:	01111927          	fsh	fa7,18(sp)
 186:	0712                	slli	a4,a4,0x4
 188:	1840                	addi	s0,sp,52
 18a:	01194297          	auipc	t0,0x1194
 18e:	07000013          	li	zero,112
 192:	0005                	c.nop	1
 194:	0b3a0e03          	lb	t3,179(s4)
 198:	0b390b3b          	0xb390b3b
 19c:	1349                	addi	t1,t1,-14
 19e:	1702                	slli	a4,a4,0x20
 1a0:	0000                	unimp
 1a2:	3408                	fld	fa0,40(s0)
 1a4:	0300                	addi	s0,sp,384
 1a6:	3a0e                	fld	fs4,224(sp)
 1a8:	390b3b0b          	0x390b3b0b
 1ac:	0213490b          	0x213490b
 1b0:	0018                	0x18
 1b2:	0900                	addi	s0,sp,144
 1b4:	0034                	addi	a3,sp,8
 1b6:	0b3a0803          	lb	a6,179(s4)
 1ba:	0b390b3b          	0xb390b3b
 1be:	1349                	addi	t1,t1,-14
 1c0:	1702                	slli	a4,a4,0x20
 1c2:	0000                	unimp
 1c4:	340a                	fld	fs0,160(sp)
 1c6:	0300                	addi	s0,sp,384
 1c8:	3a0e                	fld	fs4,224(sp)
 1ca:	390b3b0b          	0x390b3b0b
 1ce:	0213490b          	0x213490b
 1d2:	0b000017          	auipc	zero,0xb000
 1d6:	8289                	srli	a3,a3,0x2
 1d8:	0101                	addi	sp,sp,0
 1da:	0111                	addi	sp,sp,4
 1dc:	1331                	addi	t1,t1,-20
 1de:	0000                	unimp
 1e0:	8a0c                	0x8a0c
 1e2:	0182                	c.slli64	gp
 1e4:	0200                	addi	s0,sp,256
 1e6:	9118                	0x9118
 1e8:	1842                	slli	a6,a6,0x30
 1ea:	0000                	unimp
 1ec:	0f0d                	addi	t5,t5,3
 1ee:	0b00                	addi	s0,sp,400
 1f0:	0013490b          	0x13490b
 1f4:	0e00                	addi	s0,sp,784
 1f6:	002e                	c.slli	zero,0xb
 1f8:	193c193f 0e030e6e 	0xe030e6e193c193f
 200:	0b3a                	slli	s6,s6,0xe
 202:	0b390b3b          	0xb390b3b
 206:	0000                	unimp
	...

Disassembly of section .debug_aranges:

0000000000000000 <.debug_aranges>:
   0:	002c                	addi	a1,sp,8
   2:	0000                	unimp
   4:	0002                	c.slli64	zero
   6:	0000                	unimp
   8:	0000                	unimp
   a:	0008                	0x8
   c:	0000                	unimp
   e:	0000                	unimp
  10:	0000                	unimp
  12:	6100                	ld	s0,0(a0)
  14:	0000                	unimp
  16:	0000                	unimp
  18:	0010                	0x10
	...
  2e:	0000                	unimp
  30:	002c                	addi	a1,sp,8
  32:	0000                	unimp
  34:	0002                	c.slli64	zero
  36:	002e                	c.slli	zero,0xb
  38:	0000                	unimp
  3a:	0008                	0x8
  3c:	0000                	unimp
  3e:	0000                	unimp
  40:	0010                	0x10
  42:	6100                	ld	s0,0(a0)
  44:	0000                	unimp
  46:	0000                	unimp
  48:	0140                	addi	s0,sp,132
	...
  5e:	0000                	unimp
  60:	002c                	addi	a1,sp,8
  62:	0000                	unimp
  64:	0002                	c.slli64	zero
  66:	02b9                	addi	t0,t0,14
  68:	0000                	unimp
  6a:	0008                	0x8
  6c:	0000                	unimp
  6e:	0000                	unimp
  70:	0150                	addi	a2,sp,132
  72:	6100                	ld	s0,0(a0)
  74:	0000                	unimp
  76:	0000                	unimp
  78:	00b8                	addi	a4,sp,72
	...

Disassembly of section .debug_str:

0000000000000000 <.debug_str>:
   0:	3a44                	fld	fs1,176(a2)
   2:	726f772f          	0x726f772f
   6:	30322f6b          	0x30322f6b
   a:	3232                	fld	ft4,296(sp)
   c:	396c612f          	0x396c612f
  10:	3030                	fld	fa2,96(s0)
  12:	2f30                	fld	fa2,88(a4)
  14:	642f7773          	csrrci	a4,0x642,30
  18:	6265                	lui	tp,0x19
  1a:	6775                	lui	a4,0x1d
  1c:	2f726567          	0x2f726567
  20:	6c61                	lui	s8,0x18
  22:	6f5f 6570 6f6e      	0x6f6e65706f5f
  28:	632f6463          	bltu	t5,s2,650 <_start-0x60fff9b0>
  2c:	72746e6f          	jal	t3,46f52 <_start-0x60fb90ae>
  30:	6269                	lui	tp,0x1a
  32:	616f6c2f          	0x616f6c2f
  36:	6564                	ld	s1,200(a0)
  38:	7372                	ld	t1,312(sp)
  3a:	616c662f          	0x616c662f
  3e:	652f6873          	csrrsi	a6,0x652,30
  42:	6d6d                	lui	s10,0x1b
  44:	77642f63          	0x77642f63
  48:	68736d63          	bltu	t1,t2,6e2 <_start-0x60fff91e>
  4c:	72732f63          	0x72732f63
  50:	6f622f63          	0x6f622f63
  54:	722f746f          	jal	s0,f7776 <_start-0x60f0888a>
  58:	7369                	lui	t1,0xffffa
  5a:	775c7663          	bgeu	s8,s5,7c6 <_start-0x60fff83a>
  5e:	6172                	ld	sp,280(sp)
  60:	7070                	ld	a2,224(s0)
  62:	7265                	lui	tp,0xffff9
  64:	532e                	lw	t1,232(sp)
  66:	4400                	lw	s0,8(s0)
  68:	5c3a                	lw	s8,172(sp)
  6a:	6b726f77          	0x6b726f77
  6e:	325c                	fld	fa5,160(a2)
  70:	3230                	fld	fa2,96(a2)
  72:	5c32                	lw	s8,44(sp)
  74:	6c61                	lui	s8,0x18
  76:	3039                	0x3039
  78:	3030                	fld	fa2,96(s0)
  7a:	735c                	ld	a5,160(a4)
  7c:	65645c77          	0x65645c77
  80:	7562                	ld	a0,56(sp)
  82:	72656767          	0x72656767
  86:	615c                	ld	a5,128(a0)
  88:	5f6c                	lw	a1,124(a4)
  8a:	6e65706f          	j	57770 <_start-0x60fa8890>
  8e:	5c64636f          	jal	t1,46654 <_start-0x60fb99ac>
  92:	746e6f63          	bltu	t3,t1,7f0 <_start-0x60fff810>
  96:	6972                	ld	s2,280(sp)
  98:	5c62                	lw	s8,56(sp)
  9a:	6f6c                	ld	a1,216(a4)
  9c:	6461                	lui	s0,0x18
  9e:	7265                	lui	tp,0xffff9
  a0:	6c665c73          	csrrwi	s8,0x6c6,12
  a4:	7361                	lui	t1,0xffff8
  a6:	5c68                	lw	a0,124(s0)
  a8:	6d65                	lui	s10,0x19
  aa:	636d                	lui	t1,0x1b
  ac:	645c                	ld	a5,136(s0)
  ae:	736d6377          	0x736d6377
  b2:	6368                	ld	a0,192(a4)
  b4:	4700                	lw	s0,8(a4)
  b6:	554e                	lw	a0,240(sp)
  b8:	4120                	lw	s0,64(a0)
  ba:	2e322053          	0x2e322053
  be:	312e3633          	0x312e3633
  c2:	6300                	ld	s0,0(a4)
  c4:	7274                	ld	a3,224(a2)
  c6:	5f6c                	lw	a1,124(a4)
  c8:	6162                	ld	sp,24(sp)
  ca:	5f006573          	csrrsi	a0,0x5f0,0
  ce:	755f 6e69 3874      	0x38746e69755f
  d4:	745f 6500 6d6d      	0x6d6d6500745f
  da:	65725f63          	bge	tp,s7,738 <_start-0x60fff8c8>
  de:	6461                	lui	s0,0x18
  e0:	625f 6f6c 6b63      	0x6b636f6c625f
  e6:	6500                	ld	s0,8(a0)
  e8:	6d6d                	lui	s10,0x1b
  ea:	61775f63          	bge	a4,s7,708 <_start-0x60fff8f8>
  ee:	7469                	lui	s0,0xffffa
  f0:	665f 6669 006f      	0x6f6669665f
  f6:	6e75                	lui	t3,0x1d
  f8:	6e676973          	csrrsi	s2,0x6e6,14
  fc:	6465                	lui	s0,0x19
  fe:	6320                	ld	s0,64(a4)
 100:	6168                	ld	a0,192(a0)
 102:	0072                	c.slli	zero,0x1c
 104:	6f6c                	ld	a1,216(a4)
 106:	676e                	ld	a4,216(sp)
 108:	7520                	ld	s0,104(a0)
 10a:	736e                	ld	t1,248(sp)
 10c:	6769                	lui	a4,0x1a
 10e:	656e                	ld	a0,216(sp)
 110:	2064                	fld	fs1,192(s0)
 112:	6e69                	lui	t3,0x1a
 114:	0074                	addi	a3,sp,12
 116:	726f6873          	csrrsi	a6,0x726,30
 11a:	2074                	fld	fa3,192(s0)
 11c:	6e75                	lui	t3,0x1d
 11e:	6e676973          	csrrsi	s2,0x6e6,14
 122:	6465                	lui	s0,0x19
 124:	6920                	ld	s0,80(a0)
 126:	746e                	ld	s0,248(sp)
 128:	6400                	ld	s0,8(s0)
 12a:	5f656e6f          	jal	t3,56720 <_start-0x60fa98e0>
 12e:	6c66                	ld	s8,88(sp)
 130:	6761                	lui	a4,0x18
 132:	6500                	ld	s0,8(a0)
 134:	6d6d                	lui	s10,0x1b
 136:	6f705f63          	blez	s7,834 <_start-0x60fff7cc>
 13a:	6c6c                	ld	a1,216(s0)
 13c:	695f 746e 7700      	0x7700746e695f
 142:	5f6b726f          	jal	tp,b7738 <_start-0x60f488c8>
 146:	7261                	lui	tp,0xffff8
 148:	6165                	addi	sp,sp,112
 14a:	735f 6174 7472      	0x74726174735f
 150:	5f00                	lw	s0,56(a4)
 152:	755f 6e69 3374      	0x33746e69755f
 158:	5f32                	lw	t5,44(sp)
 15a:	0074                	addi	a3,sp,12
 15c:	6d65                	lui	s10,0x19
 15e:	636d                	lui	t1,0x1b
 160:	775f 6972 6574      	0x65746972775f
 166:	625f 6f6c 6b63      	0x6b636f6c625f
 16c:	6900                	ld	s0,16(a0)
 16e:	746e                	ld	s0,248(sp)
 170:	765f 6c61 4400      	0x44006c61765f
 176:	2f3a                	fld	ft10,392(sp)
 178:	6b726f77          	0x6b726f77
 17c:	3230322f          	0x3230322f
 180:	2f32                	fld	ft10,264(sp)
 182:	6c61                	lui	s8,0x18
 184:	3039                	0x3039
 186:	3030                	fld	fa2,96(s0)
 188:	2f77732f          	0x2f77732f
 18c:	6564                	ld	s1,200(a0)
 18e:	7562                	ld	a0,56(sp)
 190:	72656767          	0x72656767
 194:	5f6c612f          	0x5f6c612f
 198:	6e65706f          	j	5787e <_start-0x60fa8782>
 19c:	2f64636f          	jal	t1,46492 <_start-0x60fb9b6e>
 1a0:	746e6f63          	bltu	t3,t1,8fe <_start-0x60fff702>
 1a4:	6972                	ld	s2,280(sp)
 1a6:	2f62                	fld	ft10,24(sp)
 1a8:	6f6c                	ld	a1,216(a4)
 1aa:	6461                	lui	s0,0x18
 1ac:	7265                	lui	tp,0xffff9
 1ae:	6c662f73          	csrrs	t5,0x6c6,a2
 1b2:	7361                	lui	t1,0xffff8
 1b4:	2f68                	fld	fa0,216(a4)
 1b6:	6d65                	lui	s10,0x19
 1b8:	636d                	lui	t1,0x1b
 1ba:	6377642f          	0x6377642f
 1be:	736d                	lui	t1,0xffffb
 1c0:	6368                	ld	a0,192(a4)
 1c2:	6372732f          	0x6372732f
 1c6:	6377642f          	0x6377642f
 1ca:	736d                	lui	t1,0xffffb
 1cc:	6368                	ld	a0,192(a4)
 1ce:	632e                	ld	t1,200(sp)
 1d0:	7700                	ld	s0,40(a4)
 1d2:	5f64726f          	jal	tp,477c8 <_start-0x60fb8838>
 1d6:	00746e63          	bltu	s0,t2,1f2 <_start-0x60fffe0e>
 1da:	20554e47          	fmsub.s	ft8,fa0,ft5,ft4,rmm
 1de:	20373143          	fmadd.s	ft2,fa4,ft3,ft4,rup
 1e2:	3031                	0x3031
 1e4:	322e                	fld	ft4,232(sp)
 1e6:	302e                	fld	ft0,232(sp)
 1e8:	2d20                	fld	fs0,88(a0)
 1ea:	616d                	addi	sp,sp,240
 1ec:	6372                	ld	t1,280(sp)
 1ee:	3d68                	fld	fa0,248(a0)
 1f0:	7672                	ld	a2,312(sp)
 1f2:	3436                	fld	fs0,360(sp)
 1f4:	2069                	0x2069
 1f6:	6d2d                	lui	s10,0xb
 1f8:	6261                	lui	tp,0x18
 1fa:	3d69                	addiw	s10,s10,-6
 1fc:	706c                	ld	a1,224(s0)
 1fe:	3436                	fld	fs0,360(sp)
 200:	2d20                	fld	fs0,88(a0)
 202:	746d                	lui	s0,0xffffb
 204:	6e75                	lui	t3,0x1d
 206:	3d65                	addiw	s10,s10,-7
 208:	6f72                	ld	t5,280(sp)
 20a:	74656b63          	bltu	a0,t1,960 <_start-0x60fff6a0>
 20e:	2d20                	fld	fs0,88(a0)
 210:	616d                	addi	sp,sp,240
 212:	6372                	ld	t1,280(sp)
 214:	3d68                	fld	fa0,248(a0)
 216:	7672                	ld	a2,312(sp)
 218:	3436                	fld	fs0,360(sp)
 21a:	2069                	0x2069
 21c:	672d                	lui	a4,0xb
 21e:	2d20                	fld	fs0,88(a0)
 220:	2d20734f          	fnmadd.h	ft6,ft0,fs2,ft5
 224:	6e66                	ld	t3,88(sp)
 226:	75622d6f          	jal	s10,2297c <_start-0x60fdd684>
 22a:	6c69                	lui	s8,0x1a
 22c:	6974                	ld	a3,208(a0)
 22e:	206e                	fld	ft0,216(sp)
 230:	662d                	lui	a2,0xb
 232:	4950                	lw	a2,20(a0)
 234:	68730043          	fmadd.s	ft0,ft6,ft7,fa3,rne
 238:	2074726f          	jal	tp,47c3e <_start-0x60fb83c2>
 23c:	6e69                	lui	t3,0x1a
 23e:	0074                	addi	a3,sp,12
 240:	7562                	ld	a0,56(sp)
 242:	6666                	ld	a2,88(sp)
 244:	7265                	lui	tp,0xffff9
 246:	6c00                	ld	s0,24(s0)
 248:	20676e6f          	jal	t3,7644e <_start-0x60f89bb2>
 24c:	6e69                	lui	t3,0x1a
 24e:	0074                	addi	a3,sp,12
 250:	61656c63          	bltu	a0,s6,868 <_start-0x60fff798>
 254:	5f72                	lw	t5,60(sp)
 256:	6572                	ld	a0,280(sp)
 258:	6c660067          	jr	1734(a2) # b6c6 <_start-0x60ff493a>
 25c:	6761                	lui	a4,0x18
 25e:	6f5f 6666 6573      	0x657366666f5f
 264:	0074                	addi	a3,sp,12
 266:	3a44                	fld	fs1,176(a2)
 268:	726f772f          	0x726f772f
 26c:	30322f6b          	0x30322f6b
 270:	3232                	fld	ft4,296(sp)
 272:	396c612f          	0x396c612f
 276:	3030                	fld	fa2,96(s0)
 278:	2f30                	fld	fa2,88(a4)
 27a:	642f7773          	csrrci	a4,0x642,30
 27e:	6265                	lui	tp,0x19
 280:	6775                	lui	a4,0x1d
 282:	2f726567          	0x2f726567
 286:	6c61                	lui	s8,0x18
 288:	6f5f 6570 6f6e      	0x6f6e65706f5f
 28e:	632f6463          	bltu	t5,s2,8b6 <_start-0x60fff74a>
 292:	72746e6f          	jal	t3,471b8 <_start-0x60fb8e48>
 296:	6269                	lui	tp,0x1a
 298:	616f6c2f          	0x616f6c2f
 29c:	6564                	ld	s1,200(a0)
 29e:	7372                	ld	t1,312(sp)
 2a0:	616c662f          	0x616c662f
 2a4:	652f6873          	csrrsi	a6,0x652,30
 2a8:	6d6d                	lui	s10,0x1b
 2aa:	77642f63          	0x77642f63
 2ae:	68736d63          	bltu	t1,t2,948 <_start-0x60fff6b8>
 2b2:	72732f63          	0x72732f63
 2b6:	77642f63          	0x77642f63
 2ba:	68736d63          	bltu	t1,t2,954 <_start-0x60fff6ac>
 2be:	79735f63          	bge	t1,s7,a5c <_start-0x60fff5a4>
 2c2:	636e                	ld	t1,216(sp)
 2c4:	632e                	ld	t1,200(sp)
 2c6:	7300                	ld	s0,32(a4)
 2c8:	7a69                	lui	s4,0xffffa
 2ca:	5f65                	li	t5,-7
 2cc:	6e69                	lui	t3,0x1a
 2ce:	625f 7479 7365      	0x73657479625f
 2d4:	6200                	ld	s0,0(a2)
 2d6:	6f6c                	ld	a1,216(a4)
 2d8:	615f6b63          	bltu	t5,s5,8ee <_start-0x60fff712>
 2dc:	6464                	ld	s1,200(s0)
 2de:	0072                	c.slli	zero,0x1c
 2e0:	6c62                	ld	s8,24(sp)
 2e2:	5f6b636f          	jal	t1,b68d8 <_start-0x60f49728>
 2e6:	657a6973          	csrrsi	s2,0x657,20
 2ea:	6500                	ld	s0,8(a0)
 2ec:	6d6d                	lui	s10,0x1b
 2ee:	77645f63          	bge	s0,s6,a6c <_start-0x60fff594>
 2f2:	68736d63          	bltu	t1,t2,98c <_start-0x60fff674>
 2f6:	6c620063          	beq	tp,t1,9b6 <_start-0x60fff64a>
 2fa:	5f6b636f          	jal	t1,b68f0 <_start-0x60f49710>
 2fe:	6461                	lui	s0,0x18
 300:	7264                	ld	s1,224(a2)
 302:	735f 6968 7466      	0x74666968735f
	...

Disassembly of section .debug_loc:

0000000000000000 <.debug_loc>:
   0:	00c4                	addi	s1,sp,68
   2:	0000                	unimp
   4:	0000                	unimp
   6:	0000                	unimp
   8:	000000fb          	dkhmx8	ra,zero,zero
   c:	0000                	unimp
   e:	0000                	unimp
  10:	0001                	nop
  12:	fb5a                	sd	s6,432(sp)
  14:	0000                	unimp
  16:	0000                	unimp
  18:	0000                	unimp
  1a:	1000                	addi	s0,sp,32
  1c:	0001                	nop
  1e:	0000                	unimp
  20:	0000                	unimp
  22:	0100                	addi	s0,sp,128
  24:	5800                	lw	s0,48(s0)
  26:	0110                	addi	a2,sp,128
  28:	0000                	unimp
  2a:	0000                	unimp
  2c:	0000                	unimp
  2e:	00000127          	vse8.v	v2,(zero),v0.t
  32:	0000                	unimp
  34:	0000                	unimp
  36:	0001                	nop
  38:	275a                	fld	fa4,400(sp)
  3a:	0001                	nop
  3c:	0000                	unimp
  3e:	0000                	unimp
  40:	2800                	fld	fs0,16(s0)
  42:	0001                	nop
  44:	0000                	unimp
  46:	0000                	unimp
  48:	0400                	addi	s0,sp,512
  4a:	f300                	sd	s0,32(a4)
  4c:	5a01                	li	s4,-32
  4e:	289f 0001 0000      	0x1289f
  54:	0000                	unimp
  56:	4000                	lw	s0,0(s0)
  58:	0001                	nop
  5a:	0000                	unimp
  5c:	0000                	unimp
  5e:	0100                	addi	s0,sp,128
  60:	5800                	lw	s0,48(s0)
	...
  72:	00c4                	addi	s1,sp,68
  74:	0000                	unimp
  76:	0000                	unimp
  78:	0000                	unimp
  7a:	00f0                	addi	a2,sp,76
  7c:	0000                	unimp
  7e:	0000                	unimp
  80:	0000                	unimp
  82:	0001                	nop
  84:	0000f05b          	0xf05b
  88:	0000                	unimp
  8a:	0000                	unimp
  8c:	1c00                	addi	s0,sp,560
  8e:	0001                	nop
  90:	0000                	unimp
  92:	0000                	unimp
  94:	0100                	addi	s0,sp,128
  96:	6200                	ld	s0,0(a2)
  98:	011c                	addi	a5,sp,128
  9a:	0000                	unimp
  9c:	0000                	unimp
  9e:	0000                	unimp
  a0:	0128                	addi	a0,sp,136
  a2:	0000                	unimp
  a4:	0000                	unimp
  a6:	0000                	unimp
  a8:	0004                	0x4
  aa:	9f5b01f3          	0x9f5b01f3
  ae:	0128                	addi	a0,sp,136
  b0:	0000                	unimp
  b2:	0000                	unimp
  b4:	0000                	unimp
  b6:	0140                	addi	s0,sp,132
  b8:	0000                	unimp
  ba:	0000                	unimp
  bc:	0000                	unimp
  be:	0001                	nop
  c0:	0062                	c.slli	zero,0x18
	...
  ce:	0000                	unimp
  d0:	c400                	sw	s0,8(s0)
  d2:	0000                	unimp
  d4:	0000                	unimp
  d6:	0000                	unimp
  d8:	fb00                	sd	s0,48(a4)
  da:	0000                	unimp
  dc:	0000                	unimp
  de:	0000                	unimp
  e0:	0100                	addi	s0,sp,128
  e2:	5c00                	lw	s0,56(s0)
  e4:	000000fb          	dkhmx8	ra,zero,zero
  e8:	0000                	unimp
  ea:	0000                	unimp
  ec:	0140                	addi	s0,sp,132
  ee:	0000                	unimp
  f0:	0000                	unimp
  f2:	0000                	unimp
  f4:	0004                	0x4
  f6:	9f5c01f3          	0x9f5c01f3
	...
 10a:	00c4                	addi	s1,sp,68
 10c:	0000                	unimp
 10e:	0000                	unimp
 110:	0000                	unimp
 112:	000000fb          	dkhmx8	ra,zero,zero
 116:	0000                	unimp
 118:	0000                	unimp
 11a:	0001                	nop
 11c:	fb5d                	bnez	a4,d2 <_start-0x60ffff2e>
 11e:	0000                	unimp
 120:	0000                	unimp
 122:	0000                	unimp
 124:	4000                	lw	s0,0(s0)
 126:	0001                	nop
 128:	0000                	unimp
 12a:	0000                	unimp
 12c:	0400                	addi	s0,sp,512
 12e:	f300                	sd	s0,32(a4)
 130:	5d01                	li	s10,-32
 132:	009f 0000 0000      	0x9f
	...
 140:	0000                	unimp
 142:	fc00                	sd	s0,56(s0)
	...
 14c:	0001                	nop
 14e:	0000                	unimp
 150:	0000                	unimp
 152:	0200                	addi	s0,sp,256
 154:	3000                	fld	fs0,32(s0)
 156:	009f 0001 0000      	0x1009f
 15c:	0000                	unimp
 15e:	2700                	fld	fs0,8(a4)
 160:	0001                	nop
 162:	0000                	unimp
 164:	0000                	unimp
 166:	0100                	addi	s0,sp,128
 168:	5f00                	lw	s0,56(a4)
 16a:	0128                	addi	a0,sp,136
 16c:	0000                	unimp
 16e:	0000                	unimp
 170:	0000                	unimp
 172:	0138                	addi	a4,sp,136
 174:	0000                	unimp
 176:	0000                	unimp
 178:	0000                	unimp
 17a:	0001                	nop
 17c:	385f 0001 0000      	0x1385f
 182:	0000                	unimp
 184:	3c00                	fld	fs0,56(s0)
 186:	0001                	nop
 188:	0000                	unimp
 18a:	0000                	unimp
 18c:	0300                	addi	s0,sp,384
 18e:	7f00                	ld	s0,56(a4)
 190:	9f01                	subw	a4,a4,s0
	...
 1a2:	005c                	addi	a5,sp,4
 1a4:	0000                	unimp
 1a6:	0000                	unimp
 1a8:	0000                	unimp
 1aa:	0088                	addi	a0,sp,64
 1ac:	0000                	unimp
 1ae:	0000                	unimp
 1b0:	0000                	unimp
 1b2:	0001                	nop
 1b4:	885c                	0x885c
 1b6:	0000                	unimp
 1b8:	0000                	unimp
 1ba:	0000                	unimp
 1bc:	9800                	0x9800
 1be:	0000                	unimp
 1c0:	0000                	unimp
 1c2:	0000                	unimp
 1c4:	0400                	addi	s0,sp,512
 1c6:	7f00                	ld	s0,56(a4)
 1c8:	7c80                	ld	s0,56(s1)
 1ca:	989f 0000 0000      	0x989f
 1d0:	0000                	unimp
 1d2:	c400                	sw	s0,8(s0)
 1d4:	0000                	unimp
 1d6:	0000                	unimp
 1d8:	0000                	unimp
 1da:	0400                	addi	s0,sp,512
 1dc:	f300                	sd	s0,32(a4)
 1de:	5c01                	li	s8,-32
 1e0:	009f 0000 0000      	0x9f
	...
 1ee:	0000                	unimp
 1f0:	8800                	0x8800
 1f2:	0000                	unimp
 1f4:	0000                	unimp
 1f6:	0000                	unimp
 1f8:	9000                	0x9000
 1fa:	0000                	unimp
 1fc:	0000                	unimp
 1fe:	0000                	unimp
 200:	0b00                	addi	s0,sp,400
 202:	7c00                	ld	s0,56(s0)
 204:	7f00                	ld	s0,56(a4)
 206:	1c00                	addi	s0,sp,560
 208:	32048023          	sb	zero,800(s1)
 20c:	9f25                	addw	a4,a4,s1
 20e:	0090                	addi	a2,sp,64
 210:	0000                	unimp
 212:	0000                	unimp
 214:	0000                	unimp
 216:	0094                	addi	a3,sp,64
 218:	0000                	unimp
 21a:	0000                	unimp
 21c:	0000                	unimp
 21e:	007c000b          	0x7c000b
 222:	231c007f          	ursub16	zero,s8,a7
 226:	03fc                	addi	a5,sp,460
 228:	2532                	fld	fa0,264(sp)
 22a:	009f 0000 0000      	0x9f
	...
 238:	0000                	unimp
 23a:	7400                	ld	s0,40(s0)
 23c:	0000                	unimp
 23e:	0000                	unimp
 240:	0000                	unimp
 242:	8800                	0x8800
 244:	0000                	unimp
 246:	0000                	unimp
 248:	0000                	unimp
 24a:	0100                	addi	s0,sp,128
 24c:	5e00                	lw	s0,56(a2)
 24e:	00a0                	addi	s0,sp,72
 250:	0000                	unimp
 252:	0000                	unimp
 254:	0000                	unimp
 256:	00b0                	addi	a2,sp,72
 258:	0000                	unimp
 25a:	0000                	unimp
 25c:	0000                	unimp
 25e:	0001                	nop
 260:	b05f 0000 0000      	0xb05f
 266:	0000                	unimp
 268:	c400                	sw	s0,8(s0)
 26a:	0000                	unimp
 26c:	0000                	unimp
 26e:	0000                	unimp
 270:	0100                	addi	s0,sp,128
 272:	5e00                	lw	s0,56(a2)
	...
 284:	0074                	addi	a3,sp,12
 286:	0000                	unimp
 288:	0000                	unimp
 28a:	0000                	unimp
 28c:	0088                	addi	a0,sp,64
 28e:	0000                	unimp
 290:	0000                	unimp
 292:	0000                	unimp
 294:	007e0007          	0x7e0007
 298:	2534                	fld	fa3,72(a0)
 29a:	1a31                	addi	s4,s4,-20
 29c:	a09f 0000 0000      	0xa09f
 2a2:	0000                	unimp
 2a4:	b000                	fsd	fs0,32(s0)
 2a6:	0000                	unimp
 2a8:	0000                	unimp
 2aa:	0000                	unimp
 2ac:	0700                	addi	s0,sp,896
 2ae:	7f00                	ld	s0,56(a4)
 2b0:	3100                	fld	fs0,32(a0)
 2b2:	3125                	addiw	sp,sp,-23
 2b4:	9f1a                	add	t5,t5,t1
 2b6:	00b0                	addi	a2,sp,72
 2b8:	0000                	unimp
 2ba:	0000                	unimp
 2bc:	0000                	unimp
 2be:	00c4                	addi	s1,sp,68
 2c0:	0000                	unimp
 2c2:	0000                	unimp
 2c4:	0000                	unimp
 2c6:	007e0007          	0x7e0007
 2ca:	2531                	addiw	a0,a0,12
 2cc:	1a31                	addi	s4,s4,-20
 2ce:	009f 0000 0000      	0x9f
	...
 2dc:	0000                	unimp
 2de:	1c00                	addi	s0,sp,560
 2e0:	0000                	unimp
 2e2:	0000                	unimp
 2e4:	0000                	unimp
 2e6:	5800                	lw	s0,48(s0)
 2e8:	0000                	unimp
 2ea:	0000                	unimp
 2ec:	0000                	unimp
 2ee:	0100                	addi	s0,sp,128
 2f0:	5a00                	lw	s0,48(a2)
 2f2:	0058                	addi	a4,sp,4
 2f4:	0000                	unimp
 2f6:	0000                	unimp
 2f8:	0000                	unimp
 2fa:	005c                	addi	a5,sp,4
 2fc:	0000                	unimp
 2fe:	0000                	unimp
 300:	0000                	unimp
 302:	0004                	0x4
 304:	9f5a01f3          	0x9f5a01f3
	...
 318:	002c                	addi	a1,sp,8
 31a:	0000                	unimp
 31c:	0000                	unimp
 31e:	0000                	unimp
 320:	003c                	addi	a5,sp,8
 322:	0000                	unimp
 324:	0000                	unimp
 326:	0000                	unimp
 328:	0001                	nop
 32a:	3c5e                	fld	fs8,496(sp)
 32c:	0000                	unimp
 32e:	0000                	unimp
 330:	0000                	unimp
 332:	5c00                	lw	s0,56(s0)
 334:	0000                	unimp
 336:	0000                	unimp
 338:	0000                	unimp
 33a:	0100                	addi	s0,sp,128
 33c:	5c00                	lw	s0,56(s0)
	...
 34e:	001c                	0x1c
 350:	0000                	unimp
 352:	0000                	unimp
 354:	0000                	unimp
 356:	0024                	addi	s1,sp,8
 358:	0000                	unimp
 35a:	0000                	unimp
 35c:	0000                	unimp
 35e:	0002                	c.slli64	zero
 360:	9f30                	0x9f30
 362:	002c                	addi	a1,sp,8
 364:	0000                	unimp
 366:	0000                	unimp
 368:	0000                	unimp
 36a:	003c                	addi	a5,sp,8
 36c:	0000                	unimp
 36e:	0000                	unimp
 370:	0000                	unimp
 372:	0008                	0x8
 374:	007e                	c.slli	zero,0x1f
 376:	3125007b          	dkmda32	zero,a0,s2
 37a:	9f1a                	add	t5,t5,t1
 37c:	003c                	addi	a5,sp,8
 37e:	0000                	unimp
 380:	0000                	unimp
 382:	0000                	unimp
 384:	005c                	addi	a5,sp,4
 386:	0000                	unimp
 388:	0000                	unimp
 38a:	0000                	unimp
 38c:	0008                	0x8
 38e:	007c                	addi	a5,sp,12
 390:	3125007b          	dkmda32	zero,a0,s2
 394:	9f1a                	add	t5,t5,t1
	...
 3ae:	0004                	0x4
 3b0:	0000                	unimp
 3b2:	0000                	unimp
 3b4:	0000                	unimp
 3b6:	0001                	nop
 3b8:	045a                	slli	s0,s0,0x16
 3ba:	0000                	unimp
 3bc:	0000                	unimp
 3be:	0000                	unimp
 3c0:	1c00                	addi	s0,sp,560
 3c2:	0000                	unimp
 3c4:	0000                	unimp
 3c6:	0000                	unimp
 3c8:	0100                	addi	s0,sp,128
 3ca:	5e00                	lw	s0,56(a2)
	...
 3dc:	0004                	0x4
 3de:	0000                	unimp
 3e0:	0000                	unimp
 3e2:	0000                	unimp
 3e4:	0010                	0x10
 3e6:	0000                	unimp
 3e8:	0000                	unimp
 3ea:	0000                	unimp
 3ec:	0002                	c.slli64	zero
 3ee:	9f30                	0x9f30
 3f0:	0010                	0x10
 3f2:	0000                	unimp
 3f4:	0000                	unimp
 3f6:	0000                	unimp
 3f8:	001c                	0x1c
 3fa:	0000                	unimp
 3fc:	0000                	unimp
 3fe:	0000                	unimp
 400:	0001                	nop
 402:	005d                	c.nop	23
	...
 410:	0000                	unimp
 412:	0400                	addi	s0,sp,512
 414:	0000                	unimp
 416:	0000                	unimp
 418:	0000                	unimp
 41a:	1400                	addi	s0,sp,544
 41c:	0000                	unimp
 41e:	0000                	unimp
 420:	0000                	unimp
 422:	0200                	addi	s0,sp,256
 424:	3000                	fld	fs0,32(s0)
 426:	149f 0000 0000      	0x149f
 42c:	0000                	unimp
 42e:	1c00                	addi	s0,sp,560
 430:	0000                	unimp
 432:	0000                	unimp
 434:	0000                	unimp
 436:	0100                	addi	s0,sp,128
 438:	5a00                	lw	s0,48(a2)
	...
 452:	0070                	addi	a2,sp,12
 454:	0000                	unimp
 456:	0000                	unimp
 458:	0000                	unimp
 45a:	0001                	nop
 45c:	705a                	0x705a
 45e:	0000                	unimp
 460:	0000                	unimp
 462:	0000                	unimp
 464:	8c00                	0x8c00
 466:	0000                	unimp
 468:	0000                	unimp
 46a:	0000                	unimp
 46c:	0100                	addi	s0,sp,128
 46e:	6400                	ld	s0,8(s0)
 470:	008c                	addi	a1,sp,64
 472:	0000                	unimp
 474:	0000                	unimp
 476:	0000                	unimp
 478:	0098                	addi	a4,sp,64
 47a:	0000                	unimp
 47c:	0000                	unimp
 47e:	0000                	unimp
 480:	0004                	0x4
 482:	9f5a01f3          	0x9f5a01f3
 486:	0098                	addi	a4,sp,64
 488:	0000                	unimp
 48a:	0000                	unimp
 48c:	0000                	unimp
 48e:	00b8                	addi	a4,sp,72
 490:	0000                	unimp
 492:	0000                	unimp
 494:	0000                	unimp
 496:	0001                	nop
 498:	0064                	addi	s1,sp,12
	...
 4ae:	0000                	unimp
 4b0:	6400                	ld	s0,8(s0)
 4b2:	0000                	unimp
 4b4:	0000                	unimp
 4b6:	0000                	unimp
 4b8:	0100                	addi	s0,sp,128
 4ba:	5b00                	lw	s0,48(a4)
 4bc:	0064                	addi	s1,sp,12
 4be:	0000                	unimp
 4c0:	0000                	unimp
 4c2:	0000                	unimp
 4c4:	00b8                	addi	a4,sp,72
 4c6:	0000                	unimp
 4c8:	0000                	unimp
 4ca:	0000                	unimp
 4cc:	0004                	0x4
 4ce:	9f5b01f3          	0x9f5b01f3
	...
 4ea:	0048                	addi	a0,sp,4
 4ec:	0000                	unimp
 4ee:	0000                	unimp
 4f0:	0000                	unimp
 4f2:	0001                	nop
 4f4:	485c                	lw	a5,20(s0)
 4f6:	0000                	unimp
 4f8:	0000                	unimp
 4fa:	0000                	unimp
 4fc:	b800                	fsd	fs0,48(s0)
 4fe:	0000                	unimp
 500:	0000                	unimp
 502:	0000                	unimp
 504:	0400                	addi	s0,sp,512
 506:	f300                	sd	s0,32(a4)
 508:	5c01                	li	s8,-32
 50a:	009f 0000 0000      	0x9f
	...
 520:	0000                	unimp
 522:	4800                	lw	s0,16(s0)
 524:	0000                	unimp
 526:	0000                	unimp
 528:	0000                	unimp
 52a:	0100                	addi	s0,sp,128
 52c:	5d00                	lw	s0,56(a0)
 52e:	0048                	addi	a0,sp,4
 530:	0000                	unimp
 532:	0000                	unimp
 534:	0000                	unimp
 536:	0088                	addi	a0,sp,64
 538:	0000                	unimp
 53a:	0000                	unimp
 53c:	0000                	unimp
 53e:	0001                	nop
 540:	00009863          	bnez	ra,550 <_start-0x60fffab0>
 544:	0000                	unimp
 546:	0000                	unimp
 548:	b800                	fsd	fs0,48(s0)
 54a:	0000                	unimp
 54c:	0000                	unimp
 54e:	0000                	unimp
 550:	0100                	addi	s0,sp,128
 552:	6300                	ld	s0,0(a4)
	...
 56c:	0014                	0x14
 56e:	0000                	unimp
 570:	0000                	unimp
 572:	0000                	unimp
 574:	0001                	nop
 576:	145e                	slli	s0,s0,0x37
 578:	0000                	unimp
 57a:	0000                	unimp
 57c:	0000                	unimp
 57e:	7000                	ld	s0,32(s0)
 580:	0000                	unimp
 582:	0000                	unimp
 584:	0000                	unimp
 586:	0400                	addi	s0,sp,512
 588:	f300                	sd	s0,32(a4)
 58a:	5e01                	li	t3,-32
 58c:	709f 0000 0000      	0x709f
 592:	0000                	unimp
 594:	8000                	0x8000
 596:	0000                	unimp
 598:	0000                	unimp
 59a:	0000                	unimp
 59c:	0100                	addi	s0,sp,128
 59e:	5900                	lw	s0,48(a0)
 5a0:	0098                	addi	a4,sp,64
 5a2:	0000                	unimp
 5a4:	0000                	unimp
 5a6:	0000                	unimp
 5a8:	00ac                	addi	a1,sp,72
 5aa:	0000                	unimp
 5ac:	0000                	unimp
 5ae:	0000                	unimp
 5b0:	0001                	nop
 5b2:	0059                	c.nop	22
	...
 5c0:	0000                	unimp
 5c2:	4800                	lw	s0,16(s0)
 5c4:	0000                	unimp
 5c6:	0000                	unimp
 5c8:	0000                	unimp
 5ca:	5800                	lw	s0,48(s0)
 5cc:	0000                	unimp
 5ce:	0000                	unimp
 5d0:	0000                	unimp
 5d2:	0100                	addi	s0,sp,128
 5d4:	5f00                	lw	s0,56(a4)
	...
 5e6:	0070                	addi	a2,sp,12
 5e8:	0000                	unimp
 5ea:	0000                	unimp
 5ec:	0000                	unimp
 5ee:	007c                	addi	a5,sp,12
 5f0:	0000                	unimp
 5f2:	0000                	unimp
 5f4:	0000                	unimp
 5f6:	0001                	nop
 5f8:	9858                	0x9858
 5fa:	0000                	unimp
 5fc:	0000                	unimp
 5fe:	0000                	unimp
 600:	b000                	fsd	fs0,32(s0)
 602:	0000                	unimp
 604:	0000                	unimp
 606:	0000                	unimp
 608:	0100                	addi	s0,sp,128
 60a:	5800                	lw	s0,48(s0)
	...

Disassembly of section .debug_frame:

0000000000000000 <.debug_frame>:
   0:	000c                	0xc
   2:	0000                	unimp
   4:	ffffffff          	0xffffffff
   8:	7c010003          	lb	zero,1984(sp)
   c:	0d01                	addi	s10,s10,0
   e:	0002                	c.slli64	zero
  10:	0014                	0x14
  12:	0000                	unimp
  14:	0000                	unimp
  16:	0000                	unimp
  18:	0010                	0x10
  1a:	6100                	ld	s0,0(a0)
  1c:	0000                	unimp
  1e:	0000                	unimp
  20:	001c                	0x1c
  22:	0000                	unimp
  24:	0000                	unimp
  26:	0000                	unimp
  28:	0014                	0x14
  2a:	0000                	unimp
  2c:	0000                	unimp
  2e:	0000                	unimp
  30:	002c                	addi	a1,sp,8
  32:	6100                	ld	s0,0(a0)
  34:	0000                	unimp
  36:	0000                	unimp
  38:	0040                	addi	s0,sp,4
  3a:	0000                	unimp
  3c:	0000                	unimp
  3e:	0000                	unimp
  40:	0014                	0x14
  42:	0000                	unimp
  44:	0000                	unimp
  46:	0000                	unimp
  48:	006c                	addi	a1,sp,12
  4a:	6100                	ld	s0,0(a0)
  4c:	0000                	unimp
  4e:	0000                	unimp
  50:	0068                	addi	a0,sp,12
  52:	0000                	unimp
  54:	0000                	unimp
  56:	0000                	unimp
  58:	0034                	addi	a3,sp,8
  5a:	0000                	unimp
  5c:	0000                	unimp
  5e:	0000                	unimp
  60:	00d4                	addi	a3,sp,68
  62:	6100                	ld	s0,0(a0)
  64:	0000                	unimp
  66:	0000                	unimp
  68:	007c                	addi	a5,sp,12
  6a:	0000                	unimp
  6c:	0000                	unimp
  6e:	0000                	unimp
  70:	0e44                	addi	s1,sp,788
  72:	5020                	lw	s0,96(s0)
  74:	0488                	addi	a0,sp,576
  76:	0689                	addi	a3,a3,2
  78:	0892                	slli	a7,a7,0x4
  7a:	0281                	addi	t0,t0,0
  7c:	0a78                	addi	a4,sp,284
  7e:	44c8                	lw	a0,12(s1)
  80:	44c1                	li	s1,16
  82:	44c9                	li	s1,18
  84:	48d2                	lw	a7,20(sp)
  86:	000e                	c.slli	zero,0x3
  88:	0b44                	addi	s1,sp,404
  8a:	0000                	unimp
  8c:	0000                	unimp
  8e:	0000                	unimp
  90:	000c                	0xc
  92:	0000                	unimp
  94:	ffffffff          	0xffffffff
  98:	7c010003          	lb	zero,1984(sp)
  9c:	0d01                	addi	s10,s10,0
  9e:	0002                	c.slli64	zero
  a0:	003c                	addi	a5,sp,8
  a2:	0000                	unimp
  a4:	0090                	addi	a2,sp,64
  a6:	0000                	unimp
  a8:	0150                	addi	a2,sp,132
  aa:	6100                	ld	s0,0(a0)
  ac:	0000                	unimp
  ae:	0000                	unimp
  b0:	00b8                	addi	a4,sp,72
  b2:	0000                	unimp
  b4:	0000                	unimp
  b6:	0000                	unimp
  b8:	0e44                	addi	s1,sp,788
  ba:	4440                	lw	s0,12(s0)
  bc:	0689                	addi	a3,a3,2
  be:	8868                	0x8868
  c0:	9204                	0x9204
  c2:	9308                	0x9308
  c4:	940a                	add	s0,s0,sp
  c6:	810c                	0x810c
  c8:	9502                	jalr	a0
  ca:	020e                	slli	tp,tp,0x3
  cc:	0a48                	addi	a0,sp,276
  ce:	44c1                	li	s1,16
  d0:	44c8                	lw	a0,12(s1)
  d2:	44c9                	li	s1,18
  d4:	44d2                	lw	s1,20(sp)
  d6:	44d444d3          	0x44d444d3
  da:	44d5                	li	s1,21
  dc:	000e                	c.slli	zero,0x3
  de:	0b44                	addi	s1,sp,404
