@echo off
echo Building JTAG Writer with MSYS2/MinGW64...

REM Set compiler path
set GCC=C:\msys64\mingw64\bin\gcc.exe
set CFLAGS=-Wall -Wextra -std=c99 -Iinclude
set LDFLAGS=-lws2_32

REM Check if compiler exists
if not exist "%GCC%" (
    echo Error: GCC compiler not found at %GCC%
    echo Please ensure MSYS2 and MinGW64 are installed
    pause
    exit /b 1
)

echo Compiler found: %GCC%

REM Create build directories
if not exist build\obj\core mkdir build\obj\core
if not exist build\obj\network mkdir build\obj\network
if not exist build\obj\config mkdir build\obj\config
if not exist build\obj\ui mkdir build\obj\ui
if not exist build\obj\utils mkdir build\obj\utils

echo Compiling source files...

REM Compile main.c
echo Compiling main.c...
"%GCC%" %CFLAGS% -c src/main.c -o build/obj/main.o
if %errorlevel% neq 0 (
    echo Failed to compile main.c
    pause
    exit /b 1
)

REM Compile core files
echo Compiling core files...
"%GCC%" %CFLAGS% -c src/core/jtag_writer.c -o build/obj/core/jtag_writer.o
if %errorlevel% neq 0 (
    echo Failed to compile jtag_writer.c
    pause
    exit /b 1
)

"%GCC%" %CFLAGS% -c src/core/flash_operations.c -o build/obj/core/flash_operations.o
if %errorlevel% neq 0 (
    echo Failed to compile flash_operations.c
    pause
    exit /b 1
)

REM Compile network files
echo Compiling network files...
"%GCC%" %CFLAGS% -c src/network/openocd_client.c -o build/obj/network/openocd_client.o
if %errorlevel% neq 0 (
    echo Failed to compile openocd_client.c
    pause
    exit /b 1
)

REM Compile config files
echo Compiling config files...
"%GCC%" %CFLAGS% -c src/config/config_manager.c -o build/obj/config/config_manager.o
if %errorlevel% neq 0 (
    echo Failed to compile config_manager.c
    pause
    exit /b 1
)

REM Compile UI files
echo Compiling UI files...
"%GCC%" %CFLAGS% -c src/ui/cli_interface.c -o build/obj/ui/cli_interface.o
if %errorlevel% neq 0 (
    echo Failed to compile cli_interface.c
    pause
    exit /b 1
)

REM Compile utils files
echo Compiling utils files...
"%GCC%" %CFLAGS% -c src/utils/logger.c -o build/obj/utils/logger.o
if %errorlevel% neq 0 (
    echo Failed to compile logger.c
    pause
    exit /b 1
)

REM Link executable
echo Linking executable...
"%GCC%" build/obj/main.o build/obj/core/jtag_writer.o build/obj/core/flash_operations.o build/obj/network/openocd_client.o build/obj/config/config_manager.o build/obj/ui/cli_interface.o build/obj/utils/logger.o -o build/jtag_writer.exe %LDFLAGS%
if %errorlevel% neq 0 (
    echo Failed to link executable
    pause
    exit /b 1
)

echo Build successful!
echo Executable created: build\jtag_writer.exe

REM Test the executable
echo Testing executable...
build\jtag_writer.exe --version
if %errorlevel% neq 0 (
    echo Warning: Executable test failed
)

echo Build complete!
pause
