
D:/work/2022/al9000/sw/debugger/al_openocd/contrib/loaders/flash/emmc/dwcmshc/build/emmc_async_aarch_64.elf:     file format elf64-littleaarch64


Disassembly of section .text:

0000000061000000 <_start>:
    61000000:	5800009c 	ldr	x28, 61000010 <_start+0x10>
    61000004:	9100039f 	mov	sp, x28
    61000008:	94000047 	bl	61000124 <emmc_dwcmshc>
    6100000c:	d4400160 	hlt	#0xb
    61000010:	610003c0 	.inst	0x610003c0 ; undefined
    61000014:	00000000 	udf	#0

0000000061000018 <emmc_wait_fifo>:
    61000018:	aa0003e1 	mov	x1, x0
    6100001c:	b9400022 	ldr	w2, [x1]
    61000020:	b9400420 	ldr	w0, [x1, #4]
    61000024:	6b00005f 	cmp	w2, w0
    61000028:	54ffffa0 	b.eq	6100001c <emmc_wait_fifo+0x4>  // b.none
    6100002c:	d65f03c0 	ret

0000000061000030 <emmc_poll_int>:
    61000030:	52800022 	mov	w2, #0x1                   	// #1
    61000034:	12001c23 	and	w3, w1, #0xff
    61000038:	1ac12041 	lsl	w1, w2, w1
    6100003c:	b9403002 	ldr	w2, [x0, #48]
    61000040:	6a02003f 	tst	w1, w2
    61000044:	54ffffc0 	b.eq	6100003c <emmc_poll_int+0xc>  // b.none
    61000048:	6b4243ff 	cmp	wzr, w2, lsr #16
    6100004c:	54ffff81 	b.ne	6100003c <emmc_poll_int+0xc>  // b.any
    61000050:	52800021 	mov	w1, #0x1                   	// #1
    61000054:	1ac32021 	lsl	w1, w1, w3
    61000058:	2a020021 	orr	w1, w1, w2
    6100005c:	b9003001 	str	w1, [x0, #48]
    61000060:	52800000 	mov	w0, #0x0                   	// #0
    61000064:	d65f03c0 	ret

0000000061000068 <emmc_write_block>:
    61000068:	b9000801 	str	w1, [x0, #8]
    6100006c:	52801041 	mov	w1, #0x82                  	// #130
    61000070:	72a30741 	movk	w1, #0x183a, lsl #16
    61000074:	b9000c01 	str	w1, [x0, #12]
    61000078:	b9403001 	ldr	w1, [x0, #48]
    6100007c:	3627ffe1 	tbz	w1, #4, 61000078 <emmc_write_block+0x10>
    61000080:	6b4143ff 	cmp	wzr, w1, lsr #16
    61000084:	54ffffa1 	b.ne	61000078 <emmc_write_block+0x10>  // b.any
    61000088:	d2800001 	mov	x1, #0x0                   	// #0
    6100008c:	b8617843 	ldr	w3, [x2, x1, lsl #2]
    61000090:	91000421 	add	x1, x1, #0x1
    61000094:	b9002003 	str	w3, [x0, #32]
    61000098:	f102003f 	cmp	x1, #0x80
    6100009c:	54ffff81 	b.ne	6100008c <emmc_write_block+0x24>  // b.any
    610000a0:	b9403001 	ldr	w1, [x0, #48]
    610000a4:	360fffe1 	tbz	w1, #1, 610000a0 <emmc_write_block+0x38>
    610000a8:	6b4143ff 	cmp	wzr, w1, lsr #16
    610000ac:	54ffffa1 	b.ne	610000a0 <emmc_write_block+0x38>  // b.any
    610000b0:	321f0021 	orr	w1, w1, #0x2
    610000b4:	b9003001 	str	w1, [x0, #48]
    610000b8:	d65f03c0 	ret

00000000610000bc <emmc_read_block>:
    610000bc:	a9bd7bfd 	stp	x29, x30, [sp, #-48]!
    610000c0:	910003fd 	mov	x29, sp
    610000c4:	a90153f3 	stp	x19, x20, [sp, #16]
    610000c8:	aa0003f3 	mov	x19, x0
    610000cc:	2a0303f4 	mov	w20, w3
    610000d0:	f90013f5 	str	x21, [sp, #32]
    610000d4:	aa0103f5 	mov	x21, x1
    610000d8:	52801241 	mov	w1, #0x92                  	// #146
    610000dc:	b9000802 	str	w2, [x0, #8]
    610000e0:	72a22741 	movk	w1, #0x113a, lsl #16
    610000e4:	b9000c01 	str	w1, [x0, #12]
    610000e8:	528000a1 	mov	w1, #0x5                   	// #5
    610000ec:	97ffffd1 	bl	61000030 <emmc_poll_int>
    610000f0:	d2800000 	mov	x0, #0x0                   	// #0
    610000f4:	6b00029f 	cmp	w20, w0
    610000f8:	540000e8 	b.hi	61000114 <emmc_read_block+0x58>  // b.pmore
    610000fc:	aa1303e0 	mov	x0, x19
    61000100:	52800021 	mov	w1, #0x1                   	// #1
    61000104:	a94153f3 	ldp	x19, x20, [sp, #16]
    61000108:	f94013f5 	ldr	x21, [sp, #32]
    6100010c:	a8c37bfd 	ldp	x29, x30, [sp], #48
    61000110:	17ffffc8 	b	61000030 <emmc_poll_int>
    61000114:	b9402261 	ldr	w1, [x19, #32]
    61000118:	b8207aa1 	str	w1, [x21, x0, lsl #2]
    6100011c:	91000400 	add	x0, x0, #0x1
    61000120:	17fffff5 	b	610000f4 <emmc_read_block+0x38>

0000000061000124 <emmc_dwcmshc>:
    61000124:	a9ba7bfd 	stp	x29, x30, [sp, #-96]!
    61000128:	910003fd 	mov	x29, sp
    6100012c:	a9046bf9 	stp	x25, x26, [sp, #64]
    61000130:	13027c3a 	asr	w26, w1, #2
    61000134:	aa0403f9 	mov	x25, x4
    61000138:	937e7f5a 	sbfiz	x26, x26, #2, #32
    6100013c:	a90153f3 	stp	x19, x20, [sp, #16]
    61000140:	2a0203f4 	mov	w20, w2
    61000144:	a9025bf5 	stp	x21, x22, [sp, #32]
    61000148:	aa0303f5 	mov	x21, x3
    6100014c:	2a0503f6 	mov	w22, w5
    61000150:	a90363f7 	stp	x23, x24, [sp, #48]
    61000154:	aa0003f7 	mov	x23, x0
    61000158:	2a0103f8 	mov	w24, w1
    6100015c:	f9002bfb 	str	x27, [sp, #80]
    61000160:	9100207b 	add	x27, x3, #0x8
    61000164:	7100029f 	cmp	w20, #0x0
    61000168:	5400010c 	b.gt	61000188 <emmc_dwcmshc+0x64>
    6100016c:	a94153f3 	ldp	x19, x20, [sp, #16]
    61000170:	a9425bf5 	ldp	x21, x22, [sp, #32]
    61000174:	a94363f7 	ldp	x23, x24, [sp, #48]
    61000178:	a9446bf9 	ldp	x25, x26, [sp, #64]
    6100017c:	f9402bfb 	ldr	x27, [sp, #80]
    61000180:	a8c67bfd 	ldp	x29, x30, [sp], #96
    61000184:	d65f03c0 	ret
    61000188:	aa1503e0 	mov	x0, x21
    6100018c:	97ffffa3 	bl	61000018 <emmc_wait_fifo>
    61000190:	2a0003f3 	mov	w19, w0
    61000194:	2a1603e1 	mov	w1, w22
    61000198:	aa1303e2 	mov	x2, x19
    6100019c:	aa1703e0 	mov	x0, x23
    610001a0:	97ffffb2 	bl	61000068 <emmc_write_block>
    610001a4:	110006d6 	add	w22, w22, #0x1
    610001a8:	8b1a0266 	add	x6, x19, x26
    610001ac:	4b180294 	sub	w20, w20, w24
    610001b0:	eb1900df 	cmp	x6, x25
    610001b4:	9a9b10c6 	csel	x6, x6, x27, ne  // ne = any
    610001b8:	b90006a6 	str	w6, [x21, #4]
    610001bc:	17ffffea 	b	61000164 <emmc_dwcmshc+0x40>

Disassembly of section .data:

00000000610001c0 <stack>:
    610001c0:	08675309 	.inst	0x08675309 ; undefined
    610001c4:	00000000 	udf	#0
    610001c8:	08675309 	.inst	0x08675309 ; undefined
    610001cc:	00000000 	udf	#0
    610001d0:	08675309 	.inst	0x08675309 ; undefined
    610001d4:	00000000 	udf	#0
    610001d8:	08675309 	.inst	0x08675309 ; undefined
    610001dc:	00000000 	udf	#0
    610001e0:	08675309 	.inst	0x08675309 ; undefined
    610001e4:	00000000 	udf	#0
    610001e8:	08675309 	.inst	0x08675309 ; undefined
    610001ec:	00000000 	udf	#0
    610001f0:	08675309 	.inst	0x08675309 ; undefined
    610001f4:	00000000 	udf	#0
    610001f8:	08675309 	.inst	0x08675309 ; undefined
    610001fc:	00000000 	udf	#0
    61000200:	08675309 	.inst	0x08675309 ; undefined
    61000204:	00000000 	udf	#0
    61000208:	08675309 	.inst	0x08675309 ; undefined
    6100020c:	00000000 	udf	#0
    61000210:	08675309 	.inst	0x08675309 ; undefined
    61000214:	00000000 	udf	#0
    61000218:	08675309 	.inst	0x08675309 ; undefined
    6100021c:	00000000 	udf	#0
    61000220:	08675309 	.inst	0x08675309 ; undefined
    61000224:	00000000 	udf	#0
    61000228:	08675309 	.inst	0x08675309 ; undefined
    6100022c:	00000000 	udf	#0
    61000230:	08675309 	.inst	0x08675309 ; undefined
    61000234:	00000000 	udf	#0
    61000238:	08675309 	.inst	0x08675309 ; undefined
    6100023c:	00000000 	udf	#0
    61000240:	08675309 	.inst	0x08675309 ; undefined
    61000244:	00000000 	udf	#0
    61000248:	08675309 	.inst	0x08675309 ; undefined
    6100024c:	00000000 	udf	#0
    61000250:	08675309 	.inst	0x08675309 ; undefined
    61000254:	00000000 	udf	#0
    61000258:	08675309 	.inst	0x08675309 ; undefined
    6100025c:	00000000 	udf	#0
    61000260:	08675309 	.inst	0x08675309 ; undefined
    61000264:	00000000 	udf	#0
    61000268:	08675309 	.inst	0x08675309 ; undefined
    6100026c:	00000000 	udf	#0
    61000270:	08675309 	.inst	0x08675309 ; undefined
    61000274:	00000000 	udf	#0
    61000278:	08675309 	.inst	0x08675309 ; undefined
    6100027c:	00000000 	udf	#0
    61000280:	08675309 	.inst	0x08675309 ; undefined
    61000284:	00000000 	udf	#0
    61000288:	08675309 	.inst	0x08675309 ; undefined
    6100028c:	00000000 	udf	#0
    61000290:	08675309 	.inst	0x08675309 ; undefined
    61000294:	00000000 	udf	#0
    61000298:	08675309 	.inst	0x08675309 ; undefined
    6100029c:	00000000 	udf	#0
    610002a0:	08675309 	.inst	0x08675309 ; undefined
    610002a4:	00000000 	udf	#0
    610002a8:	08675309 	.inst	0x08675309 ; undefined
    610002ac:	00000000 	udf	#0
    610002b0:	08675309 	.inst	0x08675309 ; undefined
    610002b4:	00000000 	udf	#0
    610002b8:	08675309 	.inst	0x08675309 ; undefined
    610002bc:	00000000 	udf	#0
    610002c0:	08675309 	.inst	0x08675309 ; undefined
    610002c4:	00000000 	udf	#0
    610002c8:	08675309 	.inst	0x08675309 ; undefined
    610002cc:	00000000 	udf	#0
    610002d0:	08675309 	.inst	0x08675309 ; undefined
    610002d4:	00000000 	udf	#0
    610002d8:	08675309 	.inst	0x08675309 ; undefined
    610002dc:	00000000 	udf	#0
    610002e0:	08675309 	.inst	0x08675309 ; undefined
    610002e4:	00000000 	udf	#0
    610002e8:	08675309 	.inst	0x08675309 ; undefined
    610002ec:	00000000 	udf	#0
    610002f0:	08675309 	.inst	0x08675309 ; undefined
    610002f4:	00000000 	udf	#0
    610002f8:	08675309 	.inst	0x08675309 ; undefined
    610002fc:	00000000 	udf	#0
    61000300:	08675309 	.inst	0x08675309 ; undefined
    61000304:	00000000 	udf	#0
    61000308:	08675309 	.inst	0x08675309 ; undefined
    6100030c:	00000000 	udf	#0
    61000310:	08675309 	.inst	0x08675309 ; undefined
    61000314:	00000000 	udf	#0
    61000318:	08675309 	.inst	0x08675309 ; undefined
    6100031c:	00000000 	udf	#0
    61000320:	08675309 	.inst	0x08675309 ; undefined
    61000324:	00000000 	udf	#0
    61000328:	08675309 	.inst	0x08675309 ; undefined
    6100032c:	00000000 	udf	#0
    61000330:	08675309 	.inst	0x08675309 ; undefined
    61000334:	00000000 	udf	#0
    61000338:	08675309 	.inst	0x08675309 ; undefined
    6100033c:	00000000 	udf	#0
    61000340:	08675309 	.inst	0x08675309 ; undefined
    61000344:	00000000 	udf	#0
    61000348:	08675309 	.inst	0x08675309 ; undefined
    6100034c:	00000000 	udf	#0
    61000350:	08675309 	.inst	0x08675309 ; undefined
    61000354:	00000000 	udf	#0
    61000358:	08675309 	.inst	0x08675309 ; undefined
    6100035c:	00000000 	udf	#0
    61000360:	08675309 	.inst	0x08675309 ; undefined
    61000364:	00000000 	udf	#0
    61000368:	08675309 	.inst	0x08675309 ; undefined
    6100036c:	00000000 	udf	#0
    61000370:	08675309 	.inst	0x08675309 ; undefined
    61000374:	00000000 	udf	#0
    61000378:	08675309 	.inst	0x08675309 ; undefined
    6100037c:	00000000 	udf	#0
    61000380:	08675309 	.inst	0x08675309 ; undefined
    61000384:	00000000 	udf	#0
    61000388:	08675309 	.inst	0x08675309 ; undefined
    6100038c:	00000000 	udf	#0
    61000390:	08675309 	.inst	0x08675309 ; undefined
    61000394:	00000000 	udf	#0
    61000398:	08675309 	.inst	0x08675309 ; undefined
    6100039c:	00000000 	udf	#0
    610003a0:	08675309 	.inst	0x08675309 ; undefined
    610003a4:	00000000 	udf	#0
    610003a8:	08675309 	.inst	0x08675309 ; undefined
    610003ac:	00000000 	udf	#0
    610003b0:	08675309 	.inst	0x08675309 ; undefined
    610003b4:	00000000 	udf	#0
    610003b8:	08675309 	.inst	0x08675309 ; undefined
    610003bc:	00000000 	udf	#0

Disassembly of section .debug_line:

0000000000000000 <.debug_line>:
   0:	00000097 	udf	#151
   4:	007d0003 	.inst	0x007d0003 ; undefined
   8:	01040000 	.inst	0x01040000 ; undefined
   c:	000d0efb 	.inst	0x000d0efb ; undefined
  10:	01010101 	.inst	0x01010101 ; undefined
  14:	01000000 	.inst	0x01000000 ; undefined
  18:	44010000 	.inst	0x44010000 ; undefined
  1c:	6f772f3a 	.inst	0x6f772f3a ; undefined
  20:	322f6b72 	orr	w18, w27, #0xfffe0fff
  24:	2f323230 	.inst	0x2f323230 ; undefined
  28:	30396c61 	adr	x1, 72db5 <_start-0x60f8d24b>
  2c:	732f3030 	.inst	0x732f3030 ; undefined
  30:	65642f77 	fmls	z23.h, p3/m, z27.h, z4.h
  34:	67677562 	.inst	0x67677562 ; undefined
  38:	612f7265 	.inst	0x612f7265 ; undefined
  3c:	706f5f6c 	adr	x12, dec2b <_start-0x60f213d5>
  40:	636f6e65 	.inst	0x636f6e65 ; undefined
  44:	6f632f64 	.inst	0x6f632f64 ; undefined
  48:	6972746e 	ldpsw	x14, x29, [x3, #-112]
  4c:	6f6c2f62 	.inst	0x6f6c2f62 ; undefined
  50:	72656461 	.inst	0x72656461 ; undefined
  54:	6c662f73 	ldnp	d19, d11, [x27, #-416]
  58:	2f687361 	fcmla	v1.4h, v27.4h, v8.h[1], #270
  5c:	636d6d65 	.inst	0x636d6d65 ; undefined
  60:	6377642f 	.inst	0x6377642f ; undefined
  64:	6368736d 	.inst	0x6368736d ; undefined
  68:	6372732f 	.inst	0x6372732f ; undefined
  6c:	6f6f622f 	umlsl2	v15.4s, v17.8h, v15.h[2]
  70:	61612f74 	.inst	0x61612f74 ; undefined
  74:	00686372 	.inst	0x00686372 ; undefined
  78:	61727700 	.inst	0x61727700 ; undefined
  7c:	72657070 	.inst	0x72657070 ; undefined
  80:	0100532e 	.inst	0x0100532e ; undefined
  84:	00000000 	udf	#0
  88:	00000209 	udf	#521
  8c:	00006100 	udf	#24832
  90:	21180000 	.inst	0x21180000 ; undefined
  94:	03022221 	.inst	0x03022221 ; undefined
  98:	b6010100 	tbz	x0, #32, 20b8 <_start-0x60ffdf48>
  9c:	03000002 	.inst	0x03000002 ; undefined
  a0:	00014300 	.inst	0x00014300 ; undefined
  a4:	fb010400 	.inst	0xfb010400 ; undefined
  a8:	01000d0e 	.inst	0x01000d0e ; undefined
  ac:	00010101 	.inst	0x00010101 ; undefined
  b0:	00010000 	.inst	0x00010000 ; undefined
  b4:	3a440100 	ccmn	w8, w4, #0x0, eq  // eq = none
  b8:	726f772f 	.inst	0x726f772f ; undefined
  bc:	30322f6b 	adr	x11, 646a9 <_start-0x60f9b957>
  c0:	612f3232 	.inst	0x612f3232 ; undefined
  c4:	3030396c 	adr	x12, 607f1 <_start-0x60f9f80f>
  c8:	77732f30 	.inst	0x77732f30 ; undefined
  cc:	6265642f 	.inst	0x6265642f ; undefined
  d0:	65676775 	fnmls	z21.h, p1/m, z27.h, z7.h
  d4:	6c612f72 	ldnp	d18, d11, [x27, #-496]
  d8:	65706f5f 	fnmls	z31.h, p3/m, z26.h, z16.h
  dc:	64636f6e 	.inst	0x64636f6e ; undefined
  e0:	6e6f632f 	rsubhn2	v15.8h, v25.4s, v15.4s
  e4:	62697274 	.inst	0x62697274 ; undefined
  e8:	616f6c2f 	.inst	0x616f6c2f ; undefined
  ec:	73726564 	.inst	0x73726564 ; undefined
  f0:	616c662f 	.inst	0x616c662f ; undefined
  f4:	652f6873 	.inst	0x652f6873 ; undefined
  f8:	2f636d6d 	.inst	0x2f636d6d ; undefined
  fc:	6d637764 	ldp	d4, d29, [x27, #-464]
 100:	2f636873 	umlsl	v19.4s, v3.4h, v3.h[6]
 104:	00637273 	.inst	0x00637273 ; undefined
 108:	775c3a64 	.inst	0x775c3a64 ; undefined
 10c:	5c6b726f 	ldr	d15, d6f58 <_start-0x60f290a8>
 110:	32323032 	orr	w18, w1, #0x7ffc000
 114:	396c615c 	ldrb	w28, [x10, #2840]
 118:	5c303030 	ldr	d16, 6071c <_start-0x60f9f8e4>
 11c:	735c7773 	.inst	0x735c7773 ; undefined
 120:	735c6b64 	.inst	0x735c6b64 ; undefined
 124:	612d636f 	.inst	0x612d636f ; undefined
 128:	732d7570 	.inst	0x732d7570 ; undefined
 12c:	745c6b64 	.inst	0x745c6b64 ; undefined
 130:	736c6f6f 	.inst	0x736c6f6f ; undefined
 134:	6e69775c 	uabd	v28.8h, v26.8h, v9.8h
 138:	7261615c 	.inst	0x7261615c ; undefined
 13c:	34366863 	cbz	w3, 6ce48 <_start-0x60f931b8>
 140:	7261615c 	.inst	0x7261615c ; undefined
 144:	34366863 	cbz	w3, 6ce50 <_start-0x60f931b0>
 148:	6e6f6e2d 	umin	v13.8h, v17.8h, v15.8h
 14c:	6c652d65 	ldnp	d5, d11, [x11, #-432]
 150:	6e695c66 	uqrshl	v6.8h, v3.8h, v9.8h
 154:	64756c63 	.inst	0x64756c63 ; undefined
 158:	616d5c65 	.inst	0x616d5c65 ; undefined
 15c:	6e696863 	.inst	0x6e696863 ; undefined
 160:	3a640065 	.inst	0x3a640065 ; undefined
 164:	726f775c 	.inst	0x726f775c ; undefined
 168:	30325c6b 	adr	x11, 64cf5 <_start-0x60f9b30b>
 16c:	615c3232 	.inst	0x615c3232 ; undefined
 170:	3030396c 	adr	x12, 6089d <_start-0x60f9f763>
 174:	77735c30 	.inst	0x77735c30 ; undefined
 178:	6b64735c 	.inst	0x6b64735c ; undefined
 17c:	636f735c 	.inst	0x636f735c ; undefined
 180:	7570612d 	.inst	0x7570612d ; undefined
 184:	6b64732d 	.inst	0x6b64732d ; undefined
 188:	6f6f745c 	uqshl	v28.2d, v2.2d, #47
 18c:	775c736c 	.inst	0x775c736c ; undefined
 190:	615c6e69 	.inst	0x615c6e69 ; undefined
 194:	68637261 	.inst	0x68637261 ; undefined
 198:	615c3436 	.inst	0x615c3436 ; undefined
 19c:	68637261 	.inst	0x68637261 ; undefined
 1a0:	6e2d3436 	cmhi	v22.16b, v1.16b, v13.16b
 1a4:	2d656e6f 	ldp	s15, s27, [x19, #-216]
 1a8:	5c666c65 	ldr	d5, ccf34 <_start-0x60f330cc>
 1ac:	6c636e69 	ldnp	d9, d27, [x19, #-464]
 1b0:	5c656475 	ldr	d21, cae3c <_start-0x60f351c4>
 1b4:	00737973 	.inst	0x00737973 ; undefined
 1b8:	63776400 	.inst	0x63776400 ; undefined
 1bc:	6368736d 	.inst	0x6368736d ; undefined
 1c0:	0100632e 	.inst	0x0100632e ; undefined
 1c4:	645f0000 	fcmla	z0.h, p0/m, z0.h, z31.h, #0
 1c8:	75616665 	.inst	0x75616665 ; undefined
 1cc:	745f746c 	.inst	0x745f746c ; undefined
 1d0:	73657079 	.inst	0x73657079 ; undefined
 1d4:	0200682e 	.inst	0x0200682e ; undefined
 1d8:	735f0000 	.inst	0x735f0000 ; undefined
 1dc:	6e696474 	umax	v20.8h, v3.8h, v9.8h
 1e0:	00682e74 	.inst	0x00682e74 ; undefined
 1e4:	00000003 	udf	#3
 1e8:	09000105 	.inst	0x09000105 ; undefined
 1ec:	00001802 	udf	#6146
 1f0:	00000061 	udf	#97
 1f4:	01061500 	.inst	0x01061500 ; undefined
 1f8:	21060505 	.inst	0x21060505 ; undefined
 1fc:	010a0513 	.inst	0x010a0513 ; undefined
 200:	05140905 	mov	z5.b, p4/z, #72
 204:	0501060c 	orr	z12.b, z12.b, #0x1
 208:	05210609 	ext	z9.b, z9.b, z16.b, #9
 20c:	0501060c 	orr	z12.b, z12.b, #0x1
 210:	051d060a 	mov	z10.b, p13/z, #48
 214:	01053305 	.inst	0x01053305 ; undefined
 218:	23061306 	.inst	0x23061306 ; undefined
 21c:	13130505 	sbfiz	w5, w8, #13, #2
 220:	01050106 	.inst	0x01050106 ; undefined
 224:	0605051e 	.inst	0x0605051e ; undefined
 228:	14090531 	b	2416ec <_start-0x60dbe914>
 22c:	01061105 	.inst	0x01061105 ; undefined
 230:	21060905 	.inst	0x21060905 ; undefined
 234:	060b0513 	.inst	0x060b0513 ; undefined
 238:	00160501 	.inst	0x00160501 ; undefined
 23c:	2e010402 	.inst	0x2e010402 ; undefined
 240:	31060505 	adds	w5, w8, #0x181
 244:	01061c05 	.inst	0x01061c05 ; undefined
 248:	052e0f05 	ext	z5.b, z5.b, z24.b, #115
 24c:	01210605 	.inst	0x01210605 ; undefined
 250:	01051320 	.inst	0x01051320 ; undefined
 254:	06201306 	.inst	0x06201306 ; undefined
 258:	13050523 	sbfiz	w3, w9, #27, #2
 25c:	20011413 	.inst	0x20011413 ; undefined
 260:	20060113 	.inst	0x20060113 ; undefined
 264:	01040200 	.inst	0x01040200 ; undefined
 268:	02002e06 	.inst	0x02002e06 ; undefined
 26c:	05130104 	mov	z4.b, p3/z, #8
 270:	04020009 	.inst	0x04020009 ; undefined
 274:	11051401 	add	w1, w0, #0x145
 278:	01040200 	.inst	0x01040200 ; undefined
 27c:	09050106 	.inst	0x09050106 ; undefined
 280:	01040200 	.inst	0x01040200 ; undefined
 284:	02002106 	.inst	0x02002106 ; undefined
 288:	05130104 	mov	z4.b, p3/z, #8
 28c:	0402000b 	.inst	0x0402000b ; undefined
 290:	05010601 	orr	z1.b, z1.b, #0x1
 294:	04020016 	.inst	0x04020016 ; undefined
 298:	09052001 	.inst	0x09052001 ; undefined
 29c:	03040200 	.inst	0x03040200 ; undefined
 2a0:	02004006 	.inst	0x02004006 ; undefined
 2a4:	05010304 	orr	z4.s, z4.s, #0x1ffffff
 2a8:	04020005 	.inst	0x04020005 ; undefined
 2ac:	051f0603 	mov	z3.b, p15/z, #48
 2b0:	04020009 	.inst	0x04020009 ; undefined
 2b4:	02002103 	.inst	0x02002103 ; undefined
 2b8:	20060304 	.inst	0x20060304 ; undefined
 2bc:	02002605 	.inst	0x02002605 ; undefined
 2c0:	05110304 	mov	z4.b, p1/z, #24
 2c4:	0402000e 	.inst	0x0402000e ; undefined
 2c8:	05050103 	.inst	0x05050103 ; undefined
 2cc:	03040200 	.inst	0x03040200 ; undefined
 2d0:	30060106 	adr	x6, c2f1 <_start-0x60ff3d0f>
 2d4:	05140905 	mov	z5.b, p4/z, #72
 2d8:	05010611 	orr	z17.b, z17.b, #0x1
 2dc:	13210609 	.inst	0x13210609 ; undefined
 2e0:	01060b05 	.inst	0x01060b05 ; undefined
 2e4:	02001605 	.inst	0x02001605 ; undefined
 2e8:	05200104 	ext	z4.b, z4.b, z8.b, #0
 2ec:	01310605 	.inst	0x01310605 ; undefined
 2f0:	20062006 	.inst	0x20062006 ; undefined
 2f4:	14060105 	b	180708 <_start-0x60e7f8f8>
 2f8:	05052306 	.inst	0x05052306 ; undefined
 2fc:	05011413 	orr	z19.h, z19.h, #0x4000
 300:	660f0601 	.inst	0x660f0601 ; undefined
 304:	1f240505 	fnmadd	s5, s8, s4, s1
 308:	01132006 	.inst	0x01132006 ; undefined
 30c:	0630132e 	.inst	0x0630132e ; undefined
 310:	000e0501 	.inst	0x000e0501 ; undefined
 314:	06010402 	.inst	0x06010402 ; undefined
 318:	00050520 	.inst	0x00050520 ; undefined
 31c:	06010402 	.inst	0x06010402 ; undefined
 320:	06300601 	.inst	0x06300601 ; undefined
 324:	21010520 	.inst	0x21010520 ; undefined
 328:	05052020 	.inst	0x05052020 ; undefined
 32c:	0009051f 	.inst	0x0009051f ; undefined
 330:	06030402 	.inst	0x06030402 ; undefined
 334:	0017051f 	.inst	0x0017051f ; undefined
 338:	06030402 	.inst	0x06030402 ; undefined
 33c:	001c0501 	.inst	0x001c0501 ; undefined
 340:	06030402 	.inst	0x06030402 ; undefined
 344:	0402002d 	.inst	0x0402002d ; undefined
 348:	00010603 	.inst	0x00010603 ; undefined
 34c:	20030402 	.inst	0x20030402 ; undefined
 350:	01000102 	.inst	0x01000102 ; undefined
 354:	0001d801 	.inst	0x0001d801 ; undefined
 358:	56000300 	.inst	0x56000300 ; undefined
 35c:	04000001 	add	z1.b, p0/m, z1.b, z0.b
 360:	0d0efb01 	.inst	0x0d0efb01 ; undefined
 364:	01010100 	.inst	0x01010100 ; undefined
 368:	00000001 	udf	#1
 36c:	01000001 	.inst	0x01000001 ; undefined
 370:	772f3a44 	.inst	0x772f3a44 ; undefined
 374:	2f6b726f 	fcmla	v15.4h, v19.4h, v11.h[1], #270
 378:	32323032 	orr	w18, w1, #0x7ffc000
 37c:	396c612f 	ldrb	w15, [x9, #2840]
 380:	2f303030 	.inst	0x2f303030 ; undefined
 384:	642f7773 	.inst	0x642f7773 ; undefined
 388:	67756265 	.inst	0x67756265 ; undefined
 38c:	2f726567 	.inst	0x2f726567 ; undefined
 390:	6f5f6c61 	.inst	0x6f5f6c61 ; undefined
 394:	6f6e6570 	sqshlu	v16.2d, v11.2d, #46
 398:	632f6463 	.inst	0x632f6463 ; undefined
 39c:	72746e6f 	.inst	0x72746e6f ; undefined
 3a0:	6c2f6269 	stnp	d9, d24, [x19, #-272]
 3a4:	6564616f 	fnmls	z15.h, p0/m, z11.h, z4.h
 3a8:	662f7372 	.inst	0x662f7372 ; undefined
 3ac:	6873616c 	.inst	0x6873616c ; undefined
 3b0:	6d6d652f 	ldp	d15, d25, [x9, #-304]
 3b4:	77642f63 	.inst	0x77642f63 ; undefined
 3b8:	68736d63 	.inst	0x68736d63 ; undefined
 3bc:	72732f63 	.inst	0x72732f63 ; undefined
 3c0:	3a640063 	.inst	0x3a640063 ; undefined
 3c4:	726f775c 	.inst	0x726f775c ; undefined
 3c8:	30325c6b 	adr	x11, 64f55 <_start-0x60f9b0ab>
 3cc:	615c3232 	.inst	0x615c3232 ; undefined
 3d0:	3030396c 	adr	x12, 60afd <_start-0x60f9f503>
 3d4:	77735c30 	.inst	0x77735c30 ; undefined
 3d8:	6b64735c 	.inst	0x6b64735c ; undefined
 3dc:	636f735c 	.inst	0x636f735c ; undefined
 3e0:	7570612d 	.inst	0x7570612d ; undefined
 3e4:	6b64732d 	.inst	0x6b64732d ; undefined
 3e8:	6f6f745c 	uqshl	v28.2d, v2.2d, #47
 3ec:	775c736c 	.inst	0x775c736c ; undefined
 3f0:	615c6e69 	.inst	0x615c6e69 ; undefined
 3f4:	68637261 	.inst	0x68637261 ; undefined
 3f8:	615c3436 	.inst	0x615c3436 ; undefined
 3fc:	68637261 	.inst	0x68637261 ; undefined
 400:	6e2d3436 	cmhi	v22.16b, v1.16b, v13.16b
 404:	2d656e6f 	ldp	s15, s27, [x19, #-216]
 408:	5c666c65 	ldr	d5, cd194 <_start-0x60f32e6c>
 40c:	6c636e69 	ldnp	d9, d27, [x19, #-464]
 410:	5c656475 	ldr	d21, cb09c <_start-0x60f34f64>
 414:	6863616d 	.inst	0x6863616d ; undefined
 418:	00656e69 	.inst	0x00656e69 ; undefined
 41c:	775c3a64 	.inst	0x775c3a64 ; undefined
 420:	5c6b726f 	ldr	d15, d726c <_start-0x60f28d94>
 424:	32323032 	orr	w18, w1, #0x7ffc000
 428:	396c615c 	ldrb	w28, [x10, #2840]
 42c:	5c303030 	ldr	d16, 60a30 <_start-0x60f9f5d0>
 430:	735c7773 	.inst	0x735c7773 ; undefined
 434:	735c6b64 	.inst	0x735c6b64 ; undefined
 438:	612d636f 	.inst	0x612d636f ; undefined
 43c:	732d7570 	.inst	0x732d7570 ; undefined
 440:	745c6b64 	.inst	0x745c6b64 ; undefined
 444:	736c6f6f 	.inst	0x736c6f6f ; undefined
 448:	6e69775c 	uabd	v28.8h, v26.8h, v9.8h
 44c:	7261615c 	.inst	0x7261615c ; undefined
 450:	34366863 	cbz	w3, 6d15c <_start-0x60f92ea4>
 454:	7261615c 	.inst	0x7261615c ; undefined
 458:	34366863 	cbz	w3, 6d164 <_start-0x60f92e9c>
 45c:	6e6f6e2d 	umin	v13.8h, v17.8h, v15.8h
 460:	6c652d65 	ldnp	d5, d11, [x11, #-432]
 464:	6e695c66 	uqrshl	v6.8h, v3.8h, v9.8h
 468:	64756c63 	.inst	0x64756c63 ; undefined
 46c:	79735c65 	ldrh	w5, [x3, #6574]
 470:	64000073 	.inst	0x64000073 ; undefined
 474:	736d6377 	.inst	0x736d6377 ; undefined
 478:	615f6368 	.inst	0x615f6368 ; undefined
 47c:	636e7973 	.inst	0x636e7973 ; undefined
 480:	0100632e 	.inst	0x0100632e ; undefined
 484:	645f0000 	fcmla	z0.h, p0/m, z0.h, z31.h, #0
 488:	75616665 	.inst	0x75616665 ; undefined
 48c:	745f746c 	.inst	0x745f746c ; undefined
 490:	73657079 	.inst	0x73657079 ; undefined
 494:	0200682e 	.inst	0x0200682e ; undefined
 498:	735f0000 	.inst	0x735f0000 ; undefined
 49c:	6e696474 	umax	v20.8h, v3.8h, v9.8h
 4a0:	00682e74 	.inst	0x00682e74 ; undefined
 4a4:	64000003 	.inst	0x64000003 ; undefined
 4a8:	736d6377 	.inst	0x736d6377 ; undefined
 4ac:	682e6368 	.inst	0x682e6368 ; undefined
 4b0:	00000100 	udf	#256
 4b4:	00010500 	.inst	0x00010500 ; undefined
 4b8:	01240209 	.inst	0x01240209 ; undefined
 4bc:	00006100 	udf	#24832
 4c0:	05160000 	mov	z0.b, p6/z, #0
 4c4:	05131305 	mov	z5.b, p3/z, #-104
 4c8:	05100601 	mov	z1.b, p0/z, #48
 4cc:	3c09031a 	stur	b26, [x24, #144]
 4d0:	77030105 	.inst	0x77030105 ; undefined
 4d4:	030c0520 	.inst	0x030c0520 ; undefined
 4d8:	01052009 	.inst	0x01052009 ; undefined
 4dc:	05207703 	trn2	z3.b, z24.b, z0.b
 4e0:	900c0310 	adrp	x16, 18060000 <_start-0x48fa0000>
 4e4:	03060a05 	.inst	0x03060a05 ; undefined
 4e8:	01052076 	.inst	0x01052076 ; undefined
 4ec:	2e100306 	ext	v6.8b, v24.8b, v16.8b, #0
 4f0:	20202020 	.inst	0x20202020 ; undefined
 4f4:	06090520 	.inst	0x06090520 ; undefined
 4f8:	052e7203 	trn1	z3.b, z16.b, z14.b
 4fc:	0501061a 	orr	z26.b, z26.b, #0x1
 500:	4c3e0609 	.inst	0x4c3e0609 ; undefined
 504:	01061405 	.inst	0x01061405 ; undefined
 508:	21060905 	.inst	0x21060905 ; undefined
 50c:	01060c05 	.inst	0x01060c05 ; undefined
 510:	22060905 	.inst	0x22060905 ; undefined
 514:	17060f05 	b	fffffffffc184128 <stack_end+0xffffffff9b183d68>
 518:	051b0b05 	mov	z5.b, p11/z, #88
 51c:	09052110 	.inst	0x09052110 ; undefined
 520:	1c052206 	ldr	s6, a960 <_start-0x60ff56a0>
 524:	09050106 	.inst	0x09050106 ; undefined
 528:	01062206 	.inst	0x01062206 ; undefined
 52c:	01000102 	.inst	0x01000102 ; undefined
 530:	Address 0x0000000000000530 is out of bounds.


Disassembly of section .debug_info:

0000000000000000 <.debug_info>:
   0:	0000002a 	udf	#42
   4:	00000002 	udf	#2
   8:	01080000 	.inst	0x01080000 ; undefined
   c:	00000000 	udf	#0
  10:	61000000 	.inst	0x61000000 ; undefined
  14:	00000000 	udf	#0
  18:	61000018 	.inst	0x61000018 ; undefined
	...
  24:	00000067 	udf	#103
  28:	000000b5 	udf	#181
  2c:	02cf8001 	.inst	0x02cf8001 ; undefined
  30:	00040000 	.inst	0x00040000 ; undefined
  34:	00000014 	udf	#20
  38:	00e70108 	.inst	0x00e70108 ; undefined
  3c:	c20c0000 	.inst	0xc20c0000 ; undefined
  40:	67000001 	.inst	0x67000001 ; undefined
  44:	18000000 	ldr	w0, 44 <_start-0x60ffffbc>
  48:	00610000 	.inst	0x00610000 ; undefined
  4c:	0c000000 	st4	{v0.8b-v3.8b}, [x0]
  50:	00000001 	udf	#1
  54:	9b000000 	madd	x0, x0, x0, x0
  58:	02000000 	.inst	0x02000000 ; undefined
  5c:	01450601 	.inst	0x01450601 ; undefined
  60:	cd030000 	.inst	0xcd030000 ; undefined
  64:	02000000 	.inst	0x02000000 ; undefined
  68:	0040182b 	.inst	0x0040182b ; undefined
  6c:	01020000 	.inst	0x01020000 ; undefined
  70:	00014308 	.inst	0x00014308 ; undefined
  74:	05020200 	orr	z0.d, z0.d, #0x1ffff
  78:	00000227 	udf	#551
  7c:	63070202 	.inst	0x63070202 ; undefined
  80:	04000001 	add	z1.b, p0/m, z1.b, z0.b
  84:	6e690504 	uhadd	v4.8h, v8.8h, v9.8h
  88:	9e030074 	ucvtf	s20, x3, #64
  8c:	02000001 	.inst	0x02000001 ; undefined
  90:	0068194f 	.inst	0x0068194f ; undefined
  94:	04020000 	.inst	0x04020000 ; undefined
  98:	00015607 	.inst	0x00015607 ; undefined
  9c:	05080200 	.inst	0x05080200 ; undefined
  a0:	00000238 	udf	#568
  a4:	51070802 	sub	w2, w0, #0x1c2
  a8:	03000001 	.inst	0x03000001 ; undefined
  ac:	000000cf 	udf	#207
  b0:	34131803 	cbz	w3, 263b0 <_start-0x60fd9c50>
  b4:	03000000 	.inst	0x03000000 ; undefined
  b8:	000001a0 	udf	#416
  bc:	5c143003 	ldr	d3, 286bc <_start-0x60fd7944>
  c0:	05000000 	orr	z0.s, z0.s, #0x1
  c4:	00000089 	udf	#137
  c8:	00008906 	udf	#35078
  cc:	00d70700 	.inst	0x00d70700 ; undefined
  d0:	39010000 	strb	w0, [x0, #64]
  d4:	0000bc06 	udf	#48134
  d8:	00000061 	udf	#97
  dc:	00006800 	udf	#26624
  e0:	00000000 	udf	#0
  e4:	579c0100 	.inst	0x579c0100 ; undefined
  e8:	08000001 	stxrb	w0, w1, [x0]
  ec:	000000c3 	udf	#195
  f0:	57293901 	.inst	0x57293901 ; undefined
  f4:	0a000001 	and	w1, w0, w0
  f8:	00000000 	udf	#0
  fc:	08000000 	stxrb	w0, w0, [x0]
 100:	00000231 	udf	#561
 104:	5d3e3901 	.inst	0x5d3e3901 ; undefined
 108:	84000001 	ld1sb	{z1.s}, p0/z, [x0, z0.s, uxtw]
 10c:	7c000000 	stur	h0, [x0]
 110:	08000000 	stxrb	w0, w0, [x0]
 114:	00000250 	udf	#592
 118:	894f3901 	.inst	0x894f3901 ; undefined
 11c:	e7000000 	.inst	0xe7000000 ; undefined
 120:	e3000000 	.inst	0xe3000000 ; undefined
 124:	08000000 	stxrb	w0, w0, [x0]
 128:	0000021e 	udf	#542
 12c:	89603901 	.inst	0x89603901 ; undefined
 130:	28000000 	stnp	w0, w0, [x0]
 134:	20000001 	.inst	0x20000001 ; undefined
 138:	09000001 	.inst	0x09000001 ; undefined
 13c:	3b010069 	.inst	0x3b010069 ; undefined
 140:	0000890e 	udf	#35086
 144:	00018f00 	.inst	0x00018f00 ; undefined
 148:	00018700 	.inst	0x00018700 ; undefined
 14c:	00f00a00 	.inst	0x00f00a00 ; undefined
 150:	00006100 	udf	#24832
 154:	01f20000 	.inst	0x01f20000 ; undefined
 158:	013c0000 	.inst	0x013c0000 ; undefined
 15c:	010b0000 	.inst	0x010b0000 ; undefined
 160:	00830250 	.inst	0x00830250 ; undefined
 164:	0151010b 	.inst	0x0151010b ; undefined
 168:	140c0035 	b	30023c <_start-0x60cffdc4>
 16c:	00610001 	.inst	0x00610001 ; undefined
 170:	f2000000 	ands	x0, x0, #0x100000001
 174:	0b000001 	add	w1, w0, w0
 178:	f3035001 	.inst	0xf3035001 ; undefined
 17c:	010b5001 	.inst	0x010b5001 ; undefined
 180:	00310151 	.inst	0x00310151 ; NYI
 184:	95080d00 	bl	4203584 <_start-0x5cdfca7c>
 188:	0d000000 	st1	{v0.b}[0], [x0]
 18c:	00008908 	udf	#35080
 190:	01a90700 	.inst	0x01a90700 ; undefined
 194:	1e010000 	.inst	0x1e010000 ; undefined
 198:	00006806 	udf	#26630
 19c:	00000061 	udf	#97
 1a0:	00005400 	udf	#21504
 1a4:	00000000 	udf	#0
 1a8:	ec9c0100 	.inst	0xec9c0100 ; undefined
 1ac:	0e000001 	tbl	v1.8b, {v0.16b}, v0.8b
 1b0:	000000c3 	udf	#195
 1b4:	572a1e01 	.inst	0x572a1e01 ; undefined
 1b8:	01000001 	.inst	0x01000001 ; undefined
 1bc:	02500850 	.inst	0x02500850 ; undefined
 1c0:	1e010000 	.inst	0x1e010000 ; undefined
 1c4:	0000893e 	udf	#35134
 1c8:	0001f400 	.inst	0x0001f400 ; undefined
 1cc:	0001ee00 	.inst	0x0001ee00 ; undefined
 1d0:	02310e00 	.inst	0x02310e00 ; undefined
 1d4:	1e010000 	.inst	0x1e010000 ; undefined
 1d8:	0001ec56 	.inst	0x0001ec56 ; undefined
 1dc:	09520100 	.inst	0x09520100 ; undefined
 1e0:	20010069 	.inst	0x20010069 ; undefined
 1e4:	0000890e 	udf	#35086
 1e8:	00024300 	.inst	0x00024300 ; undefined
 1ec:	00024100 	.inst	0x00024100 ; undefined
 1f0:	01ba0f00 	.inst	0x01ba0f00 ; undefined
 1f4:	20010000 	.inst	0x20010000 ; undefined
 1f8:	00008911 	udf	#35089
 1fc:	00026a00 	.inst	0x00026a00 ; undefined
 200:	00026600 	.inst	0x00026600 ; undefined
 204:	01760f00 	.inst	0x01760f00 ; undefined
 208:	21010000 	.inst	0x21010000 ; undefined
 20c:	00007d0d 	udf	#32013
 210:	0002a400 	.inst	0x0002a400 ; undefined
 214:	0002a000 	.inst	0x0002a000 ; undefined
 218:	080d0000 	stxrb	w13, w0, [x0]
 21c:	0000009a 	udf	#154
 220:	00018010 	.inst	0x00018010 ; undefined
 224:	050e0100 	.inst	0x050e0100 ; undefined
 228:	00000055 	udf	#85
 22c:	61000030 	.inst	0x61000030 ; undefined
 230:	00000000 	udf	#0
 234:	00000038 	udf	#56
 238:	00000000 	udf	#0
 23c:	02799c01 	.inst	0x02799c01 ; undefined
 240:	c3080000 	.inst	0xc3080000 ; undefined
 244:	01000000 	.inst	0x01000000 ; undefined
 248:	0157260e 	.inst	0x0157260e ; undefined
 24c:	02ea0000 	.inst	0x02ea0000 ; undefined
 250:	02e60000 	.inst	0x02e60000 ; undefined
 254:	4b080000 	sub	w0, w0, w8
 258:	01000002 	.inst	0x01000002 ; undefined
 25c:	007d390e 	.inst	0x007d390e ; undefined
 260:	03270000 	.inst	0x03270000 ; undefined
 264:	03230000 	.inst	0x03230000 ; undefined
 268:	ba0f0000 	adcs	x0, x0, x15
 26c:	01000001 	.inst	0x01000001 ; undefined
 270:	00890e10 	.inst	0x00890e10 ; undefined
 274:	035f0000 	.inst	0x035f0000 ; undefined
 278:	035d0000 	.inst	0x035d0000 ; undefined
 27c:	410f0000 	.inst	0x410f0000 ; undefined
 280:	01000002 	.inst	0x01000002 ; undefined
 284:	00891710 	.inst	0x00891710 ; undefined
 288:	03840000 	.inst	0x03840000 ; undefined
 28c:	03820000 	.inst	0x03820000 ; undefined
 290:	760f0000 	.inst	0x760f0000 ; undefined
 294:	01000001 	.inst	0x01000001 ; undefined
 298:	007d0d11 	.inst	0x007d0d11 ; undefined
 29c:	03ab0000 	.inst	0x03ab0000 ; undefined
 2a0:	03a70000 	.inst	0x03a70000 ; undefined
 2a4:	11000000 	add	w0, w0, #0x0
 2a8:	00000134 	udf	#308
 2ac:	890b0301 	.inst	0x890b0301 ; undefined
 2b0:	18000000 	ldr	w0, 2b0 <_start-0x60fffd50>
 2b4:	00610000 	.inst	0x00610000 ; undefined
 2b8:	18000000 	ldr	w0, 2b8 <_start-0x60fffd48>
 2bc:	00000000 	udf	#0
 2c0:	01000000 	.inst	0x01000000 ; undefined
 2c4:	018e089c 	.inst	0x018e089c ; undefined
 2c8:	03010000 	.inst	0x03010000 ; undefined
 2cc:	00015d24 	.inst	0x00015d24 ; undefined
 2d0:	0003ed00 	.inst	0x0003ed00 ; undefined
 2d4:	0003e900 	.inst	0x0003e900 ; undefined
 2d8:	70770900 	adr	x0, ee3fb <_start-0x60f11c05>
 2dc:	0e050100 	tbl	v0.8b, {v8.16b}, v5.8b
 2e0:	00000089 	udf	#137
 2e4:	00000427 	udf	#1063
 2e8:	00000423 	udf	#1059
 2ec:	00707209 	.inst	0x00707209 ; undefined
 2f0:	89160501 	.inst	0x89160501 ; undefined
 2f4:	62000000 	.inst	0x62000000 ; undefined
 2f8:	5e000004 	sha1c	q4, s0, v0.4s
 2fc:	00000004 	udf	#4
 300:	00019d00 	.inst	0x00019d00 ; undefined
 304:	2e000400 	.inst	0x2e000400 ; undefined
 308:	08000001 	stxrb	w0, w1, [x0]
 30c:	0000e701 	udf	#59137
 310:	02610c00 	.inst	0x02610c00 ; undefined
 314:	00670000 	.inst	0x00670000 ; undefined
 318:	01240000 	.inst	0x01240000 ; undefined
 31c:	00006100 	udf	#24832
 320:	009c0000 	.inst	0x009c0000 ; undefined
 324:	00000000 	udf	#0
 328:	03550000 	.inst	0x03550000 ; undefined
 32c:	01020000 	.inst	0x01020000 ; undefined
 330:	00014506 	.inst	0x00014506 ; undefined
 334:	08010200 	stxrb	w1, w0, [x16]
 338:	00000143 	udf	#323
 33c:	27050202 	.inst	0x27050202 ; undefined
 340:	02000002 	.inst	0x02000002 ; undefined
 344:	01630702 	.inst	0x01630702 ; undefined
 348:	57030000 	.inst	0x57030000 ; undefined
 34c:	02000002 	.inst	0x02000002 ; undefined
 350:	0055184d 	.inst	0x0055184d ; undefined
 354:	04040000 	.inst	0x04040000 ; undefined
 358:	746e6905 	.inst	0x746e6905 ; undefined
 35c:	019e0300 	.inst	0x019e0300 ; undefined
 360:	4f020000 	.inst	0x4f020000 ; undefined
 364:	00006819 	udf	#26649
 368:	07040200 	.inst	0x07040200 ; undefined
 36c:	00000156 	udf	#342
 370:	38050802 	sttrb	w2, [x0, #80]
 374:	02000002 	.inst	0x02000002 ; undefined
 378:	01510708 	.inst	0x01510708 ; undefined
 37c:	59030000 	stlurh	w0, [x0, #48]
 380:	03000002 	.inst	0x03000002 ; undefined
 384:	0049132c 	.inst	0x0049132c ; undefined
 388:	a0030000 	.inst	0xa0030000 ; undefined
 38c:	03000001 	.inst	0x03000001 ; undefined
 390:	005c1430 	.inst	0x005c1430 ; undefined
 394:	89050000 	.inst	0x89050000 ; undefined
 398:	06000000 	.inst	0x06000000 ; undefined
 39c:	000002e7 	udf	#743
 3a0:	24060401 	cmphs	p1.b, p1/z, z0.b, z6.b
 3a4:	00610001 	.inst	0x00610001 ; undefined
 3a8:	9c000000 	ldr	q0, 3a8 <_start-0x60fffc58>
 3ac:	00000000 	udf	#0
 3b0:	01000000 	.inst	0x01000000 ; undefined
 3b4:	00017c9c 	.inst	0x00017c9c ; undefined
 3b8:	00c30700 	.inst	0x00c30700 ; undefined
 3bc:	04010000 	sub	z0.b, p0/m, z0.b, z0.b
 3c0:	00017c26 	.inst	0x00017c26 ; undefined
 3c4:	0004a100 	.inst	0x0004a100 ; undefined
 3c8:	00049900 	.inst	0x00049900 ; undefined
 3cc:	02ce0700 	.inst	0x02ce0700 ; undefined
 3d0:	04010000 	sub	z0.b, p0/m, z0.b, z0.b
 3d4:	00007d39 	udf	#32057
 3d8:	00050800 	.inst	0x00050800 ; undefined
 3dc:	00050000 	.inst	0x00050000 ; undefined
 3e0:	02e10700 	.inst	0x02e10700 ; undefined
 3e4:	04010000 	sub	z0.b, p0/m, z0.b, z0.b
 3e8:	00005549 	udf	#21833
 3ec:	00056f00 	.inst	0x00056f00 ; undefined
 3f0:	00056700 	.inst	0x00056700 ; undefined
 3f4:	02f40700 	.inst	0x02f40700 ; undefined
 3f8:	04010000 	sub	z0.b, p0/m, z0.b, z0.b
 3fc:	0001825a 	.inst	0x0001825a ; undefined
 400:	0005d500 	.inst	0x0005d500 ; undefined
 404:	0005cb00 	.inst	0x0005cb00 ; undefined
 408:	02d90700 	.inst	0x02d90700 ; undefined
 40c:	04010000 	sub	z0.b, p0/m, z0.b, z0.b
 410:	0001826f 	.inst	0x0001826f ; undefined
 414:	00065100 	.inst	0x00065100 ; undefined
 418:	00064900 	.inst	0x00064900 ; undefined
 41c:	02c30700 	.inst	0x02c30700 ; undefined
 420:	04010000 	sub	z0.b, p0/m, z0.b, z0.b
 424:	00008981 	udf	#35201
 428:	0006b600 	.inst	0x0006b600 ; undefined
 42c:	0006b000 	.inst	0x0006b000 ; undefined
 430:	70720800 	adr	x0, e4533 <_start-0x60f1bacd>
 434:	0f060100 	.inst	0x0f060100 ; undefined
 438:	00000182 	udf	#386
 43c:	00000703 	udf	#1795
 440:	000006ff 	udf	#1791
 444:	00019009 	.inst	0x00019009 ; undefined
 448:	00000061 	udf	#97
 44c:	00018800 	.inst	0x00018800 ; undefined
 450:	00015b00 	.inst	0x00015b00 ; undefined
 454:	50010a00 	adr	x0, 2596 <_start-0x60ffda6a>
 458:	00008502 	udf	#34050
 45c:	0001a40b 	.inst	0x0001a40b ; undefined
 460:	00000061 	udf	#97
 464:	00019400 	.inst	0x00019400 ; undefined
 468:	50010a00 	adr	x0, 25aa <_start-0x60ffda56>
 46c:	0a008702 	.inst	0x0a008702 ; undefined
 470:	86025101 	.inst	0x86025101 ; undefined
 474:	52010a00 	eor	w0, w16, #0x80000003
 478:	00008302 	udf	#33538
 47c:	95080c00 	bl	420347c <_start-0x5cdfcb84>
 480:	0c000000 	st4	{v0.8b-v3.8b}, [x0]
 484:	00008908 	udf	#35080
 488:	01340d00 	.inst	0x01340d00 ; undefined
 48c:	01340000 	.inst	0x01340000 ; undefined
 490:	2e040000 	ext	v0.8b, v0.8b, v4.8b, #0
 494:	01a90d0b 	.inst	0x01a90d0b ; undefined
 498:	01a90000 	.inst	0x01a90000 ; undefined
 49c:	2f040000 	.inst	0x2f040000 ; undefined
 4a0:	Address 0x00000000000004a0 is out of bounds.


Disassembly of section .debug_abbrev:

0000000000000000 <.debug_abbrev>:
   0:	10001101 	adr	x1, 220 <_start-0x60fffde0>
   4:	12011106 	and	w6, w8, #0x8000000f
   8:	1b0e0301 	madd	w1, w24, w14, w0
   c:	130e250e 	sbfiz	w14, w8, #18, #10
  10:	00000005 	udf	#5
  14:	25011101 	cmpge	p1.b, p4/z, z8.b, #1
  18:	030b130e 	.inst	0x030b130e ; undefined
  1c:	110e1b0e 	add	w14, w24, #0x386
  20:	10071201 	adr	x1, e260 <_start-0x60ff1da0>
  24:	02000017 	.inst	0x02000017 ; undefined
  28:	0b0b0024 	add	w4, w1, w11
  2c:	0e030b3e 	.inst	0x0e030b3e ; undefined
  30:	16030000 	b	fffffffff80c0030 <stack_end+0xffffffff970bfc70>
  34:	3a0e0300 	adcs	w0, w24, w14
  38:	390b3b0b 	strb	w11, [x24, #718]
  3c:	0013490b 	.inst	0x0013490b ; undefined
  40:	00240400 	.inst	0x00240400 ; NYI
  44:	0b3e0b0b 	add	w11, w24, w30, uxtb #2
  48:	00000803 	udf	#2051
  4c:	49003505 	.inst	0x49003505 ; undefined
  50:	06000013 	.inst	0x06000013 ; undefined
  54:	13490026 	.inst	0x13490026 ; undefined
  58:	2e070000 	ext	v0.8b, v0.8b, v7.8b, #0
  5c:	03193f01 	.inst	0x03193f01 ; undefined
  60:	3b0b3a0e 	.inst	0x3b0b3a0e ; undefined
  64:	270b390b 	.inst	0x270b390b ; undefined
  68:	12011119 	and	w25, w8, #0x8000000f
  6c:	97184007 	bl	fffffffffc610088 <stack_end+0xffffffff9b60fcc8>
  70:	13011942 	sbfx	w2, w10, #1, #6
  74:	05080000 	.inst	0x05080000 ; undefined
  78:	3a0e0300 	adcs	w0, w24, w14
  7c:	390b3b0b 	strb	w11, [x24, #718]
  80:	0213490b 	.inst	0x0213490b ; undefined
  84:	1742b717 	b	fffffffffd0adce0 <stack_end+0xffffffff9c0ad920>
  88:	34090000 	cbz	w0, 12088 <_start-0x60fedf78>
  8c:	3a080300 	adcs	w0, w24, w8
  90:	390b3b0b 	strb	w11, [x24, #718]
  94:	0213490b 	.inst	0x0213490b ; undefined
  98:	1742b717 	b	fffffffffd0adcf4 <stack_end+0xffffffff9c0ad934>
  9c:	890a0000 	.inst	0x890a0000 ; undefined
  a0:	11010182 	add	w2, w12, #0x40
  a4:	01133101 	.inst	0x01133101 ; undefined
  a8:	0b000013 	add	w19, w0, w0
  ac:	0001828a 	.inst	0x0001828a ; undefined
  b0:	42911802 	.inst	0x42911802 ; undefined
  b4:	0c000018 	st4	{v24.8b-v27.8b}, [x0]
  b8:	01018289 	.inst	0x01018289 ; undefined
  bc:	42950111 	.inst	0x42950111 ; undefined
  c0:	00133119 	.inst	0x00133119 ; undefined
  c4:	000f0d00 	.inst	0x000f0d00 ; undefined
  c8:	13490b0b 	.inst	0x13490b0b ; undefined
  cc:	050e0000 	.inst	0x050e0000 ; undefined
  d0:	3a0e0300 	adcs	w0, w24, w14
  d4:	390b3b0b 	strb	w11, [x24, #718]
  d8:	0213490b 	.inst	0x0213490b ; undefined
  dc:	0f000018 	.inst	0x0f000018 ; undefined
  e0:	0e030034 	tbl	v20.8b, {v1.16b}, v3.8b
  e4:	0b3b0b3a 	add	w26, w25, w27, uxtb #2
  e8:	13490b39 	.inst	0x13490b39 ; undefined
  ec:	42b71702 	.inst	0x42b71702 ; undefined
  f0:	10000017 	adr	x23, f0 <_start-0x60ffff10>
  f4:	193f012e 	.inst	0x193f012e ; undefined
  f8:	0b3a0e03 	add	w3, w16, w26, uxtb #3
  fc:	0b390b3b 	add	w27, w25, w25, uxtb #2
 100:	13491927 	.inst	0x13491927 ; undefined
 104:	07120111 	.inst	0x07120111 ; undefined
 108:	42971840 	.inst	0x42971840 ; undefined
 10c:	00130119 	.inst	0x00130119 ; undefined
 110:	012e1100 	.inst	0x012e1100 ; undefined
 114:	0e03193f 	uzp1	v31.8b, v9.8b, v3.8b
 118:	0b3b0b3a 	add	w26, w25, w27, uxtb #2
 11c:	19270b39 	.inst	0x19270b39 ; undefined
 120:	01111349 	.inst	0x01111349 ; undefined
 124:	18400712 	ldr	w18, 80204 <_start-0x60f7fdfc>
 128:	00194297 	.inst	0x00194297 ; undefined
 12c:	11010000 	add	w0, w0, #0x40
 130:	130e2501 	sbfiz	w1, w8, #18, #10
 134:	1b0e030b 	madd	w11, w24, w14, w0
 138:	1201110e 	and	w14, w8, #0x8000000f
 13c:	00171007 	.inst	0x00171007 ; undefined
 140:	00240200 	.inst	0x00240200 ; NYI
 144:	0b3e0b0b 	add	w11, w24, w30, uxtb #2
 148:	00000e03 	udf	#3587
 14c:	03001603 	.inst	0x03001603 ; undefined
 150:	3b0b3a0e 	.inst	0x3b0b3a0e ; undefined
 154:	490b390b 	.inst	0x490b390b ; undefined
 158:	04000013 	add	z19.b, p0/m, z19.b, z0.b
 15c:	0b0b0024 	add	w4, w1, w11
 160:	08030b3e 	stxrb	w3, w30, [x25]
 164:	35050000 	cbnz	w0, a164 <_start-0x60ff5e9c>
 168:	00134900 	.inst	0x00134900 ; undefined
 16c:	012e0600 	.inst	0x012e0600 ; undefined
 170:	0e03193f 	uzp1	v31.8b, v9.8b, v3.8b
 174:	0b3b0b3a 	add	w26, w25, w27, uxtb #2
 178:	19270b39 	.inst	0x19270b39 ; undefined
 17c:	07120111 	.inst	0x07120111 ; undefined
 180:	42971840 	.inst	0x42971840 ; undefined
 184:	00130119 	.inst	0x00130119 ; undefined
 188:	00050700 	.inst	0x00050700 ; undefined
 18c:	0b3a0e03 	add	w3, w16, w26, uxtb #3
 190:	0b390b3b 	add	w27, w25, w25, uxtb #2
 194:	17021349 	b	fffffffffc084eb8 <stack_end+0xffffffff9b084af8>
 198:	001742b7 	.inst	0x001742b7 ; undefined
 19c:	00340800 	.inst	0x00340800 ; NYI
 1a0:	0b3a0803 	add	w3, w0, w26, uxtb #2
 1a4:	0b390b3b 	add	w27, w25, w25, uxtb #2
 1a8:	17021349 	b	fffffffffc084ecc <stack_end+0xffffffff9b084b0c>
 1ac:	001742b7 	.inst	0x001742b7 ; undefined
 1b0:	82890900 	.inst	0x82890900 ; undefined
 1b4:	01110101 	.inst	0x01110101 ; undefined
 1b8:	13011331 	sbfx	w17, w25, #1, #4
 1bc:	8a0a0000 	and	x0, x0, x10
 1c0:	02000182 	.inst	0x02000182 ; undefined
 1c4:	18429118 	ldr	w24, 853e4 <_start-0x60f7ac1c>
 1c8:	890b0000 	.inst	0x890b0000 ; undefined
 1cc:	11010182 	add	w2, w12, #0x40
 1d0:	00133101 	.inst	0x00133101 ; undefined
 1d4:	000f0c00 	.inst	0x000f0c00 ; undefined
 1d8:	13490b0b 	.inst	0x13490b0b ; undefined
 1dc:	2e0d0000 	ext	v0.8b, v0.8b, v13.8b, #0
 1e0:	3c193f00 	str	b0, [x24, #-109]!
 1e4:	030e6e19 	.inst	0x030e6e19 ; undefined
 1e8:	3b0b3a0e 	.inst	0x3b0b3a0e ; undefined
 1ec:	000b390b 	.inst	0x000b390b ; undefined
	...

Disassembly of section .debug_aranges:

0000000000000000 <.debug_aranges>:
   0:	0000002c 	udf	#44
   4:	00000002 	udf	#2
   8:	00080000 	.inst	0x00080000 ; undefined
   c:	00000000 	udf	#0
  10:	61000000 	.inst	0x61000000 ; undefined
  14:	00000000 	udf	#0
  18:	00000018 	udf	#24
	...
  30:	0000002c 	udf	#44
  34:	002e0002 	.inst	0x002e0002 ; NYI
  38:	00080000 	.inst	0x00080000 ; undefined
  3c:	00000000 	udf	#0
  40:	61000018 	.inst	0x61000018 ; undefined
  44:	00000000 	udf	#0
  48:	0000010c 	udf	#268
	...
  60:	0000002c 	udf	#44
  64:	03010002 	.inst	0x03010002 ; undefined
  68:	00080000 	.inst	0x00080000 ; undefined
  6c:	00000000 	udf	#0
  70:	61000124 	.inst	0x61000124 ; undefined
  74:	00000000 	udf	#0
  78:	0000009c 	udf	#156
	...

Disassembly of section .debug_str:

0000000000000000 <.debug_str>:
   0:	772f3a44 	.inst	0x772f3a44 ; undefined
   4:	2f6b726f 	fcmla	v15.4h, v19.4h, v11.h[1], #270
   8:	32323032 	orr	w18, w1, #0x7ffc000
   c:	396c612f 	ldrb	w15, [x9, #2840]
  10:	2f303030 	.inst	0x2f303030 ; undefined
  14:	642f7773 	.inst	0x642f7773 ; undefined
  18:	67756265 	.inst	0x67756265 ; undefined
  1c:	2f726567 	.inst	0x2f726567 ; undefined
  20:	6f5f6c61 	.inst	0x6f5f6c61 ; undefined
  24:	6f6e6570 	sqshlu	v16.2d, v11.2d, #46
  28:	632f6463 	.inst	0x632f6463 ; undefined
  2c:	72746e6f 	.inst	0x72746e6f ; undefined
  30:	6c2f6269 	stnp	d9, d24, [x19, #-272]
  34:	6564616f 	fnmls	z15.h, p0/m, z11.h, z4.h
  38:	662f7372 	.inst	0x662f7372 ; undefined
  3c:	6873616c 	.inst	0x6873616c ; undefined
  40:	6d6d652f 	ldp	d15, d25, [x9, #-304]
  44:	77642f63 	.inst	0x77642f63 ; undefined
  48:	68736d63 	.inst	0x68736d63 ; undefined
  4c:	72732f63 	.inst	0x72732f63 ; undefined
  50:	6f622f63 	.inst	0x6f622f63 ; undefined
  54:	612f746f 	.inst	0x612f746f ; undefined
  58:	68637261 	.inst	0x68637261 ; undefined
  5c:	6172775c 	.inst	0x6172775c ; undefined
  60:	72657070 	.inst	0x72657070 ; undefined
  64:	4400532e 	.inst	0x4400532e ; undefined
  68:	6f775c3a 	.inst	0x6f775c3a ; undefined
  6c:	325c6b72 	.inst	0x325c6b72 ; undefined
  70:	5c323230 	ldr	d16, 646b4 <_start-0x60f9b94c>
  74:	30396c61 	adr	x1, 72e01 <_start-0x60f8d1ff>
  78:	735c3030 	.inst	0x735c3030 ; undefined
  7c:	65645c77 	fnmla	z23.h, p7/m, z3.h, z4.h
  80:	67677562 	.inst	0x67677562 ; undefined
  84:	615c7265 	.inst	0x615c7265 ; undefined
  88:	706f5f6c 	adr	x12, dec77 <_start-0x60f21389>
  8c:	636f6e65 	.inst	0x636f6e65 ; undefined
  90:	6f635c64 	.inst	0x6f635c64 ; undefined
  94:	6972746e 	ldpsw	x14, x29, [x3, #-112]
  98:	6f6c5c62 	.inst	0x6f6c5c62 ; undefined
  9c:	72656461 	.inst	0x72656461 ; undefined
  a0:	6c665c73 	ldnp	d19, d23, [x3, #-416]
  a4:	5c687361 	ldr	d1, d0f10 <_start-0x60f2f0f0>
  a8:	636d6d65 	.inst	0x636d6d65 ; undefined
  ac:	6377645c 	.inst	0x6377645c ; undefined
  b0:	6368736d 	.inst	0x6368736d ; undefined
  b4:	554e4700 	.inst	0x554e4700 ; undefined
  b8:	20534120 	.inst	0x20534120 ; undefined
  bc:	36332e32 	tbz	w18, #6, 6680 <_start-0x60ff9980>
  c0:	6300312e 	.inst	0x6300312e ; undefined
  c4:	5f6c7274 	sqdmlsl	s20, h19, v12.h[2]
  c8:	65736162 	fnmls	z2.h, p0/m, z11.h, z19.h
  cc:	755f5f00 	.inst	0x755f5f00 ; undefined
  d0:	38746e69 	.inst	0x38746e69 ; undefined
  d4:	6500745f 	.inst	0x6500745f ; undefined
  d8:	5f636d6d 	.inst	0x5f636d6d ; undefined
  dc:	64616572 	.inst	0x64616572 ; undefined
  e0:	6f6c625f 	umlsl2	v31.4s, v18.8h, v12.h[2]
  e4:	47006b63 	.inst	0x47006b63 ; undefined
  e8:	4320554e 	.inst	0x4320554e ; undefined
  ec:	31203731 	adds	w17, w25, #0x80d
  f0:	2e332e30 	uqsub	v16.8b, v17.8b, v19.8b
  f4:	30322031 	adr	x17, 644f9 <_start-0x60f9bb07>
  f8:	36303132 	tbz	w18, #6, 71c <_start-0x60fff8e4>
  fc:	2d203132 	stp	s18, s12, [x9, #-256]
 100:	6962616d 	ldpsw	x13, x24, [x11, #-240]
 104:	36706c3d 	tbz	w29, #14, e88 <_start-0x60fff178>
 108:	6d2d2034 	stp	d20, d8, [x1, #-304]
 10c:	7474696c 	.inst	0x7474696c ; undefined
 110:	652d656c 	.inst	0x652d656c ; undefined
 114:	6169646e 	.inst	0x6169646e ; undefined
 118:	672d206e 	.inst	0x672d206e ; undefined
 11c:	734f2d20 	.inst	0x734f2d20 ; undefined
 120:	6e662d20 	uqsub	v0.8h, v9.8h, v6.8h
 124:	75622d6f 	.inst	0x75622d6f ; undefined
 128:	69746c69 	ldpsw	x9, x27, [x3, #-96]
 12c:	662d206e 	.inst	0x662d206e ; undefined
 130:	00434950 	.inst	0x00434950 ; undefined
 134:	636d6d65 	.inst	0x636d6d65 ; undefined
 138:	6961775f 	ldpsw	xzr, x29, [x26, #-248]
 13c:	69665f74 	ldpsw	x20, x23, [x27, #-208]
 140:	75006f66 	.inst	0x75006f66 ; undefined
 144:	6769736e 	.inst	0x6769736e ; undefined
 148:	2064656e 	.inst	0x2064656e ; undefined
 14c:	72616863 	.inst	0x72616863 ; undefined
 150:	6e6f6c00 	umin	v0.8h, v0.8h, v15.8h
 154:	6e752067 	usubl2	v7.4s, v3.8h, v21.8h
 158:	6e676973 	.inst	0x6e676973 ; undefined
 15c:	69206465 	stgp	x5, x25, [x3, #-1024]
 160:	7300746e 	.inst	0x7300746e ; undefined
 164:	74726f68 	.inst	0x74726f68 ; undefined
 168:	736e7520 	.inst	0x736e7520 ; undefined
 16c:	656e6769 	fnmls	z9.h, p1/m, z27.h, z14.h
 170:	6e692064 	usubl2	v4.4s, v3.8h, v9.8h
 174:	6f640074 	mla	v20.8h, v3.8h, v4.h[2]
 178:	665f656e 	.inst	0x665f656e ; undefined
 17c:	0067616c 	.inst	0x0067616c ; undefined
 180:	636d6d65 	.inst	0x636d6d65 ; undefined
 184:	6c6f705f 	ldnp	d31, d28, [x2, #-272]
 188:	6e695f6c 	uqrshl	v12.8h, v27.8h, v9.8h
 18c:	6f770074 	mla	v20.8h, v3.8h, v7.h[3]
 190:	615f6b72 	.inst	0x615f6b72 ; undefined
 194:	5f616572 	.inst	0x5f616572 ; undefined
 198:	72617473 	.inst	0x72617473 ; undefined
 19c:	5f5f0074 	.inst	0x5f5f0074 ; undefined
 1a0:	746e6975 	.inst	0x746e6975 ; undefined
 1a4:	745f3233 	.inst	0x745f3233 ; undefined
 1a8:	6d6d6500 	ldp	d0, d25, [x8, #-304]
 1ac:	72775f63 	.inst	0x72775f63 ; undefined
 1b0:	5f657469 	sqshl	d9, d3, #37
 1b4:	636f6c62 	.inst	0x636f6c62 ; undefined
 1b8:	6e69006b 	uaddl2	v11.4s, v3.8h, v9.8h
 1bc:	61765f74 	.inst	0x61765f74 ; undefined
 1c0:	3a44006c 	ccmn	w3, w4, #0xc, eq  // eq = none
 1c4:	726f772f 	.inst	0x726f772f ; undefined
 1c8:	30322f6b 	adr	x11, 647b5 <_start-0x60f9b84b>
 1cc:	612f3232 	.inst	0x612f3232 ; undefined
 1d0:	3030396c 	adr	x12, 608fd <_start-0x60f9f703>
 1d4:	77732f30 	.inst	0x77732f30 ; undefined
 1d8:	6265642f 	.inst	0x6265642f ; undefined
 1dc:	65676775 	fnmls	z21.h, p1/m, z27.h, z7.h
 1e0:	6c612f72 	ldnp	d18, d11, [x27, #-496]
 1e4:	65706f5f 	fnmls	z31.h, p3/m, z26.h, z16.h
 1e8:	64636f6e 	.inst	0x64636f6e ; undefined
 1ec:	6e6f632f 	rsubhn2	v15.8h, v25.4s, v15.4s
 1f0:	62697274 	.inst	0x62697274 ; undefined
 1f4:	616f6c2f 	.inst	0x616f6c2f ; undefined
 1f8:	73726564 	.inst	0x73726564 ; undefined
 1fc:	616c662f 	.inst	0x616c662f ; undefined
 200:	652f6873 	.inst	0x652f6873 ; undefined
 204:	2f636d6d 	.inst	0x2f636d6d ; undefined
 208:	6d637764 	ldp	d4, d29, [x27, #-464]
 20c:	2f636873 	umlsl	v19.4s, v3.4h, v3.h[6]
 210:	2f637273 	fcmla	v19.4h, v19.4h, v3.h[1], #270
 214:	6d637764 	ldp	d4, d29, [x27, #-464]
 218:	2e636873 	.inst	0x2e636873 ; undefined
 21c:	6f770063 	mla	v3.8h, v3.8h, v7.h[3]
 220:	635f6472 	.inst	0x635f6472 ; undefined
 224:	7300746e 	.inst	0x7300746e ; undefined
 228:	74726f68 	.inst	0x74726f68 ; undefined
 22c:	746e6920 	.inst	0x746e6920 ; undefined
 230:	66756200 	.inst	0x66756200 ; undefined
 234:	00726566 	.inst	0x00726566 ; undefined
 238:	676e6f6c 	.inst	0x676e6f6c ; undefined
 23c:	746e6920 	.inst	0x746e6920 ; undefined
 240:	656c6300 	fnmls	z0.h, p0/m, z24.h, z12.h
 244:	725f7261 	.inst	0x725f7261 ; undefined
 248:	66006765 	.inst	0x66006765 ; undefined
 24c:	5f67616c 	.inst	0x5f67616c ; undefined
 250:	7366666f 	.inst	0x7366666f ; undefined
 254:	5f007465 	.inst	0x5f007465 ; undefined
 258:	746e695f 	.inst	0x746e695f ; undefined
 25c:	745f3233 	.inst	0x745f3233 ; undefined
 260:	2f3a4400 	sri	v0.2s, v0.2s, #6
 264:	6b726f77 	.inst	0x6b726f77 ; undefined
 268:	3230322f 	orr	w15, w17, #0x1fff0000
 26c:	6c612f32 	ldnp	d18, d11, [x25, #-496]
 270:	30303039 	adr	x25, 60875 <_start-0x60f9f78b>
 274:	2f77732f 	fcmla	v15.4h, v25.4h, v23.h[1], #270
 278:	75626564 	.inst	0x75626564 ; undefined
 27c:	72656767 	.inst	0x72656767 ; undefined
 280:	5f6c612f 	.inst	0x5f6c612f ; undefined
 284:	6e65706f 	uabdl2	v15.4s, v3.8h, v5.8h
 288:	2f64636f 	umlsl	v15.4s, v27.4h, v4.h[2]
 28c:	746e6f63 	.inst	0x746e6f63 ; undefined
 290:	2f626972 	umlsl	v18.4s, v11.4h, v2.h[6]
 294:	64616f6c 	.inst	0x64616f6c ; undefined
 298:	2f737265 	fcmla	v5.4h, v19.4h, v19.h[1], #270
 29c:	73616c66 	.inst	0x73616c66 ; undefined
 2a0:	6d652f68 	ldp	d8, d11, [x27, #-432]
 2a4:	642f636d 	.inst	0x642f636d ; undefined
 2a8:	736d6377 	.inst	0x736d6377 ; undefined
 2ac:	732f6368 	.inst	0x732f6368 ; undefined
 2b0:	642f6372 	.inst	0x642f6372 ; undefined
 2b4:	736d6377 	.inst	0x736d6377 ; undefined
 2b8:	615f6368 	.inst	0x615f6368 ; undefined
 2bc:	636e7973 	.inst	0x636e7973 ; undefined
 2c0:	6200632e 	.inst	0x6200632e ; undefined
 2c4:	6b636f6c 	.inst	0x6b636f6c ; undefined
 2c8:	6464615f 	.inst	0x6464615f ; undefined
 2cc:	6c620072 	ldnp	d18, d0, [x3, #-480]
 2d0:	5f6b636f 	.inst	0x5f6b636f ; undefined
 2d4:	657a6973 	fnmls	z19.h, p2/m, z11.h, z26.h
 2d8:	66756200 	.inst	0x66756200 ; undefined
 2dc:	646e655f 	.inst	0x646e655f ; undefined
 2e0:	756f6300 	.inst	0x756f6300 ; undefined
 2e4:	6500746e 	.inst	0x6500746e ; undefined
 2e8:	5f636d6d 	.inst	0x5f636d6d ; undefined
 2ec:	6d637764 	ldp	d4, d29, [x27, #-464]
 2f0:	00636873 	.inst	0x00636873 ; undefined
 2f4:	5f667562 	sqshl	d2, d11, #38
 2f8:	72617473 	.inst	0x72617473 ; undefined
 2fc:	Address 0x00000000000002fc is out of bounds.


Disassembly of section .debug_loc:

0000000000000000 <.debug_loc>:
	...
   8:	00a40000 	.inst	0x00a40000 ; undefined
   c:	00000000 	udf	#0
  10:	00d70000 	.inst	0x00d70000 ; undefined
  14:	00000000 	udf	#0
  18:	00010000 	.inst	0x00010000 ; undefined
  1c:	0000d750 	udf	#55120
  20:	00000000 	udf	#0
  24:	0000f000 	udf	#61440
  28:	00000000 	udf	#0
  2c:	63000100 	.inst	0x63000100 ; undefined
  30:	000000f0 	udf	#240
  34:	00000000 	udf	#0
  38:	000000fb 	udf	#251
  3c:	00000000 	udf	#0
  40:	fb500001 	.inst	0xfb500001 ; undefined
  44:	00000000 	udf	#0
  48:	fc000000 	stur	d0, [x0]
  4c:	00000000 	udf	#0
  50:	04000000 	add	z0.b, p0/m, z0.b, z0.b
  54:	5001f300 	adr	x0, 3eb6 <_start-0x60ffc14a>
  58:	0000fc9f 	udf	#64671
  5c:	00000000 	udf	#0
  60:	00010c00 	.inst	0x00010c00 ; undefined
  64:	00000000 	udf	#0
  68:	63000100 	.inst	0x63000100 ; undefined
	...
  84:	000000a4 	udf	#164
  88:	00000000 	udf	#0
  8c:	000000c4 	udf	#196
  90:	00000000 	udf	#0
  94:	c4510001 	ld1sb	{z1.d}, p0/z, [x0, z17.d, sxtw]
  98:	00000000 	udf	#0
  9c:	f4000000 	.inst	0xf4000000 ; undefined
  a0:	00000000 	udf	#0
  a4:	01000000 	.inst	0x01000000 ; undefined
  a8:	00f46500 	.inst	0x00f46500 ; undefined
  ac:	00000000 	udf	#0
  b0:	00fc0000 	.inst	0x00fc0000 ; undefined
  b4:	00000000 	udf	#0
  b8:	00040000 	.inst	0x00040000 ; undefined
  bc:	9f5101f3 	.inst	0x9f5101f3 ; undefined
  c0:	000000fc 	udf	#252
  c4:	00000000 	udf	#0
  c8:	0000010c 	udf	#268
  cc:	00000000 	udf	#0
  d0:	00650001 	.inst	0x00650001 ; undefined
	...
  e4:	a4000000 	ld1rqb	{z0.b}, p0/z, [x0, x0]
  e8:	00000000 	udf	#0
  ec:	d7000000 	.inst	0xd7000000 ; undefined
  f0:	00000000 	udf	#0
  f4:	01000000 	.inst	0x01000000 ; undefined
  f8:	00d75200 	.inst	0x00d75200 ; undefined
  fc:	00000000 	udf	#0
 100:	010c0000 	.inst	0x010c0000 ; undefined
 104:	00000000 	udf	#0
 108:	00040000 	.inst	0x00040000 ; undefined
 10c:	9f5201f3 	.inst	0x9f5201f3 ; undefined
	...
 128:	000000a4 	udf	#164
 12c:	00000000 	udf	#0
 130:	000000d7 	udf	#215
 134:	00000000 	udf	#0
 138:	d7530001 	.inst	0xd7530001 ; undefined
 13c:	00000000 	udf	#0
 140:	f0000000 	adrp	x0, 3000 <_start-0x60ffd000>
 144:	00000000 	udf	#0
 148:	01000000 	.inst	0x01000000 ; undefined
 14c:	00f06400 	.inst	0x00f06400 ; undefined
 150:	00000000 	udf	#0
 154:	00fc0000 	.inst	0x00fc0000 ; undefined
 158:	00000000 	udf	#0
 15c:	00040000 	.inst	0x00040000 ; undefined
 160:	9f5301f3 	.inst	0x9f5301f3 ; undefined
 164:	000000fc 	udf	#252
 168:	00000000 	udf	#0
 16c:	0000010c 	udf	#268
 170:	00000000 	udf	#0
 174:	00640001 	.inst	0x00640001 ; undefined
	...
 184:	01000000 	.inst	0x01000000 ; undefined
 188:	00000000 	udf	#0
 18c:	d8000101 	prfm	pldl1strm, 1ac <_start-0x60fffe54>
 190:	00000000 	udf	#0
 194:	dc000000 	.inst	0xdc000000 ; undefined
 198:	00000000 	udf	#0
 19c:	02000000 	.inst	0x02000000 ; undefined
 1a0:	dc9f3000 	.inst	0xdc9f3000 ; undefined
 1a4:	00000000 	udf	#0
 1a8:	e8000000 	.inst	0xe8000000 ; undefined
 1ac:	00000000 	udf	#0
 1b0:	01000000 	.inst	0x01000000 ; undefined
 1b4:	00fc5000 	.inst	0x00fc5000 ; undefined
 1b8:	00000000 	udf	#0
 1bc:	01040000 	.inst	0x01040000 ; undefined
 1c0:	00000000 	udf	#0
 1c4:	00010000 	.inst	0x00010000 ; undefined
 1c8:	00010450 	.inst	0x00010450 ; undefined
 1cc:	00000000 	udf	#0
 1d0:	00010800 	.inst	0x00010800 ; undefined
 1d4:	00000000 	udf	#0
 1d8:	70000300 	adr	x0, 23b <_start-0x60fffdc5>
 1dc:	00009f01 	udf	#40705
	...
 1f4:	00000050 	udf	#80
 1f8:	00000000 	udf	#0
 1fc:	00000058 	udf	#88
 200:	00000000 	udf	#0
 204:	58510001 	ldr	x1, a2204 <_start-0x60f5ddfc>
 208:	00000000 	udf	#0
 20c:	60000000 	.inst	0x60000000 ; undefined
 210:	00000000 	udf	#0
 214:	02000000 	.inst	0x02000000 ; undefined
 218:	60087000 	.inst	0x60087000 ; undefined
 21c:	00000000 	udf	#0
 220:	a4000000 	ld1rqb	{z0.b}, p0/z, [x0, x0]
 224:	00000000 	udf	#0
 228:	04000000 	add	z0.b, p0/m, z0.b, z0.b
 22c:	5101f300 	sub	w0, w24, #0x7c
 230:	0000009f 	udf	#159
	...
 240:	74000000 	.inst	0x74000000 ; undefined
 244:	00000000 	udf	#0
 248:	7c000000 	stur	h0, [x0]
 24c:	00000000 	udf	#0
 250:	01000000 	.inst	0x01000000 ; undefined
 254:	00005100 	udf	#20736
	...
 268:	00640000 	.inst	0x00640000 ; undefined
 26c:	00000000 	udf	#0
 270:	00740000 	.inst	0x00740000 ; undefined
 274:	00000000 	udf	#0
 278:	00010000 	.inst	0x00010000 ; undefined
 27c:	00008c51 	udf	#35921
 280:	00000000 	udf	#0
 284:	00009c00 	udf	#39936
 288:	00000000 	udf	#0
 28c:	51000100 	sub	w0, w8, #0x0
	...
 2a0:	00010001 	.inst	0x00010001 ; undefined
 2a4:	00000064 	udf	#100
 2a8:	00000000 	udf	#0
 2ac:	00000074 	udf	#116
 2b0:	00000000 	udf	#0
 2b4:	00710007 	.inst	0x00710007 ; undefined
 2b8:	1a312534 	.inst	0x1a312534 ; undefined
 2bc:	00008c9f 	udf	#35999
 2c0:	00000000 	udf	#0
 2c4:	00009c00 	udf	#39936
 2c8:	00000000 	udf	#0
 2cc:	71000700 	subs	w0, w24, #0x1
 2d0:	31253100 	adds	w0, w8, #0x94c
 2d4:	00009f1a 	udf	#40730
	...
 2e8:	00180000 	.inst	0x00180000 ; undefined
 2ec:	00000000 	udf	#0
 2f0:	004c0000 	.inst	0x004c0000 ; undefined
 2f4:	00000000 	udf	#0
 2f8:	00010000 	.inst	0x00010000 ; undefined
 2fc:	00004c50 	udf	#19536
 300:	00000000 	udf	#0
 304:	00005000 	udf	#20480
 308:	00000000 	udf	#0
 30c:	f3000400 	.inst	0xf3000400 ; undefined
 310:	009f5001 	.inst	0x009f5001 ; undefined
	...
 324:	18000000 	ldr	w0, 324 <_start-0x60fffcdc>
 328:	00000000 	udf	#0
 32c:	24000000 	cmphs	p0.b, p0/z, z0.b, z0.b
 330:	00000000 	udf	#0
 334:	01000000 	.inst	0x01000000 ; undefined
 338:	00245100 	.inst	0x00245100 ; NYI
 33c:	00000000 	udf	#0
 340:	00500000 	.inst	0x00500000 ; undefined
 344:	00000000 	udf	#0
 348:	00010000 	.inst	0x00010000 ; undefined
 34c:	00000053 	udf	#83
	...
 35c:	28000000 	stnp	w0, w0, [x0]
 360:	00000000 	udf	#0
 364:	50000000 	adr	x0, 366 <_start-0x60fffc9a>
 368:	00000000 	udf	#0
 36c:	01000000 	.inst	0x01000000 ; undefined
 370:	00005200 	udf	#20992
	...
 384:	00000044 	udf	#68
 388:	00000000 	udf	#0
 38c:	00000050 	udf	#80
 390:	00000000 	udf	#0
 394:	00510001 	.inst	0x00510001 ; undefined
	...
 3a4:	03000000 	.inst	0x03000000 ; undefined
 3a8:	18000100 	ldr	w0, 3c8 <_start-0x60fffc38>
 3ac:	00000000 	udf	#0
 3b0:	24000000 	cmphs	p0.b, p0/z, z0.b, z0.b
 3b4:	00000000 	udf	#0
 3b8:	02000000 	.inst	0x02000000 ; undefined
 3bc:	289f3000 	stp	w0, w12, [x0], #248
 3c0:	00000000 	udf	#0
 3c4:	50000000 	adr	x0, 3c6 <_start-0x60fffc3a>
 3c8:	00000000 	udf	#0
 3cc:	08000000 	stxrb	w0, w0, [x0]
 3d0:	73007200 	.inst	0x73007200 ; undefined
 3d4:	1a312500 	.inst	0x1a312500 ; undefined
 3d8:	0000009f 	udf	#159
	...
 3e8:	03030000 	.inst	0x03030000 ; undefined
	...
 3f4:	00000400 	udf	#1024
 3f8:	00000000 	udf	#0
 3fc:	50000100 	adr	x0, 41e <_start-0x60fffbe2>
 400:	00000004 	udf	#4
 404:	00000000 	udf	#0
 408:	00000018 	udf	#24
 40c:	00000000 	udf	#0
 410:	00510001 	.inst	0x00510001 ; undefined
	...
 420:	01000000 	.inst	0x01000000 ; undefined
 424:	04000000 	add	z0.b, p0/m, z0.b, z0.b
 428:	00000000 	udf	#0
 42c:	08000000 	stxrb	w0, w0, [x0]
 430:	00000000 	udf	#0
 434:	02000000 	.inst	0x02000000 ; undefined
 438:	089f3000 	stllrb	w0, [x0]
 43c:	00000000 	udf	#0
 440:	18000000 	ldr	w0, 440 <_start-0x60fffbc0>
 444:	00000000 	udf	#0
 448:	01000000 	.inst	0x01000000 ; undefined
 44c:	00005200 	udf	#20992
	...
 45c:	00010000 	.inst	0x00010000 ; undefined
 460:	00040000 	.inst	0x00040000 ; undefined
 464:	00000000 	udf	#0
 468:	000c0000 	.inst	0x000c0000 ; undefined
 46c:	00000000 	udf	#0
 470:	00020000 	.inst	0x00020000 ; undefined
 474:	000c9f30 	.inst	0x000c9f30 ; undefined
 478:	00000000 	udf	#0
 47c:	00180000 	.inst	0x00180000 ; undefined
 480:	00000000 	udf	#0
 484:	00010000 	.inst	0x00010000 ; undefined
 488:	00000050 	udf	#80
	...
 4a8:	00004000 	udf	#16384
 4ac:	00000000 	udf	#0
 4b0:	50000100 	adr	x0, 4d2 <_start-0x60fffb2e>
 4b4:	00000040 	udf	#64
 4b8:	00000000 	udf	#0
 4bc:	00000054 	udf	#84
 4c0:	00000000 	udf	#0
 4c4:	54670001 	b.ne	ce4c4 <_start-0x60f31b3c>  // b.any
 4c8:	00000000 	udf	#0
 4cc:	64000000 	.inst	0x64000000 ; undefined
 4d0:	00000000 	udf	#0
 4d4:	04000000 	add	z0.b, p0/m, z0.b, z0.b
 4d8:	5001f300 	adr	x0, 433a <_start-0x60ffbcc6>
 4dc:	0000649f 	udf	#25759
 4e0:	00000000 	udf	#0
 4e4:	00009c00 	udf	#39936
 4e8:	00000000 	udf	#0
 4ec:	67000100 	.inst	0x67000100 ; undefined
	...
 510:	00000040 	udf	#64
 514:	00000000 	udf	#0
 518:	40510001 	.inst	0x40510001 ; undefined
 51c:	00000000 	udf	#0
 520:	54000000 	b.eq	520 <_start-0x60fffae0>  // b.none
 524:	00000000 	udf	#0
 528:	01000000 	.inst	0x01000000 ; undefined
 52c:	00546800 	.inst	0x00546800 ; undefined
 530:	00000000 	udf	#0
 534:	00640000 	.inst	0x00640000 ; undefined
 538:	00000000 	udf	#0
 53c:	00040000 	.inst	0x00040000 ; undefined
 540:	9f5101f3 	.inst	0x9f5101f3 ; undefined
 544:	00000064 	udf	#100
 548:	00000000 	udf	#0
 54c:	0000009c 	udf	#156
 550:	00000000 	udf	#0
 554:	00680001 	.inst	0x00680001 ; undefined
	...
 56c:	00000100 	udf	#256
 570:	00000000 	udf	#0
 574:	40000000 	.inst	0x40000000 ; undefined
 578:	00000000 	udf	#0
 57c:	01000000 	.inst	0x01000000 ; undefined
 580:	00405200 	.inst	0x00405200 ; undefined
 584:	00000000 	udf	#0
 588:	004c0000 	.inst	0x004c0000 ; undefined
 58c:	00000000 	udf	#0
 590:	00010000 	.inst	0x00010000 ; undefined
 594:	00006464 	udf	#25700
 598:	00000000 	udf	#0
 59c:	00008c00 	udf	#35840
 5a0:	00000000 	udf	#0
 5a4:	64000100 	.inst	0x64000100 ; undefined
 5a8:	00000098 	udf	#152
 5ac:	00000000 	udf	#0
 5b0:	0000009c 	udf	#156
 5b4:	00000000 	udf	#0
 5b8:	00640001 	.inst	0x00640001 ; undefined
	...
 5dc:	00004000 	udf	#16384
 5e0:	00000000 	udf	#0
 5e4:	53000100 	ubfx	w0, w8, #0, #1
 5e8:	00000040 	udf	#64
 5ec:	00000000 	udf	#0
 5f0:	00000050 	udf	#80
 5f4:	00000000 	udf	#0
 5f8:	50650001 	adr	x1, ca5fa <_start-0x60f35a06>
 5fc:	00000000 	udf	#0
 600:	5c000000 	ldr	d0, 600 <_start-0x60fffa00>
 604:	00000000 	udf	#0
 608:	03000000 	.inst	0x03000000 ; undefined
 60c:	9f788b00 	.inst	0x9f788b00 ; undefined
 610:	0000005c 	udf	#92
 614:	00000000 	udf	#0
 618:	00000064 	udf	#100
 61c:	00000000 	udf	#0
 620:	01f30004 	.inst	0x01f30004 ; undefined
 624:	00649f53 	.inst	0x00649f53 ; undefined
 628:	00000000 	udf	#0
 62c:	009c0000 	.inst	0x009c0000 ; undefined
 630:	00000000 	udf	#0
 634:	00010000 	.inst	0x00010000 ; undefined
 638:	00000065 	udf	#101
	...
 658:	00004000 	udf	#16384
 65c:	00000000 	udf	#0
 660:	54000100 	b.eq	680 <_start-0x60fff980>  // b.none
 664:	00000040 	udf	#64
 668:	00000000 	udf	#0
 66c:	00000058 	udf	#88
 670:	00000000 	udf	#0
 674:	58690001 	ldr	x1, d2674 <_start-0x60f2d98c>
 678:	00000000 	udf	#0
 67c:	64000000 	.inst	0x64000000 ; undefined
 680:	00000000 	udf	#0
 684:	04000000 	add	z0.b, p0/m, z0.b, z0.b
 688:	5401f300 	b.eq	44e8 <_start-0x60ffbb18>  // b.none
 68c:	0000649f 	udf	#25759
 690:	00000000 	udf	#0
 694:	00009c00 	udf	#39936
 698:	00000000 	udf	#0
 69c:	69000100 	stgp	x0, x0, [x8]
	...
 6bc:	00400000 	.inst	0x00400000 ; undefined
 6c0:	00000000 	udf	#0
 6c4:	00010000 	.inst	0x00010000 ; undefined
 6c8:	00004055 	udf	#16469
 6cc:	00000000 	udf	#0
 6d0:	00005000 	udf	#20480
 6d4:	00000000 	udf	#0
 6d8:	66000100 	.inst	0x66000100 ; undefined
 6dc:	00000064 	udf	#100
 6e0:	00000000 	udf	#0
 6e4:	0000009c 	udf	#156
 6e8:	00000000 	udf	#0
 6ec:	00660001 	.inst	0x00660001 ; undefined
	...
 700:	70000000 	adr	x0, 703 <_start-0x60fff8fd>
 704:	00000000 	udf	#0
 708:	88000000 	stxr	w0, w0, [x0]
 70c:	00000000 	udf	#0
 710:	01000000 	.inst	0x01000000 ; undefined
 714:	00886300 	.inst	0x00886300 ; undefined
 718:	00000000 	udf	#0
 71c:	009c0000 	.inst	0x009c0000 ; undefined
 720:	00000000 	udf	#0
 724:	00010000 	.inst	0x00010000 ; undefined
 728:	00000056 	udf	#86
	...

Disassembly of section .comment:

0000000000000000 <.comment>:
   0:	3a434347 	ccmn	w26, w3, #0x7, mi  // mi = first
   4:	4e472820 	trn1	v0.8h, v1.8h, v7.8h
   8:	6f542055 	umlal2	v21.4s, v2.8h, v4.h[1]
   c:	68636c6f 	.inst	0x68636c6f ; undefined
  10:	206e6961 	.inst	0x206e6961 ; undefined
  14:	20726f66 	.inst	0x20726f66 ; undefined
  18:	20656874 	.inst	0x20656874 ; undefined
  1c:	72702d41 	.inst	0x72702d41 ; undefined
  20:	6c69666f 	ldnp	d15, d25, [x19, #-368]
  24:	72412065 	.inst	0x72412065 ; undefined
  28:	74696863 	.inst	0x74696863 ; undefined
  2c:	75746365 	.inst	0x75746365 ; undefined
  30:	31206572 	adds	w18, w11, #0x819
  34:	2d332e30 	stp	s16, s11, [x17, #-104]
  38:	31323032 	adds	w18, w1, #0xc8c
  3c:	2037302e 	.inst	0x2037302e ; undefined
  40:	6d726128 	ldp	d8, d24, [x9, #-224]
  44:	2e30312d 	usubw	v13.8h, v9.8h, v16.8b
  48:	29293932 	stp	w18, w14, [x9, #-184]
  4c:	2e303120 	usubw	v0.8h, v9.8h, v16.8b
  50:	20312e33 	.inst	0x20312e33 ; undefined
  54:	31323032 	adds	w18, w1, #0xc8c
  58:	31323630 	adds	w16, w17, #0xc8d
	...

Disassembly of section .debug_frame:

0000000000000000 <.debug_frame>:
   0:	0000000c 	udf	#12
   4:	ffffffff 	.inst	0xffffffff ; undefined
   8:	78040001 	sturh	w1, [x0, #64]
   c:	001f0c1e 	.inst	0x001f0c1e ; undefined
  10:	00000014 	udf	#20
  14:	00000000 	udf	#0
  18:	61000018 	.inst	0x61000018 ; undefined
  1c:	00000000 	udf	#0
  20:	00000018 	udf	#24
  24:	00000000 	udf	#0
  28:	00000014 	udf	#20
  2c:	00000000 	udf	#0
  30:	61000030 	.inst	0x61000030 ; undefined
  34:	00000000 	udf	#0
  38:	00000038 	udf	#56
  3c:	00000000 	udf	#0
  40:	00000014 	udf	#20
  44:	00000000 	udf	#0
  48:	61000068 	.inst	0x61000068 ; undefined
  4c:	00000000 	udf	#0
  50:	00000054 	udf	#84
  54:	00000000 	udf	#0
  58:	00000034 	udf	#52
  5c:	00000000 	udf	#0
  60:	610000bc 	.inst	0x610000bc ; undefined
  64:	00000000 	udf	#0
  68:	00000068 	udf	#104
  6c:	00000000 	udf	#0
  70:	9d300e41 	.inst	0x9d300e41 ; undefined
  74:	42059e06 	.inst	0x42059e06 ; undefined
  78:	03940493 	.inst	0x03940493 ; undefined
  7c:	4f029543 	orr	v3.8h, #0x4a
  80:	d5ddde0a 	.inst	0xd5ddde0a ; undefined
  84:	000ed4d3 	.inst	0x000ed4d3 ; undefined
  88:	00000b41 	udf	#2881
  8c:	00000000 	udf	#0
  90:	0000000c 	udf	#12
  94:	ffffffff 	.inst	0xffffffff ; undefined
  98:	78040001 	sturh	w1, [x0, #64]
  9c:	001f0c1e 	.inst	0x001f0c1e ; undefined
  a0:	00000044 	udf	#68
  a4:	00000090 	udf	#144
  a8:	61000124 	.inst	0x61000124 ; undefined
  ac:	00000000 	udf	#0
  b0:	0000009c 	udf	#156
  b4:	00000000 	udf	#0
  b8:	9d600e41 	.inst	0x9d600e41 ; undefined
  bc:	420b9e0c 	.inst	0x420b9e0c ; undefined
  c0:	039a0499 	.inst	0x039a0499 ; undefined
  c4:	940a9344 	bl	2a4dd4 <_start-0x60d5b22c>
  c8:	08954209 	stllrb	w9, [x16]
  cc:	97430796 	bl	fffffffffd0c1f24 <stack_end+0xffffffff9c0c1b64>
  d0:	43059806 	.inst	0x43059806 ; undefined
  d4:	0a49029b 	and	w27, w20, w9, lsr #0
  d8:	d9dbddde 	.inst	0xd9dbddde ; undefined
  dc:	d5d8d7da 	.inst	0xd5d8d7da ; undefined
  e0:	0ed4d3d6 	.inst	0x0ed4d3d6 ; undefined
  e4:	000b4100 	.inst	0x000b4100 ; undefined
