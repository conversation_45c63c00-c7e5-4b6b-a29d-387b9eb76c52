# JTAG Writer

一个基于C语言的前端烧写工具，用于向OpenOCD发送烧写请求。

## 项目概述

JTAG Writer是一个前后端分离的烧写工具：
- **前端**: 本工具，负责用户交互和烧写请求管理
- **后端**: OpenOCD，负责驱动JTAG进行实际的固件烧写操作

## 功能特性

### 烧写模式
- **本地烧写**: 直接连接本地的OpenOCD服务
- **远程烧写**: 通过网络连接远程的OpenOCD服务器

### 核心功能
- 与OpenOCD建立通信连接（TCL RPC接口）
- 发送烧写命令和固件文件
- 实时显示烧写进度和状态
- 完善的错误处理和日志记录
- 支持多种固件文件格式（bin, hex, elf等）

### 用户界面
- 命令行界面（CLI）
- 可选的图形用户界面（GUI）

## 技术栈

- **主语言**: C语言
- **网络通信**: 标准socket API
- **配置管理**: JSON格式配置文件
- **构建系统**: CMake
- **跨平台支持**: Windows, Linux, macOS

## 项目结构

```
jtag_writer/
├── src/                    # 源代码目录
│   ├── core/              # 核心功能模块
│   ├── network/           # 网络通信模块
│   ├── config/            # 配置管理模块
│   ├── ui/                # 用户界面模块
│   └── utils/             # 工具函数模块
├── include/               # 头文件目录
├── config/                # 配置文件目录
├── docs/                  # 文档目录
├── tests/                 # 测试目录
├── examples/              # 示例目录
├── build/                 # 构建目录
├── bin/                   # 可执行文件目录
├── CMakeLists.txt         # CMake构建文件
└── README.md              # 项目说明文档
```

## 快速开始

### 编译

```bash
mkdir build && cd build
cmake ..
make
```

### 使用示例

```bash
# 本地烧写
./jtag_writer -f firmware.bin -t stm32f4x

# 远程烧写
./jtag_writer -f firmware.bin -t stm32f4x -h ************* -p 6666

# 查看帮助
./jtag_writer --help
```

## 配置文件

支持JSON格式的配置文件，可以预设常用的烧写配置：

```json
{
  "default_target": "stm32f4x",
  "openocd": {
    "host": "127.0.0.1",
    "tcl_port": 6666,
    "telnet_port": 4444
  },
  "flash": {
    "base_address": "0x08000000",
    "verify": true,
    "erase_before_write": true
  }
}
```

## 开发计划

- [x] 项目架构设计
- [ ] 网络通信模块
- [ ] OpenOCD接口封装
- [ ] 烧写功能实现
- [ ] 配置管理系统
- [ ] 用户界面开发
- [ ] 测试用例编写
- [ ] 文档完善

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request。
