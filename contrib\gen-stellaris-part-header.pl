#!/usr/bin/perl
# Automatically generates the StellarisParts struct in src/flash/nor/stellaris.c
# Uses the header files from TI/Luminary's StellarisWare complete Firmware Development Package
# available from: http://www.luminarymicro.com/products/software_updates.html

$comment = "// Autogenerated by contrib/gen-stellaris-part-header.pl
// From Stellaris Firmware Development Package revision";

$struct_header = "static const struct {
	uint8_t class;
	uint8_t partno;
	const char *partname;
} StellarisParts[] = {
";

$struct_footer = "\t{0xFF, 0x00, \"Unknown Part\"}\n};\n";

$#ARGV == 1 || die "Usage: $0 <inc directory> <output file>\n";
-d $ARGV[0] || die $ARGV[0]." is not a directory\n";
$dir = $ARGV[0];
-f $ARGV[1] || die $ARGV[1]." is not a file\n";
$file = $ARGV[1];
print STDERR "Scanning $dir, Updating $file\n";

opendir(DIR, $dir) || die "can't open $dir: $!";
@files = readdir(DIR);
closedir(DIR);

@header_files = sort(grep(/lm.+\.h/, @files));

$ver = 0;
$new_struct = $struct_header;
process_file(@header_files);
$new_struct .= $struct_footer;

$dump = "$comment $ver\n$new_struct";
{
	local($/, *INPUT);
	open(INPUT, $file) || die "can't open $file: $!";
	$contents = <INPUT>;
	close(INPUT);
}

$old_struct = qr/((^\/\/.*?\n)*)\Q$struct_header\E.*?$struct_footer/sm;
$contents =~ s/$old_struct/$dump/;
open(OUTPUT, ">$file") || die "can't open file $file for writing: $!";
print OUTPUT $contents;
close(OUTPUT);

sub process_file {
	foreach $h_file (@_) {
		($base) = ($h_file =~ m/lm..(.{3,7})\.h/ig);
		$base = uc($base);
		local($/, *FILE);
		open(FILE, "$dir/$h_file");
		$content = <FILE>;
		close(FILE);
		$invalid = 0;
		if ($content =~ /This is part of revision (\d+) of/) {
			if ($ver != 0 and $ver != $1) {
				print STDERR "File version mismatch: $ver != $1\n";
				$ver = max($ver, $1);
			} else {
				$ver = $1;
			}
		}

		if ($content =~ /SYSCTL_DID0_CLASS_[^M].+?0x(\S+)/s) {
			$class = hex($1) >> 16;
		} else {
			# attempt another way to get class
			if ($content =~ /\s(\S+)-class/) {
				$class = getclass($1);
				if ($class eq 0xFF) {
					print STDERR "$h_file unknown class\n";
					$invalid = 1;
				}
			} else {
				print STDERR "$h_file is missing SYSCTL_DID0_CLASS_\n";
				$class = 0;
				$invalid = 1;
			}
		}

		if ($content =~ /SYSCTL_DID1_PRTNO_$base.+0x(\S+)/) {
			$prtno = hex($1);
			$base = "LM3S" . $base;
		} else {
			# LM4F have a changed header
			if ($content =~ /SYSCTL_DID1_PRTNO_LM4F$base.+?0x(\S+)/s) {
				$prtno = hex($1);
				$base = "LM4F" . $base;
			} else {
				print STDERR "$h_file is missing SYSCTL_DID1_PRTNO\n";
				$prtno = 0;
				$invalid = 1;
			}
		}
		$new_member = sprintf "{0x%02X, 0x%02X, \"%s\"},", $class, $prtno >> 16, $base;
		if ($invalid == 1) {
			#$new_struct .= "\t//$new_member\t// Invalid\n";
		} else {
			$new_struct .= "\t$new_member\n";
		}
	}
}

sub getclass {
	$class = $_[0];
	if ($class =~ /Sandstorm/i) {
		return 0;
	} elsif ($class =~ /Fury/i) {
		return 1;
	} elsif ($class =~ /DustDevil/i) {
		return 3;
	} elsif ($class =~ /Tempest/i) {
		return 4;
	} elsif ($class =~ /Blizzard/i) {
		return 5;
	} elsif ($class =~ /Firestorm/i) {
		return 6;
	}
	return 0xFF;
}
