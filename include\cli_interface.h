#ifndef CLI_INTERFACE_H
#define CLI_INTERFACE_H

#include "jtag_writer.h"

// 命令行选项
typedef struct {
    char short_opt;
    const char* long_opt;
    bool has_arg;
    const char* description;
} cli_option_t;

// 命令行参数解析结果
typedef struct {
    bool help_requested;
    bool version_requested;
    bool verbose;
    char config_file[MAX_PATH_LENGTH];
    char firmware_file[MAX_PATH_LENGTH];
    char target_type[64];
    char openocd_host[256];
    int openocd_tcl_port;
    int openocd_telnet_port;
    uint32_t base_address;
    firmware_type_t file_type;
    flash_mode_t flash_mode;
    log_level_t log_level;
    bool erase_before_write;
    bool verify_after_write;
    bool reset_after_write;
} cli_args_t;

// 函数声明

/**
 * 初始化CLI界面
 * @return 错误代码
 */
jtag_error_t cli_init(void);

/**
 * 清理CLI界面
 */
void cli_cleanup(void);

/**
 * 解析命令行参数
 * @param argc 参数数量
 * @param argv 参数数组
 * @param args 解析结果
 * @return 错误代码
 */
jtag_error_t cli_parse_args(int argc, char* argv[], cli_args_t* args);

/**
 * 显示帮助信息
 * @param program_name 程序名称
 */
void cli_show_help(const char* program_name);

/**
 * 显示版本信息
 */
void cli_show_version(void);

/**
 * 显示使用示例
 */
void cli_show_examples(void);

/**
 * 显示支持的目标类型
 */
void cli_show_supported_targets(void);

/**
 * 显示支持的文件类型
 */
void cli_show_supported_file_types(void);

/**
 * 进度显示回调函数
 * @param percentage 进度百分比
 * @param message 进度消息
 */
void cli_progress_callback(int percentage, const char* message);

/**
 * 显示错误信息
 * @param error 错误代码
 * @param message 错误消息
 */
void cli_show_error(jtag_error_t error, const char* message);

/**
 * 显示成功信息
 * @param message 成功消息
 */
void cli_show_success(const char* message);

/**
 * 显示警告信息
 * @param message 警告消息
 */
void cli_show_warning(const char* message);

/**
 * 显示信息
 * @param message 信息内容
 */
void cli_show_info(const char* message);

/**
 * 询问用户确认
 * @param message 确认消息
 * @return true表示确认，false表示取消
 */
bool cli_ask_confirmation(const char* message);

/**
 * 等待用户按键
 * @param message 提示消息
 */
void cli_wait_for_key(const char* message);

/**
 * 显示Flash bank信息
 * @param banks Flash bank信息数组
 * @param num_banks bank数量
 */
void cli_show_flash_banks(const flash_bank_info_t* banks, int num_banks);

/**
 * 显示烧写结果
 * @param result 烧写结果
 */
void cli_show_flash_result(const flash_result_t* result);

/**
 * 显示配置信息
 * @param config 配置结构
 */
void cli_show_config(const jtag_writer_config_t* config);

/**
 * 从字符串解析文件类型
 * @param type_str 文件类型字符串
 * @return 文件类型
 */
firmware_type_t cli_parse_file_type(const char* type_str);

/**
 * 从字符串解析烧写模式
 * @param mode_str 烧写模式字符串
 * @return 烧写模式
 */
flash_mode_t cli_parse_flash_mode(const char* mode_str);

/**
 * 从字符串解析地址
 * @param addr_str 地址字符串
 * @param address 解析后的地址
 * @return 错误代码
 */
jtag_error_t cli_parse_address(const char* addr_str, uint32_t* address);

#endif // CLI_INTERFACE_H
