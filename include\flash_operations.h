#ifndef FLASH_OPERATIONS_H
#define FLASH_OPERATIONS_H

#include "jtag_writer.h"
#include "openocd_client.h"

// Flash操作结果
typedef struct {
    bool success;
    uint32_t bytes_processed;
    uint32_t total_bytes;
    double elapsed_time;
    char error_message[256];
} flash_result_t;

// Flash bank信息
typedef struct {
    int bank_id;
    uint32_t base_address;
    uint32_t size;
    uint32_t sector_size;
    uint32_t num_sectors;
    char driver_name[64];
    bool is_protected;
} flash_bank_info_t;

// 函数声明

/**
 * 擦除Flash
 * @param client OpenOCD客户端
 * @param bank_id Flash bank ID
 * @param start_sector 起始扇区
 * @param end_sector 结束扇区
 * @param callback 进度回调函数
 * @return Flash操作结果
 */
flash_result_t flash_erase_sectors(openocd_client_t* client,
                                  int bank_id,
                                  uint32_t start_sector,
                                  uint32_t end_sector,
                                  progress_callback_t callback);

/**
 * 擦除整个Flash
 * @param client OpenOCD客户端
 * @param bank_id Flash bank ID
 * @param callback 进度回调函数
 * @return Flash操作结果
 */
flash_result_t flash_mass_erase(openocd_client_t* client,
                               int bank_id,
                               progress_callback_t callback);

/**
 * 写入Flash
 * @param client OpenOCD客户端
 * @param config 烧写配置
 * @param callback 进度回调函数
 * @return Flash操作结果
 */
flash_result_t flash_write_file(openocd_client_t* client,
                               const flash_config_t* config,
                               progress_callback_t callback);

/**
 * 验证Flash内容
 * @param client OpenOCD客户端
 * @param config 烧写配置
 * @param callback 进度回调函数
 * @return Flash操作结果
 */
flash_result_t flash_verify_file(openocd_client_t* client,
                                const flash_config_t* config,
                                progress_callback_t callback);

/**
 * 读取Flash内容
 * @param client OpenOCD客户端
 * @param bank_id Flash bank ID
 * @param address 起始地址
 * @param length 读取长度
 * @param output_file 输出文件路径
 * @param callback 进度回调函数
 * @return Flash操作结果
 */
flash_result_t flash_read_to_file(openocd_client_t* client,
                                 int bank_id,
                                 uint32_t address,
                                 uint32_t length,
                                 const char* output_file,
                                 progress_callback_t callback);

/**
 * 获取Flash bank信息
 * @param client OpenOCD客户端
 * @param bank_id Flash bank ID
 * @param info Flash bank信息结构
 * @return 错误代码
 */
jtag_error_t flash_get_bank_info(openocd_client_t* client,
                                int bank_id,
                                flash_bank_info_t* info);

/**
 * 列出所有Flash banks
 * @param client OpenOCD客户端
 * @param banks Flash bank信息数组
 * @param max_banks 最大bank数量
 * @param num_banks 实际bank数量
 * @return 错误代码
 */
jtag_error_t flash_list_banks(openocd_client_t* client,
                             flash_bank_info_t* banks,
                             int max_banks,
                             int* num_banks);

/**
 * 检查Flash保护状态
 * @param client OpenOCD客户端
 * @param bank_id Flash bank ID
 * @param is_protected 保护状态
 * @return 错误代码
 */
jtag_error_t flash_check_protection(openocd_client_t* client,
                                   int bank_id,
                                   bool* is_protected);

/**
 * 解除Flash保护
 * @param client OpenOCD客户端
 * @param bank_id Flash bank ID
 * @return 错误代码
 */
jtag_error_t flash_unlock(openocd_client_t* client, int bank_id);

/**
 * 设置Flash保护
 * @param client OpenOCD客户端
 * @param bank_id Flash bank ID
 * @return 错误代码
 */
jtag_error_t flash_lock(openocd_client_t* client, int bank_id);

/**
 * 获取固件文件类型
 * @param filename 文件名
 * @return 固件文件类型
 */
firmware_type_t flash_detect_file_type(const char* filename);

/**
 * 获取固件文件大小
 * @param filename 文件名
 * @param file_size 文件大小
 * @return 错误代码
 */
jtag_error_t flash_get_file_size(const char* filename, uint32_t* file_size);

#endif // FLASH_OPERATIONS_H
