#include "openocd_client.h"
#include "logger.h"

/**
 * 创建OpenOCD客户端
 */
openocd_client_t* openocd_client_create(const openocd_config_t* config) {
    if (!config) {
        LOG_ERROR("配置参数为空");
        return NULL;
    }
    
    openocd_client_t* client = malloc(sizeof(openocd_client_t));
    if (!client) {
        LOG_ERROR("分配内存失败");
        return NULL;
    }
    
    memset(client, 0, sizeof(openocd_client_t));
    client->socket = INVALID_SOCKET;
    client->config = *config;
    client->connected = false;
    
    LOG_DEBUG("创建OpenOCD客户端成功");
    return client;
}

/**
 * 销毁OpenOCD客户端
 */
void openocd_client_destroy(openocd_client_t* client) {
    if (!client) {
        return;
    }
    
    if (client->connected) {
        openocd_client_disconnect(client);
    }
    
    free(client);
    LOG_DEBUG("销毁OpenOCD客户端");
}

/**
 * 连接到OpenOCD服务器
 */
jtag_error_t openocd_client_connect(openocd_client_t* client) {
    if (!client) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    if (client->connected) {
        LOG_WARN("客户端已连接");
        return JTAG_SUCCESS;
    }
    
    // 创建socket
    client->socket = socket(AF_INET, SOCK_STREAM, 0);
    if (client->socket == INVALID_SOCKET) {
        LOG_ERROR("创建socket失败: %d", SOCKET_ERROR_CODE);
        return JTAG_ERROR_NETWORK_ERROR;
    }
    
    // 设置socket选项
    int opt = 1;
#ifdef _WIN32
    setsockopt(client->socket, SOL_SOCKET, SO_REUSEADDR, (char*)&opt, sizeof(opt));
#else
    setsockopt(client->socket, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
#endif
    
    // 设置超时
    if (client->config.timeout_ms > 0) {
#ifdef _WIN32
        DWORD timeout = client->config.timeout_ms;
        setsockopt(client->socket, SOL_SOCKET, SO_RCVTIMEO, (char*)&timeout, sizeof(timeout));
        setsockopt(client->socket, SOL_SOCKET, SO_SNDTIMEO, (char*)&timeout, sizeof(timeout));
#else
        struct timeval timeout;
        timeout.tv_sec = client->config.timeout_ms / 1000;
        timeout.tv_usec = (client->config.timeout_ms % 1000) * 1000;
        setsockopt(client->socket, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));
        setsockopt(client->socket, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout));
#endif
    }
    
    // 连接到服务器
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(client->config.tcl_port);
    
    if (inet_pton(AF_INET, client->config.host, &server_addr.sin_addr) <= 0) {
        LOG_ERROR("无效的IP地址: %s", client->config.host);
        CLOSE_SOCKET(client->socket);
        client->socket = INVALID_SOCKET;
        return JTAG_ERROR_NETWORK_ERROR;
    }
    
    if (connect(client->socket, (struct sockaddr*)&server_addr, sizeof(server_addr)) == SOCKET_ERROR) {
        LOG_ERROR("连接服务器失败: %s:%d, 错误码: %d", 
                 client->config.host, client->config.tcl_port, SOCKET_ERROR_CODE);
        CLOSE_SOCKET(client->socket);
        client->socket = INVALID_SOCKET;
        return JTAG_ERROR_NETWORK_ERROR;
    }
    
    client->connected = true;
    LOG_INFO("成功连接到OpenOCD服务器: %s:%d", client->config.host, client->config.tcl_port);
    
    return JTAG_SUCCESS;
}

/**
 * 断开与OpenOCD服务器的连接
 */
void openocd_client_disconnect(openocd_client_t* client) {
    if (!client || !client->connected) {
        return;
    }
    
    if (client->socket != INVALID_SOCKET) {
        CLOSE_SOCKET(client->socket);
        client->socket = INVALID_SOCKET;
    }
    
    client->connected = false;
    LOG_INFO("断开与OpenOCD服务器的连接");
}

/**
 * 发送命令到OpenOCD
 */
jtag_error_t openocd_client_send_command(openocd_client_t* client, 
                                        const char* command,
                                        char* response, 
                                        size_t response_size) {
    if (!client || !command || !response) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    if (!client->connected) {
        LOG_ERROR("客户端未连接");
        return JTAG_ERROR_NETWORK_ERROR;
    }
    
    // 构造完整命令（添加结束符）
    char full_command[MAX_COMMAND_LENGTH];
    int cmd_len = snprintf(full_command, sizeof(full_command), "%s%s", command, OPENOCD_COMMAND_TOKEN);
    if (cmd_len >= sizeof(full_command)) {
        LOG_ERROR("命令太长");
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    LOG_DEBUG("发送命令: %s", command);
    
    // 发送命令
    int sent = send(client->socket, full_command, cmd_len, 0);
    if (sent == SOCKET_ERROR || sent != cmd_len) {
        LOG_ERROR("发送命令失败: %d", SOCKET_ERROR_CODE);
        return JTAG_ERROR_NETWORK_ERROR;
    }
    
    // 接收响应
    memset(response, 0, response_size);
    int total_received = 0;
    
    while (total_received < response_size - 1) {
        int received = recv(client->socket, response + total_received, 
                          response_size - total_received - 1, 0);
        
        if (received == SOCKET_ERROR) {
            LOG_ERROR("接收响应失败: %d", SOCKET_ERROR_CODE);
            return JTAG_ERROR_NETWORK_ERROR;
        }
        
        if (received == 0) {
            LOG_ERROR("连接被服务器关闭");
            client->connected = false;
            return JTAG_ERROR_NETWORK_ERROR;
        }
        
        total_received += received;
        
        // 检查是否收到结束符
        if (total_received > 0 && response[total_received - 1] == '\x1a') {
            response[total_received - 1] = '\0'; // 移除结束符
            break;
        }
    }
    
    LOG_DEBUG("收到响应: %s", response);
    return JTAG_SUCCESS;
}

/**
 * 检查OpenOCD连接状态
 */
bool openocd_client_is_connected(const openocd_client_t* client) {
    return client && client->connected;
}

/**
 * 获取OpenOCD版本信息
 */
jtag_error_t openocd_client_get_version(openocd_client_t* client,
                                       char* version,
                                       size_t version_size) {
    return openocd_client_send_command(client, "version", version, version_size);
}

/**
 * 获取目标信息
 */
jtag_error_t openocd_client_get_target_info(openocd_client_t* client,
                                           char* target_info,
                                           size_t info_size) {
    return openocd_client_send_command(client, "targets", target_info, info_size);
}

/**
 * 获取Flash bank信息
 */
jtag_error_t openocd_client_get_flash_banks(openocd_client_t* client,
                                           char* bank_info,
                                           size_t info_size) {
    return openocd_client_send_command(client, "flash banks", bank_info, info_size);
}

/**
 * 初始化目标
 */
jtag_error_t openocd_client_init_target(openocd_client_t* client) {
    char response[MAX_RESPONSE_LENGTH];
    return openocd_client_send_command(client, "init", response, sizeof(response));
}

/**
 * 重置目标
 */
jtag_error_t openocd_client_reset_target(openocd_client_t* client, bool halt) {
    char response[MAX_RESPONSE_LENGTH];
    const char* command = halt ? "reset halt" : "reset";
    return openocd_client_send_command(client, command, response, sizeof(response));
}

/**
 * 暂停目标
 */
jtag_error_t openocd_client_halt_target(openocd_client_t* client) {
    char response[MAX_RESPONSE_LENGTH];
    return openocd_client_send_command(client, "halt", response, sizeof(response));
}

/**
 * 恢复目标运行
 */
jtag_error_t openocd_client_resume_target(openocd_client_t* client) {
    char response[MAX_RESPONSE_LENGTH];
    return openocd_client_send_command(client, "resume", response, sizeof(response));
}
