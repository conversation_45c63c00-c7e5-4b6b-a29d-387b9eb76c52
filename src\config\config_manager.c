#include "config_manager.h"
#include "logger.h"

static bool g_config_initialized = false;

/**
 * 初始化配置管理器
 */
jtag_error_t config_manager_init(void) {
    if (g_config_initialized) {
        return JTAG_SUCCESS;
    }
    
    g_config_initialized = true;
    return JTAG_SUCCESS;
}

/**
 * 清理配置管理器
 */
void config_manager_cleanup(void) {
    g_config_initialized = false;
}

/**
 * 获取默认配置
 */
void config_get_default(jtag_writer_config_t* config) {
    if (!config) {
        return;
    }
    
    memset(config, 0, sizeof(jtag_writer_config_t));
    
    // OpenOCD配置
    strcpy(config->openocd.host, DEFAULT_OPENOCD_HOST);
    config->openocd.tcl_port = DEFAULT_OPENOCD_TCL_PORT;
    config->openocd.telnet_port = DEFAULT_OPENOCD_TELNET_PORT;
    config->openocd.timeout_ms = DEFAULT_TIMEOUT_MS;
    
    // Flash配置
    config->flash.base_address = 0x08000000; // STM32默认Flash地址
    config->flash.file_type = FIRMWARE_TYPE_AUTO;
    config->flash.mode = FLASH_MODE_WRITE;
    config->flash.erase_before_write = true;
    config->flash.verify_after_write = true;
    config->flash.reset_after_write = true;
    strcpy(config->flash.target_type, "stm32f1x");
    
    // 日志配置
    config->log_level = LOG_LEVEL_INFO;
}

/**
 * 验证配置有效性
 */
jtag_error_t config_validate(const jtag_writer_config_t* config) {
    if (!config) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    // 验证OpenOCD配置
    if (strlen(config->openocd.host) == 0) {
        LOG_ERROR("OpenOCD主机地址不能为空");
        return JTAG_ERROR_CONFIG_ERROR;
    }
    
    if (config->openocd.tcl_port <= 0 || config->openocd.tcl_port > 65535) {
        LOG_ERROR("OpenOCD TCL端口无效: %d", config->openocd.tcl_port);
        return JTAG_ERROR_CONFIG_ERROR;
    }
    
    if (config->openocd.telnet_port <= 0 || config->openocd.telnet_port > 65535) {
        LOG_ERROR("OpenOCD Telnet端口无效: %d", config->openocd.telnet_port);
        return JTAG_ERROR_CONFIG_ERROR;
    }
    
    // 验证Flash配置
    if (config->flash.mode == FLASH_MODE_WRITE || config->flash.mode == FLASH_MODE_VERIFY) {
        if (strlen(config->flash.firmware_file) == 0) {
            LOG_ERROR("固件文件路径不能为空");
            return JTAG_ERROR_CONFIG_ERROR;
        }
        
        // 检查文件是否存在
        FILE* file = fopen(config->flash.firmware_file, "rb");
        if (!file) {
            LOG_ERROR("固件文件不存在: %s", config->flash.firmware_file);
            return JTAG_ERROR_FILE_NOT_FOUND;
        }
        fclose(file);
    }
    
    if (strlen(config->flash.target_type) == 0) {
        LOG_ERROR("目标类型不能为空");
        return JTAG_ERROR_CONFIG_ERROR;
    }
    
    return JTAG_SUCCESS;
}

/**
 * 打印配置信息
 */
void config_print(const jtag_writer_config_t* config) {
    if (!config) {
        return;
    }
    
    printf("=== JTAG Writer 配置信息 ===\n");
    printf("OpenOCD配置:\n");
    printf("  主机地址: %s\n", config->openocd.host);
    printf("  TCL端口: %d\n", config->openocd.tcl_port);
    printf("  Telnet端口: %d\n", config->openocd.telnet_port);
    printf("  超时时间: %d ms\n", config->openocd.timeout_ms);
    
    printf("Flash配置:\n");
    printf("  固件文件: %s\n", config->flash.firmware_file);
    printf("  目标类型: %s\n", config->flash.target_type);
    printf("  基地址: 0x%08x\n", config->flash.base_address);
    printf("  文件类型: %d\n", config->flash.file_type);
    printf("  烧写模式: %d\n", config->flash.mode);
    printf("  写前擦除: %s\n", config->flash.erase_before_write ? "是" : "否");
    printf("  写后验证: %s\n", config->flash.verify_after_write ? "是" : "否");
    printf("  写后重置: %s\n", config->flash.reset_after_write ? "是" : "否");
    
    printf("日志配置:\n");
    printf("  日志级别: %s\n", logger_level_string(config->log_level));
    printf("=============================\n");
}

/**
 * 设置OpenOCD主机地址
 */
jtag_error_t config_set_openocd_host(jtag_writer_config_t* config, const char* host) {
    if (!config || !host) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    if (strlen(host) >= sizeof(config->openocd.host)) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    strcpy(config->openocd.host, host);
    return JTAG_SUCCESS;
}

/**
 * 设置OpenOCD端口
 */
jtag_error_t config_set_openocd_ports(jtag_writer_config_t* config, int tcl_port, int telnet_port) {
    if (!config) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    if (tcl_port <= 0 || tcl_port > 65535 || telnet_port <= 0 || telnet_port > 65535) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    config->openocd.tcl_port = tcl_port;
    config->openocd.telnet_port = telnet_port;
    return JTAG_SUCCESS;
}

/**
 * 设置固件文件
 */
jtag_error_t config_set_firmware_file(jtag_writer_config_t* config, const char* firmware_file) {
    if (!config || !firmware_file) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    if (strlen(firmware_file) >= sizeof(config->flash.firmware_file)) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    strcpy(config->flash.firmware_file, firmware_file);
    return JTAG_SUCCESS;
}

/**
 * 设置目标类型
 */
jtag_error_t config_set_target_type(jtag_writer_config_t* config, const char* target_type) {
    if (!config || !target_type) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    if (strlen(target_type) >= sizeof(config->flash.target_type)) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    strcpy(config->flash.target_type, target_type);
    return JTAG_SUCCESS;
}

/**
 * 设置基地址
 */
jtag_error_t config_set_base_address(jtag_writer_config_t* config, uint32_t base_address) {
    if (!config) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    config->flash.base_address = base_address;
    return JTAG_SUCCESS;
}

/**
 * 设置文件类型
 */
jtag_error_t config_set_file_type(jtag_writer_config_t* config, firmware_type_t file_type) {
    if (!config) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    config->flash.file_type = file_type;
    return JTAG_SUCCESS;
}

/**
 * 设置烧写模式
 */
jtag_error_t config_set_flash_mode(jtag_writer_config_t* config, flash_mode_t mode) {
    if (!config) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    config->flash.mode = mode;
    return JTAG_SUCCESS;
}

/**
 * 设置日志级别
 */
jtag_error_t config_set_log_level(jtag_writer_config_t* config, log_level_t log_level) {
    if (!config) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    config->log_level = log_level;
    return JTAG_SUCCESS;
}

/**
 * 从命令行参数更新配置
 */
jtag_error_t config_update_from_args(jtag_writer_config_t* config, int argc, char* argv[]) {
    if (!config || !argv) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    // 简化的命令行解析，实际应该使用getopt或类似库
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-h") == 0 || strcmp(argv[i], "--host") == 0) {
            if (i + 1 < argc) {
                config_set_openocd_host(config, argv[++i]);
            }
        } else if (strcmp(argv[i], "-p") == 0 || strcmp(argv[i], "--port") == 0) {
            if (i + 1 < argc) {
                int port = atoi(argv[++i]);
                config->openocd.tcl_port = port;
            }
        } else if (strcmp(argv[i], "-f") == 0 || strcmp(argv[i], "--file") == 0) {
            if (i + 1 < argc) {
                config_set_firmware_file(config, argv[++i]);
            }
        } else if (strcmp(argv[i], "-t") == 0 || strcmp(argv[i], "--target") == 0) {
            if (i + 1 < argc) {
                config_set_target_type(config, argv[++i]);
            }
        } else if (strcmp(argv[i], "-a") == 0 || strcmp(argv[i], "--address") == 0) {
            if (i + 1 < argc) {
                uint32_t addr = (uint32_t)strtoul(argv[++i], NULL, 0);
                config_set_base_address(config, addr);
            }
        } else if (strcmp(argv[i], "--erase") == 0) {
            config->flash.mode = FLASH_MODE_ERASE;
        } else if (strcmp(argv[i], "--verify") == 0) {
            config->flash.mode = FLASH_MODE_VERIFY;
        } else if (strcmp(argv[i], "--read") == 0) {
            config->flash.mode = FLASH_MODE_READ;
        } else if (strcmp(argv[i], "--no-erase") == 0) {
            config->flash.erase_before_write = false;
        } else if (strcmp(argv[i], "--no-verify") == 0) {
            config->flash.verify_after_write = false;
        } else if (strcmp(argv[i], "--no-reset") == 0) {
            config->flash.reset_after_write = false;
        }
    }

    return JTAG_SUCCESS;
}

/**
 * 加载配置文件（简化版本，实际应该使用JSON解析）
 */
jtag_error_t config_load(const char* config_file, jtag_writer_config_t* config) {
    if (!config_file || !config) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    FILE* file = fopen(config_file, "r");
    if (!file) {
        LOG_WARN("无法打开配置文件: %s", config_file);
        return JTAG_ERROR_FILE_NOT_FOUND;
    }

    // 简化的配置文件解析
    // 实际应该使用JSON解析库如cJSON
    char line[256];
    while (fgets(line, sizeof(line), file)) {
        // 移除换行符
        char* newline = strchr(line, '\n');
        if (newline) *newline = '\0';

        // 跳过注释和空行
        if (line[0] == '#' || line[0] == '\0') {
            continue;
        }

        // 解析键值对
        char* equals = strchr(line, '=');
        if (equals) {
            *equals = '\0';
            char* key = line;
            char* value = equals + 1;

            // 移除前后空格
            while (*key == ' ') key++;
            while (*value == ' ') value++;

            // 解析配置项
            if (strcmp(key, "openocd_host") == 0) {
                config_set_openocd_host(config, value);
            } else if (strcmp(key, "openocd_tcl_port") == 0) {
                config->openocd.tcl_port = atoi(value);
            } else if (strcmp(key, "openocd_telnet_port") == 0) {
                config->openocd.telnet_port = atoi(value);
            } else if (strcmp(key, "firmware_file") == 0) {
                config_set_firmware_file(config, value);
            } else if (strcmp(key, "target_type") == 0) {
                config_set_target_type(config, value);
            } else if (strcmp(key, "base_address") == 0) {
                uint32_t addr = (uint32_t)strtoul(value, NULL, 0);
                config_set_base_address(config, addr);
            } else if (strcmp(key, "log_level") == 0) {
                log_level_t level = logger_parse_level(value);
                config_set_log_level(config, level);
            }
        }
    }

    fclose(file);
    LOG_INFO("成功加载配置文件: %s", config_file);
    return JTAG_SUCCESS;
}

/**
 * 保存配置文件（简化版本）
 */
jtag_error_t config_save(const char* config_file, const jtag_writer_config_t* config) {
    if (!config_file || !config) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    FILE* file = fopen(config_file, "w");
    if (!file) {
        LOG_ERROR("无法创建配置文件: %s", config_file);
        return JTAG_ERROR_CONFIG_ERROR;
    }

    fprintf(file, "# JTAG Writer 配置文件\n");
    fprintf(file, "# 自动生成，请谨慎修改\n\n");

    fprintf(file, "# OpenOCD配置\n");
    fprintf(file, "openocd_host=%s\n", config->openocd.host);
    fprintf(file, "openocd_tcl_port=%d\n", config->openocd.tcl_port);
    fprintf(file, "openocd_telnet_port=%d\n", config->openocd.telnet_port);
    fprintf(file, "\n");

    fprintf(file, "# Flash配置\n");
    fprintf(file, "firmware_file=%s\n", config->flash.firmware_file);
    fprintf(file, "target_type=%s\n", config->flash.target_type);
    fprintf(file, "base_address=0x%08x\n", config->flash.base_address);
    fprintf(file, "\n");

    fprintf(file, "# 日志配置\n");
    fprintf(file, "log_level=%s\n", logger_level_string(config->log_level));

    fclose(file);
    LOG_INFO("成功保存配置文件: %s", config_file);
    return JTAG_SUCCESS;
}
