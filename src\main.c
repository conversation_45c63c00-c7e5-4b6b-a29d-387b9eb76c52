#include "jtag_writer.h"
#include "openocd_client.h"
#include "flash_operations.h"
#include "config_manager.h"
#include "cli_interface.h"
#include "logger.h"

static jtag_writer_config_t g_config;
static openocd_client_t* g_client = NULL;

/**
 * 清理资源
 */
static void cleanup_resources(void) {
    if (g_client) {
        openocd_client_disconnect(g_client);
        openocd_client_destroy(g_client);
        g_client = NULL;
    }
    
    logger_cleanup();
    config_manager_cleanup();
    cli_cleanup();
    jtag_writer_cleanup();
}

/**
 * 信号处理函数
 */
static void signal_handler(int sig) {
    (void)sig; // 避免未使用参数警告
    printf("\n程序被中断，正在清理资源...\n");
    cleanup_resources();
    exit(EXIT_FAILURE);
}

/**
 * 初始化系统
 */
static jtag_error_t initialize_system(void) {
    jtag_error_t ret;
    
    // 初始化JTAG Writer
    ret = jtag_writer_init();
    if (ret != JTAG_SUCCESS) {
        fprintf(stderr, "初始化JTAG Writer失败: %s\n", jtag_error_string(ret));
        return ret;
    }
    
    // 初始化CLI
    ret = cli_init();
    if (ret != JTAG_SUCCESS) {
        fprintf(stderr, "初始化CLI失败: %s\n", jtag_error_string(ret));
        return ret;
    }
    
    // 初始化配置管理器
    ret = config_manager_init();
    if (ret != JTAG_SUCCESS) {
        fprintf(stderr, "初始化配置管理器失败: %s\n", jtag_error_string(ret));
        return ret;
    }
    
    return JTAG_SUCCESS;
}

/**
 * 执行烧写操作
 */
static jtag_error_t perform_flash_operation(void) {
    jtag_error_t ret;
    flash_result_t result;
    
    // 创建OpenOCD客户端
    g_client = openocd_client_create(&g_config.openocd);
    if (!g_client) {
        cli_show_error(JTAG_ERROR_MEMORY_ERROR, "创建OpenOCD客户端失败");
        return JTAG_ERROR_MEMORY_ERROR;
    }
    
    // 连接到OpenOCD
    cli_show_info("正在连接到OpenOCD服务器...");
    ret = openocd_client_connect(g_client);
    if (ret != JTAG_SUCCESS) {
        cli_show_error(ret, "连接OpenOCD服务器失败");
        return ret;
    }
    
    cli_show_success("成功连接到OpenOCD服务器");
    
    // 获取版本信息
    char version[256];
    ret = openocd_client_get_version(g_client, version, sizeof(version));
    if (ret == JTAG_SUCCESS) {
        cli_show_info(version);
    }
    
    // 初始化目标
    cli_show_info("正在初始化目标...");
    ret = openocd_client_init_target(g_client);
    if (ret != JTAG_SUCCESS) {
        cli_show_error(ret, "初始化目标失败");
        return ret;
    }
    
    // 根据模式执行不同操作
    switch (g_config.flash.mode) {
        case FLASH_MODE_WRITE:
            cli_show_info("开始烧写固件...");
            result = flash_write_file(g_client, &g_config.flash, cli_progress_callback);
            break;
            
        case FLASH_MODE_VERIFY:
            cli_show_info("开始验证固件...");
            result = flash_verify_file(g_client, &g_config.flash, cli_progress_callback);
            break;
            
        case FLASH_MODE_ERASE:
            cli_show_info("开始擦除Flash...");
            result = flash_mass_erase(g_client, 0, cli_progress_callback);
            break;
            
        case FLASH_MODE_READ:
            cli_show_info("开始读取Flash...");
            result = flash_read_to_file(g_client, 0, g_config.flash.base_address, 
                                      0, "flash_dump.bin", cli_progress_callback);
            break;
            
        default:
            cli_show_error(JTAG_ERROR_INVALID_ARGS, "不支持的烧写模式");
            return JTAG_ERROR_INVALID_ARGS;
    }
    
    // 显示结果
    cli_show_flash_result(&result);
    
    if (result.success) {
        cli_show_success("操作完成");
        
        // 如果需要重置
        if (g_config.flash.reset_after_write && g_config.flash.mode == FLASH_MODE_WRITE) {
            cli_show_info("正在重置目标...");
            ret = openocd_client_reset_target(g_client, false);
            if (ret == JTAG_SUCCESS) {
                cli_show_success("目标重置完成");
            } else {
                cli_show_warning("目标重置失败");
            }
        }
        
        return JTAG_SUCCESS;
    } else {
        cli_show_error(JTAG_ERROR_FLASH_ERROR, result.error_message);
        return JTAG_ERROR_FLASH_ERROR;
    }
}

/**
 * 主函数
 */
int main(int argc, char* argv[]) {
    jtag_error_t ret;
    cli_args_t args;
    
    // 设置信号处理
#ifdef _WIN32
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
#else
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGQUIT, signal_handler);
#endif
    
    // 初始化系统
    ret = initialize_system();
    if (ret != JTAG_SUCCESS) {
        return EXIT_FAILURE;
    }
    
    // 解析命令行参数
    ret = cli_parse_args(argc, argv, &args);
    if (ret != JTAG_SUCCESS) {
        cleanup_resources();
        return EXIT_FAILURE;
    }
    
    // 处理帮助和版本请求
    if (args.help_requested) {
        cli_show_help(argv[0]);
        cleanup_resources();
        return EXIT_SUCCESS;
    }
    
    if (args.version_requested) {
        cli_show_version();
        cleanup_resources();
        return EXIT_SUCCESS;
    }
    
    // 加载配置
    config_get_default(&g_config);
    
    if (strlen(args.config_file) > 0) {
        ret = config_load(args.config_file, &g_config);
        if (ret != JTAG_SUCCESS) {
            cli_show_warning("加载配置文件失败，使用默认配置");
        }
    }
    
    // 从命令行参数更新配置
    ret = config_update_from_args(&g_config, argc, argv);
    if (ret != JTAG_SUCCESS) {
        cli_show_error(ret, "更新配置失败");
        cleanup_resources();
        return EXIT_FAILURE;
    }
    
    // 验证配置
    ret = config_validate(&g_config);
    if (ret != JTAG_SUCCESS) {
        cli_show_error(ret, "配置验证失败");
        cleanup_resources();
        return EXIT_FAILURE;
    }
    
    // 初始化日志系统
    logger_config_t log_config = {
        .level = g_config.log_level,
        .output = args.verbose ? LOG_OUTPUT_BOTH : LOG_OUTPUT_CONSOLE,
        .timestamp_enabled = true,
        .color_enabled = true
    };
    strcpy(log_config.log_file, DEFAULT_LOG_FILE);
    
    ret = logger_init(&log_config);
    if (ret != JTAG_SUCCESS) {
        cli_show_warning("初始化日志系统失败");
    }
    
    // 显示配置信息（如果是详细模式）
    if (args.verbose) {
        cli_show_config(&g_config);
    }
    
    // 执行烧写操作
    ret = perform_flash_operation();
    
    // 清理资源
    cleanup_resources();
    
    return (ret == JTAG_SUCCESS) ? EXIT_SUCCESS : EXIT_FAILURE;
}
