#ifndef LOGGER_H
#define LOGGER_H

#include "jtag_writer.h"
#include <stdarg.h>
#include <time.h>

// 日志文件路径
#define DEFAULT_LOG_FILE "jtag_writer.log"
#define MAX_LOG_MESSAGE_SIZE 1024

// 日志输出目标
typedef enum {
    LOG_OUTPUT_CONSOLE = 1,
    LOG_OUTPUT_FILE = 2,
    LOG_OUTPUT_BOTH = 3
} log_output_t;

// 日志配置
typedef struct {
    log_level_t level;
    log_output_t output;
    char log_file[MAX_PATH_LENGTH];
    bool timestamp_enabled;
    bool color_enabled;
    FILE* file_handle;
} logger_config_t;

// 函数声明

/**
 * 初始化日志系统
 * @param config 日志配置
 * @return 错误代码
 */
jtag_error_t logger_init(const logger_config_t* config);

/**
 * 清理日志系统
 */
void logger_cleanup(void);

/**
 * 设置日志级别
 * @param level 日志级别
 */
void logger_set_level(log_level_t level);

/**
 * 获取日志级别
 * @return 当前日志级别
 */
log_level_t logger_get_level(void);

/**
 * 设置日志输出目标
 * @param output 输出目标
 * @return 错误代码
 */
jtag_error_t logger_set_output(log_output_t output);

/**
 * 设置日志文件
 * @param log_file 日志文件路径
 * @return 错误代码
 */
jtag_error_t logger_set_file(const char* log_file);

/**
 * 启用/禁用时间戳
 * @param enabled 是否启用
 */
void logger_set_timestamp(bool enabled);

/**
 * 启用/禁用颜色输出
 * @param enabled 是否启用
 */
void logger_set_color(bool enabled);

/**
 * 记录日志消息
 * @param level 日志级别
 * @param file 源文件名
 * @param line 行号
 * @param func 函数名
 * @param format 格式字符串
 * @param ... 可变参数
 */
void logger_log(log_level_t level, const char* file, int line, const char* func, const char* format, ...);

/**
 * 记录日志消息（使用va_list）
 * @param level 日志级别
 * @param file 源文件名
 * @param line 行号
 * @param func 函数名
 * @param format 格式字符串
 * @param args 参数列表
 */
void logger_vlog(log_level_t level, const char* file, int line, const char* func, const char* format, va_list args);

/**
 * 刷新日志缓冲区
 */
void logger_flush(void);

/**
 * 获取日志级别字符串
 * @param level 日志级别
 * @return 日志级别字符串
 */
const char* logger_level_string(log_level_t level);

/**
 * 从字符串解析日志级别
 * @param level_str 日志级别字符串
 * @return 日志级别
 */
log_level_t logger_parse_level(const char* level_str);

// 日志宏定义
#define LOG_ERROR(format, ...) logger_log(LOG_LEVEL_ERROR, __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)
#define LOG_WARN(format, ...)  logger_log(LOG_LEVEL_WARN,  __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)
#define LOG_INFO(format, ...)  logger_log(LOG_LEVEL_INFO,  __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)
#define LOG_DEBUG(format, ...) logger_log(LOG_LEVEL_DEBUG, __FILE__, __LINE__, __func__, format, ##__VA_ARGS__)

// 条件日志宏
#define LOG_ERROR_IF(condition, format, ...) do { if (condition) LOG_ERROR(format, ##__VA_ARGS__); } while(0)
#define LOG_WARN_IF(condition, format, ...)  do { if (condition) LOG_WARN(format, ##__VA_ARGS__); } while(0)
#define LOG_INFO_IF(condition, format, ...)  do { if (condition) LOG_INFO(format, ##__VA_ARGS__); } while(0)
#define LOG_DEBUG_IF(condition, format, ...) do { if (condition) LOG_DEBUG(format, ##__VA_ARGS__); } while(0)

#endif // LOGGER_H
