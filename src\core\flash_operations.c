#include "flash_operations.h"
#include "logger.h"
#include <time.h>

/**
 * 获取固件文件类型
 */
firmware_type_t flash_detect_file_type(const char* filename) {
    if (!filename) {
        return FIRMWARE_TYPE_AUTO;
    }
    
    const char* ext = strrchr(filename, '.');
    if (!ext) {
        return FIRMWARE_TYPE_BIN; // 默认为二进制
    }
    
    ext++; // 跳过点号
    
    if (strcasecmp(ext, "bin") == 0) {
        return FIRMWARE_TYPE_BIN;
    } else if (strcasecmp(ext, "hex") == 0) {
        return FIRMWARE_TYPE_HEX;
    } else if (strcasecmp(ext, "elf") == 0) {
        return FIRMWARE_TYPE_ELF;
    } else if (strcasecmp(ext, "s19") == 0 || strcasecmp(ext, "srec") == 0) {
        return FIRMWARE_TYPE_S19;
    }
    
    return FIRMWARE_TYPE_BIN; // 默认为二进制
}

/**
 * 获取固件文件大小
 */
jtag_error_t flash_get_file_size(const char* filename, uint32_t* file_size) {
    if (!filename || !file_size) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    FILE* file = fopen(filename, "rb");
    if (!file) {
        LOG_ERROR("无法打开文件: %s", filename);
        return JTAG_ERROR_FILE_NOT_FOUND;
    }
    
    fseek(file, 0, SEEK_END);
    long size = ftell(file);
    fclose(file);
    
    if (size < 0) {
        LOG_ERROR("获取文件大小失败: %s", filename);
        return JTAG_ERROR_FILE_NOT_FOUND;
    }
    
    *file_size = (uint32_t)size;
    return JTAG_SUCCESS;
}

/**
 * 获取文件类型字符串
 */
static const char* get_file_type_string(firmware_type_t type) {
    switch (type) {
        case FIRMWARE_TYPE_BIN: return "bin";
        case FIRMWARE_TYPE_HEX: return "ihex";
        case FIRMWARE_TYPE_ELF: return "elf";
        case FIRMWARE_TYPE_S19: return "s19";
        default: return "bin";
    }
}

/**
 * 擦除Flash扇区
 */
flash_result_t flash_erase_sectors(openocd_client_t* client,
                                  int bank_id,
                                  uint32_t start_sector,
                                  uint32_t end_sector,
                                  progress_callback_t callback) {
    flash_result_t result = {0};
    char command[MAX_COMMAND_LENGTH];
    char response[MAX_RESPONSE_LENGTH];
    clock_t start_time = clock();
    
    if (!client) {
        strcpy(result.error_message, "客户端参数为空");
        return result;
    }
    
    LOG_INFO("擦除Flash扇区 %u-%u (bank %d)", start_sector, end_sector, bank_id);
    
    if (callback) {
        callback(0, "开始擦除Flash扇区...");
    }
    
    // 构造擦除命令
    snprintf(command, sizeof(command), "flash erase_sector %d %u %u", 
             bank_id, start_sector, end_sector);
    
    jtag_error_t ret = openocd_client_send_command(client, command, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message), 
                "发送擦除命令失败: %s", jtag_error_string(ret));
        return result;
    }
    
    // 检查响应中是否有错误
    if (strstr(response, "error") || strstr(response, "Error") || strstr(response, "failed")) {
        snprintf(result.error_message, sizeof(result.error_message), 
                "擦除失败: %s", response);
        return result;
    }
    
    if (callback) {
        callback(100, "Flash扇区擦除完成");
    }
    
    result.success = true;
    result.bytes_processed = (end_sector - start_sector + 1) * 4096; // 假设扇区大小为4KB
    result.total_bytes = result.bytes_processed;
    result.elapsed_time = (double)(clock() - start_time) / CLOCKS_PER_SEC;
    
    LOG_INFO("Flash扇区擦除完成，耗时: %.2f秒", result.elapsed_time);
    return result;
}

/**
 * 擦除整个Flash
 */
flash_result_t flash_mass_erase(openocd_client_t* client,
                               int bank_id,
                               progress_callback_t callback) {
    flash_result_t result = {0};
    char command[MAX_COMMAND_LENGTH];
    char response[MAX_RESPONSE_LENGTH];
    clock_t start_time = clock();
    
    if (!client) {
        strcpy(result.error_message, "客户端参数为空");
        return result;
    }
    
    LOG_INFO("开始整片擦除Flash (bank %d)", bank_id);
    
    if (callback) {
        callback(0, "开始整片擦除Flash...");
    }
    
    // 构造整片擦除命令
    snprintf(command, sizeof(command), "flash erase_sector %d 0 last", bank_id);
    
    jtag_error_t ret = openocd_client_send_command(client, command, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message), 
                "发送整片擦除命令失败: %s", jtag_error_string(ret));
        return result;
    }
    
    // 检查响应中是否有错误
    if (strstr(response, "error") || strstr(response, "Error") || strstr(response, "failed")) {
        snprintf(result.error_message, sizeof(result.error_message), 
                "整片擦除失败: %s", response);
        return result;
    }
    
    if (callback) {
        callback(100, "Flash整片擦除完成");
    }
    
    result.success = true;
    result.elapsed_time = (double)(clock() - start_time) / CLOCKS_PER_SEC;
    
    LOG_INFO("Flash整片擦除完成，耗时: %.2f秒", result.elapsed_time);
    return result;
}

/**
 * 写入Flash
 */
flash_result_t flash_write_file(openocd_client_t* client,
                               const flash_config_t* config,
                               progress_callback_t callback) {
    flash_result_t result = {0};
    char command[MAX_COMMAND_LENGTH];
    char response[MAX_RESPONSE_LENGTH];
    clock_t start_time = clock();

    if (!client || !config) {
        strcpy(result.error_message, "参数为空");
        return result;
    }

    // 检查文件是否存在
    uint32_t file_size;
    jtag_error_t ret = flash_get_file_size(config->firmware_file, &file_size);
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "无法获取文件大小: %s", config->firmware_file);
        return result;
    }

    result.total_bytes = file_size;

    LOG_INFO("开始烧写文件: %s (大小: %u字节)", config->firmware_file, file_size);

    if (callback) {
        callback(0, "开始烧写固件...");
    }

    // 如果需要，先擦除Flash
    if (config->erase_before_write) {
        if (callback) {
            callback(10, "正在擦除Flash...");
        }

        flash_result_t erase_result = flash_mass_erase(client, 0, NULL);
        if (!erase_result.success) {
            strcpy(result.error_message, erase_result.error_message);
            return result;
        }
    }

    if (callback) {
        callback(30, "正在写入固件...");
    }

    // 构造写入命令
    const char* file_type = get_file_type_string(config->file_type);
    if (config->file_type == FIRMWARE_TYPE_BIN) {
        // 二进制文件需要指定地址
        snprintf(command, sizeof(command), "flash write_image erase \"%s\" 0x%08x %s",
                 config->firmware_file, config->base_address, file_type);
    } else {
        // 其他格式文件包含地址信息
        snprintf(command, sizeof(command), "flash write_image erase \"%s\" %s",
                 config->firmware_file, file_type);
    }

    ret = openocd_client_send_command(client, command, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "发送写入命令失败: %s", jtag_error_string(ret));
        return result;
    }

    // 检查响应中是否有错误
    if (strstr(response, "error") || strstr(response, "Error") || strstr(response, "failed")) {
        snprintf(result.error_message, sizeof(result.error_message),
                "写入失败: %s", response);
        return result;
    }

    if (callback) {
        callback(80, "固件写入完成");
    }

    // 如果需要，验证写入的内容
    if (config->verify_after_write) {
        if (callback) {
            callback(85, "正在验证固件...");
        }

        flash_result_t verify_result = flash_verify_file(client, config, NULL);
        if (!verify_result.success) {
            snprintf(result.error_message, sizeof(result.error_message),
                    "验证失败: %s", verify_result.error_message);
            return result;
        }
    }

    if (callback) {
        callback(100, "固件烧写完成");
    }

    result.success = true;
    result.bytes_processed = file_size;
    result.elapsed_time = (double)(clock() - start_time) / CLOCKS_PER_SEC;

    LOG_INFO("固件烧写完成，耗时: %.2f秒", result.elapsed_time);
    return result;
}

/**
 * 验证Flash内容
 */
flash_result_t flash_verify_file(openocd_client_t* client,
                                const flash_config_t* config,
                                progress_callback_t callback) {
    flash_result_t result = {0};
    char command[MAX_COMMAND_LENGTH];
    char response[MAX_RESPONSE_LENGTH];
    clock_t start_time = clock();

    if (!client || !config) {
        strcpy(result.error_message, "参数为空");
        return result;
    }

    // 检查文件是否存在
    uint32_t file_size;
    jtag_error_t ret = flash_get_file_size(config->firmware_file, &file_size);
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "无法获取文件大小: %s", config->firmware_file);
        return result;
    }

    result.total_bytes = file_size;

    LOG_INFO("开始验证文件: %s", config->firmware_file);

    if (callback) {
        callback(0, "开始验证固件...");
    }

    // 构造验证命令
    const char* file_type = get_file_type_string(config->file_type);
    if (config->file_type == FIRMWARE_TYPE_BIN) {
        snprintf(command, sizeof(command), "verify_image \"%s\" 0x%08x %s",
                 config->firmware_file, config->base_address, file_type);
    } else {
        snprintf(command, sizeof(command), "verify_image \"%s\" %s",
                 config->firmware_file, file_type);
    }

    ret = openocd_client_send_command(client, command, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "发送验证命令失败: %s", jtag_error_string(ret));
        return result;
    }

    // 检查响应中是否有错误
    if (strstr(response, "error") || strstr(response, "Error") || strstr(response, "failed")) {
        snprintf(result.error_message, sizeof(result.error_message),
                "验证失败: %s", response);
        return result;
    }

    if (callback) {
        callback(100, "固件验证完成");
    }

    result.success = true;
    result.bytes_processed = file_size;
    result.elapsed_time = (double)(clock() - start_time) / CLOCKS_PER_SEC;

    LOG_INFO("固件验证完成，耗时: %.2f秒", result.elapsed_time);
    return result;
}

/**
 * 读取Flash内容
 */
flash_result_t flash_read_to_file(openocd_client_t* client,
                                 int bank_id,
                                 uint32_t address,
                                 uint32_t length,
                                 const char* output_file,
                                 progress_callback_t callback) {
    flash_result_t result = {0};
    char command[MAX_COMMAND_LENGTH];
    char response[MAX_RESPONSE_LENGTH];
    clock_t start_time = clock();

    if (!client || !output_file) {
        strcpy(result.error_message, "参数为空");
        return result;
    }

    LOG_INFO("开始读取Flash: bank=%d, 地址=0x%08x, 长度=%u", bank_id, address, length);

    if (callback) {
        callback(0, "开始读取Flash...");
    }

    // 如果长度为0，读取整个bank
    if (length == 0) {
        snprintf(command, sizeof(command), "flash read_bank %d \"%s\"", bank_id, output_file);
    } else {
        snprintf(command, sizeof(command), "flash read_bank %d \"%s\" 0x%08x %u",
                 bank_id, output_file, address, length);
    }

    jtag_error_t ret = openocd_client_send_command(client, command, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "发送读取命令失败: %s", jtag_error_string(ret));
        return result;
    }

    // 检查响应中是否有错误
    if (strstr(response, "error") || strstr(response, "Error") || strstr(response, "failed")) {
        snprintf(result.error_message, sizeof(result.error_message),
                "读取失败: %s", response);
        return result;
    }

    if (callback) {
        callback(100, "Flash读取完成");
    }

    result.success = true;
    result.bytes_processed = length;
    result.total_bytes = length;
    result.elapsed_time = (double)(clock() - start_time) / CLOCKS_PER_SEC;

    LOG_INFO("Flash读取完成，耗时: %.2f秒", result.elapsed_time);
    return result;
}

/**
 * 获取Flash bank信息
 */
jtag_error_t flash_get_bank_info(openocd_client_t* client,
                                int bank_id,
                                flash_bank_info_t* info) {
    if (!client || !info) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    char response[MAX_RESPONSE_LENGTH];
    jtag_error_t ret = openocd_client_get_flash_banks(client, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        return ret;
    }

    // 解析Flash bank信息
    // 这里需要解析OpenOCD返回的Flash bank信息
    // 格式通常是: "#0 : <driver> at 0x<address>, size 0x<size>, buswidth <width>, chipwidth <width>"

    memset(info, 0, sizeof(flash_bank_info_t));
    info->bank_id = bank_id;

    // 简化的解析，实际应该更完善
    char* line = strtok(response, "\n");
    while (line) {
        if (strstr(line, "#") && strstr(line, ":")) {
            int id;
            if (sscanf(line, "#%d", &id) == 1 && id == bank_id) {
                // 解析地址
                char* addr_str = strstr(line, "at 0x");
                if (addr_str) {
                    sscanf(addr_str, "at 0x%x", &info->base_address);
                }

                // 解析大小
                char* size_str = strstr(line, "size 0x");
                if (size_str) {
                    sscanf(size_str, "size 0x%x", &info->size);
                }

                // 解析驱动名称
                char* colon = strchr(line, ':');
                if (colon) {
                    colon += 2; // 跳过": "
                    char* space = strchr(colon, ' ');
                    if (space) {
                        int len = space - colon;
                        if (len < sizeof(info->driver_name) - 1) {
                            strncpy(info->driver_name, colon, len);
                            info->driver_name[len] = '\0';
                        }
                    }
                }

                break;
            }
        }
        line = strtok(NULL, "\n");
    }

    return JTAG_SUCCESS;
}

/**
 * 列出所有Flash banks
 */
jtag_error_t flash_list_banks(openocd_client_t* client,
                             flash_bank_info_t* banks,
                             int max_banks,
                             int* num_banks) {
    if (!client || !banks || !num_banks) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    char response[MAX_RESPONSE_LENGTH];
    jtag_error_t ret = openocd_client_get_flash_banks(client, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        return ret;
    }

    *num_banks = 0;

    // 解析所有Flash bank信息
    char* line = strtok(response, "\n");
    while (line && *num_banks < max_banks) {
        if (strstr(line, "#") && strstr(line, ":")) {
            int id;
            if (sscanf(line, "#%d", &id) == 1) {
                flash_bank_info_t* info = &banks[*num_banks];
                memset(info, 0, sizeof(flash_bank_info_t));
                info->bank_id = id;

                // 解析详细信息（与上面类似）
                char* addr_str = strstr(line, "at 0x");
                if (addr_str) {
                    sscanf(addr_str, "at 0x%x", &info->base_address);
                }

                char* size_str = strstr(line, "size 0x");
                if (size_str) {
                    sscanf(size_str, "size 0x%x", &info->size);
                }

                char* colon = strchr(line, ':');
                if (colon) {
                    colon += 2;
                    char* space = strchr(colon, ' ');
                    if (space) {
                        int len = space - colon;
                        if (len < sizeof(info->driver_name) - 1) {
                            strncpy(info->driver_name, colon, len);
                            info->driver_name[len] = '\0';
                        }
                    }
                }

                (*num_banks)++;
            }
        }
        line = strtok(NULL, "\n");
    }

    return JTAG_SUCCESS;
}
