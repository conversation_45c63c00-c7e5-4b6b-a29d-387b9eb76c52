#include "logger.h"

static logger_config_t g_logger_config = {0};
static bool g_logger_initialized = false;

// ANSI颜色代码
#define COLOR_RESET   "\033[0m"
#define COLOR_RED     "\033[31m"
#define COLOR_YELLOW  "\033[33m"
#define COLOR_GREEN   "\033[32m"
#define COLOR_BLUE    "\033[34m"
#define COLOR_CYAN    "\033[36m"

/**
 * 初始化日志系统
 */
jtag_error_t logger_init(const logger_config_t* config) {
    if (!config) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    g_logger_config = *config;
    g_logger_config.file_handle = NULL;
    
    // 如果需要输出到文件，打开日志文件
    if (g_logger_config.output & LOG_OUTPUT_FILE) {
        g_logger_config.file_handle = fopen(g_logger_config.log_file, "a");
        if (!g_logger_config.file_handle) {
            fprintf(stderr, "无法打开日志文件: %s\n", g_logger_config.log_file);
            return JTAG_ERROR_CONFIG_ERROR;
        }
    }
    
    g_logger_initialized = true;
    return JTAG_SUCCESS;
}

/**
 * 清理日志系统
 */
void logger_cleanup(void) {
    if (!g_logger_initialized) {
        return;
    }
    
    if (g_logger_config.file_handle) {
        fclose(g_logger_config.file_handle);
        g_logger_config.file_handle = NULL;
    }
    
    g_logger_initialized = false;
}

/**
 * 设置日志级别
 */
void logger_set_level(log_level_t level) {
    g_logger_config.level = level;
}

/**
 * 获取日志级别
 */
log_level_t logger_get_level(void) {
    return g_logger_config.level;
}

/**
 * 设置日志输出目标
 */
jtag_error_t logger_set_output(log_output_t output) {
    g_logger_config.output = output;
    
    // 如果需要文件输出但文件未打开，则打开文件
    if ((output & LOG_OUTPUT_FILE) && !g_logger_config.file_handle) {
        g_logger_config.file_handle = fopen(g_logger_config.log_file, "a");
        if (!g_logger_config.file_handle) {
            return JTAG_ERROR_CONFIG_ERROR;
        }
    }
    
    return JTAG_SUCCESS;
}

/**
 * 设置日志文件
 */
jtag_error_t logger_set_file(const char* log_file) {
    if (!log_file) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    // 关闭当前文件
    if (g_logger_config.file_handle) {
        fclose(g_logger_config.file_handle);
        g_logger_config.file_handle = NULL;
    }
    
    // 设置新文件路径
    strncpy(g_logger_config.log_file, log_file, sizeof(g_logger_config.log_file) - 1);
    g_logger_config.log_file[sizeof(g_logger_config.log_file) - 1] = '\0';
    
    // 如果需要文件输出，打开新文件
    if (g_logger_config.output & LOG_OUTPUT_FILE) {
        g_logger_config.file_handle = fopen(g_logger_config.log_file, "a");
        if (!g_logger_config.file_handle) {
            return JTAG_ERROR_CONFIG_ERROR;
        }
    }
    
    return JTAG_SUCCESS;
}

/**
 * 启用/禁用时间戳
 */
void logger_set_timestamp(bool enabled) {
    g_logger_config.timestamp_enabled = enabled;
}

/**
 * 启用/禁用颜色输出
 */
void logger_set_color(bool enabled) {
    g_logger_config.color_enabled = enabled;
}

/**
 * 获取日志级别字符串
 */
const char* logger_level_string(log_level_t level) {
    switch (level) {
        case LOG_LEVEL_ERROR: return "ERROR";
        case LOG_LEVEL_WARN:  return "WARN ";
        case LOG_LEVEL_INFO:  return "INFO ";
        case LOG_LEVEL_DEBUG: return "DEBUG";
        default: return "UNKNOWN";
    }
}

/**
 * 获取日志级别颜色
 */
static const char* get_level_color(log_level_t level) {
    if (!g_logger_config.color_enabled) {
        return "";
    }
    
    switch (level) {
        case LOG_LEVEL_ERROR: return COLOR_RED;
        case LOG_LEVEL_WARN:  return COLOR_YELLOW;
        case LOG_LEVEL_INFO:  return COLOR_GREEN;
        case LOG_LEVEL_DEBUG: return COLOR_CYAN;
        default: return COLOR_RESET;
    }
}

/**
 * 格式化时间戳
 */
static void format_timestamp(char* buffer, size_t size) {
    if (!g_logger_config.timestamp_enabled) {
        buffer[0] = '\0';
        return;
    }
    
    time_t now = time(NULL);
    struct tm* tm_info = localtime(&now);
    strftime(buffer, size, "[%Y-%m-%d %H:%M:%S] ", tm_info);
}

/**
 * 记录日志消息
 */
void logger_log(log_level_t level, const char* file, int line, const char* func, const char* format, ...) {
    if (!g_logger_initialized || level > g_logger_config.level) {
        return;
    }
    
    va_list args;
    va_start(args, format);
    logger_vlog(level, file, line, func, format, args);
    va_end(args);
}

/**
 * 记录日志消息（使用va_list）
 */
void logger_vlog(log_level_t level, const char* file, int line, const char* func, const char* format, va_list args) {
    if (!g_logger_initialized || level > g_logger_config.level) {
        return;
    }
    
    char timestamp[64];
    char message[MAX_LOG_MESSAGE_SIZE];
    char full_message[MAX_LOG_MESSAGE_SIZE + 256];
    
    // 格式化时间戳
    format_timestamp(timestamp, sizeof(timestamp));
    
    // 格式化消息
    vsnprintf(message, sizeof(message), format, args);
    
    // 获取文件名（不包含路径）
    const char* filename = strrchr(file, '/');
    if (!filename) {
        filename = strrchr(file, '\\');
    }
    if (filename) {
        filename++;
    } else {
        filename = file;
    }
    
    // 构造完整消息
    const char* color = get_level_color(level);
    const char* reset = g_logger_config.color_enabled ? COLOR_RESET : "";
    
    snprintf(full_message, sizeof(full_message), 
             "%s%s%s [%s:%d:%s] %s%s\n",
             timestamp, color, logger_level_string(level), 
             filename, line, func, message, reset);
    
    // 输出到控制台
    if (g_logger_config.output & LOG_OUTPUT_CONSOLE) {
        fprintf(stderr, "%s", full_message);
    }
    
    // 输出到文件（不包含颜色代码）
    if ((g_logger_config.output & LOG_OUTPUT_FILE) && g_logger_config.file_handle) {
        snprintf(full_message, sizeof(full_message), 
                 "%s%s [%s:%d:%s] %s\n",
                 timestamp, logger_level_string(level), 
                 filename, line, func, message);
        fprintf(g_logger_config.file_handle, "%s", full_message);
    }
}

/**
 * 刷新日志缓冲区
 */
void logger_flush(void) {
    if (g_logger_config.output & LOG_OUTPUT_CONSOLE) {
        fflush(stderr);
    }
    
    if ((g_logger_config.output & LOG_OUTPUT_FILE) && g_logger_config.file_handle) {
        fflush(g_logger_config.file_handle);
    }
}

/**
 * 从字符串解析日志级别
 */
log_level_t logger_parse_level(const char* level_str) {
    if (!level_str) {
        return LOG_LEVEL_INFO;
    }
    
    if (strcasecmp(level_str, "error") == 0) {
        return LOG_LEVEL_ERROR;
    } else if (strcasecmp(level_str, "warn") == 0 || strcasecmp(level_str, "warning") == 0) {
        return LOG_LEVEL_WARN;
    } else if (strcasecmp(level_str, "info") == 0) {
        return LOG_LEVEL_INFO;
    } else if (strcasecmp(level_str, "debug") == 0) {
        return LOG_LEVEL_DEBUG;
    }
    
    return LOG_LEVEL_INFO;
}
