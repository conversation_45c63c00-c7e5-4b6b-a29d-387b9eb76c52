cmake_minimum_required(VERSION 3.10)
project(jtag_writer VERSION 1.0.0 LANGUAGES C)

# 设置C标准
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    add_compile_options(/W4)
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -pedantic)
endif()

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 包含目录
include_directories(include)

# 查找依赖库
if(WIN32)
    # Windows平台需要链接ws2_32库
    set(PLATFORM_LIBS ws2_32)
else()
    # Unix平台
    set(PLATFORM_LIBS)
endif()

# 源文件
set(CORE_SOURCES
    src/core/jtag_writer.c
    src/core/flash_operations.c
    src/core/progress_monitor.c
)

set(NETWORK_SOURCES
    src/network/openocd_client.c
    src/network/socket_utils.c
)

set(CONFIG_SOURCES
    src/config/config_manager.c
    src/config/json_parser.c
)

set(UI_SOURCES
    src/ui/cli_interface.c
    src/ui/command_parser.c
)

set(UTILS_SOURCES
    src/utils/logger.c
    src/utils/file_utils.c
    src/utils/string_utils.c
)

set(MAIN_SOURCE
    src/main.c
)

# 创建可执行文件
add_executable(jtag_writer
    ${MAIN_SOURCE}
    ${CORE_SOURCES}
    ${NETWORK_SOURCES}
    ${CONFIG_SOURCES}
    ${UI_SOURCES}
    ${UTILS_SOURCES}
)

# 链接库
target_link_libraries(jtag_writer ${PLATFORM_LIBS})

# 安装规则
install(TARGETS jtag_writer DESTINATION bin)
install(DIRECTORY config/ DESTINATION share/jtag_writer/config)
install(DIRECTORY docs/ DESTINATION share/jtag_writer/docs)

# 测试
enable_testing()

# 添加测试可执行文件
add_executable(test_jtag_writer
    tests/test_main.c
    tests/test_openocd_client.c
    tests/test_config_manager.c
    ${CORE_SOURCES}
    ${NETWORK_SOURCES}
    ${CONFIG_SOURCES}
    ${UTILS_SOURCES}
)

target_link_libraries(test_jtag_writer ${PLATFORM_LIBS})

add_test(NAME jtag_writer_tests COMMAND test_jtag_writer)

# 调试信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_definitions(-DDEBUG)
    if(NOT MSVC)
        add_compile_options(-g)
    endif()
endif()

# 发布信息
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    add_definitions(-DNDEBUG)
    if(NOT MSVC)
        add_compile_options(-O2)
    endif()
endif()
