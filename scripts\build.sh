#!/bin/bash
# JTAG Writer 构建脚本 (Linux/macOS)

echo "开始构建 JTAG Writer..."

# 检查是否存在构建目录
if [ ! -d "build" ]; then
    echo "创建构建目录..."
    mkdir build
fi

# 进入构建目录
cd build

# 运行CMake配置
echo "配置项目..."
cmake ..
if [ $? -ne 0 ]; then
    echo "CMake配置失败！"
    exit 1
fi

# 编译项目
echo "编译项目..."
make -j$(nproc)
if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi

echo "构建完成！"
echo "可执行文件位于: build/jtag_writer"

# 返回上级目录
cd ..

echo "运行 --help 查看使用说明:"
./build/jtag_writer --help
