# Example of a udp server which sends a response

# Listen on port 20000. No host specified means 0.0.0.0
file delete unix.sock
set s [socket unix.dgram.server unix.sock]

puts "Listening on dgram socket [$s sockname]"

set count 0

# For each request...
$s readable {
	# Get the request (max 80 chars) - need the source address
	set buf [$s recvfrom 80 addr]

	puts -nonewline "read '$buf' from client $addr"

	try {
		set result "$buf = [expr $buf]"
	} on error {msg} {
		set result "Error: $buf => $msg"
	}

	puts ", sending '$result' to client $addr"

	# Send the result back to where it came from
	$s sendto $result $addr
}

# Handle signals so the socket is removed on exit
signal handle SIGINT SIGTERM

catch -signal {
	vwait done
}
