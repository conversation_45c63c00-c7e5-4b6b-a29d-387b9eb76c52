#ifndef OPENOCD_CLIENT_H
#define OPENOCD_CLIENT_H

#include "jtag_writer.h"

// OpenOCD命令结束符
#define OPENOCD_COMMAND_TOKEN "\x1a"

// OpenOCD客户端句柄
typedef struct openocd_client openocd_client_t;

// OpenOCD客户端结构
struct openocd_client {
    SOCKET socket;
    openocd_config_t config;
    bool connected;
    char response_buffer[MAX_RESPONSE_LENGTH];
    int response_length;
};

// 函数声明

/**
 * 创建OpenOCD客户端
 * @param config OpenOCD连接配置
 * @return 客户端句柄，失败返回NULL
 */
openocd_client_t* openocd_client_create(const openocd_config_t* config);

/**
 * 销毁OpenOCD客户端
 * @param client 客户端句柄
 */
void openocd_client_destroy(openocd_client_t* client);

/**
 * 连接到OpenOCD服务器
 * @param client 客户端句柄
 * @return 错误代码
 */
jtag_error_t openocd_client_connect(openocd_client_t* client);

/**
 * 断开与OpenOCD服务器的连接
 * @param client 客户端句柄
 */
void openocd_client_disconnect(openocd_client_t* client);

/**
 * 发送命令到OpenOCD
 * @param client 客户端句柄
 * @param command 要发送的命令
 * @param response 响应缓冲区
 * @param response_size 响应缓冲区大小
 * @return 错误代码
 */
jtag_error_t openocd_client_send_command(openocd_client_t* client, 
                                        const char* command,
                                        char* response, 
                                        size_t response_size);

/**
 * 检查OpenOCD连接状态
 * @param client 客户端句柄
 * @return true表示已连接，false表示未连接
 */
bool openocd_client_is_connected(const openocd_client_t* client);

/**
 * 获取OpenOCD版本信息
 * @param client 客户端句柄
 * @param version 版本信息缓冲区
 * @param version_size 版本信息缓冲区大小
 * @return 错误代码
 */
jtag_error_t openocd_client_get_version(openocd_client_t* client,
                                       char* version,
                                       size_t version_size);

/**
 * 获取目标信息
 * @param client 客户端句柄
 * @param target_info 目标信息缓冲区
 * @param info_size 目标信息缓冲区大小
 * @return 错误代码
 */
jtag_error_t openocd_client_get_target_info(openocd_client_t* client,
                                           char* target_info,
                                           size_t info_size);

/**
 * 获取Flash bank信息
 * @param client 客户端句柄
 * @param bank_info Flash bank信息缓冲区
 * @param info_size Flash bank信息缓冲区大小
 * @return 错误代码
 */
jtag_error_t openocd_client_get_flash_banks(openocd_client_t* client,
                                           char* bank_info,
                                           size_t info_size);

/**
 * 初始化目标
 * @param client 客户端句柄
 * @return 错误代码
 */
jtag_error_t openocd_client_init_target(openocd_client_t* client);

/**
 * 重置目标
 * @param client 客户端句柄
 * @param halt 是否在重置后暂停
 * @return 错误代码
 */
jtag_error_t openocd_client_reset_target(openocd_client_t* client, bool halt);

/**
 * 暂停目标
 * @param client 客户端句柄
 * @return 错误代码
 */
jtag_error_t openocd_client_halt_target(openocd_client_t* client);

/**
 * 恢复目标运行
 * @param client 客户端句柄
 * @return 错误代码
 */
jtag_error_t openocd_client_resume_target(openocd_client_t* client);

#endif // OPENOCD_CLIENT_H
