# SPDX-License-Identifier: GPL-2.0-or-later

# <AUTHOR> <EMAIL>

on: pull_request

name: OpenOCD Snapshot

jobs:
  package:
    runs-on: [ubuntu-18.04]
    env:
      DL_DIR: ../downloads
      BUILD_DIR: ../build
    steps:
      - name: Install needed packages
        run: |
          sudo apt-get update
          sudo apt-get install autotools-dev autoconf automake libtool pkg-config cmake texinfo texlive g++-mingw-w64-i686
      - name: Checkout Code
        uses: actions/checkout@v1
      - run: ./bootstrap
      - name: Prepare libusb1
        env:
          LIBUSB1_VER: 1.0.24
        run: |
          mkdir -p $DL_DIR && cd $DL_DIR
          wget "https://github.com/libusb/libusb/releases/download/v${LIBUSB1_VER}/libusb-${LIBUSB1_VER}.tar.bz2"
          tar -xjf libusb-${LIBUSB1_VER}.tar.bz2
          echo "LIBUSB1_SRC=$PWD/libusb-${LIBUSB1_VER}" >> $GITHUB_ENV
      - name: Prepare hidapi
        env:
          HIDAPI_VER: 0.10.1
        run: |
          mkdir -p $DL_DIR && cd $DL_DIR
          wget "https://github.com/libusb/hidapi/archive/hidapi-${HIDAPI_VER}.tar.gz"
          tar -xzf hidapi-${HIDAPI_VER}.tar.gz
          cd hidapi-hidapi-${HIDAPI_VER}
          ./bootstrap
          echo "HIDAPI_SRC=$PWD" >> $GITHUB_ENV
      - name: Prepare capstone
        env:
          CAPSTONE_VER: 4.0.2
        run: |
          mkdir -p $DL_DIR && cd $DL_DIR
          CAPSTONE_NAME=${CAPSTONE_VER}
          CAPSTONE_FOLDER=capstone-${CAPSTONE_VER}
          wget "https://github.com/aquynh/capstone/archive/${CAPSTONE_VER}.tar.gz"
          tar -xzf ${CAPSTONE_VER}.tar.gz
          echo "CAPSTONE_SRC=$PWD/capstone-${CAPSTONE_VER}" >> $GITHUB_ENV
      - name: Package OpenOCD for windows
        env:
          MAKE_JOBS: 2
          HOST: i686-w64-mingw32
          LIBUSB1_CONFIG: --enable-shared --disable-static
          HIDAPI_CONFIG: --enable-shared --disable-static --disable-testgui
          CAPSTONE_CONFIG: "CAPSTONE_BUILD_CORE_ONLY=yes CAPSTONE_STATIC=yes CAPSTONE_SHARED=no"
          CAPSTONE_CFLAGS: -I$(CAPSTONE_SRC)/include/capstone
        run: |
          # check if there is tag pointing at HEAD, otherwise take the HEAD SHA-1 as OPENOCD_TAG
          OPENOCD_TAG="`git tag --points-at HEAD`"
          [ -z $OPENOCD_TAG ] && OPENOCD_TAG="`git rev-parse --short HEAD`"
          # check if there is tag pointing at HEAD, if so the release will have the same name as the tag,
          # otherwise it will be named 'latest'
          RELEASE_NAME="`git tag --points-at HEAD`"
          [ -z $RELEASE_NAME ] && RELEASE_NAME="latest"
          [[ $RELEASE_NAME = "latest" ]] && IS_PRE_RELEASE="true" || IS_PRE_RELEASE="false"
          # set env and call cross-build.sh
          export OPENOCD_TAG=$OPENOCD_TAG
          export OPENOCD_SRC=$PWD
          export OPENOCD_CONFIG=""
          mkdir -p $BUILD_DIR &&  cd $BUILD_DIR
          bash $OPENOCD_SRC/contrib/cross-build.sh $HOST
          # add missing dlls
          cd $HOST-root/usr
          cp `$HOST-gcc --print-file-name=libwinpthread-1.dll` ./bin/
          cp `$HOST-gcc --print-file-name=libgcc_s_sjlj-1.dll` ./bin/
          # prepare the artifact
          ARTIFACT="openocd-${OPENOCD_TAG}-${HOST}.tar.gz"
          tar -czf $ARTIFACT *
          echo "RELEASE_NAME=$RELEASE_NAME" >> $GITHUB_ENV
          echo "IS_PRE_RELEASE=$IS_PRE_RELEASE" >> $GITHUB_ENV
          echo "ARTIFACT_PATH=$PWD/$ARTIFACT" >> $GITHUB_ENV
