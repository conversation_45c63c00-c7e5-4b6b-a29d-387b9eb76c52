# Example of sending from an unconnected ipv6 socket

set s [socket -ipv6 dgram]

foreach i [range 1 5] {
	# Specify the address and port with sendto
	$s sendto "$i + $i + 10" {[::1]:20000}

	# Receive the response - max length of 100
	puts [$s recvfrom 100]
}

$s close

# Now sending via a connected udp socket

set s [socket -ipv6 dgram {[::1]:20000}]

foreach i [range 5 10] {
	# Socket is connected, so can just use puts here
	# But remember to flush to ensure that each message is separate
	$s puts -nonewline "$i * $i"
	$s flush

	# Receive the response - max length of 100
	puts [$s recvfrom 100]
}
