\input texinfo @c -*-texinfo-*-
@c %**start of header
@setfilename openocd.info
@settitle OpenOCD User's Guide
@dircategory Development
@direntry
* OpenOCD: (openocd).      OpenOCD User's Guide
@end direntry
@paragraphindent 0
@c %**end of header

@include version.texi

@copying

This User's Guide documents
release @value{VERSION},
dated @value{UPDATED},
of the Open On-Chip Debugger (OpenOCD).

@itemize @bullet
@item Copyright @copyright{} 2008 The OpenOCD Project
@item Copyright @copyright{} 2007-2008 <PERSON> @email{spen@@spen-soft.co.uk}
@item Copyright @copyright{} 2008-2010 Oyvind Harboe @email{oyvind.harboe@@zylin.com}
@item Copyright @copyright{} 2008 <PERSON><PERSON> @email{openocd@@duaneellis.com}
@item Copyright @copyright{} 2009-2010 <PERSON>
@end itemize

@quotation
Permission is granted to copy, distribute and/or modify this document
under the terms of the GNU Free Documentation License, Version 1.2 or
any later version published by the Free Software Foundation; with no
Invariant Sections, no Front-Cover Texts, and no Back-Cover Texts. A
copy of the license is included in the section entitled ``GNU Free
Documentation License''.
@end quotation
@end copying

@titlepage
@titlefont{@emph{Open On-Chip Debugger:}}
@sp 1
@title OpenOCD User's Guide
@subtitle for release @value{VERSION}
@subtitle @value{UPDATED}

@page
@vskip 0pt plus 1filll
@insertcopying
@end titlepage

@summarycontents
@contents

@ifnottex
@node Top
@top OpenOCD User's Guide

@insertcopying
@end ifnottex

@menu
* About::                            About OpenOCD
* Developers::                       OpenOCD Developer Resources
* Debug Adapter Hardware::           Debug Adapter Hardware
* About Jim-Tcl::                    About Jim-Tcl
* Running::                          Running OpenOCD
* OpenOCD Project Setup::            OpenOCD Project Setup
* Config File Guidelines::           Config File Guidelines
* Server Configuration::             Server Configuration
* Debug Adapter Configuration::      Debug Adapter Configuration
* Reset Configuration::              Reset Configuration
* TAP Declaration::                  TAP Declaration
* CPU Configuration::                CPU Configuration
* Flash Commands::                   Flash Commands
* Flash Programming::                Flash Programming
* PLD/FPGA Commands::                PLD/FPGA Commands
* General Commands::                 General Commands
* Architecture and Core Commands::   Architecture and Core Commands
* JTAG Commands::                    JTAG Commands
* Boundary Scan Commands::           Boundary Scan Commands
* Utility Commands::                 Utility Commands
* GDB and OpenOCD::                  Using GDB and OpenOCD
* Tcl Scripting API::                Tcl Scripting API
* FAQ::                              Frequently Asked Questions
* Tcl Crash Course::                 Tcl Crash Course
* License::                          GNU Free Documentation License

@comment DO NOT use the plain word ``Index'', reason: CYGWIN filename
@comment case issue with ``Index.html'' and ``index.html''
@comment Occurs when creating ``--html --no-split'' output
@comment This fix is based on: http://sourceware.org/ml/binutils/2006-05/msg00215.html
* OpenOCD Concept Index::            Concept Index
* Command and Driver Index::         Command and Driver Index
@end menu

@node About
@unnumbered About
@cindex about

OpenOCD was created by Dominic Rath as part of a 2005 diploma thesis written
at the University of Applied Sciences Augsburg (@uref{http://www.hs-augsburg.de}).
Since that time, the project has grown into an active open-source project,
supported by a diverse community of software and hardware developers from
around the world.

@section What is OpenOCD?
@cindex TAP
@cindex JTAG

The Open On-Chip Debugger (OpenOCD) aims to provide debugging,
in-system programming and boundary-scan testing for embedded target
devices.

It does so with the assistance of a @dfn{debug adapter}, which is
a small hardware module which helps provide the right kind of
electrical signaling to the target being debugged. These are
required since the debug host (on which OpenOCD runs) won't
usually have native support for such signaling, or the connector
needed to hook up to the target.

Such debug adapters support one or more @dfn{transport} protocols,
each of which involves different electrical signaling (and uses
different messaging protocols on top of that signaling). There
are many types of debug adapter, and little uniformity in what
they are called. (There are also product naming differences.)

These adapters are sometimes packaged as discrete dongles, which
may generically be called @dfn{hardware interface dongles}.
Some development boards also integrate them directly, which may
let the development board connect directly to the debug
host over USB (and sometimes also to power it over USB).

For example, a @dfn{JTAG Adapter} supports JTAG
signaling, and is used to communicate
with JTAG (IEEE 1149.1) compliant TAPs on your target board.
A @dfn{TAP} is a ``Test Access Port'', a module which processes
special instructions and data. TAPs are daisy-chained within and
between chips and boards. JTAG supports debugging and boundary
scan operations.

There are also @dfn{SWD Adapters} that support Serial Wire Debug (SWD)
signaling to communicate with some newer ARM cores, as well as debug
adapters which support both JTAG and SWD transports. SWD supports only
debugging, whereas JTAG also supports boundary scan operations.

For some chips, there are also @dfn{Programming Adapters} supporting
special transports used only to write code to flash memory, without
support for on-chip debugging or boundary scan.
(At this writing, OpenOCD does not support such non-debug adapters.)


@b{Dongles:} OpenOCD currently supports many types of hardware dongles:
USB-based, parallel port-based, and other standalone boxes that run
OpenOCD internally. @xref{Debug Adapter Hardware}.

@b{GDB Debug:} It allows ARM7 (ARM7TDMI and ARM720t), ARM9 (ARM920T,
ARM922T, ARM926EJ--S, ARM966E--S), XScale (PXA25x, IXP42x), Cortex-M3
(Stellaris LM3, STMicroelectronics STM32 and Energy Micro EFM32) and
Intel Quark (x10xx) based cores to be debugged via the GDB protocol.

@b{Flash Programming:} Flash writing is supported for external
CFI-compatible NOR flashes (Intel and AMD/Spansion command set) and several
internal flashes (LPC1700, LPC1800, LPC2000, LPC4300, AT91SAM7, AT91SAM3U,
STR7x, STR9x, LM3, STM32x and EFM32). Preliminary support for various NAND flash
controllers (LPC3180, Orion, S3C24xx, more) is included.

@section OpenOCD Web Site

The OpenOCD web site provides the latest public news from the community:

@uref{http://openocd.org/}

@section Latest User's Guide:

The user's guide you are now reading may not be the latest one
available. A version for more recent code may be available.
Its HTML form is published regularly at:

@uref{http://openocd.org/doc/html/index.html}

PDF form is likewise published at:

@uref{http://openocd.org/doc/pdf/openocd.pdf}

@section OpenOCD User's Forum

There is an OpenOCD forum (phpBB) hosted by SparkFun,
which might be helpful to you. Note that if you want
anything to come to the attention of developers, you
should post it to the OpenOCD Developer Mailing List
instead of this forum.

@uref{http://forum.sparkfun.com/viewforum.php?f=18}

@section OpenOCD User's Mailing List

The OpenOCD User Mailing List provides the primary means of
communication between users:

@uref{https://lists.sourceforge.net/mailman/listinfo/openocd-user}

@section OpenOCD IRC

Support can also be found on irc:
@uref{irc://irc.libera.chat/openocd}

@node Developers
@chapter OpenOCD Developer Resources
@cindex developers

If you are interested in improving the state of OpenOCD's debugging and
testing support, new contributions will be welcome. Motivated developers
can produce new target, flash or interface drivers, improve the
documentation, as well as more conventional bug fixes and enhancements.

The resources in this chapter are available for developers wishing to explore
or expand the OpenOCD source code.

@section OpenOCD Git Repository

During the 0.3.x release cycle, OpenOCD switched from Subversion to
a Git repository hosted at SourceForge. The repository URL is:

@uref{git://git.code.sf.net/p/openocd/code}

or via http

@uref{http://git.code.sf.net/p/openocd/code}

You may prefer to use a mirror and the HTTP protocol:

@uref{http://repo.or.cz/r/openocd.git}

With standard Git tools, use @command{git clone} to initialize
a local repository, and @command{git pull} to update it.
There are also gitweb pages letting you browse the repository
with a web browser, or download arbitrary snapshots without
needing a Git client:

@uref{http://repo.or.cz/w/openocd.git}

The @file{README} file contains the instructions for building the project
from the repository or a snapshot.

Developers that want to contribute patches to the OpenOCD system are
@b{strongly} encouraged to work against mainline.
Patches created against older versions may require additional
work from their submitter in order to be updated for newer releases.

@section Doxygen Developer Manual

During the 0.2.x release cycle, the OpenOCD project began
providing a Doxygen reference manual. This document contains more
technical information about the software internals, development
processes, and similar documentation:

@uref{http://openocd.org/doc/doxygen/html/index.html}

This document is a work-in-progress, but contributions would be welcome
to fill in the gaps. All of the source files are provided in-tree,
listed in the Doxyfile configuration at the top of the source tree.

@section Gerrit Review System

All changes in the OpenOCD Git repository go through the web-based Gerrit
Code Review System:

@uref{https://review.openocd.org/}

After a one-time registration and repository setup, anyone can push commits
from their local Git repository directly into Gerrit.
All users and developers are encouraged to review, test, discuss and vote
for changes in Gerrit. The feedback provides the basis for a maintainer to
eventually submit the change to the main Git repository.

The @file{HACKING} file, also available as the Patch Guide in the Doxygen
Developer Manual, contains basic information about how to connect a
repository to Gerrit, prepare and push patches. Patch authors are expected to
maintain their changes while they're in Gerrit, respond to feedback and if
necessary rework and push improved versions of the change.

@section OpenOCD Developer Mailing List

The OpenOCD Developer Mailing List provides the primary means of
communication between developers:

@uref{https://lists.sourceforge.net/mailman/listinfo/openocd-devel}

@section OpenOCD Bug Tracker

The OpenOCD Bug Tracker is hosted on SourceForge:

@uref{http://bugs.openocd.org/}


@node Debug Adapter Hardware
@chapter Debug Adapter Hardware
@cindex dongles
@cindex FTDI
@cindex wiggler
@cindex printer port
@cindex USB Adapter
@cindex RTCK

Defined: @b{dongle}: A small device that plugs into a computer and serves as
an adapter .... [snip]

In the OpenOCD case, this generally refers to @b{a small adapter} that
attaches to your computer via USB or the parallel port.


@section Choosing a Dongle

There are several things you should keep in mind when choosing a dongle.

@enumerate
@item @b{Transport} Does it support the kind of communication that you need?
OpenOCD focuses mostly on JTAG. Your version may also support
other ways to communicate with target devices.
@item @b{Voltage} What voltage is your target - 1.8, 2.8, 3.3, or 5V?
Does your dongle support it? You might need a level converter.
@item @b{Pinout} What pinout does your target board use?
Does your dongle support it? You may be able to use jumper
wires, or an "octopus" connector, to convert pinouts.
@item @b{Connection} Does your computer have the USB, parallel, or
Ethernet port needed?
@item @b{RTCK} Do you expect to use it with ARM chips and boards with
RTCK support (also known as ``adaptive clocking'')?
@end enumerate

@section USB FT2232 Based

There are many USB JTAG dongles on the market, many of them based
on a chip from ``Future Technology Devices International'' (FTDI)
known as the FTDI FT2232; this is a USB full speed (12 Mbps) chip.
See: @url{http://www.ftdichip.com} for more information.
In summer 2009, USB high speed (480 Mbps) versions of these FTDI
chips started to become available in JTAG adapters. Around 2012, a new
variant appeared - FT232H - this is a single-channel version of FT2232H.
(Adapters using those high speed FT2232H or FT232H chips may support adaptive
clocking.)

The FT2232 chips are flexible enough to support some other
transport options, such as SWD or the SPI variants used to
program some chips. They have two communications channels,
and one can be used for a UART adapter at the same time the
other one is used to provide a debug adapter.

Also, some development boards integrate an FT2232 chip to serve as
a built-in low-cost debug adapter and USB-to-serial solution.

@itemize @bullet
@item @b{usbjtag}
@* Link @url{http://elk.informatik.fh-augsburg.de/hhweb/doc/openocd/usbjtag/usbjtag.html}
@item @b{jtagkey}
@* See: @url{http://www.amontec.com/jtagkey.shtml}
@item @b{jtagkey2}
@* See: @url{http://www.amontec.com/jtagkey2.shtml}
@item @b{oocdlink}
@* See: @url{http://www.oocdlink.com} By Joern Kaipf
@item @b{signalyzer}
@* See: @url{http://www.signalyzer.com}
@item @b{Stellaris Eval Boards}
@* See: @url{http://www.ti.com} - The Stellaris eval boards
bundle FT2232-based JTAG and SWD support, which can be used to debug
the Stellaris chips. Using separate JTAG adapters is optional.
These boards can also be used in a "pass through" mode as JTAG adapters
to other target boards, disabling the Stellaris chip.
@item @b{TI/Luminary ICDI}
@* See: @url{http://www.ti.com} - TI/Luminary In-Circuit Debug
Interface (ICDI) Boards are included in Stellaris LM3S9B9x
Evaluation Kits. Like the non-detachable FT2232 support on the other
Stellaris eval boards, they can be used to debug other target boards.
@item @b{olimex-jtag}
@* See: @url{http://www.olimex.com}
@item @b{Flyswatter/Flyswatter2}
@* See: @url{http://www.tincantools.com}
@item @b{turtelizer2}
@* See:
@uref{http://www.ethernut.de/en/hardware/turtelizer/index.html, Turtelizer 2}, or
@url{http://www.ethernut.de}
@item @b{comstick}
@* Link: @url{http://www.hitex.com/index.php?id=383}
@item @b{stm32stick}
@* Link @url{http://www.hitex.com/stm32-stick}
@item @b{axm0432_jtag}
@* Axiom AXM-0432 Link @url{http://www.axman.com} - NOTE: This JTAG does not appear
to be available anymore as of April 2012.
@item @b{cortino}
@* Link @url{http://www.hitex.com/index.php?id=cortino}
@item @b{dlp-usb1232h}
@* Link @url{http://www.dlpdesign.com/usb/usb1232h.shtml}
@item @b{digilent-hs1}
@* Link @url{http://www.digilentinc.com/Products/Detail.cfm?Prod=JTAG-HS1}
@item @b{opendous}
@* Link @url{http://code.google.com/p/opendous/wiki/JTAG} FT2232H-based
(OpenHardware).
@item @b{JTAG-lock-pick Tiny 2}
@* Link @url{http://www.distortec.com/jtag-lock-pick-tiny-2} FT232H-based

@item @b{GW16042}
@* Link: @url{http://shop.gateworks.com/index.php?route=product/product&path=70_80&product_id=64}
FT2232H-based

@end itemize
@section USB-JTAG / Altera USB-Blaster compatibles

These devices also show up as FTDI devices, but are not
protocol-compatible with the FT2232 devices. They are, however,
protocol-compatible among themselves. USB-JTAG devices typically consist
of a FT245 followed by a CPLD that understands a particular protocol,
or emulates this protocol using some other hardware.

They may appear under different USB VID/PID depending on the particular
product. The driver can be configured to search for any VID/PID pair
(see the section on driver commands).

@itemize
@item @b{USB-JTAG} Kolja Waschk's USB Blaster-compatible adapter
@* Link: @url{http://ixo-jtag.sourceforge.net/}
@item @b{Altera USB-Blaster}
@* Link: @url{http://www.altera.com/literature/ug/ug_usb_blstr.pdf}
@end itemize

@section USB J-Link based
There are several OEM versions of the SEGGER @b{J-Link} adapter. It is
an example of a microcontroller based JTAG adapter, it uses an
AT91SAM764 internally.

@itemize @bullet
@item @b{SEGGER J-Link}
@* Link: @url{http://www.segger.com/jlink.html}
@item @b{Atmel SAM-ICE} (Only works with Atmel chips!)
@* Link: @url{http://www.atmel.com/tools/atmelsam-ice.aspx}
@item @b{IAR J-Link}
@end itemize

@section USB RLINK based
Raisonance has an adapter called @b{RLink}. It exists in a stripped-down form on the STM32 Primer,
permanently attached to the JTAG lines. It also exists on the STM32 Primer2, but that is wired for
SWD and not JTAG, thus not supported.

@itemize @bullet
@item @b{Raisonance RLink}
@* Link: @url{http://www.mcu-raisonance.com/~rlink-debugger-programmer__@/microcontrollers__tool~tool__T018:4cn9ziz4bnx6.html}
@item @b{STM32 Primer}
@* Link: @url{http://www.stm32circle.com/resources/stm32primer.php}
@item @b{STM32 Primer2}
@* Link: @url{http://www.stm32circle.com/resources/stm32primer2.php}
@end itemize

@section USB ST-LINK based
STMicroelectronics has an adapter called @b{ST-LINK}.
They only work with STMicroelectronics chips, notably STM32 and STM8.

@itemize @bullet
@item @b{ST-LINK}
@* This is available standalone and as part of some kits, eg. STM32VLDISCOVERY.
@* Link: @url{http://www.st.com/internet/evalboard/product/219866.jsp}
@item @b{ST-LINK/V2}
@* This is available standalone and as part of some kits, eg. STM32F4DISCOVERY.
@* Link: @url{http://www.st.com/internet/evalboard/product/251168.jsp}
@item @b{STLINK-V3}
@* This is available standalone and as part of some kits.
@* Link: @url{http://www.st.com/stlink-v3}
@end itemize

For info the original ST-LINK enumerates using the mass storage usb class; however,
its implementation is completely broken. The result is this causes issues under Linux.
The simplest solution is to get Linux to ignore the ST-LINK using one of the following methods:
@itemize @bullet
@item modprobe -r usb-storage && modprobe usb-storage quirks=483:3744:i
@item add "options usb-storage quirks=483:3744:i" to /etc/modprobe.conf
@end itemize

@section USB TI/Stellaris ICDI based
Texas Instruments has an adapter called @b{ICDI}.
It is not to be confused with the FTDI based adapters that were originally fitted to their
evaluation boards. This is the adapter fitted to the Stellaris LaunchPad.

@section USB Nuvoton Nu-Link
Nuvoton has an adapter called @b{Nu-Link}.
It is available either as stand-alone dongle and embedded on development boards.
It supports SWD, serial port bridge and mass storage for firmware update.
Both Nu-Link v1 and v2 are supported.

@section USB CMSIS-DAP based
ARM has released a interface standard called CMSIS-DAP that simplifies connecting
debuggers to ARM Cortex based targets @url{http://www.keil.com/support/man/docs/dapdebug/dapdebug_introduction.htm}.

@section USB Other
@itemize @bullet
@item @b{USBprog}
@* Link: @url{http://shop.embedded-projects.net/} - which uses an Atmel MEGA32 and a UBN9604

@item @b{USB - Presto}
@* Link: @url{http://tools.asix.net/prg_presto.htm}

@item @b{Versaloon-Link}
@* Link: @url{http://www.versaloon.com}

@item @b{ARM-JTAG-EW}
@* Link: @url{http://www.olimex.com/dev/arm-jtag-ew.html}

@item @b{Buspirate}
@* Link: @url{http://dangerousprototypes.com/bus-pirate-manual/}

@item @b{opendous}
@* Link: @url{http://code.google.com/p/opendous-jtag/} - which uses an AT90USB162

@item @b{estick}
@* Link: @url{http://code.google.com/p/estick-jtag/}

@item @b{Keil ULINK v1}
@* Link: @url{http://www.keil.com/ulink1/}

@item @b{TI XDS110 Debug Probe}
@* Link: @url{https://software-dl.ti.com/ccs/esd/documents/xdsdebugprobes/emu_xds110.html}
@* Link: @url{https://software-dl.ti.com/ccs/esd/documents/xdsdebugprobes/emu_xds_software_package_download.html#xds110-support-utilities}
@end itemize

@section IBM PC Parallel Printer Port Based

The two well-known ``JTAG Parallel Ports'' cables are the Xilinx DLC5
and the Macraigor Wiggler. There are many clones and variations of
these on the market.

Note that parallel ports are becoming much less common, so if you
have the choice you should probably avoid these adapters in favor
of USB-based ones.

@itemize @bullet

@item @b{Wiggler} - There are many clones of this.
@* Link: @url{http://www.macraigor.com/wiggler.htm}

@item @b{DLC5} - From XILINX - There are many clones of this
@* Link: Search the web for: ``XILINX DLC5'' - it is no longer
produced, PDF schematics are easily found and it is easy to make.

@item @b{Amontec - JTAG Accelerator}
@* Link: @url{http://www.amontec.com/jtag_accelerator.shtml}

@item @b{Wiggler2}
@* Link: @url{http://www.ccac.rwth-aachen.de/~michaels/index.php/hardware/armjtag}

@item @b{Wiggler_ntrst_inverted}
@* Yet another variation - See the source code, src/jtag/parport.c

@item @b{old_amt_wiggler}
@* Unknown - probably not on the market today

@item @b{arm-jtag}
@* Link: Most likely @url{http://www.olimex.com/dev/arm-jtag.html} [another wiggler clone]

@item @b{chameleon}
@* Link: @url{http://www.amontec.com/chameleon.shtml}

@item @b{Triton}
@* Unknown.

@item @b{Lattice}
@* ispDownload from Lattice Semiconductor
@url{http://www.latticesemi.com/lit/docs/@/devtools/dlcable.pdf}

@item @b{flashlink}
@* From STMicroelectronics;
@* Link: @url{http://www.st.com/internet/com/TECHNICAL_RESOURCES/TECHNICAL_LITERATURE/DATA_BRIEF/**********.pdf}

@end itemize

@section Other...
@itemize @bullet

@item @b{ep93xx}
@* An EP93xx based Linux machine using the GPIO pins directly.

@item @b{at91rm9200}
@* Like the EP93xx - but an ATMEL AT91RM9200 based solution using the GPIO pins on the chip.

@item @b{bcm2835gpio}
@* A BCM2835-based board (e.g. Raspberry Pi) using the GPIO pins of the expansion header.

@item @b{imx_gpio}
@* A NXP i.MX-based board (e.g. Wandboard) using the GPIO pins (should work on any i.MX processor).

@item @b{jtag_vpi}
@* A JTAG driver acting as a client for the JTAG VPI server interface.
@* Link: @url{http://github.com/fjullien/jtag_vpi}

@item @b{vdebug}
@* A driver for Cadence virtual Debug Interface to emulated or simulated targets.
It implements a client connecting to the vdebug server, which in turn communicates
with the emulated or simulated RTL model through a transactor. The current version
supports only JTAG as a transport, but other virtual transports, like DAP are planned.

@item @b{jtag_dpi}
@* A JTAG driver acting as a client for the SystemVerilog Direct Programming
Interface (DPI) for JTAG devices. DPI allows OpenOCD to connect to the JTAG
interface of a hardware model written in SystemVerilog, for example, on an
emulation model of target hardware.

@item @b{xlnx_pcie_xvc}
@* A JTAG driver exposing Xilinx Virtual Cable over PCI Express to OpenOCD as JTAG/SWD interface.

@item @b{linuxgpiod}
@* A bitbang JTAG driver using Linux GPIO through library libgpiod.

@item @b{sysfsgpio}
@* A bitbang JTAG driver using Linux legacy sysfs GPIO.
This is deprecated from Linux v5.3; prefer using @b{linuxgpiod}.

@end itemize

@node About Jim-Tcl
@chapter About Jim-Tcl
@cindex Jim-Tcl
@cindex tcl

OpenOCD uses a small ``Tcl Interpreter'' known as Jim-Tcl.
This programming language provides a simple and extensible
command interpreter.

All commands presented in this Guide are extensions to Jim-Tcl.
You can use them as simple commands, without needing to learn
much of anything about Tcl.
Alternatively, you can write Tcl programs with them.

You can learn more about Jim at its website, @url{http://jim.tcl.tk}.
There is an active and responsive community, get on the mailing list
if you have any questions. Jim-Tcl maintainers also lurk on the
OpenOCD mailing list.

@itemize @bullet
@item @b{Jim vs. Tcl}
@* Jim-Tcl is a stripped down version of the well known Tcl language,
which can be found here: @url{http://www.tcl.tk}. Jim-Tcl has far
fewer features. Jim-Tcl is several dozens of .C files and .H files and
implements the basic Tcl command set. In contrast: Tcl 8.6 is a
4.2 MB .zip file containing 1540 files.

@item @b{Missing Features}
@* Our practice has been: Add/clone the real Tcl feature if/when
needed. We welcome Jim-Tcl improvements, not bloat. Also there
are a large number of optional Jim-Tcl features that are not
enabled in OpenOCD.

@item @b{Scripts}
@* OpenOCD configuration scripts are Jim-Tcl Scripts. OpenOCD's
command interpreter today is a mixture of (newer)
Jim-Tcl commands, and the (older) original command interpreter.

@item @b{Commands}
@* At the OpenOCD telnet command line (or via the GDB monitor command) one
can type a Tcl for() loop, set variables, etc.
Some of the commands documented in this guide are implemented
as Tcl scripts, from a @file{startup.tcl} file internal to the server.

@item @b{Historical Note}
@* Jim-Tcl was introduced to OpenOCD in spring 2008. Fall 2010,
before OpenOCD 0.5 release, OpenOCD switched to using Jim-Tcl
as a Git submodule, which greatly simplified upgrading Jim-Tcl
to benefit from new features and bugfixes in Jim-Tcl.

@item @b{Need a crash course in Tcl?}
@*@xref{Tcl Crash Course}.
@end itemize

@node Running
@chapter Running
@cindex command line options
@cindex logfile
@cindex directory search

Properly installing OpenOCD sets up your operating system to grant it access
to the debug adapters. On Linux, this usually involves installing a file
in @file{/etc/udev/rules.d,} so OpenOCD has permissions. An example rules file
that works for many common adapters is shipped with OpenOCD in the
@file{contrib} directory. MS-Windows needs
complex and confusing driver configuration for every peripheral. Such issues
are unique to each operating system, and are not detailed in this User's Guide.

Then later you will invoke the OpenOCD server, with various options to
tell it how each debug session should work.
The @option{--help} option shows:
@verbatim
bash$ openocd --help

--help       | -h       display this help
--version    | -v       display OpenOCD version
--file       | -f       use configuration file <name>
--search     | -s       dir to search for config files and scripts
--debug      | -d       set debug level to 3
             | -d<n>    set debug level to <level>
--log_output | -l       redirect log output to file <name>
--command    | -c       run <command>
@end verbatim

If you don't give any @option{-f} or @option{-c} options,
OpenOCD tries to read the configuration file @file{openocd.cfg}.
To specify one or more different
configuration files, use @option{-f} options. For example:

@example
openocd -f config1.cfg -f config2.cfg -f config3.cfg
@end example

Configuration files and scripts are searched for in
@enumerate
@item the current directory,
@item any search dir specified on the command line using the @option{-s} option,
@item any search dir specified using the @command{add_script_search_dir} command,
@item a directory in the @env{OPENOCD_SCRIPTS} environment variable (if set),
@item @file{%APPDATA%/OpenOCD} (only on Windows),
@item @file{$HOME/Library/Preferences/org.openocd} (only on Darwin),
@item @file{$XDG_CONFIG_HOME/openocd} (@env{$XDG_CONFIG_HOME} defaults to @file{$HOME/.config}),
@item @file{$HOME/.openocd},
@item the site wide script library @file{$pkgdatadir/site} and
@item the OpenOCD-supplied script library @file{$pkgdatadir/scripts}.
@end enumerate
The first found file with a matching file name will be used.

@quotation Note
Don't try to use configuration script names or paths which
include the "#" character. That character begins Tcl comments.
@end quotation

@section Simple setup, no customization

In the best case, you can use two scripts from one of the script
libraries, hook up your JTAG adapter, and start the server ... and
your JTAG setup will just work "out of the box". Always try to
start by reusing those scripts, but assume you'll need more
customization even if this works. @xref{OpenOCD Project Setup}.

If you find a script for your JTAG adapter, and for your board or
target, you may be able to hook up your JTAG adapter then start
the server with some variation of one of the following:

@example
openocd -f interface/ADAPTER.cfg -f board/MYBOARD.cfg
openocd -f interface/ftdi/ADAPTER.cfg -f board/MYBOARD.cfg
@end example

You might also need to configure which reset signals are present,
using @option{-c 'reset_config trst_and_srst'} or something similar.
If all goes well you'll see output something like

@example
Open On-Chip Debugger 0.4.0 (2010-01-14-15:06)
For bug reports, read
        http://openocd.org/doc/doxygen/bugs.html
Info : JTAG tap: lm3s.cpu tap/device found: 0x3ba00477
       (mfg: 0x23b, part: 0xba00, ver: 0x3)
@end example

Seeing that "tap/device found" message, and no warnings, means
the JTAG communication is working. That's a key milestone, but
you'll probably need more project-specific setup.

@section What OpenOCD does as it starts

OpenOCD starts by processing the configuration commands provided
on the command line or, if there were no @option{-c command} or
@option{-f file.cfg} options given, in @file{openocd.cfg}.
@xref{configurationstage,,Configuration Stage}.
At the end of the configuration stage it verifies the JTAG scan
chain defined using those commands; your configuration should
ensure that this always succeeds.
Normally, OpenOCD then starts running as a server.
Alternatively, commands may be used to terminate the configuration
stage early, perform work (such as updating some flash memory),
and then shut down without acting as a server.

Once OpenOCD starts running as a server, it waits for connections from
clients (Telnet, GDB, RPC) and processes the commands issued through
those channels.

If you are having problems, you can enable internal debug messages via
the @option{-d} option.

Also it is possible to interleave Jim-Tcl commands w/config scripts using the
@option{-c} command line switch.

To enable debug output (when reporting problems or working on OpenOCD
itself), use the @option{-d} command line switch. This sets the
@option{debug_level} to "3", outputting the most information,
including debug messages. The default setting is "2", outputting only
informational messages, warnings and errors. You can also change this
setting from within a telnet or gdb session using @command{debug_level<n>}
(@pxref{debuglevel,,debug_level}).

You can redirect all output from the server to a file using the
@option{-l <logfile>} switch.

Note! OpenOCD will launch the GDB & telnet server even if it can not
establish a connection with the target. In general, it is possible for
the JTAG controller to be unresponsive until the target is set up
correctly via e.g. GDB monitor commands in a GDB init script.

@node OpenOCD Project Setup
@chapter OpenOCD Project Setup

To use OpenOCD with your development projects, you need to do more than
just connect the JTAG adapter hardware (dongle) to your development board
and start the OpenOCD server.
You also need to configure your OpenOCD server so that it knows
about your adapter and board, and helps your work.
You may also want to connect OpenOCD to GDB, possibly
using Eclipse or some other GUI.

@section Hooking up the JTAG Adapter

Today's most common case is a dongle with a JTAG cable on one side
(such as a ribbon cable with a 10-pin or 20-pin IDC connector)
and a USB cable on the other.
Instead of USB, some dongles use Ethernet;
older ones may use a PC parallel port, or even a serial port.

@enumerate
@item @emph{Start with power to your target board turned off},
and nothing connected to your JTAG adapter.
If you're particularly paranoid, unplug power to the board.
It's important to have the ground signal properly set up,
unless you are using a JTAG adapter which provides
galvanic isolation between the target board and the
debugging host.

@item @emph{Be sure it's the right kind of JTAG connector.}
If your dongle has a 20-pin ARM connector, you need some kind
of adapter (or octopus, see below) to hook it up to
boards using 14-pin or 10-pin connectors ... or to 20-pin
connectors which don't use ARM's pinout.

In the same vein, make sure the voltage levels are compatible.
Not all JTAG adapters have the level shifters needed to work
with 1.2 Volt boards.

@item @emph{Be certain the cable is properly oriented} or you might
damage your board. In most cases there are only two possible
ways to connect the cable.
Connect the JTAG cable from your adapter to the board.
Be sure it's firmly connected.

In the best case, the connector is keyed to physically
prevent you from inserting it wrong.
This is most often done using a slot on the board's male connector
housing, which must match a key on the JTAG cable's female connector.
If there's no housing, then you must look carefully and
make sure pin 1 on the cable hooks up to pin 1 on the board.
Ribbon cables are frequently all grey except for a wire on one
edge, which is red. The red wire is pin 1.

Sometimes dongles provide cables where one end is an ``octopus'' of
color coded single-wire connectors, instead of a connector block.
These are great when converting from one JTAG pinout to another,
but are tedious to set up.
Use these with connector pinout diagrams to help you match up the
adapter signals to the right board pins.

@item @emph{Connect the adapter's other end} once the JTAG cable is connected.
A USB, parallel, or serial port connector will go to the host which
you are using to run OpenOCD.
For Ethernet, consult the documentation and your network administrator.

For USB-based JTAG adapters you have an easy sanity check at this point:
does the host operating system see the JTAG adapter? If you're running
Linux, try the @command{lsusb} command. If that host is an
MS-Windows host, you'll need to install a driver before OpenOCD works.

@item @emph{Connect the adapter's power supply, if needed.}
This step is primarily for non-USB adapters,
but sometimes USB adapters need extra power.

@item @emph{Power up the target board.}
Unless you just let the magic smoke escape,
you're now ready to set up the OpenOCD server
so you can use JTAG to work with that board.

@end enumerate

Talk with the OpenOCD server using
telnet (@code{telnet localhost 4444} on many systems) or GDB.
@xref{GDB and OpenOCD}.

@section Project Directory

There are many ways you can configure OpenOCD and start it up.

A simple way to organize them all involves keeping a
single directory for your work with a given board.
When you start OpenOCD from that directory,
it searches there first for configuration files, scripts,
files accessed through semihosting,
and for code you upload to the target board.
It is also the natural place to write files,
such as log files and data you download from the board.

@section Configuration Basics

There are two basic ways of configuring OpenOCD, and
a variety of ways you can mix them.
Think of the difference as just being how you start the server:

@itemize
@item Many @option{-f file} or @option{-c command} options on the command line
@item No options, but a @dfn{user config file}
in the current directory named @file{openocd.cfg}
@end itemize

Here is an example @file{openocd.cfg} file for a setup
using a Signalyzer FT2232-based JTAG adapter to talk to
a board with an Atmel AT91SAM7X256 microcontroller:

@example
source [find interface/ftdi/signalyzer.cfg]

# GDB can also flash my flash!
gdb_memory_map enable
gdb_flash_program enable

source [find target/sam7x256.cfg]
@end example

Here is the command line equivalent of that configuration:

@example
openocd -f interface/ftdi/signalyzer.cfg \
        -c "gdb_memory_map enable" \
        -c "gdb_flash_program enable" \
        -f target/sam7x256.cfg
@end example

You could wrap such long command lines in shell scripts,
each supporting a different development task.
One might re-flash the board with a specific firmware version.
Another might set up a particular debugging or run-time environment.

@quotation Important
At this writing (October 2009) the command line method has
problems with how it treats variables.
For example, after @option{-c "set VAR value"}, or doing the
same in a script, the variable @var{VAR} will have no value
that can be tested in a later script.
@end quotation

Here we will focus on the simpler solution: one user config
file, including basic configuration plus any TCL procedures
to simplify your work.

@section User Config Files
@cindex config file, user
@cindex user config file
@cindex config file, overview

A user configuration file ties together all the parts of a project
in one place.
One of the following will match your situation best:

@itemize
@item Ideally almost everything comes from configuration files
provided by someone else.
For example, OpenOCD distributes a @file{scripts} directory
(probably in @file{/usr/share/openocd/scripts} on Linux).
Board and tool vendors can provide these too, as can individual
user sites; the @option{-s} command line option lets you say
where to find these files. (@xref{Running}.)
The AT91SAM7X256 example above works this way.

Three main types of non-user configuration file each have their
own subdirectory in the @file{scripts} directory:

@enumerate
@item @b{interface} -- one for each different debug adapter;
@item @b{board} -- one for each different board
@item @b{target} -- the chips which integrate CPUs and other JTAG TAPs
@end enumerate

Best case: include just two files, and they handle everything else.
The first is an interface config file.
The second is board-specific, and it sets up the JTAG TAPs and
their GDB targets (by deferring to some @file{target.cfg} file),
declares all flash memory, and leaves you nothing to do except
meet your deadline:

@example
source [find interface/olimex-jtag-tiny.cfg]
source [find board/csb337.cfg]
@end example

Boards with a single microcontroller often won't need more
than the target config file, as in the AT91SAM7X256 example.
That's because there is no external memory (flash, DDR RAM), and
the board differences are encapsulated by application code.

@item Maybe you don't know yet what your board looks like to JTAG.
Once you know the @file{interface.cfg} file to use, you may
need help from OpenOCD to discover what's on the board.
Once you find the JTAG TAPs, you can just search for appropriate
target and board
configuration files ... or write your own, from the bottom up.
@xref{autoprobing,,Autoprobing}.

@item You can often reuse some standard config files but
need to write a few new ones, probably a @file{board.cfg} file.
You will be using commands described later in this User's Guide,
and working with the guidelines in the next chapter.

For example, there may be configuration files for your JTAG adapter
and target chip, but you need a new board-specific config file
giving access to your particular flash chips.
Or you might need to write another target chip configuration file
for a new chip built around the Cortex-M3 core.

@quotation Note
When you write new configuration files, please submit
them for inclusion in the next OpenOCD release.
For example, a @file{board/newboard.cfg} file will help the
next users of that board, and a @file{target/newcpu.cfg}
will help support users of any board using that chip.
@end quotation

@item
You may need to write some C code.
It may be as simple as supporting a new FT2232 or parport
based adapter; a bit more involved, like a NAND or NOR flash
controller driver; or a big piece of work like supporting
a new chip architecture.
@end itemize

Reuse the existing config files when you can.
Look first in the @file{scripts/boards} area, then @file{scripts/targets}.
You may find a board configuration that's a good example to follow.

When you write config files, separate the reusable parts
(things every user of that interface, chip, or board needs)
from ones specific to your environment and debugging approach.
@itemize

@item
For example, a @code{gdb-attach} event handler that invokes
the @command{reset init} command will interfere with debugging
early boot code, which performs some of the same actions
that the @code{reset-init} event handler does.

@item
Likewise, the @command{arm9 vector_catch} command (or
@cindex vector_catch
its siblings @command{xscale vector_catch}
and @command{cortex_m vector_catch}) can be a time-saver
during some debug sessions, but don't make everyone use that either.
Keep those kinds of debugging aids in your user config file,
along with messaging and tracing setup.
(@xref{softwaredebugmessagesandtracing,,Software Debug Messages and Tracing}.)

@item
You might need to override some defaults.
For example, you might need to move, shrink, or back up the target's
work area if your application needs much SRAM.

@item
TCP/IP port configuration is another example of something which
is environment-specific, and should only appear in
a user config file. @xref{tcpipports,,TCP/IP Ports}.
@end itemize

@section Project-Specific Utilities

A few project-specific utility
routines may well speed up your work.
Write them, and keep them in your project's user config file.

For example, if you are making a boot loader work on a
board, it's nice to be able to debug the ``after it's
loaded to RAM'' parts separately from the finicky early
code which sets up the DDR RAM controller and clocks.
A script like this one, or a more GDB-aware sibling,
may help:

@example
proc ramboot @{ @} @{
    # Reset, running the target's "reset-init" scripts
    # to initialize clocks and the DDR RAM controller.
    # Leave the CPU halted.
    reset init

    # Load CONFIG_SKIP_LOWLEVEL_INIT version into DDR RAM.
    load_image u-boot.bin 0x20000000

    # Start running.
    resume 0x20000000
@}
@end example

Then once that code is working you will need to make it
boot from NOR flash; a different utility would help.
Alternatively, some developers write to flash using GDB.
(You might use a similar script if you're working with a flash
based microcontroller application instead of a boot loader.)

@example
proc newboot @{ @} @{
    # Reset, leaving the CPU halted. The "reset-init" event
    # proc gives faster access to the CPU and to NOR flash;
    # "reset halt" would be slower.
    reset init

    # Write standard version of U-Boot into the first two
    # sectors of NOR flash ... the standard version should
    # do the same lowlevel init as "reset-init".
    flash protect 0 0 1 off
    flash erase_sector 0 0 1
    flash write_bank 0 u-boot.bin 0x0
    flash protect 0 0 1 on

    # Reboot from scratch using that new boot loader.
    reset run
@}
@end example

You may need more complicated utility procedures when booting
from NAND.
That often involves an extra bootloader stage,
running from on-chip SRAM to perform DDR RAM setup so it can load
the main bootloader code (which won't fit into that SRAM).

Other helper scripts might be used to write production system images,
involving considerably more than just a three stage bootloader.

@section Target Software Changes

Sometimes you may want to make some small changes to the software
you're developing, to help make JTAG debugging work better.
For example, in C or assembly language code you might
use @code{#ifdef JTAG_DEBUG} (or its converse) around code
handling issues like:

@itemize @bullet

@item @b{Watchdog Timers}...
Watchdog timers are typically used to automatically reset systems if
some application task doesn't periodically reset the timer. (The
assumption is that the system has locked up if the task can't run.)
When a JTAG debugger halts the system, that task won't be able to run
and reset the timer ... potentially causing resets in the middle of
your debug sessions.

It's rarely a good idea to disable such watchdogs, since their usage
needs to be debugged just like all other parts of your firmware.
That might however be your only option.

Look instead for chip-specific ways to stop the watchdog from counting
while the system is in a debug halt state. It may be simplest to set
that non-counting mode in your debugger startup scripts. You may however
need a different approach when, for example, a motor could be physically
damaged by firmware remaining inactive in a debug halt state. That might
involve a type of firmware mode where that "non-counting" mode is disabled
at the beginning then re-enabled at the end; a watchdog reset might fire
and complicate the debug session, but hardware (or people) would be
protected.@footnote{Note that many systems support a "monitor mode" debug
that is a somewhat cleaner way to address such issues. You can think of
it as only halting part of the system, maybe just one task,
instead of the whole thing.
At this writing, January 2010, OpenOCD based debugging does not support
monitor mode debug, only "halt mode" debug.}

@item @b{ARM Semihosting}...
@cindex ARM semihosting
When linked with a special runtime library provided with many
toolchains@footnote{See chapter 8 "Semihosting" in
@uref{http://infocenter.arm.com/help/topic/com.arm.doc.dui0203i/DUI0203I_rvct_developer_guide.pdf,
ARM DUI 0203I}, the "RealView Compilation Tools Developer Guide".
The CodeSourcery EABI toolchain also includes a semihosting library.},
your target code can use I/O facilities on the debug host. That library
provides a small set of system calls which are handled by OpenOCD.
It can let the debugger provide your system console and a file system,
helping with early debugging or providing a more capable environment
for sometimes-complex tasks like installing system firmware onto
NAND or SPI flash.

@item @b{ARM Wait-For-Interrupt}...
Many ARM chips synchronize the JTAG clock using the core clock.
Low power states which stop that core clock thus prevent JTAG access.
Idle loops in tasking environments often enter those low power states
via the @code{WFI} instruction (or its coprocessor equivalent, before ARMv7).

You may want to @emph{disable that instruction} in source code,
or otherwise prevent using that state,
to ensure you can get JTAG access at any time.@footnote{As a more
polite alternative, some processors have special debug-oriented
registers which can be used to change various features including
how the low power states are clocked while debugging.
The STM32 DBGMCU_CR register is an example; at the cost of extra
power consumption, JTAG can be used during low power states.}
For example, the OpenOCD @command{halt} command may not
work for an idle processor otherwise.

@item @b{Delay after reset}...
Not all chips have good support for debugger access
right after reset; many LPC2xxx chips have issues here.
Similarly, applications that reconfigure pins used for
JTAG access as they start will also block debugger access.

To work with boards like this, @emph{enable a short delay loop}
the first thing after reset, before "real" startup activities.
For example, one second's delay is usually more than enough
time for a JTAG debugger to attach, so that
early code execution can be debugged
or firmware can be replaced.

@item @b{Debug Communications Channel (DCC)}...
Some processors include mechanisms to send messages over JTAG.
Many ARM cores support these, as do some cores from other vendors.
(OpenOCD may be able to use this DCC internally, speeding up some
operations like writing to memory.)

Your application may want to deliver various debugging messages
over JTAG, by @emph{linking with a small library of code}
provided with OpenOCD and using the utilities there to send
various kinds of message.
@xref{softwaredebugmessagesandtracing,,Software Debug Messages and Tracing}.

@end itemize

@section Target Hardware Setup

Chip vendors often provide software development boards which
are highly configurable, so that they can support all options
that product boards may require. @emph{Make sure that any
jumpers or switches match the system configuration you are
working with.}

Common issues include:

@itemize @bullet

@item @b{JTAG setup} ...
Boards may support more than one JTAG configuration.
Examples include jumpers controlling pullups versus pulldowns
on the nTRST and/or nSRST signals, and choice of connectors
(e.g. which of two headers on the base board,
or one from a daughtercard).
For some Texas Instruments boards, you may need to jumper the
EMU0 and EMU1 signals (which OpenOCD won't currently control).

@item @b{Boot Modes} ...
Complex chips often support multiple boot modes, controlled
by external jumpers. Make sure this is set up correctly.
For example many i.MX boards from NXP need to be jumpered
to "ATX mode" to start booting using the on-chip ROM, when
using second stage bootloader code stored in a NAND flash chip.

Such explicit configuration is common, and not limited to
booting from NAND. You might also need to set jumpers to
start booting using code loaded from an MMC/SD card; external
SPI flash; Ethernet, UART, or USB links; NOR flash; OneNAND
flash; some external host; or various other sources.


@item @b{Memory Addressing} ...
Boards which support multiple boot modes may also have jumpers
to configure memory addressing. One board, for example, jumpers
external chipselect 0 (used for booting) to address either
a large SRAM (which must be pre-loaded via JTAG), NOR flash,
or NAND flash. When it's jumpered to address NAND flash, that
board must also be told to start booting from on-chip ROM.

Your @file{board.cfg} file may also need to be told this jumper
configuration, so that it can know whether to declare NOR flash
using @command{flash bank} or instead declare NAND flash with
@command{nand device}; and likewise which probe to perform in
its @code{reset-init} handler.

A closely related issue is bus width. Jumpers might need to
distinguish between 8 bit or 16 bit bus access for the flash
used to start booting.

@item @b{Peripheral Access} ...
Development boards generally provide access to every peripheral
on the chip, sometimes in multiple modes (such as by providing
multiple audio codec chips).
This interacts with software
configuration of pin multiplexing, where for example a
given pin may be routed either to the MMC/SD controller
or the GPIO controller. It also often interacts with
configuration jumpers. One jumper may be used to route
signals to an MMC/SD card slot or an expansion bus (which
might in turn affect booting); others might control which
audio or video codecs are used.

@end itemize

Plus you should of course have @code{reset-init} event handlers
which set up the hardware to match that jumper configuration.
That includes in particular any oscillator or PLL used to clock
the CPU, and any memory controllers needed to access external
memory and peripherals. Without such handlers, you won't be
able to access those resources without working target firmware
which can do that setup ... this can be awkward when you're
trying to debug that target firmware. Even if there's a ROM
bootloader which handles a few issues, it rarely provides full
access to all board-specific capabilities.


@node Config File Guidelines
@chapter Config File Guidelines

This chapter is aimed at any user who needs to write a config file,
including developers and integrators of OpenOCD and any user who
needs to get a new board working smoothly.
It provides guidelines for creating those files.

You should find the following directories under
@t{$(INSTALLDIR)/scripts}, with config files maintained upstream. Use
them as-is where you can; or as models for new files.
@itemize @bullet
@item @file{interface} ...
These are for debug adapters. Files that specify configuration to use
specific JTAG, SWD and other adapters go here.
@item @file{board} ...
Think Circuit Board, PWA, PCB, they go by many names. Board files
contain initialization items that are specific to a board.

They reuse target configuration files, since the same
microprocessor chips are used on many boards,
but support for external parts varies widely. For
example, the SDRAM initialization sequence for the board, or the type
of external flash and what address it uses. Any initialization
sequence to enable that external flash or SDRAM should be found in the
board file. Boards may also contain multiple targets: two CPUs; or
a CPU and an FPGA.
@item @file{target} ...
Think chip. The ``target'' directory represents the JTAG TAPs
on a chip
which OpenOCD should control, not a board. Two common types of targets
are ARM chips and FPGA or CPLD chips.
When a chip has multiple TAPs (maybe it has both ARM and DSP cores),
the target config file defines all of them.
@item @emph{more} ... browse for other library files which may be useful.
For example, there are various generic and CPU-specific utilities.
@end itemize

The @file{openocd.cfg} user config
file may override features in any of the above files by
setting variables before sourcing the target file, or by adding
commands specific to their situation.

@section Interface Config Files

The user config file
should be able to source one of these files with a command like this:

@example
source [find interface/FOOBAR.cfg]
@end example

A preconfigured interface file should exist for every debug adapter
in use today with OpenOCD.
That said, perhaps some of these config files
have only been used by the developer who created it.

A separate chapter gives information about how to set these up.
@xref{Debug Adapter Configuration}.
Read the OpenOCD source code (and Developer's Guide)
if you have a new kind of hardware interface
and need to provide a driver for it.

@deffn {Command} {find} 'filename'
Prints full path to @var{filename} according to OpenOCD search rules.
@end deffn

@deffn {Command} {ocd_find} 'filename'
Prints full path to @var{filename} according to OpenOCD search rules. This
is a low level function used by the @command{find}. Usually you want
to use @command{find}, instead.
@end deffn

@section Board Config Files
@cindex config file, board
@cindex board config file

The user config file
should be able to source one of these files with a command like this:

@example
source [find board/FOOBAR.cfg]
@end example

The point of a board config file is to package everything
about a given board that user config files need to know.
In summary the board files should contain (if present)

@enumerate
@item One or more @command{source [find target/...cfg]} statements
@item NOR flash configuration (@pxref{norconfiguration,,NOR Configuration})
@item NAND flash configuration (@pxref{nandconfiguration,,NAND Configuration})
@item Target @code{reset} handlers for SDRAM and I/O configuration
@item JTAG adapter reset configuration (@pxref{Reset Configuration})
@item All things that are not ``inside a chip''
@end enumerate

Generic things inside target chips belong in target config files,
not board config files. So for example a @code{reset-init} event
handler should know board-specific oscillator and PLL parameters,
which it passes to target-specific utility code.

The most complex task of a board config file is creating such a
@code{reset-init} event handler.
Define those handlers last, after you verify the rest of the board
configuration works.

@subsection Communication Between Config files

In addition to target-specific utility code, another way that
board and target config files communicate is by following a
convention on how to use certain variables.

The full Tcl/Tk language supports ``namespaces'', but Jim-Tcl does not.
Thus the rule we follow in OpenOCD is this: Variables that begin with
a leading underscore are temporary in nature, and can be modified and
used at will within a target configuration file.

Complex board config files can do the things like this,
for a board with three chips:

@example
# Chip #1: PXA270 for network side, big endian
set CHIPNAME network
set ENDIAN big
source [find target/pxa270.cfg]
# on return: _TARGETNAME = network.cpu
# other commands can refer to the "network.cpu" target.
$_TARGETNAME configure .... events for this CPU..

# Chip #2: PXA270 for video side, little endian
set CHIPNAME video
set ENDIAN little
source [find target/pxa270.cfg]
# on return: _TARGETNAME = video.cpu
# other commands can refer to the "video.cpu" target.
$_TARGETNAME configure .... events for this CPU..

# Chip #3: Xilinx FPGA for glue logic
set CHIPNAME xilinx
unset ENDIAN
source [find target/spartan3.cfg]
@end example

That example is oversimplified because it doesn't show any flash memory,
or the @code{reset-init} event handlers to initialize external DRAM
or (assuming it needs it) load a configuration into the FPGA.
Such features are usually needed for low-level work with many boards,
where ``low level'' implies that the board initialization software may
not be working. (That's a common reason to need JTAG tools. Another
is to enable working with microcontroller-based systems, which often
have no debugging support except a JTAG connector.)

Target config files may also export utility functions to board and user
config files. Such functions should use name prefixes, to help avoid
naming collisions.

Board files could also accept input variables from user config files.
For example, there might be a @code{J4_JUMPER} setting used to identify
what kind of flash memory a development board is using, or how to set
up other clocks and peripherals.

@subsection Variable Naming Convention
@cindex variable names

Most boards have only one instance of a chip.
However, it should be easy to create a board with more than
one such chip (as shown above).
Accordingly, we encourage these conventions for naming
variables associated with different @file{target.cfg} files,
to promote consistency and
so that board files can override target defaults.

Inputs to target config files include:

@itemize @bullet
@item @code{CHIPNAME} ...
This gives a name to the overall chip, and is used as part of
tap identifier dotted names.
While the default is normally provided by the chip manufacturer,
board files may need to distinguish between instances of a chip.
@item @code{ENDIAN} ...
By default @option{little} - although chips may hard-wire @option{big}.
Chips that can't change endianness don't need to use this variable.
@item @code{CPUTAPID} ...
When OpenOCD examines the JTAG chain, it can be told verify the
chips against the JTAG IDCODE register.
The target file will hold one or more defaults, but sometimes the
chip in a board will use a different ID (perhaps a newer revision).
@end itemize

Outputs from target config files include:

@itemize @bullet
@item @code{_TARGETNAME} ...
By convention, this variable is created by the target configuration
script. The board configuration file may make use of this variable to
configure things like a ``reset init'' script, or other things
specific to that board and that target.
If the chip has 2 targets, the names are @code{_TARGETNAME0},
@code{_TARGETNAME1}, ... etc.
@end itemize

@subsection The reset-init Event Handler
@cindex event, reset-init
@cindex reset-init handler

Board config files run in the OpenOCD configuration stage;
they can't use TAPs or targets, since they haven't been
fully set up yet.
This means you can't write memory or access chip registers;
you can't even verify that a flash chip is present.
That's done later in event handlers, of which the target @code{reset-init}
handler is one of the most important.

Except on microcontrollers, the basic job of @code{reset-init} event
handlers is setting up flash and DRAM, as normally handled by boot loaders.
Microcontrollers rarely use boot loaders; they run right out of their
on-chip flash and SRAM memory. But they may want to use one of these
handlers too, if just for developer convenience.

@quotation Note
Because this is so very board-specific, and chip-specific, no examples
are included here.
Instead, look at the board config files distributed with OpenOCD.
If you have a boot loader, its source code will help; so will
configuration files for other JTAG tools
(@pxref{translatingconfigurationfiles,,Translating Configuration Files}).
@end quotation

Some of this code could probably be shared between different boards.
For example, setting up a DRAM controller often doesn't differ by
much except the bus width (16 bits or 32?) and memory timings, so a
reusable TCL procedure loaded by the @file{target.cfg} file might take
those as parameters.
Similarly with oscillator, PLL, and clock setup;
and disabling the watchdog.
Structure the code cleanly, and provide comments to help
the next developer doing such work.
(@emph{You might be that next person} trying to reuse init code!)

The last thing normally done in a @code{reset-init} handler is probing
whatever flash memory was configured. For most chips that needs to be
done while the associated target is halted, either because JTAG memory
access uses the CPU or to prevent conflicting CPU access.

@subsection JTAG Clock Rate

Before your @code{reset-init} handler has set up
the PLLs and clocking, you may need to run with
a low JTAG clock rate.
@xref{jtagspeed,,JTAG Speed}.
Then you'd increase that rate after your handler has
made it possible to use the faster JTAG clock.
When the initial low speed is board-specific, for example
because it depends on a board-specific oscillator speed, then
you should probably set it up in the board config file;
if it's target-specific, it belongs in the target config file.

For most ARM-based processors the fastest JTAG clock@footnote{A FAQ
@uref{http://www.arm.com/support/faqdev/4170.html} gives details.}
is one sixth of the CPU clock; or one eighth for ARM11 cores.
Consult chip documentation to determine the peak JTAG clock rate,
which might be less than that.

@quotation Warning
On most ARMs, JTAG clock detection is coupled to the core clock, so
software using a @option{wait for interrupt} operation blocks JTAG access.
Adaptive clocking provides a partial workaround, but a more complete
solution just avoids using that instruction with JTAG debuggers.
@end quotation

If both the chip and the board support adaptive clocking,
use the @command{jtag_rclk}
command, in case your board is used with JTAG adapter which
also supports it. Otherwise use @command{adapter speed}.
Set the slow rate at the beginning of the reset sequence,
and the faster rate as soon as the clocks are at full speed.

@anchor{theinitboardprocedure}
@subsection The init_board procedure
@cindex init_board procedure

The concept of @code{init_board} procedure is very similar to @code{init_targets}
(@xref{theinittargetsprocedure,,The init_targets procedure}.) - it's a replacement of ``linear''
configuration scripts. This procedure is meant to be executed when OpenOCD enters run stage
(@xref{enteringtherunstage,,Entering the Run Stage},) after @code{init_targets}. The idea to have
separate @code{init_targets} and @code{init_board} procedures is to allow the first one to configure
everything target specific (internal flash, internal RAM, etc.) and the second one to configure
everything board specific (reset signals, chip frequency, reset-init event handler, external memory, etc.).
Additionally ``linear'' board config file will most likely fail when target config file uses
@code{init_targets} scheme (``linear'' script is executed before @code{init} and @code{init_targets} - after),
so separating these two configuration stages is very convenient, as the easiest way to overcome this
problem is to convert board config file to use @code{init_board} procedure. Board config scripts don't
need to override @code{init_targets} defined in target config files when they only need to add some specifics.

Just as @code{init_targets}, the @code{init_board} procedure can be overridden by ``next level'' script (which sources
the original), allowing greater code reuse.

@example
### board_file.cfg ###

# source target file that does most of the config in init_targets
source [find target/target.cfg]

proc enable_fast_clock @{@} @{
    # enables fast on-board clock source
    # configures the chip to use it
@}

# initialize only board specifics - reset, clock, adapter frequency
proc init_board @{@} @{
    reset_config trst_and_srst trst_pulls_srst

    $_TARGETNAME configure -event reset-start @{
        adapter speed 100
    @}

    $_TARGETNAME configure -event reset-init @{
        enable_fast_clock
        adapter speed 10000
    @}
@}
@end example

@section Target Config Files
@cindex config file, target
@cindex target config file

Board config files communicate with target config files using
naming conventions as described above, and may source one or
more target config files like this:

@example
source [find target/FOOBAR.cfg]
@end example

The point of a target config file is to package everything
about a given chip that board config files need to know.
In summary the target files should contain

@enumerate
@item Set defaults
@item Add TAPs to the scan chain
@item Add CPU targets (includes GDB support)
@item CPU/Chip/CPU-Core specific features
@item On-Chip flash
@end enumerate

As a rule of thumb, a target file sets up only one chip.
For a microcontroller, that will often include a single TAP,
which is a CPU needing a GDB target, and its on-chip flash.

More complex chips may include multiple TAPs, and the target
config file may need to define them all before OpenOCD
can talk to the chip.
For example, some phone chips have JTAG scan chains that include
an ARM core for operating system use, a DSP,
another ARM core embedded in an image processing engine,
and other processing engines.

@subsection Default Value Boiler Plate Code

All target configuration files should start with code like this,
letting board config files express environment-specific
differences in how things should be set up.

@example
# Boards may override chip names, perhaps based on role,
# but the default should match what the vendor uses
if @{ [info exists CHIPNAME] @} @{
   set  _CHIPNAME $CHIPNAME
@} else @{
   set  _CHIPNAME sam7x256
@}

# ONLY use ENDIAN with targets that can change it.
if @{ [info exists ENDIAN] @} @{
   set  _ENDIAN $ENDIAN
@} else @{
   set  _ENDIAN little
@}

# TAP identifiers may change as chips mature, for example with
# new revision fields (the "3" here). Pick a good default; you
# can pass several such identifiers to the "jtag newtap" command.
if @{ [info exists CPUTAPID ] @} @{
   set _CPUTAPID $CPUTAPID
@} else @{
   set _CPUTAPID 0x3f0f0f0f
@}
@end example
@c but 0x3f0f0f0f is for an str73x part ...

@emph{Remember:} Board config files may include multiple target
config files, or the same target file multiple times
(changing at least @code{CHIPNAME}).

Likewise, the target configuration file should define
@code{_TARGETNAME} (or @code{_TARGETNAME0} etc) and
use it later on when defining debug targets:

@example
set _TARGETNAME $_CHIPNAME.cpu
target create $_TARGETNAME arm7tdmi -chain-position $_TARGETNAME
@end example

@subsection Adding TAPs to the Scan Chain
After the ``defaults'' are set up,
add the TAPs on each chip to the JTAG scan chain.
@xref{TAP Declaration}, and the naming convention
for taps.

In the simplest case the chip has only one TAP,
probably for a CPU or FPGA.
The config file for the Atmel AT91SAM7X256
looks (in part) like this:

@example
jtag newtap $_CHIPNAME cpu -irlen 4 -expected-id $_CPUTAPID
@end example

A board with two such at91sam7 chips would be able
to source such a config file twice, with different
values for @code{CHIPNAME}, so
it adds a different TAP each time.

If there are nonzero @option{-expected-id} values,
OpenOCD attempts to verify the actual tap id against those values.
It will issue error messages if there is mismatch, which
can help to pinpoint problems in OpenOCD configurations.

@example
JTAG tap: sam7x256.cpu tap/device found: 0x3f0f0f0f
                (Manufacturer: 0x787, Part: 0xf0f0, Version: 0x3)
ERROR: Tap: sam7x256.cpu - Expected id: 0x12345678, Got: 0x3f0f0f0f
ERROR: expected: mfg: 0x33c, part: 0x2345, ver: 0x1
ERROR:      got: mfg: 0x787, part: 0xf0f0, ver: 0x3
@end example

There are more complex examples too, with chips that have
multiple TAPs. Ones worth looking at include:

@itemize
@item @file{target/omap3530.cfg} -- with disabled ARM and DSP,
plus a JRC to enable them
@item @file{target/str912.cfg} -- with flash, CPU, and boundary scan
@item @file{target/ti_dm355.cfg} -- with ETM, ARM, and JRC (this JRC
is not currently used)
@end itemize

@subsection Add CPU targets

After adding a TAP for a CPU, you should set it up so that
GDB and other commands can use it.
@xref{CPU Configuration}.
For the at91sam7 example above, the command can look like this;
note that @code{$_ENDIAN} is not needed, since OpenOCD defaults
to little endian, and this chip doesn't support changing that.

@example
set _TARGETNAME $_CHIPNAME.cpu
target create $_TARGETNAME arm7tdmi -chain-position $_TARGETNAME
@end example

Work areas are small RAM areas associated with CPU targets.
They are used by OpenOCD to speed up downloads,
and to download small snippets of code to program flash chips.
If the chip includes a form of ``on-chip-ram'' - and many do - define
a work area if you can.
Again using the at91sam7 as an example, this can look like:

@example
$_TARGETNAME configure -work-area-phys 0x00200000 \
             -work-area-size 0x4000 -work-area-backup 0
@end example

@anchor{definecputargetsworkinginsmp}
@subsection Define CPU targets working in SMP
@cindex SMP
After setting targets, you can define a list of targets working in SMP.

@example
set _TARGETNAME_1 $_CHIPNAME.cpu1
set _TARGETNAME_2 $_CHIPNAME.cpu2
target create $_TARGETNAME_1 cortex_a -chain-position $_CHIPNAME.dap \
-coreid 0 -dbgbase $_DAP_DBG1
target create $_TARGETNAME_2 cortex_a -chain-position $_CHIPNAME.dap \
-coreid 1 -dbgbase $_DAP_DBG2
#define 2 targets working in smp.
target smp $_CHIPNAME.cpu2 $_CHIPNAME.cpu1
@end example
In the above example on cortex_a, 2 cpus are working in SMP.
In SMP only one GDB instance is created and :
@itemize @bullet
@item a set of hardware breakpoint sets the same breakpoint on all targets in the list.
@item halt command triggers the halt of all targets in the list.
@item resume command triggers the write context and the restart of all targets in the list.
@item following a breakpoint: the target stopped by the breakpoint is displayed to the GDB session.
@item dedicated GDB serial protocol packets are implemented for switching/retrieving the target
displayed by the GDB session @pxref{usingopenocdsmpwithgdb,,Using OpenOCD SMP with GDB}.
@end itemize

The SMP behaviour can be disabled/enabled dynamically. On cortex_a following
command have been implemented.
@itemize @bullet
@item cortex_a smp on : enable SMP mode, behaviour is as described above.
@item cortex_a smp off : disable SMP mode, the current target is the one
displayed in the GDB session, only this target is now controlled by GDB
session. This behaviour is useful during system boot up.
@item cortex_a smp : display current SMP mode.
@item cortex_a smp_gdb : display/fix the core id displayed in GDB session see
following example.
@end itemize

@example
>cortex_a smp_gdb
gdb coreid  0 -> -1
#0 : coreid 0 is displayed to GDB ,
#-> -1 : next resume triggers a real resume
> cortex_a smp_gdb 1
gdb coreid  0 -> 1
#0 :coreid 0 is displayed to GDB ,
#->1  : next resume displays coreid 1 to GDB
> resume
> cortex_a smp_gdb
gdb coreid  1 -> 1
#1 :coreid 1 is displayed to GDB ,
#->1 : next resume displays coreid 1 to GDB
> cortex_a smp_gdb -1
gdb coreid  1 -> -1
#1 :coreid 1 is displayed to GDB,
#->-1 : next resume triggers a real resume
@end example


@subsection Chip Reset Setup

As a rule, you should put the @command{reset_config} command
into the board file. Most things you think you know about a
chip can be tweaked by the board.

Some chips have specific ways the TRST and SRST signals are
managed. In the unusual case that these are @emph{chip specific}
and can never be changed by board wiring, they could go here.
For example, some chips can't support JTAG debugging without
both signals.

Provide a @code{reset-assert} event handler if you can.
Such a handler uses JTAG operations to reset the target,
letting this target config be used in systems which don't
provide the optional SRST signal, or on systems where you
don't want to reset all targets at once.
Such a handler might write to chip registers to force a reset,
use a JRC to do that (preferable -- the target may be wedged!),
or force a watchdog timer to trigger.
(For Cortex-M targets, this is not necessary.  The target
driver knows how to use trigger an NVIC reset when SRST is
not available.)

Some chips need special attention during reset handling if
they're going to be used with JTAG.
An example might be needing to send some commands right
after the target's TAP has been reset, providing a
@code{reset-deassert-post} event handler that writes a chip
register to report that JTAG debugging is being done.
Another would be reconfiguring the watchdog so that it stops
counting while the core is halted in the debugger.

JTAG clocking constraints often change during reset, and in
some cases target config files (rather than board config files)
are the right places to handle some of those issues.
For example, immediately after reset most chips run using a
slower clock than they will use later.
That means that after reset (and potentially, as OpenOCD
first starts up) they must use a slower JTAG clock rate
than they will use later.
@xref{jtagspeed,,JTAG Speed}.

@quotation Important
When you are debugging code that runs right after chip
reset, getting these issues right is critical.
In particular, if you see intermittent failures when
OpenOCD verifies the scan chain after reset,
look at how you are setting up JTAG clocking.
@end quotation

@anchor{theinittargetsprocedure}
@subsection The init_targets procedure
@cindex init_targets procedure

Target config files can either be ``linear'' (script executed line-by-line when parsed in
configuration stage, @xref{configurationstage,,Configuration Stage},) or they can contain a special
procedure called @code{init_targets}, which will be executed when entering run stage
(after parsing all config files or after @code{init} command, @xref{enteringtherunstage,,Entering the Run Stage}.)
Such procedure can be overridden by ``next level'' script (which sources the original).
This concept facilitates code reuse when basic target config files provide generic configuration
procedures and @code{init_targets} procedure, which can then be sourced and enhanced or changed in
a ``more specific'' target config file. This is not possible with ``linear'' config scripts,
because sourcing them executes every initialization commands they provide.

@example
### generic_file.cfg ###

proc setup_my_chip @{chip_name flash_size ram_size@} @{
    # basic initialization procedure ...
@}

proc init_targets @{@} @{
    # initializes generic chip with 4kB of flash and 1kB of RAM
    setup_my_chip MY_GENERIC_CHIP 4096 1024
@}

### specific_file.cfg ###

source [find target/generic_file.cfg]

proc init_targets @{@} @{
    # initializes specific chip with 128kB of flash and 64kB of RAM
    setup_my_chip MY_CHIP_WITH_128K_FLASH_64KB_RAM 131072 65536
@}
@end example

The easiest way to convert ``linear'' config files to @code{init_targets} version is to
enclose every line of ``code'' (i.e. not @code{source} commands, procedures, etc.) in this procedure.

For an example of this scheme see LPC2000 target config files.

The @code{init_boards} procedure is a similar concept concerning board config files
(@xref{theinitboardprocedure,,The init_board procedure}.)

@anchor{theinittargeteventsprocedure}
@subsection The init_target_events procedure
@cindex init_target_events procedure

A special procedure called @code{init_target_events} is run just after
@code{init_targets} (@xref{theinittargetsprocedure,,The init_targets
procedure}.) and before @code{init_board}
(@xref{theinitboardprocedure,,The init_board procedure}.) It is used
to set up default target events for the targets that do not have those
events already assigned.

@subsection ARM Core Specific Hacks

If the chip has a DCC, enable it. If the chip is an ARM9 with some
special high speed download features - enable it.

If present, the MMU, the MPU and the CACHE should be disabled.

Some ARM cores are equipped with trace support, which permits
examination of the instruction and data bus activity. Trace
activity is controlled through an ``Embedded Trace Module'' (ETM)
on one of the core's scan chains. The ETM emits voluminous data
through a ``trace port''. (@xref{armhardwaretracing,,ARM Hardware Tracing}.)
If you are using an external trace port,
configure it in your board config file.
If you are using an on-chip ``Embedded Trace Buffer'' (ETB),
configure it in your target config file.

@example
etm config $_TARGETNAME 16 normal full etb
etb config $_TARGETNAME $_CHIPNAME.etb
@end example

@subsection Internal Flash Configuration

This applies @b{ONLY TO MICROCONTROLLERS} that have flash built in.

@b{Never ever} in the ``target configuration file'' define any type of
flash that is external to the chip. (For example a BOOT flash on
Chip Select 0.) Such flash information goes in a board file - not
the TARGET (chip) file.

Examples:
@itemize @bullet
@item at91sam7x256 - has 256K flash YES enable it.
@item str912 - has flash internal YES enable it.
@item imx27 - uses boot flash on CS0 - it goes in the board file.
@item pxa270 - again - CS0 flash - it goes in the board file.
@end itemize

@anchor{translatingconfigurationfiles}
@section Translating Configuration Files
@cindex translation
If you have a configuration file for another hardware debugger
or toolset (Abatron, BDI2000, BDI3000, CCS,
Lauterbach, SEGGER, Macraigor, etc.), translating
it into OpenOCD syntax is often quite straightforward. The most tricky
part of creating a configuration script is oftentimes the reset init
sequence where e.g. PLLs, DRAM and the like is set up.

One trick that you can use when translating is to write small
Tcl procedures to translate the syntax into OpenOCD syntax. This
can avoid manual translation errors and make it easier to
convert other scripts later on.

Example of transforming quirky arguments to a simple search and
replace job:

@example
#   Lauterbach syntax(?)
#
#       Data.Set c15:0x042f %long 0x40000015
#
#   OpenOCD syntax when using procedure below.
#
#       setc15 0x01 0x00050078

proc setc15 @{regs value@} @{
    global TARGETNAME

    echo [format "set p15 0x%04x, 0x%08x" $regs $value]

    arm mcr 15 [expr @{($regs >> 12) & 0x7@}] \
        [expr @{($regs >> 0) & 0xf@}] [expr @{($regs >> 4) & 0xf@}] \
        [expr @{($regs >> 8) & 0x7@}] $value
@}
@end example



@node Server Configuration
@chapter Server Configuration
@cindex initialization
The commands here are commonly found in the openocd.cfg file and are
used to specify what TCP/IP ports are used, and how GDB should be
supported.

@anchor{configurationstage}
@section Configuration Stage
@cindex configuration stage
@cindex config command

When the OpenOCD server process starts up, it enters a
@emph{configuration stage} which is the only time that
certain commands, @emph{configuration commands}, may be issued.
Normally, configuration commands are only available
inside startup scripts.

In this manual, the definition of a configuration command is
presented as a @emph{Config Command}, not as a @emph{Command}
which may be issued interactively.
The runtime @command{help} command also highlights configuration
commands, and those which may be issued at any time.

Those configuration commands include declaration of TAPs,
flash banks,
the interface used for JTAG communication,
and other basic setup.
The server must leave the configuration stage before it
may access or activate TAPs.
After it leaves this stage, configuration commands may no
longer be issued.

@deffn {Command} {command mode} [command_name]
Returns the command modes allowed by a command: 'any', 'config', or
'exec'. If no command is specified, returns the current command
mode. Returns 'unknown' if an unknown command is given. Command can be
multiple tokens. (command valid any time)

In this document, the modes are described as stages, 'config' and
'exec' mode correspond configuration stage and run stage. 'any' means
the command can be executed in either
stages. @xref{configurationstage,,Configuration Stage}, and
@xref{enteringtherunstage,,Entering the Run Stage}.
@end deffn

@anchor{enteringtherunstage}
@section Entering the Run Stage

The first thing OpenOCD does after leaving the configuration
stage is to verify that it can talk to the scan chain
(list of TAPs) which has been configured.
It will warn if it doesn't find TAPs it expects to find,
or finds TAPs that aren't supposed to be there.
You should see no errors at this point.
If you see errors, resolve them by correcting the
commands you used to configure the server.
Common errors include using an initial JTAG speed that's too
fast, and not providing the right IDCODE values for the TAPs
on the scan chain.

Once OpenOCD has entered the run stage, a number of commands
become available.
A number of these relate to the debug targets you may have declared.
For example, the @command{mww} command will not be available until
a target has been successfully instantiated.
If you want to use those commands, you may need to force
entry to the run stage.

@deffn {Config Command} {init}
This command terminates the configuration stage and
enters the run stage. This helps when you need to have
the startup scripts manage tasks such as resetting the target,
programming flash, etc. To reset the CPU upon startup, add "init" and
"reset" at the end of the config script or at the end of the OpenOCD
command line using the @option{-c} command line switch.

If this command does not appear in any startup/configuration file
OpenOCD executes the command for you after processing all
configuration files and/or command line options.

@b{NOTE:} This command normally occurs near the end of your
openocd.cfg file to force OpenOCD to ``initialize'' and make the
targets ready. For example: If your openocd.cfg file needs to
read/write memory on your target, @command{init} must occur before
the memory read/write commands. This includes @command{nand probe}.

@command{init} calls the following internal OpenOCD commands to initialize
corresponding subsystems:
@deffn {Config Command} {target init}
@deffnx {Command} {transport init}
@deffnx {Command} {dap init}
@deffnx {Config Command} {flash init}
@deffnx {Config Command} {nand init}
@deffnx {Config Command} {pld init}
@deffnx {Command} {tpiu init}
@end deffn
@end deffn

@deffn {Config Command} {noinit}
Prevent OpenOCD from implicit @command{init} call at the end of startup.
Allows issuing configuration commands over telnet or Tcl connection.
When you are done with configuration use @command{init} to enter
the run stage.
@end deffn

@deffn {Overridable Procedure} {jtag_init}
This is invoked at server startup to verify that it can talk
to the scan chain (list of TAPs) which has been configured.

The default implementation first tries @command{jtag arp_init},
which uses only a lightweight JTAG reset before examining the
scan chain.
If that fails, it tries again, using a harder reset
from the overridable procedure @command{init_reset}.

Implementations must have verified the JTAG scan chain before
they return.
This is done by calling @command{jtag arp_init}
(or @command{jtag arp_init-reset}).
@end deffn

@anchor{tcpipports}
@section TCP/IP Ports
@cindex TCP port
@cindex server
@cindex port
@cindex security
The OpenOCD server accepts remote commands in several syntaxes.
Each syntax uses a different TCP/IP port, which you may specify
only during configuration (before those ports are opened).

For reasons including security, you may wish to prevent remote
access using one or more of these ports.
In such cases, just specify the relevant port number as "disabled".
If you disable all access through TCP/IP, you will need to
use the command line @option{-pipe} option.

@anchor{gdb_port}
@deffn {Config Command} {gdb_port} [number]
@cindex GDB server
Normally gdb listens to a TCP/IP port, but GDB can also
communicate via pipes(stdin/out or named pipes). The name
"gdb_port" stuck because it covers probably more than 90% of
the normal use cases.

No arguments reports GDB port. "pipe" means listen to stdin
output to stdout, an integer is base port number, "disabled"
disables the gdb server.

When using "pipe", also use log_output to redirect the log
output to a file so as not to flood the stdin/out pipes.

Any other string is interpreted as named pipe to listen to.
Output pipe is the same name as input pipe, but with 'o' appended,
e.g. /var/gdb, /var/gdbo.

The GDB port for the first target will be the base port, the
second target will listen on gdb_port + 1, and so on.
When not specified during the configuration stage,
the port @var{number} defaults to 3333.
When @var{number} is not a numeric value, incrementing it to compute
the next port number does not work. In this case, specify the proper
@var{number} for each target by using the option @code{-gdb-port} of the
commands @command{target create} or @command{$target_name configure}.
@xref{gdbportoverride,,option -gdb-port}.

Note: when using "gdb_port pipe", increasing the default remote timeout in
gdb (with 'set remotetimeout') is recommended. An insufficient timeout may
cause initialization to fail with "Unknown remote qXfer reply: OK".
@end deffn

@deffn {Config Command} {tcl_port} [number]
Specify or query the port used for a simplified RPC
connection that can be used by clients to issue TCL commands and get the
output from the Tcl engine.
Intended as a machine interface.
When not specified during the configuration stage,
the port @var{number} defaults to 6666.
When specified as "disabled", this service is not activated.
@end deffn

@deffn {Config Command} {telnet_port} [number]
Specify or query the
port on which to listen for incoming telnet connections.
This port is intended for interaction with one human through TCL commands.
When not specified during the configuration stage,
the port @var{number} defaults to 4444.
When specified as "disabled", this service is not activated.
@end deffn

@anchor{gdbconfiguration}
@section GDB Configuration
@cindex GDB
@cindex GDB configuration
You can reconfigure some GDB behaviors if needed.
The ones listed here are static and global.
@xref{targetconfiguration,,Target Configuration}, about configuring individual targets.
@xref{targetevents,,Target Events}, about configuring target-specific event handling.

@anchor{gdbbreakpointoverride}
@deffn {Command} {gdb_breakpoint_override} [@option{hard}|@option{soft}|@option{disable}]
Force breakpoint type for gdb @command{break} commands.
This option supports GDB GUIs which don't
distinguish hard versus soft breakpoints, if the default OpenOCD and
GDB behaviour is not sufficient. GDB normally uses hardware
breakpoints if the memory map has been set up for flash regions.
@end deffn

@anchor{gdbflashprogram}
@deffn {Config Command} {gdb_flash_program} (@option{enable}|@option{disable})
Set to @option{enable} to cause OpenOCD to program the flash memory when a
vFlash packet is received.
The default behaviour is @option{enable}.
@end deffn

@deffn {Config Command} {gdb_memory_map} (@option{enable}|@option{disable})
Set to @option{enable} to cause OpenOCD to send the memory configuration to GDB when
requested. GDB will then know when to set hardware breakpoints, and program flash
using the GDB load command. @command{gdb_flash_program enable} must also be enabled
for flash programming to work.
Default behaviour is @option{enable}.
@xref{gdbflashprogram,,gdb_flash_program}.
@end deffn

@deffn {Config Command} {gdb_report_data_abort} (@option{enable}|@option{disable})
Specifies whether data aborts cause an error to be reported
by GDB memory read packets.
The default behaviour is @option{disable};
use @option{enable} see these errors reported.
@end deffn

@deffn {Config Command} {gdb_report_register_access_error} (@option{enable}|@option{disable})
Specifies whether register accesses requested by GDB register read/write
packets report errors or not.
The default behaviour is @option{disable};
use @option{enable} see these errors reported.
@end deffn

@deffn {Config Command} {gdb_target_description} (@option{enable}|@option{disable})
Set to @option{enable} to cause OpenOCD to send the target descriptions to gdb via qXfer:features:read packet.
The default behaviour is @option{enable}.
@end deffn

@deffn {Command} {gdb_save_tdesc}
Saves the target description file to the local file system.

The file name is @i{target_name}.xml.
@end deffn

@anchor{eventpolling}
@section Event Polling

Hardware debuggers are parts of asynchronous systems,
where significant events can happen at any time.
The OpenOCD server needs to detect some of these events,
so it can report them to through TCL command line
or to GDB.

Examples of such events include:

@itemize
@item One of the targets can stop running ... maybe it triggers
a code breakpoint or data watchpoint, or halts itself.
@item Messages may be sent over ``debug message'' channels ... many
targets support such messages sent over JTAG,
for receipt by the person debugging or tools.
@item Loss of power ... some adapters can detect these events.
@item Resets not issued through JTAG ... such reset sources
can include button presses or other system hardware, sometimes
including the target itself (perhaps through a watchdog).
@item Debug instrumentation sometimes supports event triggering
such as ``trace buffer full'' (so it can quickly be emptied)
or other signals (to correlate with code behavior).
@end itemize

None of those events are signaled through standard JTAG signals.
However, most conventions for JTAG connectors include voltage
level and system reset (SRST) signal detection.
Some connectors also include instrumentation signals, which
can imply events when those signals are inputs.

In general, OpenOCD needs to periodically check for those events,
either by looking at the status of signals on the JTAG connector
or by sending synchronous ``tell me your status'' JTAG requests
to the various active targets.
There is a command to manage and monitor that polling,
which is normally done in the background.

@deffn {Command} {poll} [@option{on}|@option{off}]
Poll the current target for its current state.
(Also, @pxref{targetcurstate,,target curstate}.)
If that target is in debug mode, architecture
specific information about the current state is printed.
An optional parameter
allows background polling to be enabled and disabled.

You could use this from the TCL command shell, or
from GDB using @command{monitor poll} command.
Leave background polling enabled while you're using GDB.
@example
> poll
background polling: on
target state: halted
target halted in ARM state due to debug-request, \
               current mode: Supervisor
cpsr: 0x800000d3 pc: 0x11081bfc
MMU: disabled, D-Cache: disabled, I-Cache: enabled
>
@end example
@end deffn

@node Debug Adapter Configuration
@chapter Debug Adapter Configuration
@cindex config file, interface
@cindex interface config file

Correctly installing OpenOCD includes making your operating system give
OpenOCD access to debug adapters. Once that has been done, Tcl commands
are used to select which one is used, and to configure how it is used.

@quotation Note
Because OpenOCD started out with a focus purely on JTAG, you may find
places where it wrongly presumes JTAG is the only transport protocol
in use. Be aware that recent versions of OpenOCD are removing that
limitation. JTAG remains more functional than most other transports.
Other transports do not support boundary scan operations, or may be
specific to a given chip vendor. Some might be usable only for
programming flash memory, instead of also for debugging.
@end quotation

Debug Adapters/Interfaces/Dongles are normally configured
through commands in an interface configuration
file which is sourced by your @file{openocd.cfg} file, or
through a command line @option{-f interface/....cfg} option.

@example
source [find interface/olimex-jtag-tiny.cfg]
@end example

These commands tell
OpenOCD what type of JTAG adapter you have, and how to talk to it.
A few cases are so simple that you only need to say what driver to use:

@example
# jlink interface
adapter driver jlink
@end example

Most adapters need a bit more configuration than that.


@section Adapter Configuration

The @command{adapter driver} command tells OpenOCD what type of debug adapter you are
using. Depending on the type of adapter, you may need to use one or
more additional commands to further identify or configure the adapter.

@deffn {Config Command} {adapter driver} name
Use the adapter driver @var{name} to connect to the
target.
@end deffn

@deffn {Command} {adapter list}
List the debug adapter drivers that have been built into
the running copy of OpenOCD.
@end deffn
@deffn {Config Command} {adapter transports} transport_name+
Specifies the transports supported by this debug adapter.
The adapter driver builds-in similar knowledge; use this only
when external configuration (such as jumpering) changes what
the hardware can support.
@end deffn



@deffn {Command} {adapter name}
Returns the name of the debug adapter driver being used.
@end deffn

@anchor{adapter_usb_location}
@deffn {Config Command} {adapter usb location} [<bus>-<port>[.<port>]...]
Displays or specifies the physical USB port of the adapter to use. The path
roots at @var{bus} and walks down the physical ports, with each
@var{port} option specifying a deeper level in the bus topology, the last
@var{port} denoting where the target adapter is actually plugged.
The USB bus topology can be queried with the command @emph{lsusb -t} or @emph{dmesg}.

This command is only available if your libusb1 is at least version 1.0.16.
@end deffn

@deffn {Config Command} {adapter serial} serial_string
Specifies the @var{serial_string} of the adapter to use.
If this command is not specified, serial strings are not checked.
Only the following adapter drivers use the serial string from this command:
aice (aice_usb), arm-jtag-ew, cmsis_dap, ft232r, ftdi, hla (stlink, ti-icdi), jlink, kitprog, opendus,
openjtag, osbdm, presto, rlink, st-link, usb_blaster (ublast2), usbprog, vsllink, xds110.
@end deffn

@section Interface Drivers

Each of the interface drivers listed here must be explicitly
enabled when OpenOCD is configured, in order to be made
available at run time.

@deffn {Interface Driver} {amt_jtagaccel}
Amontec Chameleon in its JTAG Accelerator configuration,
connected to a PC's EPP mode parallel port.
This defines some driver-specific commands:

@deffn {Config Command} {parport port} number
Specifies either the address of the I/O port (default: 0x378 for LPT1) or
the number of the @file{/dev/parport} device.
@end deffn

@deffn {Config Command} {rtck} [@option{enable}|@option{disable}]
Displays status of RTCK option.
Optionally sets that option first.
@end deffn
@end deffn

@deffn {Interface Driver} {arm-jtag-ew}
Olimex ARM-JTAG-EW USB adapter
This has one driver-specific command:

@deffn {Command} {armjtagew_info}
Logs some status
@end deffn
@end deffn

@deffn {Interface Driver} {at91rm9200}
Supports bitbanged JTAG from the local system,
presuming that system is an Atmel AT91rm9200
and a specific set of GPIOs is used.
@c command:	at91rm9200_device NAME
@c chooses among list of bit configs ... only one option
@end deffn

@deffn {Interface Driver} {cmsis-dap}
ARM CMSIS-DAP compliant based adapter v1 (USB HID based)
or v2 (USB bulk).

@deffn {Config Command} {cmsis_dap_vid_pid} [vid pid]+
The vendor ID and product ID of the CMSIS-DAP device. If not specified
the driver will attempt to auto detect the CMSIS-DAP device.
Currently, up to eight [@var{vid}, @var{pid}] pairs may be given, e.g.
@example
cmsis_dap_vid_pid 0xc251 0xf001 0x0d28 0x0204
@end example
@end deffn

@deffn {Config Command} {cmsis_dap_backend} [@option{auto}|@option{usb_bulk}|@option{hid}]
Specifies how to communicate with the adapter:

@itemize @minus
@item @option{hid} Use HID generic reports - CMSIS-DAP v1
@item @option{usb_bulk} Use USB bulk - CMSIS-DAP v2
@item @option{auto} First try USB bulk CMSIS-DAP v2, if not found try HID CMSIS-DAP v1.
This is the default if @command{cmsis_dap_backend} is not specified.
@end itemize
@end deffn

@deffn {Config Command} {cmsis_dap_usb interface} [number]
Specifies the @var{number} of the USB interface to use in v2 mode (USB bulk).
In most cases need not to be specified and interfaces are searched by
interface string or for user class interface.
@end deffn

@deffn {Command} {cmsis-dap info}
Display various device information, like hardware version, firmware version, current bus status.
@end deffn

@deffn {Command} {cmsis-dap cmd} number number ...
Execute an arbitrary CMSIS-DAP command. Use for adapter testing or for handling
of an adapter vendor specific command from a Tcl script.

Take given numbers as bytes, assemble a CMSIS-DAP protocol command packet
from them and send it to the adapter. The first 4 bytes of the adapter response
are logged.
See @url{https://arm-software.github.io/CMSIS_5/DAP/html/group__DAP__Commands__gr.html}
@end deffn
@end deffn

@deffn {Interface Driver} {dummy}
A dummy software-only driver for debugging.
@end deffn

@deffn {Interface Driver} {ep93xx}
Cirrus Logic EP93xx based single-board computer bit-banging (in development)
@end deffn

@deffn {Interface Driver} {ftdi}
This driver is for adapters using the MPSSE (Multi-Protocol Synchronous Serial
Engine) mode built into many FTDI chips, such as the FT2232, FT4232 and FT232H.

The driver is using libusb-1.0 in asynchronous mode to talk to the FTDI device,
bypassing intermediate libraries like libftdi.

Support for new FTDI based adapters can be added completely through
configuration files, without the need to patch and rebuild OpenOCD.

The driver uses a signal abstraction to enable Tcl configuration files to
define outputs for one or several FTDI GPIO. These outputs can then be
controlled using the @command{ftdi set_signal} command. Special signal names
are reserved for nTRST, nSRST and LED (for blink) so that they, if defined,
will be used for their customary purpose. Inputs can be read using the
@command{ftdi get_signal} command.

To support SWD, a signal named SWD_EN must be defined. It is set to 1 when the
SWD protocol is selected. When set, the adapter should route the SWDIO pin to
the data input. An SWDIO_OE signal, if defined, will be set to 1 or 0 as
required by the protocol, to tell the adapter to drive the data output onto
the SWDIO pin or keep the SWDIO pin Hi-Z, respectively.

Depending on the type of buffer attached to the FTDI GPIO, the outputs have to
be controlled differently. In order to support tristateable signals such as
nSRST, both a data GPIO and an output-enable GPIO can be specified for each
signal. The following output buffer configurations are supported:

@itemize @minus
@item Push-pull with one FTDI output as (non-)inverted data line
@item Open drain with one FTDI output as (non-)inverted output-enable
@item Tristate with one FTDI output as (non-)inverted data line and another
      FTDI output as (non-)inverted output-enable
@item Unbuffered, using the FTDI GPIO as a tristate output directly by
      switching data and direction as necessary
@end itemize

These interfaces have several commands, used to configure the driver
before initializing the JTAG scan chain:

@deffn {Config Command} {ftdi vid_pid} [vid pid]+
The vendor ID and product ID of the adapter. Up to eight
[@var{vid}, @var{pid}] pairs may be given, e.g.
@example
ftdi vid_pid 0x0403 0xcff8 0x15ba 0x0003
@end example
@end deffn

@deffn {Config Command} {ftdi device_desc} description
Provides the USB device description (the @emph{iProduct string})
of the adapter. If not specified, the device description is ignored
during device selection.
@end deffn

@deffn {Config Command} {ftdi channel} channel
Selects the channel of the FTDI device to use for MPSSE operations. Most
adapters use the default, channel 0, but there are exceptions.
@end deffn

@deffn {Config Command} {ftdi layout_init} data direction
Specifies the initial values of the FTDI GPIO data and direction registers.
Each value is a 16-bit number corresponding to the concatenation of the high
and low FTDI GPIO registers. The values should be selected based on the
schematics of the adapter, such that all signals are set to safe levels with
minimal impact on the target system. Avoid floating inputs, conflicting outputs
and initially asserted reset signals.
@end deffn

@deffn {Command} {ftdi oscan1_mode} on|off
Enable or disable OScan1 mode. This mode is intended for use with an adapter,
such as the ARM-JTAG-SWD by Olimex, that sits in between the FTDI chip and the
target. The cJTAG prococol is composed of two wires: TCKC (clock) and TMSC (data).
TMSC is a bidirectional signal which is time-multiplexed alternating TDI, TMS and
TDO. The multiplexing is achieved by a tri-state buffer which puts TMSC in Hi-Z
when the device is supposed to take the control of the line (TDO phase).

The ARM-JTAG-SWD adapter uses standard TRST and TMS signals to control TMSC
direction. TRST is used by the adapter as selector for the multiplexers which set
the JTAG probe in 2-wire mode. Whatever signal is used for this purpose, it must
be defined with the name JTAG_SEL using @command{ftdi layout_signal}. JTAG_SEL is
set to 0 during OScan1 initialization.

Some JTAG probes like the Digilent JTAG-HS2, support cJTAG by using a
separate pin to control when TMS is driven onto TMSC. You can use such
probes by defining the signal TMSC_EN using
@command{ftdi layout_signal TMSC_EN -data <mask>}.
@end deffn

@deffn {Command} {ftdi jscan3_mode} on|off
Enable or disable JScan3 mode. This mode uses the classic 4-wire JTAG protocol
in chips whose JTAG port is only compliant with the cJTAG standard (IEEE 1149.7).

Since cJTAG needs a 2-wire escape sequence to select the operating mode,
a cJTAG adapter like ARM-JTAG-SWD by Olimex is still required. This means
that a cJTAG probe configuration script must be used too.
@end deffn

@deffn {Command} {ftdi layout_signal} name [@option{-data}|@option{-ndata} data_mask] [@option{-input}|@option{-ninput} input_mask] [@option{-oe}|@option{-noe} oe_mask] [@option{-alias}|@option{-nalias} name]
Creates a signal with the specified @var{name}, controlled by one or more FTDI
GPIO pins via a range of possible buffer connections. The masks are FTDI GPIO
register bitmasks to tell the driver the connection and type of the output
buffer driving the respective signal. @var{data_mask} is the bitmask for the
pin(s) connected to the data input of the output buffer. @option{-ndata} is
used with inverting data inputs and @option{-data} with non-inverting inputs.
The @option{-oe} (or @option{-noe}) option tells where the output-enable (or
not-output-enable) input to the output buffer is connected. The options
@option{-input} and @option{-ninput} specify the bitmask for pins to be read
with the method @command{ftdi get_signal}.

Both @var{data_mask} and @var{oe_mask} need not be specified. For example, a
simple open-collector transistor driver would be specified with @option{-oe}
only. In that case the signal can only be set to drive low or to Hi-Z and the
driver will complain if the signal is set to drive high. Which means that if
it's a reset signal, @command{reset_config} must be specified as
@option{srst_open_drain}, not @option{srst_push_pull}.

A special case is provided when @option{-data} and @option{-oe} is set to the
same bitmask. Then the FTDI pin is considered being connected straight to the
target without any buffer. The FTDI pin is then switched between output and
input as necessary to provide the full set of low, high and Hi-Z
characteristics. In all other cases, the pins specified in a signal definition
are always driven by the FTDI.

If @option{-alias} or @option{-nalias} is used, the signal is created
identical (or with data inverted) to an already specified signal
@var{name}.
@end deffn

@deffn {Command} {ftdi set_signal} name @option{0}|@option{1}|@option{z}
Set a previously defined signal to the specified level.
@itemize @minus
@item @option{0}, drive low
@item @option{1}, drive high
@item @option{z}, set to high-impedance
@end itemize
@end deffn

@deffn {Command} {ftdi get_signal} name
Get the value of a previously defined signal.
@end deffn

@deffn {Command} {ftdi tdo_sample_edge} @option{rising}|@option{falling}
Configure TCK edge at which the adapter samples the value of the TDO signal

Due to signal propagation delays, sampling TDO on rising TCK can become quite
peculiar at high JTAG clock speeds. However, FTDI chips offer a possibility to sample
TDO on falling edge of TCK. With some board/adapter configurations, this may increase
stability at higher JTAG clocks.
@itemize @minus
@item @option{rising}, sample TDO on rising edge of TCK - this is the default
@item @option{falling}, sample TDO on falling edge of TCK
@end itemize
@end deffn

For example adapter definitions, see the configuration files shipped in the
@file{interface/ftdi} directory.

@end deffn

@deffn {Interface Driver} {ft232r}
This driver is implementing synchronous bitbang mode of an FTDI FT232R,
FT230X, FT231X and similar USB UART bridge ICs by reusing RS232 signals as GPIO.
It currently doesn't support using CBUS pins as GPIO.

List of connections (default physical pin numbers for FT232R in 28-pin SSOP package):
@itemize @minus
@item RXD(5) - TDI
@item TXD(1) - TCK
@item RTS(3) - TDO
@item CTS(11) - TMS
@item DTR(2) - TRST
@item DCD(10) - SRST
@end itemize

User can change default pinout by supplying configuration
commands with GPIO numbers or RS232 signal names.
GPIO numbers correspond to bit numbers in FTDI GPIO register.
They differ from physical pin numbers.
For details see actual FTDI chip datasheets.
Every JTAG line must be configured to unique GPIO number
different than any other JTAG line, even those lines
that are sometimes not used like TRST or SRST.

FT232R
@itemize @minus
@item bit 7 - RI
@item bit 6 - DCD
@item bit 5 - DSR
@item bit 4 - DTR
@item bit 3 - CTS
@item bit 2 - RTS
@item bit 1 - RXD
@item bit 0 - TXD
@end itemize

These interfaces have several commands, used to configure the driver
before initializing the JTAG scan chain:

@deffn {Config Command} {ft232r vid_pid} @var{vid} @var{pid}
The vendor ID and product ID of the adapter. If not specified, default
0x0403:0x6001 is used.
@end deffn

@deffn {Config Command} {ft232r jtag_nums} @var{tck} @var{tms} @var{tdi} @var{tdo}
Set four JTAG GPIO numbers at once.
If not specified, default 0 3 1 2 or TXD CTS RXD RTS is used.
@end deffn

@deffn {Config Command} {ft232r tck_num} @var{tck}
Set TCK GPIO number. If not specified, default 0 or TXD is used.
@end deffn

@deffn {Config Command} {ft232r tms_num} @var{tms}
Set TMS GPIO number. If not specified, default 3 or CTS is used.
@end deffn

@deffn {Config Command} {ft232r tdi_num} @var{tdi}
Set TDI GPIO number. If not specified, default 1 or RXD is used.
@end deffn

@deffn {Config Command} {ft232r tdo_num} @var{tdo}
Set TDO GPIO number. If not specified, default 2 or RTS is used.
@end deffn

@deffn {Config Command} {ft232r trst_num} @var{trst}
Set TRST GPIO number. If not specified, default 4 or DTR is used.
@end deffn

@deffn {Config Command} {ft232r srst_num} @var{srst}
Set SRST GPIO number. If not specified, default 6 or DCD is used.
@end deffn

@deffn {Config Command} {ft232r restore_serial} @var{word}
Restore serial port after JTAG. This USB bitmode control word
(16-bit) will be sent before quit. Lower byte should
set GPIO direction register to a "sane" state:
0x15 for TXD RTS DTR as outputs (1), others as inputs (0). Higher
byte is usually 0 to disable bitbang mode.
When kernel driver reattaches, serial port should continue to work.
Value 0xFFFF disables sending control word and serial port,
then kernel driver will not reattach.
If not specified, default 0xFFFF is used.
@end deffn

@end deffn

@deffn {Interface Driver} {remote_bitbang}
Drive JTAG from a remote process. This sets up a UNIX or TCP socket connection
with a remote process and sends ASCII encoded bitbang requests to that process
instead of directly driving JTAG.

The remote_bitbang driver is useful for debugging software running on
processors which are being simulated.

@deffn {Config Command} {remote_bitbang port} number
Specifies the TCP port of the remote process to connect to or 0 to use UNIX
sockets instead of TCP.
@end deffn

@deffn {Config Command} {remote_bitbang host} hostname
Specifies the hostname of the remote process to connect to using TCP, or the
name of the UNIX socket to use if remote_bitbang port is 0.
@end deffn

For example, to connect remotely via TCP to the host foobar you might have
something like:

@example
adapter driver remote_bitbang
remote_bitbang port 3335
remote_bitbang host foobar
@end example

To connect to another process running locally via UNIX sockets with socket
named mysocket:

@example
adapter driver remote_bitbang
remote_bitbang port 0
remote_bitbang host mysocket
@end example
@end deffn

@deffn {Interface Driver} {usb_blaster}
USB JTAG/USB-Blaster compatibles over one of the userspace libraries
for FTDI chips. These interfaces have several commands, used to
configure the driver before initializing the JTAG scan chain:

@deffn {Config Command} {usb_blaster vid_pid} vid pid
The vendor ID and product ID of the FTDI FT245 device. If not specified,
default values are used.
Currently, only one @var{vid}, @var{pid} pair may be given, e.g. for
Altera USB-Blaster (default):
@example
usb_blaster vid_pid 0x09FB 0x6001
@end example
The following VID/PID is for Kolja Waschk's USB JTAG:
@example
usb_blaster vid_pid 0x16C0 0x06AD
@end example
@end deffn

@deffn {Command} {usb_blaster pin} (@option{pin6}|@option{pin8}) (@option{0}|@option{1}|@option{s}|@option{t})
Sets the state or function of the unused GPIO pins on USB-Blasters
(pins 6 and 8 on the female JTAG header). These pins can be used as
SRST and/or TRST provided the appropriate connections are made on the
target board.

For example, to use pin 6 as SRST:
@example
usb_blaster pin pin6 s
reset_config srst_only
@end example
@end deffn

@deffn {Config Command} {usb_blaster lowlevel_driver} (@option{ftdi}|@option{ublast2})
Chooses the low level access method for the adapter. If not specified,
@option{ftdi} is selected unless it wasn't enabled during the
configure stage. USB-Blaster II needs @option{ublast2}.
@end deffn

@deffn {Config Command} {usb_blaster firmware} @var{path}
This command specifies @var{path} to access USB-Blaster II firmware
image. To be used with USB-Blaster II only.
@end deffn

@end deffn

@deffn {Interface Driver} {gw16012}
Gateworks GW16012 JTAG programmer.
This has one driver-specific command:

@deffn {Config Command} {parport port} [port_number]
Display either the address of the I/O port
(default: 0x378 for LPT1) or the number of the @file{/dev/parport} device.
If a parameter is provided, first switch to use that port.
This is a write-once setting.
@end deffn
@end deffn

@deffn {Interface Driver} {jlink}
SEGGER J-Link family of USB adapters. It currently supports JTAG and SWD
transports.

@quotation Compatibility Note
SEGGER released many firmware versions for the many hardware versions they
produced. OpenOCD was extensively tested and intended to run on all of them,
but some combinations were reported as incompatible. As a general
recommendation, it is advisable to use the latest firmware version
available for each hardware version. However the current V8 is a moving
target, and SEGGER firmware versions released after the OpenOCD was
released may not be compatible. In such cases it is recommended to
revert to the last known functional version. For 0.5.0, this is from
"Feb  8 2012 14:30:39", packed with 4.42c. For 0.6.0, the last known
version is from "May  3 2012 18:36:22", packed with 4.46f.
@end quotation

@deffn {Command} {jlink hwstatus}
Display various hardware related information, for example target voltage and pin
states.
@end deffn
@deffn {Command} {jlink freemem}
Display free device internal memory.
@end deffn
@deffn {Command} {jlink jtag} [@option{2}|@option{3}]
Set the JTAG command version to be used. Without argument, show the actual JTAG
command version.
@end deffn
@deffn {Command} {jlink config}
Display the device configuration.
@end deffn
@deffn {Command} {jlink config targetpower} [@option{on}|@option{off}]
Set the target power state on JTAG-pin 19. Without argument, show the target
power state.
@end deffn
@deffn {Command} {jlink config mac} [@option{ff:ff:ff:ff:ff:ff}]
Set the MAC address of the device. Without argument, show the MAC address.
@end deffn
@deffn {Command} {jlink config ip} [@option{A.B.C.D}(@option{/E}|@option{F.G.H.I})]
Set the IP configuration of the device, where A.B.C.D is the IP address, E the
bit of the subnet mask and F.G.H.I the subnet mask. Without arguments, show the
IP configuration.
@end deffn
@deffn {Command} {jlink config usb} [@option{0} to @option{3}]
Set the USB address of the device. This will also change the USB Product ID
(PID) of the device. Without argument, show the USB address.
@end deffn
@deffn {Command} {jlink config reset}
Reset the current configuration.
@end deffn
@deffn {Command} {jlink config write}
Write the current configuration to the internal persistent storage.
@end deffn
@deffn {Command} {jlink emucom write} <channel> <data>
Write data to an EMUCOM channel. The data needs to be encoded as hexadecimal
pairs.

The following example shows how to write the three bytes 0xaa, 0x0b and 0x23 to
the EMUCOM channel 0x10:
@example
> jlink emucom write 0x10 aa0b23
@end example
@end deffn
@deffn {Command} {jlink emucom read} <channel> <length>
Read data from an EMUCOM channel. The read data is encoded as hexadecimal
pairs.

The following example shows how to read 4 bytes from the EMUCOM channel 0x0:
@example
> jlink emucom read 0x0 4
77a90000
@end example
@end deffn
@deffn {Config Command} {jlink usb} <@option{0} to @option{3}>
Set the USB address of the interface, in case more than one adapter is connected
to the host. If not specified, USB addresses are not considered. Device
selection via USB address is not always unambiguous. It is recommended to use
the serial number instead, if possible.

As a configuration command, it can be used only before 'init'.
@end deffn
@end deffn

@deffn {Interface Driver} {kitprog}
This driver is for Cypress Semiconductor's KitProg adapters. The KitProg is an
SWD-only adapter that is designed to be used with Cypress's PSoC and PRoC device
families, but it is possible to use it with some other devices. If you are using
this adapter with a PSoC or a PRoC, you may need to add
@command{kitprog_init_acquire_psoc} or @command{kitprog acquire_psoc} to your
configuration script.

Note that this driver is for the proprietary KitProg protocol, not the CMSIS-DAP
mode introduced in firmware 2.14. If the KitProg is in CMSIS-DAP mode, it cannot
be used with this driver, and must either be used with the cmsis-dap driver or
switched back to KitProg mode. See the Cypress KitProg User Guide for
instructions on how to switch KitProg modes.

Known limitations:
@itemize @bullet
@item The frequency of SWCLK cannot be configured, and varies between 1.6 MHz
and 2.7 MHz.
@item For firmware versions below 2.14, "JTAG to SWD" sequences are replaced by
"SWD line reset" in the driver. This is for two reasons. First, the KitProg does
not support sending arbitrary SWD sequences, and only firmware 2.14 and later
implement both "JTAG to SWD" and "SWD line reset" in firmware. Earlier firmware
versions only implement "SWD line reset". Second, due to a firmware quirk, an
SWD sequence must be sent after every target reset in order to re-establish
communications with the target.
@item Due in part to the limitation above, KitProg devices with firmware below
version 2.14 will need to use @command{kitprog_init_acquire_psoc} in order to
communicate with PSoC 5LP devices. This is because, assuming debug is not
disabled on the PSoC, the PSoC 5LP needs its JTAG interface switched to SWD
mode before communication can begin, but prior to firmware 2.14, "JTAG to SWD"
could only be sent with an acquisition sequence.
@end itemize

@deffn {Config Command} {kitprog_init_acquire_psoc}
Indicate that a PSoC acquisition sequence needs to be run during adapter init.
Please be aware that the acquisition sequence hard-resets the target.
@end deffn

@deffn {Command} {kitprog acquire_psoc}
Run a PSoC acquisition sequence immediately. Typically, this should not be used
outside of the target-specific configuration scripts since it hard-resets the
target as a side-effect.
This is necessary for "reset halt" on some PSoC 4 series devices.
@end deffn

@deffn {Command} {kitprog info}
Display various adapter information, such as the hardware version, firmware
version, and target voltage.
@end deffn
@end deffn

@deffn {Interface Driver} {parport}
Supports PC parallel port bit-banging cables:
Wigglers, PLD download cable, and more.
These interfaces have several commands, used to configure the driver
before initializing the JTAG scan chain:

@deffn {Config Command} {parport cable} name
Set the layout of the parallel port cable used to connect to the target.
This is a write-once setting.
Currently valid cable @var{name} values include:

@itemize @minus
@item @b{altium} Altium Universal JTAG cable.
@item @b{arm-jtag} Same as original wiggler except SRST and
TRST connections reversed and TRST is also inverted.
@item @b{chameleon} The Amontec Chameleon's CPLD when operated
in configuration mode. This is only used to
program the Chameleon itself, not a connected target.
@item @b{dlc5} The Xilinx Parallel cable III.
@item @b{flashlink} The ST Parallel cable.
@item @b{lattice} Lattice ispDOWNLOAD Cable
@item @b{old_amt_wiggler} The Wiggler configuration that comes with
some versions of
Amontec's Chameleon Programmer. The new version available from
the website uses the original Wiggler layout ('@var{wiggler}')
@item @b{triton} The parallel port adapter found on the
``Karo Triton 1 Development Board''.
This is also the layout used by the HollyGates design
(see @uref{http://www.lartmaker.nl/projects/jtag/}).
@item @b{wiggler} The original Wiggler layout, also supported by
several clones, such as the Olimex ARM-JTAG
@item @b{wiggler2} Same as original wiggler except an led is fitted on D5.
@item @b{wiggler_ntrst_inverted} Same as original wiggler except TRST is inverted.
@end itemize
@end deffn

@deffn {Config Command} {parport port} [port_number]
Display either the address of the I/O port
(default: 0x378 for LPT1) or the number of the @file{/dev/parport} device.
If a parameter is provided, first switch to use that port.
This is a write-once setting.

When using PPDEV to access the parallel port, use the number of the parallel port:
@option{parport port 0} (the default). If @option{parport port 0x378} is specified
you may encounter a problem.
@end deffn

@deffn {Config Command} {parport toggling_time} [nanoseconds]
Displays how many nanoseconds the hardware needs to toggle TCK;
the parport driver uses this value to obey the
@command{adapter speed} configuration.
When the optional @var{nanoseconds} parameter is given,
that setting is changed before displaying the current value.

The default setting should work reasonably well on commodity PC hardware.
However, you may want to calibrate for your specific hardware.
@quotation Tip
To measure the toggling time with a logic analyzer or a digital storage
oscilloscope, follow the procedure below:
@example
> parport toggling_time 1000
> adapter speed 500
@end example
This sets the maximum JTAG clock speed of the hardware, but
the actual speed probably deviates from the requested 500 kHz.
Now, measure the time between the two closest spaced TCK transitions.
You can use @command{runtest 1000} or something similar to generate a
large set of samples.
Update the setting to match your measurement:
@example
> parport toggling_time <measured nanoseconds>
@end example
Now the clock speed will be a better match for @command{adapter speed}
command given in OpenOCD scripts and event handlers.

You can do something similar with many digital multimeters, but note
that you'll probably need to run the clock continuously for several
seconds before it decides what clock rate to show. Adjust the
toggling time up or down until the measured clock rate is a good
match with the rate you specified in the @command{adapter speed} command;
be conservative.
@end quotation
@end deffn

@deffn {Config Command} {parport write_on_exit} (@option{on}|@option{off})
This will configure the parallel driver to write a known
cable-specific value to the parallel interface on exiting OpenOCD.
@end deffn

For example, the interface configuration file for a
classic ``Wiggler'' cable on LPT2 might look something like this:

@example
adapter driver parport
parport port 0x278
parport cable wiggler
@end example
@end deffn

@deffn {Interface Driver} {presto}
ASIX PRESTO USB JTAG programmer.
@end deffn

@deffn {Interface Driver} {rlink}
Raisonance RLink USB adapter
@end deffn

@deffn {Interface Driver} {usbprog}
usbprog is a freely programmable USB adapter.
@end deffn

@deffn {Interface Driver} {vsllink}
vsllink is part of Versaloon which is a versatile USB programmer.

@quotation Note
This defines quite a few driver-specific commands,
which are not currently documented here.
@end quotation
@end deffn

@anchor{hla_interface}
@deffn {Interface Driver} {hla}
This is a driver that supports multiple High Level Adapters.
This type of adapter does not expose some of the lower level api's
that OpenOCD would normally use to access the target.

Currently supported adapters include the STMicroelectronics ST-LINK, TI ICDI
and Nuvoton Nu-Link.
ST-LINK firmware version >= V2.J21.S4 recommended due to issues with earlier
versions of firmware where serial number is reset after first use.  Suggest
using ST firmware update utility to upgrade ST-LINK firmware even if current
version reported is V2.J21.S4.

@deffn {Config Command} {hla_device_desc} description
Currently Not Supported.
@end deffn

@deffn {Config Command} {hla_layout} (@option{stlink}|@option{icdi}|@option{nulink})
Specifies the adapter layout to use.
@end deffn

@deffn {Config Command} {hla_vid_pid} [vid pid]+
Pairs of vendor IDs and product IDs of the device.
@end deffn

@deffn {Config Command} {hla_stlink_backend} (usb | tcp [port])
@emph{ST-Link only:} Choose between 'exclusive' USB communication (the default backend) or
'shared' mode using ST-Link TCP server (the default port is 7184).

@emph{Note:} ST-Link TCP server is a binary application provided by ST
available from @url{https://www.st.com/en/development-tools/st-link-server.html,
ST-LINK server software module}.
@end deffn

@deffn {Command} {hla_command} command
Execute a custom adapter-specific command. The @var{command} string is
passed as is to the underlying adapter layout handler.
@end deffn
@end deffn

@anchor{st_link_dap_interface}
@deffn {Interface Driver} {st-link}
This is a driver that supports STMicroelectronics adapters ST-LINK/V2
(from firmware V2J24) and STLINK-V3, thanks to a new API that provides
directly access the arm ADIv5 DAP.

The new API provide access to multiple AP on the same DAP, but the
maximum number of the AP port is limited by the specific firmware version
(e.g. firmware V2J29 has 3 as maximum AP number, while V2J32 has 8).
An error is returned for any AP number above the maximum allowed value.

@emph{Note:} Either these same adapters and their older versions are
also supported by @ref{hla_interface, the hla interface driver}.

@deffn {Config Command} {st-link backend} (usb | tcp [port])
Choose between 'exclusive' USB communication (the default backend) or
'shared' mode using ST-Link TCP server (the default port is 7184).

@emph{Note:} ST-Link TCP server is a binary application provided by ST
available from @url{https://www.st.com/en/development-tools/st-link-server.html,
ST-LINK server software module}.

@emph{Note:} ST-Link TCP server does not support the SWIM transport.
@end deffn

@deffn {Config Command} {st-link vid_pid} [vid pid]+
Pairs of vendor IDs and product IDs of the device.
@end deffn

@deffn {Command} {st-link cmd} rx_n (tx_byte)+
Sends an arbitrary command composed by the sequence of bytes @var{tx_byte}
and receives @var{rx_n} bytes.

For example, the command to read the target's supply voltage is one byte 0xf7 followed
by 15 bytes zero. It returns 8 bytes, where the first 4 bytes represent the ADC sampling
of the reference voltage 1.2V and the last 4 bytes represent the ADC sampling of half
the target's supply voltage.
@example
> st-link cmd 8 0xf7 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
0xf1 0x05 0x00 0x00 0x0b 0x08 0x00 0x00
@end example
The result can be converted to Volts (ignoring the most significant bytes, always zero)
@example
> set a [st-link cmd 8 0xf7 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]
> set n [expr @{[lindex $a 4] + 256 * [lindex $a 5]@}]
> set d [expr @{[lindex $a 0] + 256 * [lindex $a 1]@}]
> echo [expr @{2 * 1.2 * $n / $d@}]
3.24891518738
@end example
@end deffn
@end deffn

@deffn {Interface Driver} {opendous}
opendous-jtag is a freely programmable USB adapter.
@end deffn

@deffn {Interface Driver} {ulink}
This is the Keil ULINK v1 JTAG debugger.
@end deffn

@deffn {Interface Driver} {xds110}
The XDS110 is included as the embedded debug probe on many Texas Instruments
LaunchPad evaluation boards. The XDS110 is also available as a stand-alone USB
debug probe with the added capability to supply power to the target board. The
following commands are supported by the XDS110 driver:

@deffn {Config Command} {xds110 supply} voltage_in_millivolts
Available only on the XDS110 stand-alone probe. Sets the voltage level of the
XDS110 power supply. A value of 0 leaves the supply off. Otherwise, the supply
can be set to any value in the range 1800 to 3600 millivolts.
@end deffn

@deffn {Command} {xds110 info}
Displays information about the connected XDS110 debug probe (e.g. firmware
version).
@end deffn
@end deffn

@deffn {Interface Driver} {xlnx_pcie_xvc}
This driver supports the Xilinx Virtual Cable (XVC) over PCI Express.
It is commonly found in Xilinx based PCI Express designs. It allows debugging
fabric based JTAG/SWD devices such as Cortex-M1/M3 microcontrollers. Access to this is
exposed via extended capability registers in the PCI Express configuration space.

For more information see Xilinx PG245 (Section on From_PCIE_to_JTAG mode).

@deffn {Config Command} {xlnx_pcie_xvc config} device
Specifies the PCI Express device via parameter @var{device} to use.

The correct value for @var{device} can be obtained by looking at the output
of lscpi -D (first column) for the corresponding device.

The string will be of the format "DDDD:BB:SS.F" such as "0000:65:00.1".

@end deffn
@end deffn

@deffn {Interface Driver} {bcm2835gpio}
This SoC is present in Raspberry Pi which is a cheap single-board computer
exposing some GPIOs on its expansion header.

The driver accesses memory-mapped GPIO peripheral registers directly
for maximum performance, but the only possible race condition is for
the pins' modes/muxing (which is highly unlikely), so it should be
able to coexist nicely with both sysfs bitbanging and various
peripherals' kernel drivers. The driver restores the previous
configuration on exit.

GPIO numbers >= 32 can't be used for performance reasons.

See @file{interface/raspberrypi-native.cfg} for a sample config and
pinout.

@deffn {Config Command} {bcm2835gpio jtag_nums} @var{tck} @var{tms} @var{tdi} @var{tdo}
Set JTAG transport GPIO numbers for TCK, TMS, TDI, and TDO (in that order).
Must be specified to enable JTAG transport. These pins can also be specified
individually.
@end deffn

@deffn {Config Command} {bcm2835gpio tck_num} @var{tck}
Set TCK GPIO number. Must be specified to enable JTAG transport. Can also be
specified using the configuration command @command{bcm2835gpio jtag_nums}.
@end deffn

@deffn {Config Command} {bcm2835gpio tms_num} @var{tms}
Set TMS GPIO number. Must be specified to enable JTAG transport. Can also be
specified using the configuration command @command{bcm2835gpio jtag_nums}.
@end deffn

@deffn {Config Command} {bcm2835gpio tdo_num} @var{tdo}
Set TDO GPIO number. Must be specified to enable JTAG transport. Can also be
specified using the configuration command @command{bcm2835gpio jtag_nums}.
@end deffn

@deffn {Config Command} {bcm2835gpio tdi_num} @var{tdi}
Set TDI GPIO number. Must be specified to enable JTAG transport. Can also be
specified using the configuration command @command{bcm2835gpio jtag_nums}.
@end deffn

@deffn {Config Command} {bcm2835gpio swd_nums} @var{swclk} @var{swdio}
Set SWD transport GPIO numbers for SWCLK and SWDIO (in that order). Must be
specified to enable SWD transport. These pins can also be specified individually.
@end deffn

@deffn {Config Command} {bcm2835gpio swclk_num} @var{swclk}
Set SWCLK GPIO number. Must be specified to enable SWD transport. Can also be
specified using the configuration command @command{bcm2835gpio swd_nums}.
@end deffn

@deffn {Config Command} {bcm2835gpio swdio_num} @var{swdio}
Set SWDIO GPIO number. Must be specified to enable SWD transport. Can also be
specified using the configuration command @command{bcm2835gpio swd_nums}.
@end deffn

@deffn {Config Command} {bcm2835gpio swdio_dir_num} @var{swdio} @var{dir}
Set SWDIO direction control pin GPIO number. If specified, this pin can be used
to control the direction of an external buffer on the SWDIO pin (set=output
mode, clear=input mode). If not specified, this feature is disabled.
@end deffn

@deffn {Config Command} {bcm2835gpio srst_num} @var{srst}
Set SRST GPIO number. Must be specified to enable SRST.
@end deffn

@deffn {Config Command} {bcm2835gpio trst_num} @var{trst}
Set TRST GPIO number. Must be specified to enable TRST.
@end deffn

@deffn {Config Command} {bcm2835gpio speed_coeffs} @var{speed_coeff} @var{speed_offset}
Set SPEED_COEFF and SPEED_OFFSET for delay calculations. If unspecified,
speed_coeff defaults to 113714, and speed_offset defaults to 28.
@end deffn

@deffn {Config Command} {bcm2835gpio peripheral_base} @var{base}
Set the peripheral base register address to access GPIOs. For the RPi1, use
0x20000000. For RPi2 and RPi3, use 0x3F000000. For RPi4, use 0xFE000000. A full
list can be found in the
@uref{https://www.raspberrypi.org/documentation/hardware/raspberrypi/peripheral_addresses.md, official guide}.
@end deffn

@end deffn

@deffn {Interface Driver} {imx_gpio}
i.MX SoC is present in many community boards. Wandboard is an example
of the one which is most popular.

This driver is mostly the same as bcm2835gpio.

See @file{interface/imx-native.cfg} for a sample config and
pinout.

@end deffn


@deffn {Interface Driver} {linuxgpiod}
Linux provides userspace access to GPIO through libgpiod since Linux kernel version v4.6.
The driver emulates either JTAG and SWD transport through bitbanging.

See @file{interface/dln-2-gpiod.cfg} for a sample config.
@end deffn


@deffn {Interface Driver} {sysfsgpio}
Linux legacy userspace access to GPIO through sysfs is deprecated from Linux kernel version v5.3.
Prefer using @b{linuxgpiod}, instead.

See @file{interface/sysfsgpio-raspberrypi.cfg} for a sample config.
@end deffn


@deffn {Interface Driver} {openjtag}
OpenJTAG compatible USB adapter.
This defines some driver-specific commands:

@deffn {Config Command} {openjtag variant} variant
Specifies the variant of the OpenJTAG adapter (see @uref{http://www.openjtag.org/}).
Currently valid @var{variant} values include:

@itemize @minus
@item @b{standard} Standard variant (default).
@item @b{cy7c65215} Cypress CY7C65215 Dual Channel USB-Serial Bridge Controller
(see @uref{http://www.cypress.com/?rID=82870}).
@end itemize
@end deffn

@deffn {Config Command} {openjtag device_desc} string
The USB device description string of the adapter.
This value is only used with the standard variant.
@end deffn
@end deffn


@deffn {Interface Driver} {vdebug}
Cadence Virtual Debug Interface driver.

@deffn {Config Command} {vdebug server} host:port
Specifies the host and TCP port number where the vdebug server runs.
@end deffn

@deffn {Config Command} {vdebug batching} value
Specifies the batching method for the vdebug request. Possible values are
0 for no batching
1 or wr to batch write transactions together (default)
2 or rw to batch both read and write transactions
@end deffn

@deffn {Config Command} {vdebug polling} min max
Takes two values, representing the polling interval in ms. Lower values mean faster
debugger responsiveness, but lower emulation performance. The minimum should be
around 10, maximum should not exceed 1000, which is the default gdb and keepalive
timeout value.
@end deffn

@deffn {Config Command} {vdebug bfm_path} path clk_period
Specifies the hierarchical path and input clk period of the vdebug BFM in the design.
The hierarchical path uses Verilog notation top.inst.inst
The clock period must include the unit, for instance 40ns.
@end deffn

@deffn {Config Command} {vdebug mem_path} path base size
Specifies the hierarchical path to the design memory instance for backdoor access.
Up to 4 memories can be specified. The hierarchical path uses Verilog notation.
The base specifies start address in the design address space, size its size in bytes.
Both values can use hexadecimal notation with prefix 0x.
@end deffn
@end deffn

@deffn {Interface Driver} {jtag_dpi}
SystemVerilog Direct Programming Interface (DPI) compatible driver for
JTAG devices in emulation. The driver acts as a client for the SystemVerilog
DPI server interface.

@deffn {Config Command} {jtag_dpi set_port} port
Specifies the TCP/IP port number of the SystemVerilog DPI server interface.
@end deffn

@deffn {Config Command} {jtag_dpi set_address} address
Specifies the TCP/IP address of the SystemVerilog DPI server interface.
@end deffn
@end deffn


@deffn {Interface Driver} {buspirate}

This driver is for the Bus Pirate (see @url{http://dangerousprototypes.com/docs/Bus_Pirate}) and compatible devices.
It uses a simple data protocol over a serial port connection.

Most hardware development boards have a UART, a real serial port, or a virtual USB serial device, so this driver
allows you to start building your own JTAG adapter without the complexity of a custom USB connection.

@deffn {Config Command} {buspirate port} serial_port
Specify the serial port's filename. For example:
@example
buspirate port /dev/ttyUSB0
@end example
@end deffn

@deffn {Config Command} {buspirate speed} (normal|fast)
Set the communication speed to 115k (normal) or 1M (fast). For example:
@example
buspirate speed normal
@end example
@end deffn

@deffn {Config Command} {buspirate mode} (normal|open-drain)
Set the Bus Pirate output mode.
@itemize @minus
@item In normal mode (push/pull), do not enable the pull-ups, and do not connect I/O header pin VPU to JTAG VREF.
@item In open drain mode, you will then need to enable the pull-ups.
@end itemize
For example:
@example
buspirate mode normal
@end example
@end deffn

@deffn {Config Command} {buspirate pullup} (0|1)
Whether to connect (1) or not (0) the I/O header pin VPU (JTAG VREF)
to the pull-up/pull-down resistors on MOSI (JTAG TDI), CLK (JTAG TCK), MISO (JTAG TDO) and CS (JTAG TMS).
For example:
@example
buspirate pullup 0
@end example
@end deffn

@deffn {Config Command} {buspirate vreg} (0|1)
Whether to enable (1) or disable (0) the built-in voltage regulator,
which can be used to supply power to a test circuit through
I/O header pins +3V3 and +5V. For example:
@example
buspirate vreg 0
@end example
@end deffn

@deffn {Command} {buspirate led} (0|1)
Turns the Bus Pirate's LED on (1) or off (0). For example:
@end deffn
@example
buspirate led 1
@end example

@end deffn


@section Transport Configuration
@cindex Transport
As noted earlier, depending on the version of OpenOCD you use,
and the debug adapter you are using,
several transports may be available to
communicate with debug targets (or perhaps to program flash memory).
@deffn {Command} {transport list}
displays the names of the transports supported by this
version of OpenOCD.
@end deffn

@deffn {Command} {transport select} @option{transport_name}
Select which of the supported transports to use in this OpenOCD session.

When invoked with @option{transport_name}, attempts to select the named
transport.  The transport must be supported by the debug adapter
hardware and by the version of OpenOCD you are using (including the
adapter's driver).

If no transport has been selected and no @option{transport_name} is
provided, @command{transport select} auto-selects the first transport
supported by the debug adapter.

@command{transport select} always returns the name of the session's selected
transport, if any.
@end deffn

@subsection JTAG Transport
@cindex JTAG
JTAG is the original transport supported by OpenOCD, and most
of the OpenOCD commands support it.
JTAG transports expose a chain of one or more Test Access Points (TAPs),
each of which must be explicitly declared.
JTAG supports both debugging and boundary scan testing.
Flash programming support is built on top of debug support.

JTAG transport is selected with the command @command{transport select
jtag}. Unless your adapter uses either @ref{hla_interface,the hla interface
driver} (in which case the command is @command{transport select hla_jtag})
or @ref{st_link_dap_interface,the st-link interface driver} (in which case
the command is @command{transport select dapdirect_jtag}).

@subsection SWD Transport
@cindex SWD
@cindex Serial Wire Debug
SWD (Serial Wire Debug) is an ARM-specific transport which exposes one
Debug Access Point (DAP, which must be explicitly declared.
(SWD uses fewer signal wires than JTAG.)
SWD is debug-oriented, and does not support boundary scan testing.
Flash programming support is built on top of debug support.
(Some processors support both JTAG and SWD.)

SWD transport is selected with the command @command{transport select
swd}. Unless your adapter uses either @ref{hla_interface,the hla interface
driver} (in which case the command is @command{transport select hla_swd})
or @ref{st_link_dap_interface,the st-link interface driver} (in which case
the command is @command{transport select dapdirect_swd}).

@deffn {Config Command} {swd newdap} ...
Declares a single DAP which uses SWD transport.
Parameters are currently the same as "jtag newtap" but this is
expected to change.
@end deffn

@cindex SWD multi-drop
The newer SWD devices (SW-DP v2 or SWJ-DP v2) support the multi-drop extension
of SWD protocol: two or more devices can be connected to one SWD adapter.
SWD transport works in multi-drop mode if @ref{dap_create,DAP} is configured
with both @code{-dp-id} and @code{-instance-id} parameters regardless how many
DAPs are created.

Not all adapters and adapter drivers support SWD multi-drop. Only the following
adapter drivers are SWD multi-drop capable:
cmsis_dap (use an adapter with CMSIS-DAP version 2.0), ftdi, all bitbang based.

@subsection SPI Transport
@cindex SPI
@cindex Serial Peripheral Interface
The Serial Peripheral Interface (SPI) is a general purpose transport
which uses four wire signaling. Some processors use it as part of a
solution for flash programming.

@anchor{swimtransport}
@subsection SWIM Transport
@cindex SWIM
@cindex Single Wire Interface Module
The Single Wire Interface Module (SWIM) is a low-pin-count debug protocol used
by the STMicroelectronics MCU family STM8 and documented in the
@uref{https://www.st.com/resource/en/user_manual/cd00173911.pdf, User Manual UM470}.

SWIM does not support boundary scan testing nor multiple cores.

The SWIM transport is selected with the command @command{transport select swim}.

The concept of TAPs does not fit in the protocol since SWIM does not implement
a scan chain. Nevertheless, the current SW model of OpenOCD requires defining a
virtual SWIM TAP through the command @command{swim newtap basename tap_type}.
The TAP definition must precede the target definition command
@command{target create target_name stm8 -chain-position basename.tap_type}.

@anchor{jtagspeed}
@section JTAG Speed
JTAG clock setup is part of system setup.
It @emph{does not belong with interface setup} since any interface
only knows a few of the constraints for the JTAG clock speed.
Sometimes the JTAG speed is
changed during the target initialization process: (1) slow at
reset, (2) program the CPU clocks, (3) run fast.
Both the "slow" and "fast" clock rates are functions of the
oscillators used, the chip, the board design, and sometimes
power management software that may be active.

The speed used during reset, and the scan chain verification which
follows reset, can be adjusted using a @code{reset-start}
target event handler.
It can then be reconfigured to a faster speed by a
@code{reset-init} target event handler after it reprograms those
CPU clocks, or manually (if something else, such as a boot loader,
sets up those clocks).
@xref{targetevents,,Target Events}.
When the initial low JTAG speed is a chip characteristic, perhaps
because of a required oscillator speed, provide such a handler
in the target config file.
When that speed is a function of a board-specific characteristic
such as which speed oscillator is used, it belongs in the board
config file instead.
In both cases it's safest to also set the initial JTAG clock rate
to that same slow speed, so that OpenOCD never starts up using a
clock speed that's faster than the scan chain can support.

@example
jtag_rclk 3000
$_TARGET.cpu configure -event reset-start @{ jtag_rclk 3000 @}
@end example

If your system supports adaptive clocking (RTCK), configuring
JTAG to use that is probably the most robust approach.
However, it introduces delays to synchronize clocks; so it
may not be the fastest solution.

@b{NOTE:} Script writers should consider using @command{jtag_rclk}
instead of @command{adapter speed}, but only for (ARM) cores and boards
which support adaptive clocking.

@deffn {Command} {adapter speed} max_speed_kHz
A non-zero speed is in KHZ. Hence: 3000 is 3mhz.
JTAG interfaces usually support a limited number of
speeds. The speed actually used won't be faster
than the speed specified.

Chip data sheets generally include a top JTAG clock rate.
The actual rate is often a function of a CPU core clock,
and is normally less than that peak rate.
For example, most ARM cores accept at most one sixth of the CPU clock.

Speed 0 (khz) selects RTCK method.
@xref{faqrtck,,FAQ RTCK}.
If your system uses RTCK, you won't need to change the
JTAG clocking after setup.
Not all interfaces, boards, or targets support ``rtck''.
If the interface device can not
support it, an error is returned when you try to use RTCK.
@end deffn

@defun jtag_rclk fallback_speed_kHz
@cindex adaptive clocking
@cindex RTCK
This Tcl proc (defined in @file{startup.tcl}) attempts to enable RTCK/RCLK.
If that fails (maybe the interface, board, or target doesn't
support it), falls back to the specified frequency.
@example
# Fall back to 3mhz if RTCK is not supported
jtag_rclk 3000
@end example
@end defun

@node Reset Configuration
@chapter Reset Configuration
@cindex Reset Configuration

Every system configuration may require a different reset
configuration. This can also be quite confusing.
Resets also interact with @var{reset-init} event handlers,
which do things like setting up clocks and DRAM, and
JTAG clock rates. (@xref{jtagspeed,,JTAG Speed}.)
They can also interact with JTAG routers.
Please see the various board files for examples.

@quotation Note
To maintainers and integrators:
Reset configuration touches several things at once.
Normally the board configuration file
should define it and assume that the JTAG adapter supports
everything that's wired up to the board's JTAG connector.

However, the target configuration file could also make note
of something the silicon vendor has done inside the chip,
which will be true for most (or all) boards using that chip.
And when the JTAG adapter doesn't support everything, the
user configuration file will need to override parts of
the reset configuration provided by other files.
@end quotation

@section Types of Reset

There are many kinds of reset possible through JTAG, but
they may not all work with a given board and adapter.
That's part of why reset configuration can be error prone.

@itemize @bullet
@item
@emph{System Reset} ... the @emph{SRST} hardware signal
resets all chips connected to the JTAG adapter, such as processors,
power management chips, and I/O controllers. Normally resets triggered
with this signal behave exactly like pressing a RESET button.
@item
@emph{JTAG TAP Reset} ... the @emph{TRST} hardware signal resets
just the TAP controllers connected to the JTAG adapter.
Such resets should not be visible to the rest of the system; resetting a
device's TAP controller just puts that controller into a known state.
@item
@emph{Emulation Reset} ... many devices can be reset through JTAG
commands. These resets are often distinguishable from system
resets, either explicitly (a "reset reason" register says so)
or implicitly (not all parts of the chip get reset).
@item
@emph{Other Resets} ... system-on-chip devices often support
several other types of reset.
You may need to arrange that a watchdog timer stops
while debugging, preventing a watchdog reset.
There may be individual module resets.
@end itemize

In the best case, OpenOCD can hold SRST, then reset
the TAPs via TRST and send commands through JTAG to halt the
CPU at the reset vector before the 1st instruction is executed.
Then when it finally releases the SRST signal, the system is
halted under debugger control before any code has executed.
This is the behavior required to support the @command{reset halt}
and @command{reset init} commands; after @command{reset init} a
board-specific script might do things like setting up DRAM.
(@xref{resetcommand,,Reset Command}.)

@anchor{srstandtrstissues}
@section SRST and TRST Issues

Because SRST and TRST are hardware signals, they can have a
variety of system-specific constraints. Some of the most
common issues are:

@itemize @bullet

@item @emph{Signal not available} ... Some boards don't wire
SRST or TRST to the JTAG connector. Some JTAG adapters don't
support such signals even if they are wired up.
Use the @command{reset_config} @var{signals} options to say
when either of those signals is not connected.
When SRST is not available, your code might not be able to rely
on controllers having been fully reset during code startup.
Missing TRST is not a problem, since JTAG-level resets can
be triggered using with TMS signaling.

@item @emph{Signals shorted} ... Sometimes a chip, board, or
adapter will connect SRST to TRST, instead of keeping them separate.
Use the @command{reset_config} @var{combination} options to say
when those signals aren't properly independent.

@item @emph{Timing} ... Reset circuitry like a resistor/capacitor
delay circuit, reset supervisor, or on-chip features can extend
the effect of a JTAG adapter's reset for some time after the adapter
stops issuing the reset. For example, there may be chip or board
requirements that all reset pulses last for at least a
certain amount of time; and reset buttons commonly have
hardware debouncing.
Use the @command{adapter srst delay} and @command{jtag_ntrst_delay}
commands to say when extra delays are needed.

@item @emph{Drive type} ... Reset lines often have a pullup
resistor, letting the JTAG interface treat them as open-drain
signals. But that's not a requirement, so the adapter may need
to use push/pull output drivers.
Also, with weak pullups it may be advisable to drive
signals to both levels (push/pull) to minimize rise times.
Use the @command{reset_config} @var{trst_type} and
@var{srst_type} parameters to say how to drive reset signals.

@item @emph{Special initialization} ... Targets sometimes need
special JTAG initialization sequences to handle chip-specific
issues (not limited to errata).
For example, certain JTAG commands might need to be issued while
the system as a whole is in a reset state (SRST active)
but the JTAG scan chain is usable (TRST inactive).
Many systems treat combined assertion of SRST and TRST as a
trigger for a harder reset than SRST alone.
Such custom reset handling is discussed later in this chapter.
@end itemize

There can also be other issues.
Some devices don't fully conform to the JTAG specifications.
Trivial system-specific differences are common, such as
SRST and TRST using slightly different names.
There are also vendors who distribute key JTAG documentation for
their chips only to developers who have signed a Non-Disclosure
Agreement (NDA).

Sometimes there are chip-specific extensions like a requirement to use
the normally-optional TRST signal (precluding use of JTAG adapters which
don't pass TRST through), or needing extra steps to complete a TAP reset.

In short, SRST and especially TRST handling may be very finicky,
needing to cope with both architecture and board specific constraints.

@section Commands for Handling Resets

@deffn {Command} {adapter srst pulse_width} milliseconds
Minimum amount of time (in milliseconds) OpenOCD should wait
after asserting nSRST (active-low system reset) before
allowing it to be deasserted.
@end deffn

@deffn {Command} {adapter srst delay} milliseconds
How long (in milliseconds) OpenOCD should wait after deasserting
nSRST (active-low system reset) before starting new JTAG operations.
When a board has a reset button connected to SRST line it will
probably have hardware debouncing, implying you should use this.
@end deffn

@deffn {Command} {jtag_ntrst_assert_width} milliseconds
Minimum amount of time (in milliseconds) OpenOCD should wait
after asserting nTRST (active-low JTAG TAP reset) before
allowing it to be deasserted.
@end deffn

@deffn {Command} {jtag_ntrst_delay} milliseconds
How long (in milliseconds) OpenOCD should wait after deasserting
nTRST (active-low JTAG TAP reset) before starting new JTAG operations.
@end deffn

@anchor{reset_config}
@deffn {Command} {reset_config} mode_flag ...
This command displays or modifies the reset configuration
of your combination of JTAG board and target in target
configuration scripts.

Information earlier in this section describes the kind of problems
the command is intended to address (@pxref{srstandtrstissues,,SRST and TRST Issues}).
As a rule this command belongs only in board config files,
describing issues like @emph{board doesn't connect TRST};
or in user config files, addressing limitations derived
from a particular combination of interface and board.
(An unlikely example would be using a TRST-only adapter
with a board that only wires up SRST.)

The @var{mode_flag} options can be specified in any order, but only one
of each type -- @var{signals}, @var{combination}, @var{gates},
@var{trst_type}, @var{srst_type} and @var{connect_type}
-- may be specified at a time.
If you don't provide a new value for a given type, its previous
value (perhaps the default) is unchanged.
For example, this means that you don't need to say anything at all about
TRST just to declare that if the JTAG adapter should want to drive SRST,
it must explicitly be driven high (@option{srst_push_pull}).

@itemize
@item
@var{signals} can specify which of the reset signals are connected.
For example, If the JTAG interface provides SRST, but the board doesn't
connect that signal properly, then OpenOCD can't use it.
Possible values are @option{none} (the default), @option{trst_only},
@option{srst_only} and @option{trst_and_srst}.

@quotation Tip
If your board provides SRST and/or TRST through the JTAG connector,
you must declare that so those signals can be used.
@end quotation

@item
The @var{combination} is an optional value specifying broken reset
signal implementations.
The default behaviour if no option given is @option{separate},
indicating everything behaves normally.
@option{srst_pulls_trst} states that the
test logic is reset together with the reset of the system (e.g. NXP
LPC2000, "broken" board layout), @option{trst_pulls_srst} says that
the system is reset together with the test logic (only hypothetical, I
haven't seen hardware with such a bug, and can be worked around).
@option{combined} implies both @option{srst_pulls_trst} and
@option{trst_pulls_srst}.

@item
The @var{gates} tokens control flags that describe some cases where
JTAG may be unavailable during reset.
@option{srst_gates_jtag} (default)
indicates that asserting SRST gates the
JTAG clock. This means that no communication can happen on JTAG
while SRST is asserted.
Its converse is @option{srst_nogate}, indicating that JTAG commands
can safely be issued while SRST is active.

@item
The @var{connect_type} tokens control flags that describe some cases where
SRST is asserted while connecting to the target. @option{srst_nogate}
is required to use this option.
@option{connect_deassert_srst} (default)
indicates that SRST will not be asserted while connecting to the target.
Its converse is @option{connect_assert_srst}, indicating that SRST will
be asserted before any target connection.
Only some targets support this feature, STM32 and STR9 are examples.
This feature is useful if you are unable to connect to your target due
to incorrect options byte config or illegal program execution.
@end itemize

The optional @var{trst_type} and @var{srst_type} parameters allow the
driver mode of each reset line to be specified. These values only affect
JTAG interfaces with support for different driver modes, like the Amontec
JTAGkey and JTAG Accelerator. Also, they are necessarily ignored if the
relevant signal (TRST or SRST) is not connected.

@itemize
@item
Possible @var{trst_type} driver modes for the test reset signal (TRST)
are the default @option{trst_push_pull}, and @option{trst_open_drain}.
Most boards connect this signal to a pulldown, so the JTAG TAPs
never leave reset unless they are hooked up to a JTAG adapter.

@item
Possible @var{srst_type} driver modes for the system reset signal (SRST)
are the default @option{srst_open_drain}, and @option{srst_push_pull}.
Most boards connect this signal to a pullup, and allow the
signal to be pulled low by various events including system
power-up and pressing a reset button.
@end itemize
@end deffn

@section Custom Reset Handling
@cindex events

OpenOCD has several ways to help support the various reset
mechanisms provided by chip and board vendors.
The commands shown in the previous section give standard parameters.
There are also @emph{event handlers} associated with TAPs or Targets.
Those handlers are Tcl procedures you can provide, which are invoked
at particular points in the reset sequence.

@emph{When SRST is not an option} you must set
up a @code{reset-assert} event handler for your target.
For example, some JTAG adapters don't include the SRST signal;
and some boards have multiple targets, and you won't always
want to reset everything at once.

After configuring those mechanisms, you might still
find your board doesn't start up or reset correctly.
For example, maybe it needs a slightly different sequence
of SRST and/or TRST manipulations, because of quirks that
the @command{reset_config} mechanism doesn't address;
or asserting both might trigger a stronger reset, which
needs special attention.

Experiment with lower level operations, such as
@command{adapter assert}, @command{adapter deassert}
and the @command{jtag arp_*} operations shown here,
to find a sequence of operations that works.
@xref{JTAG Commands}.
When you find a working sequence, it can be used to override
@command{jtag_init}, which fires during OpenOCD startup
(@pxref{configurationstage,,Configuration Stage});
or @command{init_reset}, which fires during reset processing.

You might also want to provide some project-specific reset
schemes. For example, on a multi-target board the standard
@command{reset} command would reset all targets, but you
may need the ability to reset only one target at time and
thus want to avoid using the board-wide SRST signal.

@deffn {Overridable Procedure} {init_reset} mode
This is invoked near the beginning of the @command{reset} command,
usually to provide as much of a cold (power-up) reset as practical.
By default it is also invoked from @command{jtag_init} if
the scan chain does not respond to pure JTAG operations.
The @var{mode} parameter is the parameter given to the
low level reset command (@option{halt},
@option{init}, or @option{run}), @option{setup},
or potentially some other value.

The default implementation just invokes @command{jtag arp_init-reset}.
Replacements will normally build on low level JTAG
operations such as @command{adapter assert} and @command{adapter deassert}.
Operations here must not address individual TAPs
(or their associated targets)
until the JTAG scan chain has first been verified to work.

Implementations must have verified the JTAG scan chain before
they return.
This is done by calling @command{jtag arp_init}
(or @command{jtag arp_init-reset}).
@end deffn

@deffn {Command} {jtag arp_init}
This validates the scan chain using just the four
standard JTAG signals (TMS, TCK, TDI, TDO).
It starts by issuing a JTAG-only reset.
Then it performs checks to verify that the scan chain configuration
matches the TAPs it can observe.
Those checks include checking IDCODE values for each active TAP,
and verifying the length of their instruction registers using
TAP @code{-ircapture} and @code{-irmask} values.
If these tests all pass, TAP @code{setup} events are
issued to all TAPs with handlers for that event.
@end deffn

@deffn {Command} {jtag arp_init-reset}
This uses TRST and SRST to try resetting
everything on the JTAG scan chain
(and anything else connected to SRST).
It then invokes the logic of @command{jtag arp_init}.
@end deffn


@node TAP Declaration
@chapter TAP Declaration
@cindex TAP declaration
@cindex TAP configuration

@emph{Test Access Ports} (TAPs) are the core of JTAG.
TAPs serve many roles, including:

@itemize @bullet
@item @b{Debug Target} A CPU TAP can be used as a GDB debug target.
@item @b{Flash Programming} Some chips program the flash directly via JTAG.
Others do it indirectly, making a CPU do it.
@item @b{Program Download} Using the same CPU support GDB uses,
you can initialize a DRAM controller, download code to DRAM, and then
start running that code.
@item @b{Boundary Scan} Most chips support boundary scan, which
helps test for board assembly problems like solder bridges
and missing connections.
@end itemize

OpenOCD must know about the active TAPs on your board(s).
Setting up the TAPs is the core task of your configuration files.
Once those TAPs are set up, you can pass their names to code
which sets up CPUs and exports them as GDB targets,
probes flash memory, performs low-level JTAG operations, and more.

@section Scan Chains
@cindex scan chain

TAPs are part of a hardware @dfn{scan chain},
which is a daisy chain of TAPs.
They also need to be added to
OpenOCD's software mirror of that hardware list,
giving each member a name and associating other data with it.
Simple scan chains, with a single TAP, are common in
systems with a single microcontroller or microprocessor.
More complex chips may have several TAPs internally.
Very complex scan chains might have a dozen or more TAPs:
several in one chip, more in the next, and connecting
to other boards with their own chips and TAPs.

You can display the list with the @command{scan_chain} command.
(Don't confuse this with the list displayed by the @command{targets}
command, presented in the next chapter.
That only displays TAPs for CPUs which are configured as
debugging targets.)
Here's what the scan chain might look like for a chip more than one TAP:

@verbatim
   TapName            Enabled IdCode     Expected   IrLen IrCap IrMask
-- ------------------ ------- ---------- ---------- ----- ----- ------
 0 omap5912.dsp          Y    0x03df1d81 0x03df1d81    38 0x01  0x03
 1 omap5912.arm          Y    0x0692602f 0x0692602f     4 0x01  0x0f
 2 omap5912.unknown      Y    0x00000000 0x00000000     8 0x01  0x03
@end verbatim

OpenOCD can detect some of that information, but not all
of it. @xref{autoprobing,,Autoprobing}.
Unfortunately, those TAPs can't always be autoconfigured,
because not all devices provide good support for that.
JTAG doesn't require supporting IDCODE instructions, and
chips with JTAG routers may not link TAPs into the chain
until they are told to do so.

The configuration mechanism currently supported by OpenOCD
requires explicit configuration of all TAP devices using
@command{jtag newtap} commands, as detailed later in this chapter.
A command like this would declare one tap and name it @code{chip1.cpu}:

@example
jtag newtap chip1 cpu -irlen 4 -expected-id 0x3ba00477
@end example

Each target configuration file lists the TAPs provided
by a given chip.
Board configuration files combine all the targets on a board,
and so forth.
Note that @emph{the order in which TAPs are declared is very important.}
That declaration order must match the order in the JTAG scan chain,
both inside a single chip and between them.
@xref{faqtaporder,,FAQ TAP Order}.

For example, the STMicroelectronics STR912 chip has
three separate TAPs@footnote{See the ST
document titled: @emph{STR91xFAxxx, Section 3.15 Jtag Interface, Page:
28/102, Figure 3: JTAG chaining inside the STR91xFA}.
@url{http://eu.st.com/stonline/products/literature/ds/13495.pdf}}.
To configure those taps, @file{target/str912.cfg}
includes commands something like this:

@example
jtag newtap str912 flash ... params ...
jtag newtap str912 cpu ... params ...
jtag newtap str912 bs ... params ...
@end example

Actual config files typically use a variable such as @code{$_CHIPNAME}
instead of literals like @option{str912}, to support more than one chip
of each type.  @xref{Config File Guidelines}.

@deffn {Command} {jtag names}
Returns the names of all current TAPs in the scan chain.
Use @command{jtag cget} or @command{jtag tapisenabled}
to examine attributes and state of each TAP.
@example
foreach t [jtag names] @{
    puts [format "TAP: %s\n" $t]
@}
@end example
@end deffn

@deffn {Command} {scan_chain}
Displays the TAPs in the scan chain configuration,
and their status.
The set of TAPs listed by this command is fixed by
exiting the OpenOCD configuration stage,
but systems with a JTAG router can
enable or disable TAPs dynamically.
@end deffn

@c FIXME! "jtag cget" should be able to return all TAP
@c attributes, like "$target_name cget" does for targets.

@c Probably want "jtag eventlist", and a "tap-reset" event
@c (on entry to RESET state).

@section TAP Names
@cindex dotted name

When TAP objects are declared with @command{jtag newtap},
a @dfn{dotted.name} is created for the TAP, combining the
name of a module (usually a chip) and a label for the TAP.
For example: @code{xilinx.tap}, @code{str912.flash},
@code{omap3530.jrc}, @code{dm6446.dsp}, or @code{stm32.cpu}.
Many other commands use that dotted.name to manipulate or
refer to the TAP. For example, CPU configuration uses the
name, as does declaration of NAND or NOR flash banks.

The components of a dotted name should follow ``C'' symbol
name rules: start with an alphabetic character, then numbers
and underscores are OK; while others (including dots!) are not.

@section TAP Declaration Commands

@deffn {Config Command} {jtag newtap} chipname tapname configparams...
Declares a new TAP with the dotted name @var{chipname}.@var{tapname},
and configured according to the various @var{configparams}.

The @var{chipname} is a symbolic name for the chip.
Conventionally target config files use @code{$_CHIPNAME},
defaulting to the model name given by the chip vendor but
overridable.

@cindex TAP naming convention
The @var{tapname} reflects the role of that TAP,
and should follow this convention:

@itemize @bullet
@item @code{bs} -- For boundary scan if this is a separate TAP;
@item @code{cpu} -- The main CPU of the chip, alternatively
@code{arm} and @code{dsp} on chips with both ARM and DSP CPUs,
@code{arm1} and @code{arm2} on chips with two ARMs, and so forth;
@item @code{etb} -- For an embedded trace buffer (example: an ARM ETB11);
@item @code{flash} -- If the chip has a flash TAP, like the str912;
@item @code{jrc} -- For JTAG route controller (example: the ICEPick modules
on many Texas Instruments chips, like the OMAP3530 on Beagleboards);
@item @code{tap} -- Should be used only for FPGA- or CPLD-like devices
with a single TAP;
@item @code{unknownN} -- If you have no idea what the TAP is for (N is a number);
@item @emph{when in doubt} -- Use the chip maker's name in their data sheet.
For example, the Freescale i.MX31 has a SDMA (Smart DMA) with
a JTAG TAP; that TAP should be named @code{sdma}.
@end itemize

Every TAP requires at least the following @var{configparams}:

@itemize @bullet
@item @code{-irlen} @var{NUMBER}
@*The length in bits of the
instruction register, such as 4 or 5 bits.
@end itemize

A TAP may also provide optional @var{configparams}:

@itemize @bullet
@item @code{-disable} (or @code{-enable})
@*Use the @code{-disable} parameter to flag a TAP which is not
linked into the scan chain after a reset using either TRST
or the JTAG state machine's @sc{reset} state.
You may use @code{-enable} to highlight the default state
(the TAP is linked in).
@xref{enablinganddisablingtaps,,Enabling and Disabling TAPs}.
@item @code{-expected-id} @var{NUMBER}
@*A non-zero @var{number} represents a 32-bit IDCODE
which you expect to find when the scan chain is examined.
These codes are not required by all JTAG devices.
@emph{Repeat the option} as many times as required if more than one
ID code could appear (for example, multiple versions).
Specify @var{number} as zero to suppress warnings about IDCODE
values that were found but not included in the list.

Provide this value if at all possible, since it lets OpenOCD
tell when the scan chain it sees isn't right. These values
are provided in vendors' chip documentation, usually a technical
reference manual. Sometimes you may need to probe the JTAG
hardware to find these values.
@xref{autoprobing,,Autoprobing}.
@item @code{-ignore-version}
@*Specify this to ignore the JTAG version field in the @code{-expected-id}
option. When vendors put out multiple versions of a chip, or use the same
JTAG-level ID for several largely-compatible chips, it may be more practical
to ignore the version field than to update config files to handle all of
the various chip IDs. The version field is defined as bit 28-31 of the IDCODE.
@item @code{-ignore-bypass}
@*Specify this to ignore the 'bypass' bit of the idcode. Some vendor put
an invalid idcode regarding this bit. Specify this to ignore this bit and
to not consider this tap in bypass mode.
@item @code{-ircapture} @var{NUMBER}
@*The bit pattern loaded by the TAP into the JTAG shift register
on entry to the @sc{ircapture} state, such as 0x01.
JTAG requires the two LSBs of this value to be 01.
By default, @code{-ircapture} and @code{-irmask} are set
up to verify that two-bit value. You may provide
additional bits if you know them, or indicate that
a TAP doesn't conform to the JTAG specification.
@item @code{-irmask} @var{NUMBER}
@*A mask used with @code{-ircapture}
to verify that instruction scans work correctly.
Such scans are not used by OpenOCD except to verify that
there seems to be no problems with JTAG scan chain operations.
@item @code{-ignore-syspwrupack}
@*Specify this to ignore the CSYSPWRUPACK bit in the ARM DAP DP CTRL/STAT
register during initial examination and when checking the sticky error bit.
This bit is normally checked after setting the CSYSPWRUPREQ bit, but some
devices do not set the ack bit until sometime later.
@end itemize
@end deffn

@section Other TAP commands

@deffn {Command} {jtag cget} dotted.name @option{-idcode}
Get the value of the IDCODE found in hardware.
@end deffn

@deffn {Command} {jtag cget} dotted.name @option{-event} event_name
@deffnx {Command} {jtag configure} dotted.name @option{-event} event_name handler
At this writing this TAP attribute
mechanism is limited and used mostly for event handling.
(It is not a direct analogue of the @code{cget}/@code{configure}
mechanism for debugger targets.)
See the next section for information about the available events.

The @code{configure} subcommand assigns an event handler,
a TCL string which is evaluated when the event is triggered.
The @code{cget} subcommand returns that handler.
@end deffn

@section TAP Events
@cindex events
@cindex TAP events

OpenOCD includes two event mechanisms.
The one presented here applies to all JTAG TAPs.
The other applies to debugger targets,
which are associated with certain TAPs.

The TAP events currently defined are:

@itemize @bullet
@item @b{post-reset}
@* The TAP has just completed a JTAG reset.
The tap may still be in the JTAG @sc{reset} state.
Handlers for these events might perform initialization sequences
such as issuing TCK cycles, TMS sequences to ensure
exit from the ARM SWD mode, and more.

Because the scan chain has not yet been verified, handlers for these events
@emph{should not issue commands which scan the JTAG IR or DR registers}
of any particular target.
@b{NOTE:} As this is written (September 2009), nothing prevents such access.
@item @b{setup}
@* The scan chain has been reset and verified.
This handler may enable TAPs as needed.
@item @b{tap-disable}
@* The TAP needs to be disabled. This handler should
implement @command{jtag tapdisable}
by issuing the relevant JTAG commands.
@item @b{tap-enable}
@* The TAP needs to be enabled. This handler should
implement @command{jtag tapenable}
by issuing the relevant JTAG commands.
@end itemize

If you need some action after each JTAG reset which isn't actually
specific to any TAP (since you can't yet trust the scan chain's
contents to be accurate), you might:

@example
jtag configure CHIP.jrc -event post-reset @{
  echo "JTAG Reset done"
  ... non-scan jtag operations to be done after reset
@}
@end example


@anchor{enablinganddisablingtaps}
@section Enabling and Disabling TAPs
@cindex JTAG Route Controller
@cindex jrc

In some systems, a @dfn{JTAG Route Controller} (JRC)
is used to enable and/or disable specific JTAG TAPs.
Many ARM-based chips from Texas Instruments include
an ``ICEPick'' module, which is a JRC.
Such chips include DaVinci and OMAP3 processors.

A given TAP may not be visible until the JRC has been
told to link it into the scan chain; and if the JRC
has been told to unlink that TAP, it will no longer
be visible.
Such routers address problems that JTAG ``bypass mode''
ignores, such as:

@itemize
@item The scan chain can only go as fast as its slowest TAP.
@item Having many TAPs slows instruction scans, since all
TAPs receive new instructions.
@item TAPs in the scan chain must be powered up, which wastes
power and prevents debugging some power management mechanisms.
@end itemize

The IEEE 1149.1 JTAG standard has no concept of a ``disabled'' tap,
as implied by the existence of JTAG routers.
However, the upcoming IEEE 1149.7 framework (layered on top of JTAG)
does include a kind of JTAG router functionality.

@c (a) currently the event handlers don't seem to be able to
@c     fail in a way that could lead to no-change-of-state.

In OpenOCD, tap enabling/disabling is invoked by the Tcl commands
shown below, and is implemented using TAP event handlers.
So for example, when defining a TAP for a CPU connected to
a JTAG router, your @file{target.cfg} file
should define TAP event handlers using
code that looks something like this:

@example
jtag configure CHIP.cpu -event tap-enable @{
  ... jtag operations using CHIP.jrc
@}
jtag configure CHIP.cpu -event tap-disable @{
  ... jtag operations using CHIP.jrc
@}
@end example

Then you might want that CPU's TAP enabled almost all the time:

@example
jtag configure $CHIP.jrc -event setup "jtag tapenable $CHIP.cpu"
@end example

Note how that particular setup event handler declaration
uses quotes to evaluate @code{$CHIP} when the event is configured.
Using brackets @{ @} would cause it to be evaluated later,
at runtime, when it might have a different value.

@deffn {Command} {jtag tapdisable} dotted.name
If necessary, disables the tap
by sending it a @option{tap-disable} event.
Returns the string "1" if the tap
specified by @var{dotted.name} is enabled,
and "0" if it is disabled.
@end deffn

@deffn {Command} {jtag tapenable} dotted.name
If necessary, enables the tap
by sending it a @option{tap-enable} event.
Returns the string "1" if the tap
specified by @var{dotted.name} is enabled,
and "0" if it is disabled.
@end deffn

@deffn {Command} {jtag tapisenabled} dotted.name
Returns the string "1" if the tap
specified by @var{dotted.name} is enabled,
and "0" if it is disabled.

@quotation Note
Humans will find the @command{scan_chain} command more helpful
for querying the state of the JTAG taps.
@end quotation
@end deffn

@anchor{autoprobing}
@section Autoprobing
@cindex autoprobe
@cindex JTAG autoprobe

TAP configuration is the first thing that needs to be done
after interface and reset configuration. Sometimes it's
hard finding out what TAPs exist, or how they are identified.
Vendor documentation is not always easy to find and use.

To help you get past such problems, OpenOCD has a limited
@emph{autoprobing} ability to look at the scan chain, doing
a @dfn{blind interrogation} and then reporting the TAPs it finds.
To use this mechanism, start the OpenOCD server with only data
that configures your JTAG interface, and arranges to come up
with a slow clock (many devices don't support fast JTAG clocks
right when they come out of reset).

For example, your @file{openocd.cfg} file might have:

@example
source [find interface/olimex-arm-usb-tiny-h.cfg]
reset_config trst_and_srst
jtag_rclk 8
@end example

When you start the server without any TAPs configured, it will
attempt to autoconfigure the TAPs. There are two parts to this:

@enumerate
@item @emph{TAP discovery} ...
After a JTAG reset (sometimes a system reset may be needed too),
each TAP's data registers will hold the contents of either the
IDCODE or BYPASS register.
If JTAG communication is working, OpenOCD will see each TAP,
and report what @option{-expected-id} to use with it.
@item @emph{IR Length discovery} ...
Unfortunately JTAG does not provide a reliable way to find out
the value of the @option{-irlen} parameter to use with a TAP
that is discovered.
If OpenOCD can discover the length of a TAP's instruction
register, it will report it.
Otherwise you may need to consult vendor documentation, such
as chip data sheets or BSDL files.
@end enumerate

In many cases your board will have a simple scan chain with just
a single device. Here's what OpenOCD reported with one board
that's a bit more complex:

@example
clock speed 8 kHz
There are no enabled taps. AUTO PROBING MIGHT NOT WORK!!
AUTO auto0.tap - use "jtag newtap auto0 tap -expected-id 0x2b900f0f ..."
AUTO auto1.tap - use "jtag newtap auto1 tap -expected-id 0x07926001 ..."
AUTO auto2.tap - use "jtag newtap auto2 tap -expected-id 0x0b73b02f ..."
AUTO auto0.tap - use "... -irlen 4"
AUTO auto1.tap - use "... -irlen 4"
AUTO auto2.tap - use "... -irlen 6"
no gdb ports allocated as no target has been specified
@end example

Given that information, you should be able to either find some existing
config files to use, or create your own. If you create your own, you
would configure from the bottom up: first a @file{target.cfg} file
with these TAPs, any targets associated with them, and any on-chip
resources; then a @file{board.cfg} with off-chip resources, clocking,
and so forth.

@anchor{dapdeclaration}
@section DAP declaration (ARMv6-M, ARMv7 and ARMv8 targets)
@cindex DAP declaration

Since OpenOCD version 0.11.0, the Debug Access Port (DAP) is
no longer implicitly created together with the target. It must be
explicitly declared using the @command{dap create} command. For all ARMv6-M, ARMv7
and ARMv8 targets, the option "@option{-dap} @var{dap_name}" has to be used
instead of "@option{-chain-position} @var{dotted.name}" when the target is created.

The @command{dap} command group supports the following sub-commands:

@anchor{dap_create}
@deffn {Command} {dap create} dap_name @option{-chain-position} dotted.name configparams...
Declare a DAP instance named @var{dap_name} linked to the JTAG tap
@var{dotted.name}. This also creates a new command (@command{dap_name})
which is used for various purposes including additional configuration.
There can only be one DAP for each JTAG tap in the system.

A DAP may also provide optional @var{configparams}:

@itemize @bullet
@item @code{-ignore-syspwrupack}
@*Specify this to ignore the CSYSPWRUPACK bit in the ARM DAP DP CTRL/STAT
register during initial examination and when checking the sticky error bit.
This bit is normally checked after setting the CSYSPWRUPREQ bit, but some
devices do not set the ack bit until sometime later.

@item @code{-dp-id} @var{number}
@*Debug port identification number for SWD DPv2 multidrop.
The @var{number} is written to bits 0..27 of DP TARGETSEL during DP selection.
To find the id number of a single connected device read DP TARGETID:
@code{device.dap dpreg 0x24}
Use bits 0..27 of TARGETID.

@item @code{-instance-id} @var{number}
@*Instance identification number for SWD DPv2 multidrop.
The @var{number} is written to bits 28..31 of DP TARGETSEL during DP selection.
To find the instance number of a single connected device read DP DLPIDR:
@code{device.dap dpreg 0x34}
The instance number is in bits 28..31 of DLPIDR value.
@end itemize
@end deffn

@deffn {Command} {dap names}
This command returns a list of all registered DAP objects. It it useful mainly
for TCL scripting.
@end deffn

@deffn {Command} {dap info} [num]
Displays the ROM table for MEM-AP @var{num},
defaulting to the currently selected AP of the currently selected target.
@end deffn

@deffn {Command} {dap init}
Initialize all registered DAPs. This command is used internally
during initialization. It can be issued at any time after the
initialization, too.
@end deffn

The following commands exist as subcommands of DAP instances:

@deffn {Command} {$dap_name info} [num]
Displays the ROM table for MEM-AP @var{num},
defaulting to the currently selected AP.
@end deffn

@deffn {Command} {$dap_name apid} [num]
Displays ID register from AP @var{num}, defaulting to the currently selected AP.
@end deffn

@anchor{DAP subcommand apreg}
@deffn {Command} {$dap_name apreg} ap_num reg [value]
Displays content of a register @var{reg} from AP @var{ap_num}
or set a new value @var{value}.
@var{reg} is byte address of a word register, 0, 4, 8 ... 0xfc.
@end deffn

@deffn {Command} {$dap_name apsel} [num]
Select AP @var{num}, defaulting to 0.
@end deffn

@deffn {Command} {$dap_name dpreg} reg [value]
Displays the content of DP register at address @var{reg}, or set it to a new
value @var{value}.

In case of SWD, @var{reg} is a value in packed format
@math{dpbanksel << 4 | addr} and assumes values 0, 4, 8 ... 0xfc.
In case of JTAG it only assumes values 0, 4, 8 and 0xc.

@emph{Note:} Consider using @command{poll off} to avoid any disturbing
background activity by OpenOCD while you are operating at such low-level.
@end deffn

@deffn {Command} {$dap_name baseaddr} [num]
Displays debug base address from MEM-AP @var{num},
defaulting to the currently selected AP.
@end deffn

@deffn {Command} {$dap_name memaccess} [value]
Displays the number of extra tck cycles in the JTAG idle to use for MEM-AP
memory bus access [0-255], giving additional time to respond to reads.
If @var{value} is defined, first assigns that.
@end deffn

@deffn {Command} {$dap_name apcsw} [value [mask]]
Displays or changes CSW bit pattern for MEM-AP transfers.

At the begin of each memory access the CSW pattern is extended (bitwise or-ed)
by @dfn{Size} and @dfn{AddrInc} bit-fields according to transfer requirements
and the result is written to the real CSW register. All bits except dynamically
updated fields @dfn{Size} and @dfn{AddrInc} can be changed by changing
the CSW pattern. Refer to ARM ADI v5 manual chapter 7.6.4 and appendix A
for details.

Use @var{value} only syntax if you want to set the new CSW pattern as a whole.
The example sets HPROT1 bit (required by Cortex-M) and clears the rest of
the pattern:
@example
kx.dap apcsw 0x2000000
@end example

If @var{mask} is also used, the CSW pattern is changed only on bit positions
where the mask bit is 1. The following example sets HPROT3 (cacheable)
and leaves the rest of the pattern intact. It configures memory access through
DCache on Cortex-M7.
@example
set CSW_HPROT3_CACHEABLE [expr @{1 << 27@}]
samv.dap apcsw $CSW_HPROT3_CACHEABLE $CSW_HPROT3_CACHEABLE
@end example

Another example clears SPROT bit and leaves the rest of pattern intact:
@example
set CSW_SPROT [expr @{1 << 30@}]
samv.dap apcsw 0 $CSW_SPROT
@end example

@emph{Note:} If you want to check the real value of CSW, not CSW pattern, use
@code{xxx.dap apreg 0}. @xref{DAP subcommand apreg,,}.

@emph{Warning:} Some of the CSW bits are vital for working memory transfer.
If you set a wrong CSW pattern and MEM-AP stopped working, use the following
example with a proper dap name:
@example
xxx.dap apcsw default
@end example
@end deffn

@deffn {Config Command} {$dap_name ti_be_32_quirks} [@option{enable}]
Set/get quirks mode for TI TMS450/TMS570 processors
Disabled by default
@end deffn


@node CPU Configuration
@chapter CPU Configuration
@cindex GDB target

This chapter discusses how to set up GDB debug targets for CPUs.
You can also access these targets without GDB
(@pxref{Architecture and Core Commands},
and @ref{targetstatehandling,,Target State handling}) and
through various kinds of NAND and NOR flash commands.
If you have multiple CPUs you can have multiple such targets.

We'll start by looking at how to examine the targets you have,
then look at how to add one more target and how to configure it.

@section Target List
@cindex target, current
@cindex target, list

All targets that have been set up are part of a list,
where each member has a name.
That name should normally be the same as the TAP name.
You can display the list with the @command{targets}
(plural!) command.
This display often has only one CPU; here's what it might
look like with more than one:
@verbatim
    TargetName         Type       Endian TapName            State
--  ------------------ ---------- ------ ------------------ ------------
 0* at91rm9200.cpu     arm920t    little at91rm9200.cpu     running
 1  MyTarget           cortex_m   little mychip.foo         tap-disabled
@end verbatim

One member of that list is the @dfn{current target}, which
is implicitly referenced by many commands.
It's the one marked with a @code{*} near the target name.
In particular, memory addresses often refer to the address
space seen by that current target.
Commands like @command{mdw} (memory display words)
and @command{flash erase_address} (erase NOR flash blocks)
are examples; and there are many more.

Several commands let you examine the list of targets:

@deffn {Command} {target current}
Returns the name of the current target.
@end deffn

@deffn {Command} {target names}
Lists the names of all current targets in the list.
@example
foreach t [target names] @{
    puts [format "Target: %s\n" $t]
@}
@end example
@end deffn

@c yep, "target list" would have been better.
@c plus maybe "target setdefault".

@deffn {Command} {targets} [name]
@emph{Note: the name of this command is plural. Other target
command names are singular.}

With no parameter, this command displays a table of all known
targets in a user friendly form.

With a parameter, this command sets the current target to
the given target with the given @var{name}; this is
only relevant on boards which have more than one target.
@end deffn

@section Target CPU Types
@cindex target type
@cindex CPU type

Each target has a @dfn{CPU type}, as shown in the output of
the @command{targets} command. You need to specify that type
when calling @command{target create}.
The CPU type indicates more than just the instruction set.
It also indicates how that instruction set is implemented,
what kind of debug support it integrates,
whether it has an MMU (and if so, what kind),
what core-specific commands may be available
(@pxref{Architecture and Core Commands}),
and more.

It's easy to see what target types are supported,
since there's a command to list them.

@anchor{targettypes}
@deffn {Command} {target types}
Lists all supported target types.
At this writing, the supported CPU types are:

@itemize @bullet
@item @code{aarch64} -- this is an ARMv8-A core with an MMU.
@item @code{arm11} -- this is a generation of ARMv6 cores.
@item @code{arm720t} -- this is an ARMv4 core with an MMU.
@item @code{arm7tdmi} -- this is an ARMv4 core.
@item @code{arm920t} -- this is an ARMv4 core with an MMU.
@item @code{arm926ejs} -- this is an ARMv5 core with an MMU.
@item @code{arm946e} -- this is an ARMv5 core with an MMU.
@item @code{arm966e} -- this is an ARMv5 core.
@item @code{arm9tdmi} -- this is an ARMv4 core.
@item @code{avr} -- implements Atmel's 8-bit AVR instruction set.
(Support for this is preliminary and incomplete.)
@item @code{avr32_ap7k} -- this an AVR32 core.
@item @code{cortex_a} -- this is an ARMv7-A core with an MMU.
@item @code{cortex_m} -- this is an ARMv7-M core, supporting only the
compact Thumb2 instruction set. Supports also ARMv6-M and ARMv8-M cores
@item @code{cortex_r4} -- this is an ARMv7-R core.
@item @code{dragonite} -- resembles arm966e.
@item @code{dsp563xx} -- implements Freescale's 24-bit DSP.
(Support for this is still incomplete.)
@item @code{dsp5680xx} -- implements Freescale's 5680x DSP.
@item @code{esirisc} -- this is an EnSilica eSi-RISC core.
The current implementation supports eSi-32xx cores.
@item @code{esp32c2} -- this is an Espressif SoC with single RISC-V core.
@item @code{esp32c3} -- this is an Espressif SoC with single RISC-V core.
@item @code{fa526} -- resembles arm920 (w/o Thumb).
@item @code{feroceon} -- resembles arm926.
@item @code{hla_target} -- a Cortex-M alternative to work with HL adapters like ST-Link.
@item @code{ls1_sap} -- this is the SAP on NXP LS102x CPUs,
allowing access to physical memory addresses independently of CPU cores.
@item @code{mem_ap} -- this is an ARM debug infrastructure Access Port without
a CPU, through which bus read and write cycles can be generated; it may be
useful for working with non-CPU hardware behind an AP or during development of
support for new CPUs.
It's possible to connect a GDB client to this target (the GDB port has to be
specified, @xref{gdbportoverride,,option -gdb-port}.), and a fake ARM core will
be emulated to comply to GDB remote protocol.
@item @code{mips_m4k} -- a MIPS core.
@item @code{mips_mips64} -- a MIPS64 core.
@item @code{nds32_v2} -- this is an Andes NDS32 v2 core (deprecated; would be removed in v0.13.0).
@item @code{nds32_v3} -- this is an Andes NDS32 v3 core (deprecated; would be removed in v0.13.0).
@item @code{nds32_v3m} -- this is an Andes NDS32 v3m core (deprecated; would be removed in v0.13.0).
@item @code{or1k} -- this is an OpenRISC 1000 core.
The current implementation supports three JTAG TAP cores:
@itemize @minus
@item @code{OpenCores TAP} (See: @url{http://opencores.org/project@comma{}jtag})
@item @code{Altera Virtual JTAG TAP} (See: @url{http://www.altera.com/literature/ug/ug_virtualjtag.pdf})
@item @code{Xilinx BSCAN_* virtual JTAG interface} (See: @url{http://www.xilinx.com/support/documentation/sw_manuals/xilinx14_2/spartan6_hdl.pdf})
@end itemize
And two debug interfaces cores:
@itemize @minus
@item @code{Advanced debug interface}
@*(See: @url{http://opencores.org/project@comma{}adv_debug_sys})
@item @code{SoC Debug Interface}
@*(See: @url{http://opencores.org/project@comma{}dbg_interface})
@end itemize
@item @code{quark_d20xx} -- an Intel Quark D20xx core.
@item @code{quark_x10xx} -- an Intel Quark X10xx core.
@item @code{riscv} -- a RISC-V core.
@item @code{stm8} -- implements an STM8 core.
@item @code{testee} -- a dummy target for cases without a real CPU, e.g. CPLD.
@item @code{xscale} -- this is actually an architecture,
not a CPU type. It is based on the ARMv5 architecture.
@end itemize
@end deffn

To avoid being confused by the variety of ARM based cores, remember
this key point: @emph{ARM is a technology licencing company}.
(See: @url{http://www.arm.com}.)
The CPU name used by OpenOCD will reflect the CPU design that was
licensed, not a vendor brand which incorporates that design.
Name prefixes like arm7, arm9, arm11, and cortex
reflect design generations;
while names like ARMv4, ARMv5, ARMv6, ARMv7 and ARMv8
reflect an architecture version implemented by a CPU design.

@anchor{targetconfiguration}
@section Target Configuration

Before creating a ``target'', you must have added its TAP to the scan chain.
When you've added that TAP, you will have a @code{dotted.name}
which is used to set up the CPU support.
The chip-specific configuration file will normally configure its CPU(s)
right after it adds all of the chip's TAPs to the scan chain.

Although you can set up a target in one step, it's often clearer if you
use shorter commands and do it in two steps: create it, then configure
optional parts.
All operations on the target after it's created will use a new
command, created as part of target creation.

The two main things to configure after target creation are
a work area, which usually has target-specific defaults even
if the board setup code overrides them later;
and event handlers (@pxref{targetevents,,Target Events}), which tend
to be much more board-specific.
The key steps you use might look something like this

@example
dap create mychip.dap -chain-position mychip.cpu
target create MyTarget cortex_m -dap mychip.dap
MyTarget configure -work-area-phys 0x08000 -work-area-size 8096
MyTarget configure -event reset-deassert-pre @{ jtag_rclk 5 @}
MyTarget configure -event reset-init @{ myboard_reinit @}
@end example

You should specify a working area if you can; typically it uses some
on-chip SRAM.
Such a working area can speed up many things, including bulk
writes to target memory;
flash operations like checking to see if memory needs to be erased;
GDB memory checksumming;
and more.

@quotation Warning
On more complex chips, the work area can become
inaccessible when application code
(such as an operating system)
enables or disables the MMU.
For example, the particular MMU context used to access the virtual
address will probably matter ... and that context might not have
easy access to other addresses needed.
At this writing, OpenOCD doesn't have much MMU intelligence.
@end quotation

It's often very useful to define a @code{reset-init} event handler.
For systems that are normally used with a boot loader,
common tasks include updating clocks and initializing memory
controllers.
That may be needed to let you write the boot loader into flash,
in order to ``de-brick'' your board; or to load programs into
external DDR memory without having run the boot loader.

@deffn {Config Command} {target create} target_name type configparams...
This command creates a GDB debug target that refers to a specific JTAG tap.
It enters that target into a list, and creates a new
command (@command{@var{target_name}}) which is used for various
purposes including additional configuration.

@itemize @bullet
@item @var{target_name} ... is the name of the debug target.
By convention this should be the same as the @emph{dotted.name}
of the TAP associated with this target, which must be specified here
using the @code{-chain-position @var{dotted.name}} configparam.

This name is also used to create the target object command,
referred to here as @command{$target_name},
and in other places the target needs to be identified.
@item @var{type} ... specifies the target type. @xref{targettypes,,target types}.
@item @var{configparams} ... all parameters accepted by
@command{$target_name configure} are permitted.
If the target is big-endian, set it here with @code{-endian big}.

You @emph{must} set the @code{-chain-position @var{dotted.name}} or
@code{-dap @var{dap_name}} here.
@end itemize
@end deffn

@deffn {Command} {$target_name configure} configparams...
The options accepted by this command may also be
specified as parameters to @command{target create}.
Their values can later be queried one at a time by
using the @command{$target_name cget} command.

@emph{Warning:} changing some of these after setup is dangerous.
For example, moving a target from one TAP to another;
and changing its endianness.

@itemize @bullet

@item @code{-chain-position} @var{dotted.name} -- names the TAP
used to access this target.

@item @code{-dap} @var{dap_name} -- names the DAP used to access
this target. @xref{dapdeclaration,,DAP declaration}, on how to
create and manage DAP instances.

@item @code{-endian} (@option{big}|@option{little}) -- specifies
whether the CPU uses big or little endian conventions

@item @code{-event} @var{event_name} @var{event_body} --
@xref{targetevents,,Target Events}.
Note that this updates a list of named event handlers.
Calling this twice with two different event names assigns
two different handlers, but calling it twice with the
same event name assigns only one handler.

Current target is temporarily overridden to the event issuing target
before handler code starts and switched back after handler is done.

@item @code{-work-area-backup} (@option{0}|@option{1}) -- says
whether the work area gets backed up; by default,
@emph{it is not backed up.}
When possible, use a working_area that doesn't need to be backed up,
since performing a backup slows down operations.
For example, the beginning of an SRAM block is likely to
be used by most build systems, but the end is often unused.

@item @code{-work-area-size} @var{size} -- specify work are size,
in bytes. The same size applies regardless of whether its physical
or virtual address is being used.

@item @code{-work-area-phys} @var{address} -- set the work area
base @var{address} to be used when no MMU is active.

@item @code{-work-area-virt} @var{address} -- set the work area
base @var{address} to be used when an MMU is active.
@emph{Do not specify a value for this except on targets with an MMU.}
The value should normally correspond to a static mapping for the
@code{-work-area-phys} address, set up by the current operating system.

@anchor{rtostype}
@item @code{-rtos} @var{rtos_type} -- enable rtos support for target,
@var{rtos_type} can be one of @option{auto}, @option{eCos},
@option{ThreadX}, @option{FreeRTOS}, @option{linux}, @option{ChibiOS},
@option{embKernel}, @option{mqx}, @option{uCOS-III}, @option{nuttx},
@option{RIOT}, @option{Zephyr}
@xref{gdbrtossupport,,RTOS Support}.

@item @code{-defer-examine} -- skip target examination at initial JTAG chain
scan and after a reset. A manual call to arp_examine is required to
access the target for debugging.

@item @code{-ap-num} @var{ap_number} -- set DAP access port for target,
@var{ap_number} is the numeric index of the DAP AP the target is connected to.
Use this option with systems where multiple, independent cores are connected
to separate access ports of the same DAP.

@item @code{-cti} @var{cti_name} -- set Cross-Trigger Interface (CTI) connected
to the target. Currently, only the @code{aarch64} target makes use of this option,
where it is a mandatory configuration for the target run control.
@xref{armcrosstrigger,,ARM Cross-Trigger Interface},
for instruction on how to declare and control a CTI instance.

@anchor{gdbportoverride}
@item @code{-gdb-port} @var{number} -- see command @command{gdb_port} for the
possible values of the parameter @var{number}, which are not only numeric values.
Use this option to override, for this target only, the global parameter set with
command @command{gdb_port}.
@xref{gdb_port,,command gdb_port}.

@item @code{-gdb-max-connections} @var{number} -- EXPERIMENTAL: set the maximum
number of GDB connections that are allowed for the target. Default is 1.
A negative value for @var{number} means unlimited connections.
See @xref{gdbmeminspect,,Using GDB as a non-intrusive memory inspector}.
@end itemize
@end deffn

@section Other $target_name Commands
@cindex object command

The Tcl/Tk language has the concept of object commands,
and OpenOCD adopts that same model for targets.

A good Tk example is a on screen button.
Once a button is created a button
has a name (a path in Tk terms) and that name is useable as a first
class command. For example in Tk, one can create a button and later
configure it like this:

@example
# Create
button .foobar -background red -command @{ foo @}
# Modify
.foobar configure -foreground blue
# Query
set x [.foobar cget -background]
# Report
puts [format "The button is %s" $x]
@end example

In OpenOCD's terms, the ``target'' is an object just like a Tcl/Tk
button, and its object commands are invoked the same way.

@example
str912.cpu    mww 0x1234 0x42
omap3530.cpu  mww 0x5555 123
@end example

The commands supported by OpenOCD target objects are:

@deffn {Command} {$target_name arp_examine} @option{allow-defer}
@deffnx {Command} {$target_name arp_halt}
@deffnx {Command} {$target_name arp_poll}
@deffnx {Command} {$target_name arp_reset}
@deffnx {Command} {$target_name arp_waitstate}
Internal OpenOCD scripts (most notably @file{startup.tcl})
use these to deal with specific reset cases.
They are not otherwise documented here.
@end deffn

@deffn {Command} {$target_name set_reg} dict
Set register values of the target.

@itemize
@item @var{dict} ... Tcl dictionary with pairs of register names and values.
@end itemize

For example, the following command sets the value 0 to the program counter (pc)
register and 0x1000 to the stack pointer (sp) register:

@example
set_reg @{pc 0 sp 0x1000@}
@end example
@end deffn

@deffn {Command} {$target_name get_reg} [-force] list
Get register values from the target and return them as Tcl dictionary with pairs
of register names and values.
If option "-force" is set, the register values are read directly from the
target, bypassing any caching.

@itemize
@item @var{list} ... List of register names
@end itemize

For example, the following command retrieves the values from the program
counter (pc) and stack pointer (sp) register:

@example
get_reg @{pc sp@}
@end example
@end deffn

@deffn {Command} {$target_name write_memory} address width data ['phys']
This function provides an efficient way to write to the target memory from a Tcl
script.

@itemize
@item @var{address} ... target memory address
@item @var{width} ... memory access bit size, can be 8, 16, 32 or 64
@item @var{data} ... Tcl list with the elements to write
@item ['phys'] ... treat the memory address as physical instead of virtual address
@end itemize

For example, the following command writes two 32 bit words into the target
memory at address 0x20000000:

@example
write_memory 0x20000000 32 @{0xdeadbeef 0x00230500@}
@end example
@end deffn

@deffn {Command} {$target_name read_memory} address width count ['phys']
This function provides an efficient way to read the target memory from a Tcl
script.
A Tcl list containing the requested memory elements is returned by this function.

@itemize
@item @var{address} ... target memory address
@item @var{width} ... memory access bit size, can be 8, 16, 32 or 64
@item @var{count} ... number of elements to read
@item ['phys'] ... treat the memory address as physical instead of virtual address
@end itemize

For example, the following command reads two 32 bit words from the target
memory at address 0x20000000:

@example
read_memory 0x20000000 32 2
@end example
@end deffn

@deffn {Command} {$target_name cget} queryparm
Each configuration parameter accepted by
@command{$target_name configure}
can be individually queried, to return its current value.
The @var{queryparm} is a parameter name
accepted by that command, such as @code{-work-area-phys}.
There are a few special cases:

@itemize @bullet
@item @code{-event} @var{event_name} -- returns the handler for the
event named @var{event_name}.
This is a special case because setting a handler requires
two parameters.
@item @code{-type} -- returns the target type.
This is a special case because this is set using
@command{target create} and can't be changed
using @command{$target_name configure}.
@end itemize

For example, if you wanted to summarize information about
all the targets you might use something like this:

@example
foreach name [target names] @{
    set y [$name cget -endian]
    set z [$name cget -type]
    puts [format "Chip %d is %s, Endian: %s, type: %s" \
                 $x $name $y $z]
@}
@end example
@end deffn

@anchor{targetcurstate}
@deffn {Command} {$target_name curstate}
Displays the current target state:
@code{debug-running},
@code{halted},
@code{reset},
@code{running}, or @code{unknown}.
(Also, @pxref{eventpolling,,Event Polling}.)
@end deffn

@deffn {Command} {$target_name eventlist}
Displays a table listing all event handlers
currently associated with this target.
@xref{targetevents,,Target Events}.
@end deffn

@deffn {Command} {$target_name invoke-event} event_name
Invokes the handler for the event named @var{event_name}.
(This is primarily intended for use by OpenOCD framework
code, for example by the reset code in @file{startup.tcl}.)
@end deffn

@deffn {Command} {$target_name mdd} [phys] addr [count]
@deffnx {Command} {$target_name mdw} [phys] addr [count]
@deffnx {Command} {$target_name mdh} [phys] addr [count]
@deffnx {Command} {$target_name mdb} [phys] addr [count]
Display contents of address @var{addr}, as
64-bit doublewords (@command{mdd}),
32-bit words (@command{mdw}), 16-bit halfwords (@command{mdh}),
or 8-bit bytes (@command{mdb}).
When the current target has an MMU which is present and active,
@var{addr} is interpreted as a virtual address.
Otherwise, or if the optional @var{phys} flag is specified,
@var{addr} is interpreted as a physical address.
If @var{count} is specified, displays that many units.
(If you want to process the data instead of displaying it,
see the @code{read_memory} primitives.)
@end deffn

@deffn {Command} {$target_name mwd} [phys] addr doubleword [count]
@deffnx {Command} {$target_name mww} [phys] addr word [count]
@deffnx {Command} {$target_name mwh} [phys] addr halfword [count]
@deffnx {Command} {$target_name mwb} [phys] addr byte [count]
Writes the specified @var{doubleword} (64 bits), @var{word} (32 bits),
@var{halfword} (16 bits), or @var{byte} (8-bit) value,
at the specified address @var{addr}.
When the current target has an MMU which is present and active,
@var{addr} is interpreted as a virtual address.
Otherwise, or if the optional @var{phys} flag is specified,
@var{addr} is interpreted as a physical address.
If @var{count} is specified, fills that many units of consecutive address.
@end deffn

@anchor{targetevents}
@section Target Events
@cindex target events
@cindex events
At various times, certain things can happen, or you want them to happen.
For example:
@itemize @bullet
@item What should happen when GDB connects? Should your target reset?
@item When GDB tries to flash the target, do you need to enable the flash via a special command?
@item Is using SRST appropriate (and possible) on your system?
Or instead of that, do you need to issue JTAG commands to trigger reset?
SRST usually resets everything on the scan chain, which can be inappropriate.
@item During reset, do you need to write to certain memory locations
to set up system clocks or
to reconfigure the SDRAM?
How about configuring the watchdog timer, or other peripherals,
to stop running while you hold the core stopped for debugging?
@end itemize

All of the above items can be addressed by target event handlers.
These are set up by @command{$target_name configure -event} or
@command{target create ... -event}.

The programmer's model matches the @code{-command} option used in Tcl/Tk
buttons and events. The two examples below act the same, but one creates
and invokes a small procedure while the other inlines it.

@example
proc my_init_proc @{ @} @{
    echo "Disabling watchdog..."
    mww 0xfffffd44 0x00008000
@}
mychip.cpu configure -event reset-init my_init_proc
mychip.cpu configure -event reset-init @{
    echo "Disabling watchdog..."
    mww 0xfffffd44 0x00008000
@}
@end example

The following target events are defined:

@itemize @bullet
@item @b{debug-halted}
@* The target has halted for debug reasons (i.e.: breakpoint)
@item @b{debug-resumed}
@* The target has resumed (i.e.: GDB said run)
@item @b{early-halted}
@* Occurs early in the halt process
@item @b{examine-start}
@* Before target examine is called.
@item @b{examine-end}
@* After target examine is called with no errors.
@item @b{examine-fail}
@* After target examine fails.
@item @b{gdb-attach}
@* When GDB connects. Issued before any GDB communication with the target
starts. GDB expects the target is halted during attachment.
@xref{gdbmeminspect,,GDB as a non-intrusive memory inspector}, how to
connect GDB to running target.
The event can be also used to set up the target so it is possible to probe flash.
Probing flash is necessary during GDB connect if you want to use
@pxref{programmingusinggdb,,programming using GDB}.
Another use of the flash memory map is for GDB to automatically choose
hardware or software breakpoints depending on whether the breakpoint
is in RAM or read only memory.
Default is @code{halt}
@item @b{gdb-detach}
@* When GDB disconnects
@item @b{gdb-end}
@* When the target has halted and GDB is not doing anything (see early halt)
@item @b{gdb-flash-erase-start}
@* Before the GDB flash process tries to erase the flash (default is
@code{reset init})
@item @b{gdb-flash-erase-end}
@* After the GDB flash process has finished erasing the flash
@item @b{gdb-flash-write-start}
@* Before GDB writes to the flash
@item @b{gdb-flash-write-end}
@* After GDB writes to the flash (default is @code{reset halt})
@item @b{gdb-start}
@* Before the target steps, GDB is trying to start/resume the target
@item @b{halted}
@* The target has halted
@item @b{reset-assert-pre}
@* Issued as part of @command{reset} processing
after @command{reset-start} was triggered
but before either SRST alone is asserted on the scan chain,
or @code{reset-assert} is triggered.
@item @b{reset-assert}
@* Issued as part of @command{reset} processing
after @command{reset-assert-pre} was triggered.
When such a handler is present, cores which support this event will use
it instead of asserting SRST.
This support is essential for debugging with JTAG interfaces which
don't include an SRST line (JTAG doesn't require SRST), and for
selective reset on scan chains that have multiple targets.
@item @b{reset-assert-post}
@* Issued as part of @command{reset} processing
after @code{reset-assert} has been triggered.
or the target asserted SRST on the entire scan chain.
@item @b{reset-deassert-pre}
@* Issued as part of @command{reset} processing
after @code{reset-assert-post} has been triggered.
@item @b{reset-deassert-post}
@* Issued as part of @command{reset} processing
after @code{reset-deassert-pre} has been triggered
and (if the target is using it) after SRST has been
released on the scan chain.
@item @b{reset-end}
@* Issued as the final step in @command{reset} processing.
@item @b{reset-init}
@* Used by @b{reset init} command for board-specific initialization.
This event fires after @emph{reset-deassert-post}.

This is where you would configure PLLs and clocking, set up DRAM so
you can download programs that don't fit in on-chip SRAM, set up pin
multiplexing, and so on.
(You may be able to switch to a fast JTAG clock rate here, after
the target clocks are fully set up.)
@item @b{reset-start}
@* Issued as the first step in @command{reset} processing
before @command{reset-assert-pre} is called.

This is the most robust place to use @command{jtag_rclk}
or @command{adapter speed} to switch to a low JTAG clock rate,
when reset disables PLLs needed to use a fast clock.
@item @b{resume-start}
@* Before any target is resumed
@item @b{resume-end}
@* After all targets have resumed
@item @b{resumed}
@* Target has resumed
@item @b{step-start}
@* Before a target is single-stepped
@item @b{step-end}
@* After single-step has completed
@item @b{trace-config}
@* After target hardware trace configuration was changed
@item @b{semihosting-user-cmd-0x100}
@* The target made a semihosting call with user-defined operation number 0x100
@item @b{semihosting-user-cmd-0x101}
@* The target made a semihosting call with user-defined operation number 0x101
@item @b{semihosting-user-cmd-0x102}
@* The target made a semihosting call with user-defined operation number 0x102
@item @b{semihosting-user-cmd-0x103}
@* The target made a semihosting call with user-defined operation number 0x103
@item @b{semihosting-user-cmd-0x104}
@* The target made a semihosting call with user-defined operation number 0x104
@item @b{semihosting-user-cmd-0x105}
@* The target made a semihosting call with user-defined operation number 0x105
@item @b{semihosting-user-cmd-0x106}
@* The target made a semihosting call with user-defined operation number 0x106
@item @b{semihosting-user-cmd-0x107}
@* The target made a semihosting call with user-defined operation number 0x107
@end itemize

@quotation Note
OpenOCD events are not supposed to be preempt by another event, but this
is not enforced in current code. Only the target event @b{resumed} is
executed with polling disabled; this avoids polling to trigger the event
@b{halted}, reversing the logical order of execution of their handlers.
Future versions of OpenOCD will prevent the event preemption and will
disable the schedule of polling during the event execution. Do not rely
on polling in any event handler; this means, don't expect the status of
a core to change during the execution of the handler. The event handler
will have to enable polling or use @command{$target_name arp_poll} to
check if the core has changed status.
@end quotation

@node Flash Commands
@chapter Flash Commands

OpenOCD has different commands for NOR and NAND flash;
the ``flash'' command works with NOR flash, while
the ``nand'' command works with NAND flash.
This partially reflects different hardware technologies:
NOR flash usually supports direct CPU instruction and data bus access,
while data from a NAND flash must be copied to memory before it can be
used. (SPI flash must also be copied to memory before use.)
However, the documentation also uses ``flash'' as a generic term;
for example, ``Put flash configuration in board-specific files''.

Flash Steps:
@enumerate
@item Configure via the command @command{flash bank}
@* Do this in a board-specific configuration file,
passing parameters as needed by the driver.
@item Operate on the flash via @command{flash subcommand}
@* Often commands to manipulate the flash are typed by a human, or run
via a script in some automated way. Common tasks include writing a
boot loader, operating system, or other data.
@item GDB Flashing
@* Flashing via GDB requires the flash be configured via ``flash
bank'', and the GDB flash features be enabled.
@xref{gdbconfiguration,,GDB Configuration}.
@end enumerate

Many CPUs have the ability to ``boot'' from the first flash bank.
This means that misprogramming that bank can ``brick'' a system,
so that it can't boot.
JTAG tools, like OpenOCD, are often then used to ``de-brick'' the
board by (re)installing working boot firmware.

@anchor{norconfiguration}
@section Flash Configuration Commands
@cindex flash configuration

@deffn {Config Command} {flash bank} name driver base size chip_width bus_width target [driver_options]
Configures a flash bank which provides persistent storage
for addresses from @math{base} to @math{base + size - 1}.
These banks will often be visible to GDB through the target's memory map.
In some cases, configuring a flash bank will activate extra commands;
see the driver-specific documentation.

@itemize @bullet
@item @var{name} ... may be used to reference the flash bank
in other flash commands. A number is also available.
@item @var{driver} ... identifies the controller driver
associated with the flash bank being declared.
This is usually @code{cfi} for external flash, or else
the name of a microcontroller with embedded flash memory.
@xref{flashdriverlist,,Flash Driver List}.
@item @var{base} ... Base address of the flash chip.
@item @var{size} ... Size of the chip, in bytes.
For some drivers, this value is detected from the hardware.
@item @var{chip_width} ... Width of the flash chip, in bytes;
ignored for most microcontroller drivers.
@item @var{bus_width} ... Width of the data bus used to access the
chip, in bytes; ignored for most microcontroller drivers.
@item @var{target} ... Names the target used to issue
commands to the flash controller.
@comment Actually, it's currently a controller-specific parameter...
@item @var{driver_options} ... drivers may support, or require,
additional parameters. See the driver-specific documentation
for more information.
@end itemize
@quotation Note
This command is not available after OpenOCD initialization has completed.
Use it in board specific configuration files, not interactively.
@end quotation
@end deffn

@comment less confusing would be: "flash list" (like "nand list")
@deffn {Command} {flash banks}
Prints a one-line summary of each device that was
declared using @command{flash bank}, numbered from zero.
Note that this is the @emph{plural} form;
the @emph{singular} form is a very different command.
@end deffn

@deffn {Command} {flash list}
Retrieves a list of associative arrays for each device that was
declared using @command{flash bank}, numbered from zero.
This returned list can be manipulated easily from within scripts.
@end deffn

@deffn {Command} {flash probe} num
Identify the flash, or validate the parameters of the configured flash. Operation
depends on the flash type.
The @var{num} parameter is a value shown by @command{flash banks}.
Most flash commands will implicitly @emph{autoprobe} the bank;
flash drivers can distinguish between probing and autoprobing,
but most don't bother.
@end deffn

@section Preparing a Target before Flash Programming

The target device should be in well defined state before the flash programming
begins.

@emph{Always issue} @command{reset init} before @ref{flashprogrammingcommands,,Flash Programming Commands}.
Do not issue another @command{reset} or @command{reset halt} or @command{resume}
until the programming session is finished.

If you use @ref{programmingusinggdb,,Programming using GDB},
the target is prepared automatically in the event gdb-flash-erase-start

The jimtcl script @command{program} calls @command{reset init} explicitly.

@section Erasing, Reading, Writing to Flash
@cindex flash erasing
@cindex flash reading
@cindex flash writing
@cindex flash programming
@anchor{flashprogrammingcommands}

One feature distinguishing NOR flash from NAND or serial flash technologies
is that for read access, it acts exactly like any other addressable memory.
This means you can use normal memory read commands like @command{mdw} or
@command{dump_image} with it, with no special @command{flash} subcommands.
@xref{memoryaccess,,Memory access}, and @ref{imageaccess,,Image access}.

Write access works differently. Flash memory normally needs to be erased
before it's written. Erasing a sector turns all of its bits to ones, and
writing can turn ones into zeroes. This is why there are special commands
for interactive erasing and writing, and why GDB needs to know which parts
of the address space hold NOR flash memory.

@quotation Note
Most of these erase and write commands leverage the fact that NOR flash
chips consume target address space. They implicitly refer to the current
JTAG target, and map from an address in that target's address space
back to a flash bank.
@comment In May 2009, those mappings may fail if any bank associated
@comment with that target doesn't successfully autoprobe ... bug worth fixing?
A few commands use abstract addressing based on bank and sector numbers,
and don't depend on searching the current target and its address space.
Avoid confusing the two command models.
@end quotation

Some flash chips implement software protection against accidental writes,
since such buggy writes could in some cases ``brick'' a system.
For such systems, erasing and writing may require sector protection to be
disabled first.
Examples include CFI flash such as ``Intel Advanced Bootblock flash'',
and AT91SAM7 on-chip flash.
@xref{flashprotect,,flash protect}.

@deffn {Command} {flash erase_sector} num first last
Erase sectors in bank @var{num}, starting at sector @var{first}
up to and including @var{last}.
Sector numbering starts at 0.
Providing a @var{last} sector of @option{last}
specifies "to the end of the flash bank".
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {flash erase_address} [@option{pad}] [@option{unlock}] address length
Erase sectors starting at @var{address} for @var{length} bytes.
Unless @option{pad} is specified, @math{address} must begin a
flash sector, and @math{address + length - 1} must end a sector.
Specifying @option{pad} erases extra data at the beginning and/or
end of the specified region, as needed to erase only full sectors.
The flash bank to use is inferred from the @var{address}, and
the specified length must stay within that bank.
As a special case, when @var{length} is zero and @var{address} is
the start of the bank, the whole flash is erased.
If @option{unlock} is specified, then the flash is unprotected
before erase starts.
@end deffn

@deffn {Command} {flash filld} address double-word length
@deffnx {Command} {flash fillw} address word length
@deffnx {Command} {flash fillh} address halfword length
@deffnx {Command} {flash fillb} address byte length
Fills flash memory with the specified @var{double-word} (64 bits), @var{word} (32 bits),
@var{halfword} (16 bits), or @var{byte} (8-bit) pattern,
starting at @var{address} and continuing
for @var{length} units (word/halfword/byte).
No erasure is done before writing; when needed, that must be done
before issuing this command.
Writes are done in blocks of up to 1024 bytes, and each write is
verified by reading back the data and comparing it to what was written.
The flash bank to use is inferred from the @var{address} of
each block, and the specified length must stay within that bank.
@end deffn
@comment no current checks for errors if fill blocks touch multiple banks!

@deffn {Command} {flash mdw} addr [count]
@deffnx {Command} {flash mdh} addr [count]
@deffnx {Command} {flash mdb} addr [count]
Display contents of address @var{addr}, as
32-bit words (@command{mdw}), 16-bit halfwords (@command{mdh}),
or 8-bit bytes (@command{mdb}).
If @var{count} is specified, displays that many units.
Reads from flash using the flash driver, therefore it enables reading
from a bank not mapped in target address space.
The flash bank to use is inferred from the @var{address} of
each block, and the specified length must stay within that bank.
@end deffn

@deffn {Command} {flash write_bank} num filename [offset]
Write the binary @file{filename} to flash bank @var{num},
starting at @var{offset} bytes from the beginning of the bank. If @var{offset}
is omitted, start at the beginning of the flash bank.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {flash read_bank} num filename [offset [length]]
Read @var{length} bytes from the flash bank @var{num} starting at @var{offset}
and write the contents to the binary @file{filename}. If @var{offset} is
omitted, start at the beginning of the flash bank. If @var{length} is omitted,
read the remaining bytes from the flash bank.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {flash verify_bank} num filename [offset]
Compare the contents of the binary file @var{filename} with the contents of the
flash bank @var{num} starting at @var{offset}. If @var{offset} is omitted,
start at the beginning of the flash bank. Fail if the contents do not match.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {flash write_image} [erase] [unlock] filename [offset] [type]
Write the image @file{filename} to the current target's flash bank(s).
Only loadable sections from the image are written.
A relocation @var{offset} may be specified, in which case it is added
to the base address for each section in the image.
The file [@var{type}] can be specified
explicitly as @option{bin} (binary), @option{ihex} (Intel hex),
@option{elf} (ELF file), @option{s19} (Motorola s19).
@option{mem}, or @option{builder}.
The relevant flash sectors will be erased prior to programming
if the @option{erase} parameter is given. If @option{unlock} is
provided, then the flash banks are unlocked before erase and
program. The flash bank to use is inferred from the address of
each image section.

@quotation Warning
Be careful using the @option{erase} flag when the flash is holding
data you want to preserve.
Portions of the flash outside those described in the image's
sections might be erased with no notice.
@itemize
@item
When a section of the image being written does not fill out all the
sectors it uses, the unwritten parts of those sectors are necessarily
also erased, because sectors can't be partially erased.
@item
Data stored in sector "holes" between image sections are also affected.
For example, "@command{flash write_image erase ...}" of an image with
one byte at the beginning of a flash bank and one byte at the end
erases the entire bank -- not just the two sectors being written.
@end itemize
Also, when flash protection is important, you must re-apply it after
it has been removed by the @option{unlock} flag.
@end quotation

@end deffn

@deffn {Command} {flash verify_image} filename [offset] [type]
Verify the image @file{filename} to the current target's flash bank(s).
Parameters follow the description of 'flash write_image'.
In contrast to the 'verify_image' command, for banks with specific
verify method, that one is used instead of the usual target's read
memory methods. This is necessary for flash banks not readable by
ordinary memory reads.
This command gives only an overall good/bad result for each bank, not
addresses of individual failed bytes as it's intended only as quick
check for successful programming.
@end deffn

@section Other Flash commands
@cindex flash protection

@deffn {Command} {flash erase_check} num
Check erase state of sectors in flash bank @var{num},
and display that status.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {flash info} num [sectors]
Print info about flash bank @var{num}, a list of protection blocks
and their status. Use @option{sectors} to show a list of sectors instead.

The @var{num} parameter is a value shown by @command{flash banks}.
This command will first query the hardware, it does not print cached
and possibly stale information.
@end deffn

@anchor{flashprotect}
@deffn {Command} {flash protect} num first last (@option{on}|@option{off})
Enable (@option{on}) or disable (@option{off}) protection of flash blocks
in flash bank @var{num}, starting at protection block @var{first}
and continuing up to and including @var{last}.
Providing a @var{last} block of @option{last}
specifies "to the end of the flash bank".
The @var{num} parameter is a value shown by @command{flash banks}.
The protection block is usually identical to a flash sector.
Some devices may utilize a protection block distinct from flash sector.
See @command{flash info} for a list of protection blocks.
@end deffn

@deffn {Command} {flash padded_value} num value
Sets the default value used for padding any image sections, This should
normally match the flash bank erased value. If not specified by this
command or the flash driver then it defaults to 0xff.
@end deffn

@anchor{program}
@deffn {Command} {program} filename [preverify] [verify] [reset] [exit] [offset]
This is a helper script that simplifies using OpenOCD as a standalone
programmer. The only required parameter is @option{filename}, the others are optional.
@xref{Flash Programming}.
@end deffn

@anchor{flashdriverlist}
@section Flash Driver List
As noted above, the @command{flash bank} command requires a driver name,
and allows driver-specific options and behaviors.
Some drivers also activate driver-specific commands.

@deffn {Flash Driver} {virtual}
This is a special driver that maps a previously defined bank to another
address. All bank settings will be copied from the master physical bank.

The @var{virtual} driver defines one mandatory parameters,

@itemize
@item @var{master_bank} The bank that this virtual address refers to.
@end itemize

So in the following example addresses 0xbfc00000 and 0x9fc00000 refer to
the flash bank defined at address 0x1fc00000. Any command executed on
the virtual banks is actually performed on the physical banks.
@example
flash bank $_FLASHNAME pic32mx 0x1fc00000 0 0 0 $_TARGETNAME
flash bank vbank0 virtual 0xbfc00000 0 0 0 \
           $_TARGETNAME $_FLASHNAME
flash bank vbank1 virtual 0x9fc00000 0 0 0 \
           $_TARGETNAME $_FLASHNAME
@end example
@end deffn

@subsection External Flash

@deffn {Flash Driver} {cfi}
@cindex Common Flash Interface
@cindex CFI
The ``Common Flash Interface'' (CFI) is the main standard for
external NOR flash chips, each of which connects to a
specific external chip select on the CPU.
Frequently the first such chip is used to boot the system.
Your board's @code{reset-init} handler might need to
configure additional chip selects using other commands (like: @command{mww} to
configure a bus and its timings), or
perhaps configure a GPIO pin that controls the ``write protect'' pin
on the flash chip.
The CFI driver can use a target-specific working area to significantly
speed up operation.

The CFI driver can accept the following optional parameters, in any order:

@itemize
@item @var{jedec_probe} ... is used to detect certain non-CFI flash ROMs,
like AM29LV010 and similar types.
@item @var{x16_as_x8} ... when a 16-bit flash is hooked up to an 8-bit bus.
@item @var{bus_swap} ... when data bytes in a 16-bit flash needs to be swapped.
@item @var{data_swap} ... when data bytes in a 16-bit flash needs to be
swapped when writing data values (i.e. not CFI commands).
@end itemize

To configure two adjacent banks of 16 MBytes each, both sixteen bits (two bytes)
wide on a sixteen bit bus:

@example
flash bank $_FLASHNAME cfi 0x00000000 0x01000000 2 2 $_TARGETNAME
flash bank $_FLASHNAME cfi 0x01000000 0x01000000 2 2 $_TARGETNAME
@end example

To configure one bank of 32 MBytes
built from two sixteen bit (two byte) wide parts wired in parallel
to create a thirty-two bit (four byte) bus with doubled throughput:

@example
flash bank $_FLASHNAME cfi 0x00000000 0x02000000 2 4 $_TARGETNAME
@end example

@c "cfi part_id" disabled
@end deffn

@deffn {Flash Driver} {jtagspi}
@cindex Generic JTAG2SPI driver
@cindex SPI
@cindex jtagspi
@cindex bscan_spi
Several FPGAs and CPLDs can retrieve their configuration (bitstream) from a
SPI flash connected to them. To access this flash from the host, the device
is first programmed with a special proxy bitstream that
exposes the SPI flash on the device's JTAG interface. The flash can then be
accessed through JTAG.

Since signaling between JTAG and SPI is compatible, all that is required for
a proxy bitstream is to connect TDI-MOSI, TDO-MISO, TCK-CLK and activate
the flash chip select when the JTAG state machine is in SHIFT-DR. Such
a bitstream for several Xilinx FPGAs can be found in
@file{contrib/loaders/flash/fpga/xilinx_bscan_spi.py}. It requires
@uref{https://github.com/m-labs/migen, migen} and a Xilinx toolchain to build.

This flash bank driver requires a target on a JTAG tap and will access that
tap directly. Since no support from the target is needed, the target can be a
"testee" dummy. Since the target does not expose the flash memory
mapping, target commands that would otherwise be expected to access the flash
will not work. These include all @command{*_image} and
@command{$target_name m*} commands as well as @command{program}. Equivalent
functionality is available through the @command{flash write_bank},
@command{flash read_bank}, and @command{flash verify_bank} commands.

According to device size, 1- to 4-byte addresses are sent. However, some
flash chips additionally have to be switched to 4-byte addresses by an extra
command, see below.

@itemize
@item @var{ir} ... is loaded into the JTAG IR to map the flash as the JTAG DR.
For the bitstreams generated from @file{xilinx_bscan_spi.py} this is the
@var{USER1} instruction.
@end itemize

@example
target create $_TARGETNAME testee -chain-position $_CHIPNAME.fpga
set _XILINX_USER1 0x02
flash bank $_FLASHNAME spi 0x0 0 0 0 \
           $_TARGETNAME $_XILINX_USER1
@end example

@deffn Command {jtagspi set} bank_id name total_size page_size read_cmd unused pprg_cmd mass_erase_cmd sector_size sector_erase_cmd
Sets flash parameters: @var{name} human readable string, @var{total_size}
size in bytes, @var{page_size} is write page size. @var{read_cmd} and @var{pprg_cmd}
are commands for read and page program, respectively. @var{mass_erase_cmd},
@var{sector_size} and @var{sector_erase_cmd} are optional.
@example
jtagspi set 0 w25q128 0x1000000 0x100 0x03 0 0x02 0xC7 0x10000 0xD8
@end example
@end deffn

@deffn Command {jtagspi cmd} bank_id resp_num cmd_byte ...
Sends command @var{cmd_byte} and at most 20 following bytes and reads
@var{resp_num} bytes afterwards. E.g. for 'Enter 4-byte address mode'
@example
jtagspi cmd 0 0 0xB7
@end example
@end deffn

@deffn Command {jtagspi always_4byte} bank_id [ on | off ]
Some devices use 4-byte addresses for all commands except the legacy 0x03 read
regardless of device size. This command controls the corresponding hack.
@end deffn
@end deffn

@deffn {Flash Driver} {xcf}
@cindex Xilinx Platform flash driver
@cindex xcf
Xilinx FPGAs can be configured from specialized flash ICs named Platform Flash.
It is (almost) regular NOR flash with erase sectors, program pages, etc. The
only difference is special registers controlling its FPGA specific behavior.
They must be properly configured for successful FPGA loading using
additional @var{xcf} driver command:

@deffn {Command} {xcf ccb} <bank_id>
command accepts additional parameters:
@itemize
@item @var{external|internal} ... selects clock source.
@item @var{serial|parallel} ... selects serial or parallel data bus mode.
@item @var{slave|master} ... selects slave of master mode for flash device.
@item @var{40|20} ... selects clock frequency in MHz for internal clock
in master mode.
@end itemize
@example
xcf ccb 0 external parallel slave 40
@end example
All of them must be specified even if clock frequency is pointless
in slave mode. If only bank id specified than command prints current
CCB register value. Note: there is no need to write this register
every time you erase/program data sectors because it stores in
dedicated sector.
@end deffn

@deffn {Command} {xcf configure} <bank_id>
Initiates FPGA loading procedure. Useful if your board has no "configure"
button.
@example
xcf configure 0
@end example
@end deffn

Additional driver notes:
@itemize
@item Only single revision supported.
@item Driver automatically detects need of bit reverse, but
only "bin" (raw binary, do not confuse it with "bit") and "mcs"
(Intel hex) file types supported.
@item For additional info check xapp972.pdf and ug380.pdf.
@end itemize
@end deffn

@deffn {Flash Driver} {lpcspifi}
@cindex NXP SPI Flash Interface
@cindex SPIFI
@cindex lpcspifi
NXP's LPC43xx and LPC18xx families include a proprietary SPI
Flash Interface (SPIFI) peripheral that can drive and provide
memory mapped access to external SPI flash devices.

The lpcspifi driver initializes this interface and provides
program and erase functionality for these serial flash devices.
Use of this driver @b{requires} a working area of at least 1kB
to be configured on the target device; more than this will
significantly reduce flash programming times.

The setup command only requires the @var{base} parameter. All
other parameters are ignored, and the flash size and layout
are configured by the driver.

@example
flash bank $_FLASHNAME lpcspifi 0x14000000 0 0 0 $_TARGETNAME
@end example

@end deffn

@deffn {Flash Driver} {stmsmi}
@cindex STMicroelectronics Serial Memory Interface
@cindex SMI
@cindex stmsmi
Some devices from STMicroelectronics (e.g. STR75x MCU family,
SPEAr MPU family) include a proprietary
``Serial Memory Interface'' (SMI) controller able to drive external
SPI flash devices.
Depending on specific device and board configuration, up to 4 external
flash devices can be connected.

SMI makes the flash content directly accessible in the CPU address
space; each external device is mapped in a memory bank.
CPU can directly read data, execute code and boot from SMI banks.
Normal OpenOCD commands like @command{mdw} can be used to display
the flash content.

The setup command only requires the @var{base} parameter in order
to identify the memory bank.
All other parameters are ignored. Additional information, like
flash size, are detected automatically.

@example
flash bank $_FLASHNAME stmsmi 0xf8000000 0 0 0 $_TARGETNAME
@end example

@end deffn

@deffn {Flash Driver} {stmqspi}
@cindex STMicroelectronics QuadSPI/OctoSPI Interface
@cindex QuadSPI
@cindex OctoSPI
@cindex stmqspi
Some devices from STMicroelectronics include a proprietary ``QuadSPI Interface''
(e.g. STM32F4, STM32F7, STM32L4) or ``OctoSPI Interface'' (e.g. STM32L4+)
controller able to drive one or even two (dual mode) external SPI flash devices.
The OctoSPI is a superset of QuadSPI, its presence is detected automatically.
Currently only the regular command mode is supported, whereas the HyperFlash
mode is not.

QuadSPI/OctoSPI makes the flash contents directly accessible in the CPU address
space; in case of dual mode both devices must be of the same type and are
mapped in the same memory bank (even and odd addresses interleaved).
CPU can directly read data, execute code (but not boot) from QuadSPI bank.

The 'flash bank' command only requires the @var{base} parameter and the extra
parameter @var{io_base} in order to identify the memory bank. Both are fixed
by hardware, see datasheet or RM. All other parameters are ignored.

The controller must be initialized after each reset and properly configured
for memory-mapped read operation for the particular flash chip(s), for the full
list of available register settings cf. the controller's RM. This setup is quite
board specific (that's why booting from this memory is not possible). The
flash driver infers all parameters from current controller register values when
'flash probe @var{bank_id}' is executed.

Normal OpenOCD commands like @command{mdw} can be used to display the flash content,
but only after proper controller initialization as described above. However,
due to a silicon bug in some devices, attempting to access the very last word
should be avoided.

It is possible to use two (even different) flash chips alternatingly, if individual
bank chip selects are available. For some package variants, this is not the case
due to limited pin count. To switch from one to another, adjust FSEL bit accordingly
and re-issue 'flash probe bank_id'. Note that the bank base address will @emph{not}
change, so the address spaces of both devices will overlap. In dual flash mode
both chips must be identical regarding size and most other properties.

Block or sector protection internal to the flash chip is not handled by this
driver at all, but can be dealt with manually by the 'cmd' command, see below.
The sector protection via 'flash protect' command etc. is completely internal to
openocd, intended only to prevent accidental erase or overwrite and it does not
persist across openocd invocations.

OpenOCD contains a hardcoded list of flash devices with their properties,
these are auto-detected. If a device is not included in this list, SFDP discovery
is attempted. If this fails or gives inappropriate results, manual setting is
required (see 'set' command).

@example
flash bank $_FLASHNAME stmqspi 0x90000000 0 0 0 \
           $_TARGETNAME 0xA0001000
flash bank $_FLASHNAME stmqspi 0x70000000 0 0 0 \
           $_TARGETNAME 0xA0001400
@end example

There are three specific commands
@deffn {Command} {stmqspi mass_erase} bank_id
Clears sector protections and performs a mass erase. Works only if there is no
chip specific write protection engaged.
@end deffn

@deffn {Command} {stmqspi set} bank_id name total_size page_size read_cmd fread_cmd pprg_cmd mass_erase_cmd sector_size sector_erase_cmd
Set flash parameters: @var{name} human readable string, @var{total_size} size
in bytes, @var{page_size} is write page size. @var{read_cmd}, @var{fread_cmd} and @var{pprg_cmd}
are commands for reading and page programming. @var{fread_cmd} is used in DPI and QPI modes,
@var{read_cmd} in normal SPI (single line) mode. @var{mass_erase_cmd}, @var{sector_size}
and @var{sector_erase_cmd} are optional.

This command is required if chip id is not hardcoded yet and e.g. for EEPROMs or FRAMs
which don't support an id command.

In dual mode parameters of both chips are set identically. The parameters refer to
a single chip, so the whole bank gets twice the specified capacity etc.
@end deffn

@deffn {Command} {stmqspi cmd} bank_id resp_num cmd_byte ...
If @var{resp_num} is zero, sends command @var{cmd_byte} and following data
bytes. In dual mode command byte is sent to @emph{both} chips but data bytes are
sent @emph{alternatingly} to chip 1 and 2, first to flash 1, second to flash 2, etc.,
i.e. the total number of bytes (including cmd_byte) must be odd.

If @var{resp_num} is not zero, cmd and at most four following data bytes are
sent, in dual mode @emph{simultaneously} to both chips. Then @var{resp_num} bytes
are read interleaved from both chips starting with chip 1. In this case
@var{resp_num} must be even.

Note the hardware dictated subtle difference of those two cases in dual-flash mode.

To check basic communication settings, issue
@example
stmqspi cmd bank_id 0 0x04; stmqspi cmd bank_id 1 0x05
stmqspi cmd bank_id 0 0x06; stmqspi cmd bank_id 1 0x05
@end example
for single flash mode or
@example
stmqspi cmd bank_id 0 0x04; stmqspi cmd bank_id 2 0x05
stmqspi cmd bank_id 0 0x06; stmqspi cmd bank_id 2 0x05
@end example
for dual flash mode. This should return the status register contents.

In 8-line mode, @var{cmd_byte} is sent twice - first time as given, second time
complemented. Additionally, in 8-line mode only, some commands (e.g. Read Status)
need a dummy address, e.g.
@example
stmqspi cmd bank_id 1 0x05 0x00 0x00 0x00 0x00
@end example
should return the status register contents.

@end deffn

@end deffn

@deffn {Flash Driver} {mrvlqspi}
This driver supports QSPI flash controller of Marvell's Wireless
Microcontroller platform.

The flash size is autodetected based on the table of known JEDEC IDs
hardcoded in the OpenOCD sources.

@example
flash bank $_FLASHNAME mrvlqspi 0x0 0 0 0 $_TARGETNAME 0x46010000
@end example

@end deffn

@deffn {Flash Driver} {ath79}
@cindex Atheros ath79 SPI driver
@cindex ath79
Members of ATH79 SoC family from Atheros include a SPI interface with 3
chip selects.
On reset a SPI flash connected to the first chip select (CS0) is made
directly read-accessible in the CPU address space (up to 16MBytes)
and is usually used to store the bootloader and operating system.
Normal OpenOCD commands like @command{mdw} can be used to display
the flash content while it is in memory-mapped mode (only the first
4MBytes are accessible without additional configuration on reset).

The setup command only requires the @var{base} parameter in order
to identify the memory bank. The actual value for the base address
is not otherwise used by the driver. However the mapping is passed
to gdb. Thus for the memory mapped flash (chipselect CS0) the base
address should be the actual memory mapped base address. For unmapped
chipselects (CS1 and CS2) care should be taken to use a base address
that does not overlap with real memory regions.
Additional information, like flash size, are detected automatically.
An optional additional parameter sets the chipselect for the bank,
with the default CS0.
CS1 and CS2 require additional GPIO setup before they can be used
since the alternate function must be enabled on the GPIO pin
CS1/CS2 is routed to on the given SoC.

@example
flash bank $_FLASHNAME ath79 0xbf000000 0 0 0 $_TARGETNAME

# When using multiple chipselects the base should be different
# for each, otherwise the write_image command is not able to
# distinguish the banks.
flash bank flash0 ath79 0xbf000000 0 0 0 $_TARGETNAME cs0
flash bank flash1 ath79 0x10000000 0 0 0 $_TARGETNAME cs1
flash bank flash2 ath79 0x20000000 0 0 0 $_TARGETNAME cs2
@end example

@end deffn

@deffn {Flash Driver} {fespi}
@cindex Freedom E SPI
@cindex fespi

SiFive's Freedom E SPI controller, used in HiFive and other boards.

@example
flash bank $_FLASHNAME fespi 0x20000000 0 0 0 $_TARGETNAME
@end example
@end deffn

@subsection Internal Flash (Microcontrollers)

@deffn {Flash Driver} {aduc702x}
The ADUC702x analog microcontrollers from Analog Devices
include internal flash and use ARM7TDMI cores.
The aduc702x flash driver works with models ADUC7019 through ADUC7028.
The setup command only requires the @var{target} argument
since all devices in this family have the same memory layout.

@example
flash bank $_FLASHNAME aduc702x 0 0 0 0 $_TARGETNAME
@end example
@end deffn

@deffn {Flash Driver} {ambiqmicro}
@cindex ambiqmicro
@cindex apollo
All members of the Apollo microcontroller family from
Ambiq Micro include internal flash and use ARM's Cortex-M4 core.
The host connects over USB to an FTDI interface that communicates
with the target using SWD.

The @var{ambiqmicro} driver reads the Chip Information Register detect
the device class of the MCU.
The Flash and SRAM sizes directly follow device class, and are used
to set up the flash banks.
If this fails, the driver will use default values set to the minimum
sizes of an Apollo chip.

All Apollo chips have two flash banks of the same size.
In all cases the first flash bank starts at location 0,
and the second bank starts after the first.

@example
# Flash bank 0
flash bank $_FLASHNAME ambiqmicro 0 0x00040000 0 0 $_TARGETNAME
# Flash bank 1 - same size as bank0, starts after bank 0.
flash bank $_FLASHNAME ambiqmicro 0x00040000 0x00040000 0 0 \
           $_TARGETNAME
@end example

Flash is programmed using custom entry points into the bootloader.
This is the only way to program the flash as no flash control registers
are available to the user.

The @var{ambiqmicro} driver adds some additional commands:

@deffn {Command} {ambiqmicro mass_erase} <bank>
Erase entire bank.
@end deffn
@deffn {Command} {ambiqmicro page_erase} <bank> <first> <last>
Erase device pages.
@end deffn
@deffn {Command} {ambiqmicro program_otp} <bank> <offset> <count>
Program OTP is a one time operation to create write protected flash.
The user writes sectors to SRAM starting at 0x10000010.
Program OTP will write these sectors from SRAM to flash, and write protect
the flash.
@end deffn
@end deffn

@anchor{at91samd}
@deffn {Flash Driver} {at91samd}
@cindex at91samd
All members of the ATSAM D2x, D1x, D0x, ATSAMR, ATSAML and ATSAMC microcontroller
families from Atmel include internal flash and use ARM's Cortex-M0+ core.

Do not use for ATSAM D51 and E5x: use @xref{atsame5}.

The devices have one flash bank:

@example
flash bank $_FLASHNAME at91samd 0x00000000 0 1 1 $_TARGETNAME
@end example

@deffn {Command} {at91samd chip-erase}
Issues a complete Flash erase via the Device Service Unit (DSU). This can be
used to erase a chip back to its factory state and does not require the
processor to be halted.
@end deffn

@deffn {Command} {at91samd set-security}
Secures the Flash via the Set Security Bit (SSB) command. This prevents access
to the Flash and can only be undone by using the chip-erase command which
erases the Flash contents and turns off the security bit. Warning: at this
time, openocd will not be able to communicate with a secured chip and it is
therefore not possible to chip-erase it without using another tool.

@example
at91samd set-security enable
@end example
@end deffn

@deffn {Command} {at91samd eeprom}
Shows or sets the EEPROM emulation size configuration, stored in the User Row
of the Flash. When setting, the EEPROM size must be specified in bytes and it
must be one of the permitted sizes according to the datasheet. Settings are
written immediately but only take effect on MCU reset. EEPROM emulation
requires additional firmware support and the minimum EEPROM size may not be
the same as the minimum that the hardware supports. Set the EEPROM size to 0
in order to disable this feature.

@example
at91samd eeprom
at91samd eeprom 1024
@end example
@end deffn

@deffn {Command} {at91samd bootloader}
Shows or sets the bootloader size configuration, stored in the User Row of the
Flash. This is called the BOOTPROT region. When setting, the bootloader size
must be specified in bytes and it must be one of the permitted sizes according
to the datasheet. Settings are written immediately but only take effect on
MCU reset. Setting the bootloader size to 0 disables bootloader protection.

@example
at91samd bootloader
at91samd bootloader 16384
@end example
@end deffn

@deffn {Command} {at91samd dsu_reset_deassert}
This command releases internal reset held by DSU
and prepares reset vector catch in case of reset halt.
Command is used internally in event reset-deassert-post.
@end deffn

@deffn {Command} {at91samd nvmuserrow}
Writes or reads the entire 64 bit wide NVM user row register which is located at
0x804000. This register includes various fuses lock-bits and factory calibration
data. Reading the register is done by invoking this command without any
arguments. Writing is possible by giving 1 or 2 hex values. The first argument
is the register value to be written and the second one is an optional changemask.
Every bit which value in changemask is 0 will stay unchanged. The lock- and
reserved-bits are masked out and cannot be changed.

@example
# Read user row
>at91samd nvmuserrow
NVMUSERROW: 0xFFFFFC5DD8E0C788
# Write 0xFFFFFC5DD8E0C788 to user row
>at91samd nvmuserrow 0xFFFFFC5DD8E0C788
# Write 0x12300 to user row but leave other bits and low
# byte unchanged
>at91samd nvmuserrow 0x12345 0xFFF00
@end example
@end deffn

@end deffn

@anchor{at91sam3}
@deffn {Flash Driver} {at91sam3}
@cindex at91sam3
All members of the AT91SAM3 microcontroller family from
Atmel include internal flash and use ARM's Cortex-M3 core. The driver
currently (6/22/09) recognizes the AT91SAM3U[1/2/4][C/E] chips. Note
that the driver was orginaly developed and tested using the
AT91SAM3U4E, using a SAM3U-EK eval board. Support for other chips in
the family was cribbed from the data sheet. @emph{Note to future
readers/updaters: Please remove this worrisome comment after other
chips are confirmed.}

The AT91SAM3U4[E/C] (256K) chips have two flash banks; most other chips
have one flash bank. In all cases the flash banks are at
the following fixed locations:

@example
# Flash bank 0 - all chips
flash bank $_FLASHNAME at91sam3 0x00080000 0 1 1 $_TARGETNAME
# Flash bank 1 - only 256K chips
flash bank $_FLASHNAME at91sam3 0x00100000 0 1 1 $_TARGETNAME
@end example

Internally, the AT91SAM3 flash memory is organized as follows.
Unlike the AT91SAM7 chips, these are not used as parameters
to the @command{flash bank} command:

@itemize
@item @emph{N-Banks:} 256K chips have 2 banks, others have 1 bank.
@item @emph{Bank Size:} 128K/64K Per flash bank
@item @emph{Sectors:} 16 or 8 per bank
@item @emph{SectorSize:} 8K Per Sector
@item @emph{PageSize:} 256 bytes per page. Note that OpenOCD operates on 'sector' sizes, not page sizes.
@end itemize

The AT91SAM3 driver adds some additional commands:

@deffn {Command} {at91sam3 gpnvm}
@deffnx {Command} {at91sam3 gpnvm clear} number
@deffnx {Command} {at91sam3 gpnvm set} number
@deffnx {Command} {at91sam3 gpnvm show} [@option{all}|number]
With no parameters, @command{show} or @command{show all},
shows the status of all GPNVM bits.
With @command{show} @var{number}, displays that bit.

With @command{set} @var{number} or @command{clear} @var{number},
modifies that GPNVM bit.
@end deffn

@deffn {Command} {at91sam3 info}
This command attempts to display information about the AT91SAM3
chip. @emph{First} it read the @code{CHIPID_CIDR} [address 0x400e0740, see
Section 28.2.1, page 505 of the AT91SAM3U 29/may/2009 datasheet,
document id: doc6430A] and decodes the values. @emph{Second} it reads the
various clock configuration registers and attempts to display how it
believes the chip is configured. By default, the SLOWCLK is assumed to
be 32768 Hz, see the command @command{at91sam3 slowclk}.
@end deffn

@deffn {Command} {at91sam3 slowclk} [value]
This command shows/sets the slow clock frequency used in the
@command{at91sam3 info} command calculations above.
@end deffn
@end deffn

@deffn {Flash Driver} {at91sam4}
@cindex at91sam4
All members of the AT91SAM4 microcontroller family from
Atmel include internal flash and use ARM's Cortex-M4 core.
This driver uses the same command names/syntax as @xref{at91sam3}.
@end deffn

@deffn {Flash Driver} {at91sam4l}
@cindex at91sam4l
All members of the AT91SAM4L microcontroller family from
Atmel include internal flash and use ARM's Cortex-M4 core.
This driver uses the same command names/syntax as @xref{at91sam3}.

The AT91SAM4L driver adds some additional commands:
@deffn {Command} {at91sam4l smap_reset_deassert}
This command releases internal reset held by SMAP
and prepares reset vector catch in case of reset halt.
Command is used internally in event reset-deassert-post.
@end deffn
@end deffn

@anchor{atsame5}
@deffn {Flash Driver} {atsame5}
@cindex atsame5
All members of the SAM E54, E53, E51 and D51 microcontroller
families from Microchip (former Atmel) include internal flash
and use ARM's Cortex-M4 core.

The devices have two ECC flash banks with a swapping feature.
This driver handles both banks together as it were one.
Bank swapping is not supported yet.

@example
flash bank $_FLASHNAME atsame5 0x00000000 0 1 1 $_TARGETNAME
@end example

@deffn {Command} {atsame5 bootloader}
Shows or sets the bootloader size configuration, stored in the User Page of the
Flash. This is called the BOOTPROT region. When setting, the bootloader size
must be specified in bytes. The nearest bigger protection size is used.
Settings are written immediately but only take effect on MCU reset.
Setting the bootloader size to 0 disables bootloader protection.

@example
atsame5 bootloader
atsame5 bootloader 16384
@end example
@end deffn

@deffn {Command} {atsame5 chip-erase}
Issues a complete Flash erase via the Device Service Unit (DSU). This can be
used to erase a chip back to its factory state and does not require the
processor to be halted.
@end deffn

@deffn {Command} {atsame5 dsu_reset_deassert}
This command releases internal reset held by DSU
and prepares reset vector catch in case of reset halt.
Command is used internally in event reset-deassert-post.
@end deffn

@deffn {Command} {atsame5 userpage}
Writes or reads the first 64 bits of NVM User Page which is located at
0x804000. This field includes various fuses.
Reading is done by invoking this command without any arguments.
Writing is possible by giving 1 or 2 hex values. The first argument
is the value to be written and the second one is an optional bit mask
(a zero bit in the mask means the bit stays unchanged).
The reserved fields are always masked out and cannot be changed.

@example
# Read
>atsame5 userpage
USER PAGE: 0xAEECFF80FE9A9239
# Write
>atsame5 userpage 0xAEECFF80FE9A9239
# Write 2 to SEESBLK and 4 to SEEPSZ fields but leave other
# bits unchanged (setup SmartEEPROM of virtual size 8192
# bytes)
>atsame5 userpage 0x4200000000 0x7f00000000
@end example
@end deffn

@end deffn

@deffn {Flash Driver} {atsamv}
@cindex atsamv
All members of the ATSAMV7x, ATSAMS70, and ATSAME70 families from
Atmel include internal flash and use ARM's Cortex-M7 core.
This driver uses the same command names/syntax as @xref{at91sam3}.

@example
flash bank $_FLASHNAME atsamv 0x00400000 0 0 0 $_TARGETNAME
@end example

@deffn {Command} {atsamv gpnvm} [@option{show} [@option{all}|number]]
@deffnx {Command} {atsamv gpnvm} (@option{clr}|@option{set}) number
With no parameters, @option{show} or @option{show all},
shows the status of all GPNVM bits.
With @option{show} @var{number}, displays that bit.

With @option{set} @var{number} or @option{clear} @var{number},
modifies that GPNVM bit.
@end deffn

@end deffn

@deffn {Flash Driver} {at91sam7}
All members of the AT91SAM7 microcontroller family from Atmel include
internal flash and use ARM7TDMI cores. The driver automatically
recognizes a number of these chips using the chip identification
register, and autoconfigures itself.

@example
flash bank $_FLASHNAME at91sam7 0 0 0 0 $_TARGETNAME
@end example

For chips which are not recognized by the controller driver, you must
provide additional parameters in the following order:

@itemize
@item @var{chip_model} ... label used with @command{flash info}
@item @var{banks}
@item @var{sectors_per_bank}
@item @var{pages_per_sector}
@item @var{pages_size}
@item @var{num_nvm_bits}
@item @var{freq_khz} ... required if an external clock is provided,
optional (but recommended) when the oscillator frequency is known
@end itemize

It is recommended that you provide zeroes for all of those values
except the clock frequency, so that everything except that frequency
will be autoconfigured.
Knowing the frequency helps ensure correct timings for flash access.

The flash controller handles erases automatically on a page (128/256 byte)
basis, so explicit erase commands are not necessary for flash programming.
However, there is an ``EraseAll`` command that can erase an entire flash
plane (of up to 256KB), and it will be used automatically when you issue
@command{flash erase_sector} or @command{flash erase_address} commands.

@deffn {Command} {at91sam7 gpnvm} bitnum (@option{set}|@option{clear})
Set or clear a ``General Purpose Non-Volatile Memory'' (GPNVM)
bit for the processor. Each processor has a number of such bits,
used for controlling features such as brownout detection (so they
are not truly general purpose).
@quotation Note
This assumes that the first flash bank (number 0) is associated with
the appropriate at91sam7 target.
@end quotation
@end deffn
@end deffn

@deffn {Flash Driver} {avr}
The AVR 8-bit microcontrollers from Atmel integrate flash memory.
@emph{The current implementation is incomplete.}
@comment - defines mass_erase ... pointless given flash_erase_address
@end deffn

@deffn {Flash Driver} {bluenrg-x}
STMicroelectronics BlueNRG-1, BlueNRG-2 and BlueNRG-LP Bluetooth low energy wireless system-on-chip. They include ARM Cortex-M0/M0+ core and internal flash memory.
The driver automatically recognizes these chips using
the chip identification registers, and autoconfigures itself.

@example
flash bank $_FLASHNAME bluenrg-x 0 0 0 0 $_TARGETNAME
@end example

Note that when users ask to erase all the sectors of the flash, a mass erase command is used which is faster than erasing
each single sector one by one.

@example
flash erase_sector 0 0 last # It will perform a mass erase
@end example

Triggering a mass erase is also useful when users want to disable readout protection.
@end deffn

@deffn {Flash Driver} {cc26xx}
All versions of the SimpleLink CC13xx and CC26xx microcontrollers from Texas
Instruments include internal flash. The cc26xx flash driver supports both the
CC13xx and CC26xx family of devices. The driver automatically recognizes the
specific version's flash parameters and autoconfigures itself. The flash bank
starts at address 0.

@example
flash bank $_FLASHNAME cc26xx 0 0 0 0 $_TARGETNAME
@end example
@end deffn

@deffn {Flash Driver} {cc3220sf}
The CC3220SF version of the SimpleLink CC32xx microcontrollers from Texas
Instruments includes 1MB of internal flash. The cc3220sf flash driver only
supports the internal flash. The serial flash on SimpleLink boards is
programmed via the bootloader over a UART connection. Security features of
the CC3220SF may erase the internal flash during power on reset. Refer to
documentation at @url{www.ti.com/cc3220sf} for details on security features
and programming the serial flash.

@example
flash bank $_FLASHNAME cc3220sf 0 0 0 0 $_TARGETNAME
@end example
@end deffn

@deffn {Flash Driver} {efm32}
All members of the EFM32/EFR32 microcontroller family from Energy Micro (now Silicon Labs)
include internal flash and use Arm Cortex-M3 or Cortex-M4 cores. The driver automatically
recognizes a number of these chips using the chip identification register, and
autoconfigures itself.
@example
flash bank $_FLASHNAME efm32 0 0 0 0 $_TARGETNAME
@end example
It supports writing to the user data page, as well as the portion of the lockbits page
past 512 bytes on chips with larger page sizes. The latter is used by the SiLabs
bootloader/AppLoader system for encryption keys. Setting protection on these pages is
currently not supported.
@example
flash bank userdata.flash efm32 0x0FE00000 0 0 0 $_TARGETNAME
flash bank lockbits.flash efm32 0x0FE04000 0 0 0 $_TARGETNAME
@end example

A special feature of efm32 controllers is that it is possible to completely disable the
debug interface by writing the correct values to the 'Debug Lock Word'. OpenOCD supports
this via the following command:
@example
efm32 debuglock num
@end example
The @var{num} parameter is a value shown by @command{flash banks}.
Note that in order for this command to take effect, the target needs to be reset.
@emph{The current implementation is incomplete. Unprotecting flash pages is not
supported.}
@end deffn

@deffn {Flash Driver} {esirisc}
Members of the eSi-RISC family may optionally include internal flash programmed
via the eSi-TSMC Flash interface. Additional parameters are required to
configure the driver: @option{cfg_address} is the base address of the
configuration register interface, @option{clock_hz} is the expected clock
frequency, and @option{wait_states} is the number of configured read wait states.

@example
flash bank $_FLASHNAME esirisc base_address size_bytes 0 0 \
           $_TARGETNAME cfg_address clock_hz wait_states
@end example

@deffn {Command} {esirisc flash mass_erase} bank_id
Erase all pages in data memory for the bank identified by @option{bank_id}.
@end deffn

@deffn {Command} {esirisc flash ref_erase} bank_id
Erase the reference cell for the bank identified by @option{bank_id}. @emph{This
is an uncommon operation.}
@end deffn
@end deffn

@deffn {Flash Driver} {fm3}
All members of the FM3 microcontroller family from Fujitsu
include internal flash and use ARM Cortex-M3 cores.
The @var{fm3} driver uses the @var{target} parameter to select the
correct bank config, it can currently be one of the following:
@code{mb9bfxx1.cpu}, @code{mb9bfxx2.cpu}, @code{mb9bfxx3.cpu},
@code{mb9bfxx4.cpu}, @code{mb9bfxx5.cpu} or @code{mb9bfxx6.cpu}.

@example
flash bank $_FLASHNAME fm3 0 0 0 0 $_TARGETNAME
@end example
@end deffn

@deffn {Flash Driver} {fm4}
All members of the FM4 microcontroller family from Spansion (formerly Fujitsu)
include internal flash and use ARM Cortex-M4 cores.
The @var{fm4} driver uses a @var{family} parameter to select the
correct bank config, it can currently be one of the following:
@code{MB9BFx64}, @code{MB9BFx65}, @code{MB9BFx66}, @code{MB9BFx67}, @code{MB9BFx68},
@code{S6E2Cx8}, @code{S6E2Cx9}, @code{S6E2CxA} or @code{S6E2Dx},
with @code{x} treated as wildcard and otherwise case (and any trailing
characters) ignored.

@example
flash bank $@{_FLASHNAME@}0 fm4 0x00000000 0 0 0 \
           $_TARGETNAME S6E2CCAJ0A
flash bank $@{_FLASHNAME@}1 fm4 0x00100000 0 0 0 \
           $_TARGETNAME S6E2CCAJ0A
@end example
@emph{The current implementation is incomplete. Protection is not supported,
nor is Chip Erase (only Sector Erase is implemented).}
@end deffn

@deffn {Flash Driver} {kinetis}
@cindex kinetis
Kx, KLx, KVx and KE1x members of the Kinetis microcontroller family
from NXP (former Freescale) include
internal flash and use ARM Cortex-M0+ or M4 cores. The driver automatically
recognizes flash size and a number of flash banks (1-4) using the chip
identification register, and autoconfigures itself.
Use kinetis_ke driver for KE0x and KEAx devices.

The @var{kinetis} driver defines option:
@itemize
@item -sim-base @var{addr} ... base of System Integration Module where chip identification resides. Driver tries two known locations if option is omitted.
@end itemize

@example
flash bank $_FLASHNAME kinetis 0 0 0 0 $_TARGETNAME
@end example

@deffn {Config Command} {kinetis create_banks}
Configuration command enables automatic creation of additional flash banks
based on real flash layout of device. Banks are created during device probe.
Use 'flash probe 0' to force probe.
@end deffn

@deffn {Command} {kinetis fcf_source} [protection|write]
Select what source is used when writing to a Flash Configuration Field.
@option{protection} mode builds FCF content from protection bits previously
set by 'flash protect' command.
This mode is default. MCU is protected from unwanted locking by immediate
writing FCF after erase of relevant sector.
@option{write} mode enables direct write to FCF.
Protection cannot be set by 'flash protect' command. FCF is written along
with the rest of a flash image.
@emph{BEWARE: Incorrect flash configuration may permanently lock the device!}
@end deffn

@deffn {Command} {kinetis fopt} [num]
Set value to write to FOPT byte of Flash Configuration Field.
Used in kinetis 'fcf_source protection' mode only.
@end deffn

@deffn {Command} {kinetis mdm check_security}
Checks status of device security lock. Used internally in examine-end
and examine-fail event.
@end deffn

@deffn {Command} {kinetis mdm halt}
Issues a halt via the MDM-AP. This command can be used to break a watchdog reset
loop when connecting to an unsecured target.
@end deffn

@deffn {Command} {kinetis mdm mass_erase}
Issues a complete flash erase via the MDM-AP. This can be used to erase a chip
back to its factory state, removing security. It does not require the processor
to be halted, however the target will remain in a halted state after this
command completes.
@end deffn

@deffn {Command} {kinetis nvm_partition}
For FlexNVM devices only (KxxDX and KxxFX).
Command shows or sets data flash or EEPROM backup size in kilobytes,
sets two EEPROM blocks sizes in bytes and enables/disables loading
of EEPROM contents to FlexRAM during reset.

For details see device reference manual, Flash Memory Module,
Program Partition command.

Setting is possible only once after mass_erase.
Reset the device after partition setting.

Show partition size:
@example
kinetis nvm_partition info
@end example

Set 32 KB data flash, rest of FlexNVM is EEPROM backup. EEPROM has two blocks
of 512 and 1536 bytes and its contents is loaded to FlexRAM during reset:
@example
kinetis nvm_partition dataflash 32 512 1536 on
@end example

Set 16 KB EEPROM backup, rest of FlexNVM is a data flash. EEPROM has two blocks
of 1024 bytes and its contents is not loaded to FlexRAM during reset:
@example
kinetis nvm_partition eebkp 16 1024 1024 off
@end example
@end deffn

@deffn {Command} {kinetis mdm reset}
Issues a reset via the MDM-AP. This causes the MCU to output a low pulse on the
RESET pin, which can be used to reset other hardware on board.
@end deffn

@deffn {Command} {kinetis disable_wdog}
For Kx devices only (KLx has different COP watchdog, it is not supported).
Command disables watchdog timer.
@end deffn
@end deffn

@deffn {Flash Driver} {kinetis_ke}
@cindex kinetis_ke
KE0x and KEAx members of the Kinetis microcontroller family from NXP include
internal flash and use ARM Cortex-M0+. The driver automatically recognizes
the KE0x sub-family using the chip identification register, and
autoconfigures itself.
Use kinetis (not kinetis_ke) driver for KE1x devices.

@example
flash bank $_FLASHNAME kinetis_ke 0 0 0 0 $_TARGETNAME
@end example

@deffn {Command} {kinetis_ke mdm check_security}
Checks status of device security lock. Used internally in examine-end event.
@end deffn

@deffn {Command} {kinetis_ke mdm mass_erase}
Issues a complete Flash erase via the MDM-AP.
This can be used to erase a chip back to its factory state.
Command removes security lock from a device (use of SRST highly recommended).
It does not require the processor to be halted.
@end deffn

@deffn {Command} {kinetis_ke disable_wdog}
Command disables watchdog timer.
@end deffn
@end deffn

@deffn {Flash Driver} {lpc2000}
This is the driver to support internal flash of all members of the
LPC11(x)00 and LPC1300 microcontroller families and most members of
the LPC800, LPC1500, LPC1700, LPC1800, LPC2000, LPC4000, LPC54100,
LPC8Nxx and NHS31xx microcontroller families from NXP.

@quotation Note
There are LPC2000 devices which are not supported by the @var{lpc2000}
driver:
The LPC2888 is supported by the @var{lpc288x} driver.
The LPC29xx family is supported by the @var{lpc2900} driver.
@end quotation

The @var{lpc2000} driver defines two mandatory and two optional parameters,
which must appear in the following order:

@itemize
@item @var{variant} ... required, may be
@option{lpc2000_v1} (older LPC21xx and LPC22xx)
@option{lpc2000_v2} (LPC213x, LPC214x, LPC210[123], LPC23xx and LPC24xx)
@option{lpc1700} (LPC175x and LPC176x and LPC177x/8x)
@option{lpc4300} - available also as @option{lpc1800} alias (LPC18x[2357] and
LPC43x[2357])
@option{lpc800} (LPC8xx)
@option{lpc1100} (LPC11(x)xx and LPC13xx)
@option{lpc1500} (LPC15xx)
@option{lpc54100} (LPC541xx)
@option{lpc4000} (LPC40xx)
or @option{auto} - automatically detects flash variant and size for LPC11(x)00,
LPC8xx, LPC13xx, LPC17xx, LPC40xx, LPC8Nxx and NHS31xx
@item @var{clock_kHz} ... the frequency, in kiloHertz,
at which the core is running
@item @option{calc_checksum} ... optional (but you probably want to provide this!),
telling the driver to calculate a valid checksum for the exception vector table.
@quotation Note
If you don't provide @option{calc_checksum} when you're writing the vector
table, the boot ROM will almost certainly ignore your flash image.
However, if you do provide it,
with most tool chains @command{verify_image} will fail.
@end quotation
@item @option{iap_entry} ... optional telling the driver to use a different
ROM IAP entry point.
@end itemize

LPC flashes don't require the chip and bus width to be specified.

@example
flash bank $_FLASHNAME lpc2000 0x0 0x7d000 0 0 $_TARGETNAME \
      lpc2000_v2 14765 calc_checksum
@end example

@deffn {Command} {lpc2000 part_id} bank
Displays the four byte part identifier associated with
the specified flash @var{bank}.
@end deffn
@end deffn

@deffn {Flash Driver} {lpc288x}
The LPC2888 microcontroller from NXP needs slightly different flash
support from its lpc2000 siblings.
The @var{lpc288x} driver defines one mandatory parameter,
the programming clock rate in Hz.
LPC flashes don't require the chip and bus width to be specified.

@example
flash bank $_FLASHNAME lpc288x 0 0 0 0 $_TARGETNAME ********
@end example
@end deffn

@deffn {Flash Driver} {lpc2900}
This driver supports the LPC29xx ARM968E based microcontroller family
from NXP.

The predefined parameters @var{base}, @var{size}, @var{chip_width} and
@var{bus_width} of the @code{flash bank} command are ignored. Flash size and
sector layout are auto-configured by the driver.
The driver has one additional mandatory parameter: The CPU clock rate
(in kHz) at the time the flash operations will take place. Most of the time this
will not be the crystal frequency, but a higher PLL frequency. The
@code{reset-init} event handler in the board script is usually the place where
you start the PLL.

The driver rejects flashless devices (currently the LPC2930).

The EEPROM in LPC2900 devices is not mapped directly into the address space.
It must be handled much more like NAND flash memory, and will therefore be
handled by a separate @code{lpc2900_eeprom} driver (not yet available).

Sector protection in terms of the LPC2900 is handled transparently. Every time a
sector needs to be erased or programmed, it is automatically unprotected.
What is shown as protection status in the @code{flash info} command, is
actually the LPC2900 @emph{sector security}. This is a mechanism to prevent a
sector from ever being erased or programmed again. As this is an irreversible
mechanism, it is handled by a special command (@code{lpc2900 secure_sector}),
and not by the standard @code{flash protect} command.

Example for a 125 MHz clock frequency:
@example
flash bank $_FLASHNAME lpc2900 0 0 0 0 $_TARGETNAME 125000
@end example

Some @code{lpc2900}-specific commands are defined. In the following command list,
the @var{bank} parameter is the bank number as obtained by the
@code{flash banks} command.

@deffn {Command} {lpc2900 signature} bank
Calculates a 128-bit hash value, the @emph{signature}, from the whole flash
content. This is a hardware feature of the flash block, hence the calculation is
very fast. You may use this to verify the content of a programmed device against
a known signature.
Example:
@example
lpc2900 signature 0
  signature: 0x5f40cdc8:0xc64e592e:0x10490f89:0x32a0f317
@end example
@end deffn

@deffn {Command} {lpc2900 read_custom} bank filename
Reads the 912 bytes of customer information from the flash index sector, and
saves it to a file in binary format.
Example:
@example
lpc2900 read_custom 0 /path_to/customer_info.bin
@end example
@end deffn

The index sector of the flash is a @emph{write-only} sector. It cannot be
erased! In order to guard against unintentional write access, all following
commands need to be preceded by a successful call to the @code{password}
command:

@deffn {Command} {lpc2900 password} bank password
You need to use this command right before each of the following commands:
@code{lpc2900 write_custom}, @code{lpc2900 secure_sector},
@code{lpc2900 secure_jtag}.

The password string is fixed to "I_know_what_I_am_doing".
Example:
@example
lpc2900 password 0 I_know_what_I_am_doing
  Potentially dangerous operation allowed in next command!
@end example
@end deffn

@deffn {Command} {lpc2900 write_custom} bank filename type
Writes the content of the file into the customer info space of the flash index
sector. The filetype can be specified with the @var{type} field. Possible values
for @var{type} are: @var{bin} (binary), @var{ihex} (Intel hex format),
@var{elf} (ELF binary) or @var{s19} (Motorola S-records). The file must
contain a single section, and the contained data length must be exactly
912 bytes.
@quotation Attention
This cannot be reverted! Be careful!
@end quotation
Example:
@example
lpc2900 write_custom 0 /path_to/customer_info.bin bin
@end example
@end deffn

@deffn {Command} {lpc2900 secure_sector} bank first last
Secures the sector range from @var{first} to @var{last} (including) against
further program and erase operations. The sector security will be effective
after the next power cycle.
@quotation Attention
This cannot be reverted! Be careful!
@end quotation
Secured sectors appear as @emph{protected} in the @code{flash info} command.
Example:
@example
lpc2900 secure_sector 0 1 1
flash info 0
  #0 : lpc2900 at 0x20000000, size 0x000c0000, (...)
          #  0: 0x00000000 (0x2000 8kB) not protected
          #  1: 0x00002000 (0x2000 8kB) protected
          #  2: 0x00004000 (0x2000 8kB) not protected
@end example
@end deffn

@deffn {Command} {lpc2900 secure_jtag} bank
Irreversibly disable the JTAG port. The new JTAG security setting will be
effective after the next power cycle.
@quotation Attention
This cannot be reverted! Be careful!
@end quotation
Examples:
@example
lpc2900 secure_jtag 0
@end example
@end deffn
@end deffn

@deffn {Flash Driver} {mdr}
This drivers handles the integrated NOR flash on Milandr Cortex-M
based controllers. A known limitation is that the Info memory can't be
read or verified as it's not memory mapped.

@example
flash bank <name> mdr <base> <size> \
      0 0 <target#> @var{type} @var{page_count} @var{sec_count}
@end example

@itemize @bullet
@item @var{type} - 0 for main memory, 1 for info memory
@item @var{page_count} - total number of pages
@item @var{sec_count} - number of sector per page count
@end itemize

Example usage:
@example
if @{ [info exists IMEMORY] && [string equal $IMEMORY true] @} @{
   flash bank $@{_CHIPNAME@}_info.flash mdr 0x00000000 0x01000 \
         0 0 $_TARGETNAME 1 1 4
@} else @{
   flash bank $_CHIPNAME.flash mdr 0x00000000 0x20000 \
         0 0 $_TARGETNAME 0 32 4
@}
@end example
@end deffn

@deffn {Flash Driver} {msp432}
All versions of the SimpleLink MSP432 microcontrollers from Texas
Instruments include internal flash. The msp432 flash driver automatically
recognizes the specific version's flash parameters and autoconfigures itself.
Main program flash starts at address 0. The information flash region on
MSP432P4 versions starts at address 0x200000.

@example
flash bank $_FLASHNAME msp432 0 0 0 0 $_TARGETNAME
@end example

@deffn {Command} {msp432 mass_erase} bank_id [main|all]
Performs a complete erase of flash. By default, @command{mass_erase} will erase
only the main program flash.

On MSP432P4 versions, using @command{mass_erase all} will erase both the
main program and information flash regions. To also erase the BSL in information
flash, the user must first use the @command{bsl} command.
@end deffn

@deffn {Command} {msp432 bsl} bank_id [unlock|lock]
On MSP432P4 versions, @command{bsl} unlocks and locks the bootstrap loader (BSL)
region in information flash so that flash commands can erase or write the BSL.
Leave the BSL locked to prevent accidentally corrupting the bootstrap loader.

To erase and program the BSL:
@example
msp432 bsl unlock
flash erase_address 0x202000 0x2000
flash write_image bsl.bin 0x202000
msp432 bsl lock
@end example
@end deffn
@end deffn

@deffn {Flash Driver} {niietcm4}
This drivers handles the integrated NOR flash on NIIET Cortex-M4
based controllers. Flash size and sector layout are auto-configured by the driver.
Main flash memory is called "Bootflash" and has main region and info region.
Info region is NOT memory mapped by default,
but it can replace first part of main region if needed.
Full erase, single and block writes are supported for both main and info regions.
There is additional not memory mapped flash called "Userflash", which
also have division into regions: main and info.
Purpose of userflash - to store system and user settings.
Driver has special commands to perform operations with this memory.

@example
flash bank $_FLASHNAME niietcm4 0 0 0 0 $_TARGETNAME
@end example

Some niietcm4-specific commands are defined:

@deffn {Command} {niietcm4 uflash_read_byte} bank ('main'|'info') address
Read byte from main or info userflash region.
@end deffn

@deffn {Command} {niietcm4 uflash_write_byte} bank ('main'|'info') address value
Write byte to main or info userflash region.
@end deffn

@deffn {Command} {niietcm4 uflash_full_erase} bank
Erase all userflash including info region.
@end deffn

@deffn {Command} {niietcm4 uflash_erase} bank ('main'|'info') first_sector last_sector
Erase sectors of main or info userflash region, starting at sector first up to and including last.
@end deffn

@deffn {Command} {niietcm4 uflash_protect_check} bank ('main'|'info')
Check sectors protect.
@end deffn

@deffn {Command} {niietcm4 uflash_protect} bank ('main'|'info') first_sector last_sector ('on'|'off')
Protect sectors of main or info userflash region, starting at sector first up to and including last.
@end deffn

@deffn {Command} {niietcm4 bflash_info_remap} bank ('on'|'off')
Enable remapping bootflash info region to 0x00000000 (or 0x40000000 if external memory boot used).
@end deffn

@deffn {Command} {niietcm4 extmem_cfg} bank ('gpioa'|'gpiob'|'gpioc'|'gpiod'|'gpioe'|'gpiof'|'gpiog'|'gpioh') pin_num ('func1'|'func3')
Configure external memory interface for boot.
@end deffn

@deffn {Command} {niietcm4 service_mode_erase} bank
Perform emergency erase of all flash (bootflash and userflash).
@end deffn

@deffn {Command} {niietcm4 driver_info} bank
Show information about flash driver.
@end deffn

@end deffn

@deffn {Flash Driver} {npcx}
All versions of the NPCX microcontroller families from Nuvoton include internal
flash. The NPCX flash driver supports the NPCX family of devices. The driver
automatically recognizes the specific version's flash parameters and
autoconfigures itself. The flash bank starts at address 0x64000000.

@example
flash bank $_FLASHNAME npcx 0x64000000 0 0 0 $_TARGETNAME
@end example
@end deffn

@deffn {Flash Driver} {nrf5}
All members of the nRF51 microcontroller families from Nordic Semiconductor
include internal flash and use ARM Cortex-M0 core. nRF52 family powered
by ARM Cortex-M4 or M4F core is supported too. nRF52832 is fully supported
including BPROT flash protection scheme. nRF52833 and nRF52840 devices are
supported with the exception of security extensions (flash access control list
- ACL).

@example
flash bank $_FLASHNAME nrf5 0 0x00000000 0 0 $_TARGETNAME
@end example

Some nrf5-specific commands are defined:

@deffn {Command} {nrf5 mass_erase}
Erases the contents of the code memory and user information
configuration registers as well. It must be noted that this command
works only for chips that do not have factory pre-programmed region 0
code.
@end deffn

@deffn {Command} {nrf5 info}
Decodes and shows information from FICR and UICR registers.
@end deffn

@end deffn

@deffn {Flash Driver} {ocl}
This driver is an implementation of the ``on chip flash loader''
protocol proposed by Pavel Chromy.

It is a minimalistic command-response protocol intended to be used
over a DCC when communicating with an internal or external flash
loader running from RAM. An example implementation for AT91SAM7x is
available in @file{contrib/loaders/flash/at91sam7x/}.

@example
flash bank $_FLASHNAME ocl 0 0 0 0 $_TARGETNAME
@end example
@end deffn

@deffn {Flash Driver} {pic32mx}
The PIC32MX microcontrollers are based on the MIPS 4K cores,
and integrate flash memory.

@example
flash bank $_FLASHNAME pix32mx 0x1fc00000 0 0 0 $_TARGETNAME
flash bank $_FLASHNAME pix32mx 0x1d000000 0 0 0 $_TARGETNAME
@end example

@comment numerous *disabled* commands are defined:
@comment - chip_erase ... pointless given flash_erase_address
@comment - lock, unlock ... pointless given protect on/off (yes?)
@comment - pgm_word ... shouldn't bank be deduced from address??
Some pic32mx-specific commands are defined:
@deffn {Command} {pic32mx pgm_word} address value bank
Programs the specified 32-bit @var{value} at the given @var{address}
in the specified chip @var{bank}.
@end deffn
@deffn {Command} {pic32mx unlock} bank
Unlock and erase specified chip @var{bank}.
This will remove any Code Protection.
@end deffn
@end deffn

@deffn {Flash Driver} {psoc4}
All members of the PSoC 41xx/42xx microcontroller family from Cypress
include internal flash and use ARM Cortex-M0 cores.
The driver automatically recognizes a number of these chips using
the chip identification register, and autoconfigures itself.

Note: Erased internal flash reads as 00.
System ROM of PSoC 4 does not implement erase of a flash sector.

@example
flash bank $_FLASHNAME psoc4 0 0 0 0 $_TARGETNAME
@end example

psoc4-specific commands
@deffn {Command} {psoc4 flash_autoerase} num (on|off)
Enables or disables autoerase mode for a flash bank.

If flash_autoerase is off, use mass_erase before flash programming.
Flash erase command fails if region to erase is not whole flash memory.

If flash_autoerase is on, a sector is both erased and programmed in one
system ROM call. Flash erase command is ignored.
This mode is suitable for gdb load.

The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {psoc4 mass_erase} num
Erases the contents of the flash memory, protection and security lock.

The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn
@end deffn

@deffn {Flash Driver} {psoc5lp}
All members of the PSoC 5LP microcontroller family from Cypress
include internal program flash and use ARM Cortex-M3 cores.
The driver probes for a number of these chips and autoconfigures itself,
apart from the base address.

@example
flash bank $_FLASHNAME psoc5lp 0x00000000 0 0 0 $_TARGETNAME
@end example

@b{Note:} PSoC 5LP chips can be configured to have ECC enabled or disabled.
@quotation Attention
If flash operations are performed in ECC-disabled mode, they will also affect
the ECC flash region. Erasing a 16k flash sector in the 0x00000000 area will
then also erase the corresponding 2k data bytes in the 0x48000000 area.
Writing to the ECC data bytes in ECC-disabled mode is not implemented.
@end quotation

Commands defined in the @var{psoc5lp} driver:

@deffn {Command} {psoc5lp mass_erase}
Erases all flash data and ECC/configuration bytes, all flash protection rows,
and all row latches in all flash arrays on the device.
@end deffn
@end deffn

@deffn {Flash Driver} {psoc5lp_eeprom}
All members of the PSoC 5LP microcontroller family from Cypress
include internal EEPROM and use ARM Cortex-M3 cores.
The driver probes for a number of these chips and autoconfigures itself,
apart from the base address.

@example
flash bank $_CHIPNAME.eeprom psoc5lp_eeprom 0x40008000 0 0 0 \
           $_TARGETNAME
@end example
@end deffn

@deffn {Flash Driver} {psoc5lp_nvl}
All members of the PSoC 5LP microcontroller family from Cypress
include internal Nonvolatile Latches and use ARM Cortex-M3 cores.
The driver probes for a number of these chips and autoconfigures itself.

@example
flash bank $_CHIPNAME.nvl psoc5lp_nvl 0 0 0 0 $_TARGETNAME
@end example

PSoC 5LP chips have multiple NV Latches:

@itemize
@item Device Configuration NV Latch - 4 bytes
@item Write Once (WO) NV Latch - 4 bytes
@end itemize

@b{Note:} This driver only implements the Device Configuration NVL.

The @var{psoc5lp} driver reads the ECC mode from Device Configuration NVL.
@quotation Attention
Switching ECC mode via write to Device Configuration NVL will require a reset
after successful write.
@end quotation
@end deffn

@deffn {Flash Driver} {psoc6}
Supports PSoC6 (CY8C6xxx) family of Cypress microcontrollers.
PSoC6 is a dual-core device with CM0+ and CM4 cores. Both cores share
the same Flash/RAM/MMIO address space.

Flash in PSoC6 is split into three regions:
@itemize @bullet
@item Main Flash - this is the main storage for user application.
Total size varies among devices, sector size: 256 kBytes, row size:
512 bytes. Supports erase operation on individual rows.
@item Work Flash - intended to be used as storage for user data
(e.g. EEPROM emulation). Total size: 32 KBytes, sector size: 32 KBytes,
row size: 512 bytes.
@item Supervisory Flash - special region which contains device-specific
service data. This region does not support erase operation. Only few rows can
be programmed by the user, most of the rows are read only. Programming
operation will erase row automatically.
@end itemize

All three flash regions are supported by the driver. Flash geometry is detected
automatically by parsing data in SPCIF_GEOMETRY register.

PSoC6 is equipped with NOR Flash so erased Flash reads as 0x00.

@example
flash bank main_flash_cm0 psoc6 0x10000000 0 0 0 \
           $@{TARGET@}.cm0
flash bank work_flash_cm0 psoc6 0x14000000 0 0 0 \
           $@{TARGET@}.cm0
flash bank super_flash_user_cm0 psoc6 0x16000800 0 0 0 \
           $@{TARGET@}.cm0
flash bank super_flash_nar_cm0 psoc6 0x16001A00 0 0 0 \
           $@{TARGET@}.cm0
flash bank super_flash_key_cm0 psoc6 0x16005A00 0 0 0 \
           $@{TARGET@}.cm0
flash bank super_flash_toc2_cm0 psoc6 0x16007C00 0 0 0 \
           $@{TARGET@}.cm0

flash bank main_flash_cm4 psoc6 0x10000000 0 0 0 \
           $@{TARGET@}.cm4
flash bank work_flash_cm4 psoc6 0x14000000 0 0 0 \
           $@{TARGET@}.cm4
flash bank super_flash_user_cm4 psoc6 0x16000800 0 0 0 \
           $@{TARGET@}.cm4
flash bank super_flash_nar_cm4 psoc6 0x16001A00 0 0 0 \
           $@{TARGET@}.cm4
flash bank super_flash_key_cm4 psoc6 0x16005A00 0 0 0 \
           $@{TARGET@}.cm4
flash bank super_flash_toc2_cm4 psoc6 0x16007C00 0 0 0 \
           $@{TARGET@}.cm4
@end example

psoc6-specific commands
@deffn {Command} {psoc6 reset_halt}
Command can be used to simulate broken Vector Catch from gdbinit or tcl scripts.
When invoked for CM0+ target, it will set break point at application entry point
and issue SYSRESETREQ. This will reset both cores and all peripherals. CM0+ will
reset CM4 during boot anyway so this is safe. On CM4 target, VECTRESET is used
instead of SYSRESETREQ to avoid unwanted reset of CM0+;
@end deffn

@deffn {Command} {psoc6 mass_erase} num
Erases the contents given flash bank. The @var{num} parameter is a value shown
by @command{flash banks}.
Note: only Main and Work flash regions support Erase operation.
@end deffn
@end deffn

@deffn {Flash Driver} {rp2040}
Supports RP2040 "Raspberry Pi Pico" microcontroller.
RP2040 is a dual-core device with two CM0+ cores. Both cores share the same
Flash/RAM/MMIO address space.  Non-volatile storage is achieved with an
external QSPI flash; a Boot ROM provides helper functions.

@example
flash bank $_FLASHNAME rp2040_flash $_FLASHBASE $_FLASHSIZE 1 32 $_TARGETNAME
@end example
@end deffn

@deffn {Flash Driver} {sim3x}
All members of the SiM3 microcontroller family from Silicon Laboratories
include internal flash and use ARM Cortex-M3 cores. It supports both JTAG
and SWD interface.
The @var{sim3x} driver tries to probe the device to auto detect the MCU.
If this fails, it will use the @var{size} parameter as the size of flash bank.

@example
flash bank $_FLASHNAME sim3x 0 $_CPUROMSIZE 0 0 $_TARGETNAME
@end example

There are 2 commands defined in the @var{sim3x} driver:

@deffn {Command} {sim3x mass_erase}
Erases the complete flash. This is used to unlock the flash.
And this command is only possible when using the SWD interface.
@end deffn

@deffn {Command} {sim3x lock}
Lock the flash. To unlock use the @command{sim3x mass_erase} command.
@end deffn
@end deffn

@deffn {Flash Driver} {stellaris}
All members of the Stellaris LM3Sxxx, LM4x and Tiva C microcontroller
families from Texas Instruments include internal flash. The driver
automatically recognizes a number of these chips using the chip
identification register, and autoconfigures itself.

@example
flash bank $_FLASHNAME stellaris 0 0 0 0 $_TARGETNAME
@end example

@deffn {Command} {stellaris recover}
Performs the @emph{Recovering a "Locked" Device} procedure to restore
the flash and its associated nonvolatile registers to their factory
default values (erased). This is the only way to remove flash
protection or re-enable debugging if that capability has been
disabled.

Note that the final "power cycle the chip" step in this procedure
must be performed by hand, since OpenOCD can't do it.
@quotation Warning
if more than one Stellaris chip is connected, the procedure is
applied to all of them.
@end quotation
@end deffn
@end deffn

@deffn {Flash Driver} {stm32f1x}
All members of the STM32F0, STM32F1 and STM32F3 microcontroller families
from STMicroelectronics and all members of the GD32F1x0, GD32F3x0 and GD32E23x microcontroller
families from GigaDevice include internal flash and use ARM Cortex-M0/M3/M4/M23 cores.
The driver also works with GD32VF103 powered by RISC-V core.
The driver automatically recognizes a number of these chips using
the chip identification register, and autoconfigures itself.

@example
flash bank $_FLASHNAME stm32f1x 0 0 0 0 $_TARGETNAME
@end example

Note that some devices have been found that have a flash size register that contains
an invalid value, to workaround this issue you can override the probed value used by
the flash driver.

@example
flash bank $_FLASHNAME stm32f1x 0 0x20000 0 0 $_TARGETNAME
@end example

If you have a target with dual flash banks then define the second bank
as per the following example.
@example
flash bank $_FLASHNAME stm32f1x 0x08080000 0 0 0 $_TARGETNAME
@end example

Some stm32f1x-specific commands are defined:

@deffn {Command} {stm32f1x lock} num
Locks the entire stm32 device against reading.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {stm32f1x unlock} num
Unlocks the entire stm32 device for reading. This command will cause
a mass erase of the entire stm32 device if previously locked.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {stm32f1x mass_erase} num
Mass erases the entire stm32 device.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {stm32f1x options_read} num
Reads and displays active stm32 option bytes loaded during POR
or upon executing the @command{stm32f1x options_load} command.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {stm32f1x options_write} num (@option{SWWDG}|@option{HWWDG}) (@option{RSTSTNDBY}|@option{NORSTSTNDBY}) (@option{RSTSTOP}|@option{NORSTSTOP}) (@option{USEROPT} user_data)
Writes the stm32 option byte with the specified values.
The @var{num} parameter is a value shown by @command{flash banks}.
The @var{user_data} parameter is content of higher 16 bits of the option byte register (Data0 and Data1 as one 16bit number).
@end deffn

@deffn {Command} {stm32f1x options_load} num
Generates a special kind of reset to re-load the stm32 option bytes written
by the @command{stm32f1x options_write} or @command{flash protect} commands
without having to power cycle the target. Not applicable to stm32f1x devices.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn
@end deffn

@deffn {Flash Driver} {stm32f2x}
All members of the STM32F2, STM32F4 and STM32F7 microcontroller families from STMicroelectronics
include internal flash and use ARM Cortex-M3/M4/M7 cores.
The driver automatically recognizes a number of these chips using
the chip identification register, and autoconfigures itself.

@example
flash bank $_FLASHNAME stm32f2x 0 0 0 0 $_TARGETNAME
@end example

If you use OTP (One-Time Programmable) memory define it as a second bank
as per the following example.
@example
flash bank $_FLASHNAME stm32f2x 0x1FFF7800 0 0 0 $_TARGETNAME
@end example

@deffn {Command} {stm32f2x otp} num (@option{enable}|@option{disable}|@option{show})
Enables or disables OTP write commands for bank @var{num}.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

Note that some devices have been found that have a flash size register that contains
an invalid value, to workaround this issue you can override the probed value used by
the flash driver.

@example
flash bank $_FLASHNAME stm32f2x 0 0x20000 0 0 $_TARGETNAME
@end example

Some stm32f2x-specific commands are defined:

@deffn {Command} {stm32f2x lock} num
Locks the entire stm32 device.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {stm32f2x unlock} num
Unlocks the entire stm32 device.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {stm32f2x mass_erase} num
Mass erases the entire stm32f2x device.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {stm32f2x options_read} num
Reads and displays user options and (where implemented) boot_addr0, boot_addr1, optcr2.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {stm32f2x options_write} num user_options boot_addr0 boot_addr1
Writes user options and (where implemented) boot_addr0 and boot_addr1 in raw format.
Warning: The meaning of the various bits depends on the device, always check datasheet!
The @var{num} parameter is a value shown by @command{flash banks}, @var{user_options} a
12 bit value, consisting of bits 31-28 and 7-0 of FLASH_OPTCR, @var{boot_addr0} and
@var{boot_addr1} two halfwords (of FLASH_OPTCR1).
@end deffn

@deffn {Command} {stm32f2x optcr2_write} num optcr2
Writes FLASH_OPTCR2 options. Warning: Clearing PCROPi bits requires a full mass erase!
The @var{num} parameter is a value shown by @command{flash banks}, @var{optcr2} a 32-bit word.
@end deffn
@end deffn

@deffn {Flash Driver} {stm32h7x}
All members of the STM32H7 microcontroller families from STMicroelectronics
include internal flash and use ARM Cortex-M7 core.
The driver automatically recognizes a number of these chips using
the chip identification register, and autoconfigures itself.

@example
flash bank $_FLASHNAME stm32h7x 0 0 0 0 $_TARGETNAME
@end example

Note that some devices have been found that have a flash size register that contains
an invalid value, to workaround this issue you can override the probed value used by
the flash driver.

@example
flash bank $_FLASHNAME stm32h7x 0 0x20000 0 0 $_TARGETNAME
@end example

Some stm32h7x-specific commands are defined:

@deffn {Command} {stm32h7x lock} num
Locks the entire stm32 device.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {stm32h7x unlock} num
Unlocks the entire stm32 device.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {stm32h7x mass_erase} num
Mass erases the entire stm32h7x device.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {stm32h7x option_read} num reg_offset
Reads an option byte register from the stm32h7x device.
The @var{num} parameter is a value shown by @command{flash banks}, @var{reg_offset}
is the register offset of the option byte to read from the used bank registers' base.
For example: in STM32H74x/H75x the bank 1 registers' base is 0x52002000 and 0x52002100 for bank 2.

Example usage:
@example
# read OPTSR_CUR
stm32h7x option_read 0 0x1c
# read WPSN_CUR1R
stm32h7x option_read 0 0x38
# read WPSN_CUR2R
stm32h7x option_read 1 0x38
@end example
@end deffn

@deffn {Command} {stm32h7x option_write} num reg_offset value [reg_mask]
Writes an option byte register of the stm32h7x device.
The @var{num} parameter is a value shown by @command{flash banks}, @var{reg_offset}
is the register offset of the option byte to write from the used bank register base,
and @var{reg_mask} is the mask to apply when writing the register (only bits with a '1'
will be touched).

Example usage:
@example
# swap bank 1 and bank 2 in dual bank devices
# by setting SWAP_BANK_OPT bit in OPTSR_PRG
stm32h7x option_write 0 0x20 0x8000000 0x8000000
@end example
@end deffn
@end deffn

@deffn {Flash Driver} {stm32lx}
All members of the STM32L0 and STM32L1 microcontroller families from STMicroelectronics
include internal flash and use ARM Cortex-M3 and Cortex-M0+ cores.
The driver automatically recognizes a number of these chips using
the chip identification register, and autoconfigures itself.

@example
flash bank $_FLASHNAME stm32lx 0 0 0 0 $_TARGETNAME
@end example

Note that some devices have been found that have a flash size register that contains
an invalid value, to workaround this issue you can override the probed value used by
the flash driver. If you use 0 as the bank base address, it tells the
driver to autodetect the bank location assuming you're configuring the
second bank.

@example
flash bank $_FLASHNAME stm32lx 0x08000000 0x20000 0 0 $_TARGETNAME
@end example

Some stm32lx-specific commands are defined:

@deffn {Command} {stm32lx lock} num
Locks the entire stm32 device.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {stm32lx unlock} num
Unlocks the entire stm32 device.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {stm32lx mass_erase} num
Mass erases the entire stm32lx device (all flash banks and EEPROM
data). This is the only way to unlock a protected flash (unless RDP
Level is 2 which can't be unlocked at all).
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn
@end deffn

@deffn {Flash Driver} {stm32l4x}
All members of the STM32 G0, G4, L4, L4+, L5, U5, WB and WL
microcontroller families from STMicroelectronics include internal flash
and use ARM Cortex-M0+, M4 and M33 cores.
The driver automatically recognizes a number of these chips using
the chip identification register, and autoconfigures itself.

@example
flash bank $_FLASHNAME stm32l4x 0 0 0 0 $_TARGETNAME
@end example

If you use OTP (One-Time Programmable) memory define it as a second bank
as per the following example.
@example
flash bank $_FLASHNAME stm32l4x 0x1FFF7000 0 0 0 $_TARGETNAME
@end example

@deffn {Command} {stm32l4x otp} num (@option{enable}|@option{disable}|@option{show})
Enables or disables OTP write commands for bank @var{num}.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

Note that some devices have been found that have a flash size register that contains
an invalid value, to workaround this issue you can override the probed value used by
the flash driver. However, specifying a wrong value might lead to a completely
wrong flash layout, so this feature must be used carefully.

@example
flash bank $_FLASHNAME stm32l4x 0x08000000 0x40000 0 0 $_TARGETNAME
@end example

Some stm32l4x-specific commands are defined:

@deffn {Command} {stm32l4x lock} num
Locks the entire stm32 device.
The @var{num} parameter is a value shown by @command{flash banks}.

@emph{Note:} To apply the protection change immediately, use @command{stm32l4x option_load}.
@end deffn

@deffn {Command} {stm32l4x unlock} num
Unlocks the entire stm32 device.
The @var{num} parameter is a value shown by @command{flash banks}.

@emph{Note:} To apply the protection change immediately, use @command{stm32l4x option_load}.
@end deffn

@deffn {Command} {stm32l4x mass_erase} num
Mass erases the entire stm32l4x device.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn {Command} {stm32l4x option_read} num reg_offset
Reads an option byte register from the stm32l4x device.
The @var{num} parameter is a value shown by @command{flash banks}, @var{reg_offset}
is the register offset of the Option byte to read.

For example to read the FLASH_OPTR register:
@example
stm32l4x option_read 0 0x20
# Option Register (for STM32L4x): <0x40022020> = 0xffeff8aa
# Option Register (for STM32WBx): <0x58004020> = ...
# The correct flash base address will be used automatically
@end example

The above example will read out the FLASH_OPTR register which contains the RDP
option byte, Watchdog configuration, BOR level etc.
@end deffn

@deffn {Command} {stm32l4x option_write} num reg_offset reg_mask
Write an option byte register of the stm32l4x device.
The @var{num} parameter is a value shown by @command{flash banks}, @var{reg_offset}
is the register offset of the Option byte to write, and @var{reg_mask} is the mask
to apply when writing the register (only bits with a '1' will be touched).

@emph{Note:} To apply the option bytes change immediately, use @command{stm32l4x option_load}.

For example to write the WRP1AR option bytes:
@example
stm32l4x option_write 0 0x28 0x00FF0000 0x00FF00FF
@end example

The above example will write the WRP1AR option register configuring the Write protection
Area A for bank 1. The above example set WRP1AR_END=255, WRP1AR_START=0.
This will effectively write protect all sectors in flash bank 1.
@end deffn

@deffn {Command} {stm32l4x wrp_info} num [device_bank]
List the protected areas using WRP.
The @var{num} parameter is a value shown by @command{flash banks}.
@var{device_bank} parameter is optional, possible values 'bank1' or 'bank2',
if not specified, the command will display the whole flash protected areas.

@b{Note:} @var{device_bank} is different from banks created using @code{flash bank}.
Devices supported in this flash driver, can have main flash memory organized
in single or dual-banks mode.
Thus the usage of @var{device_bank} is meaningful only in dual-bank mode, to get
write protected areas in a specific @var{device_bank}

@end deffn

@deffn {Command} {stm32l4x option_load} num
Forces a re-load of the option byte registers. Will cause a system reset of the device.
The @var{num} parameter is a value shown by @command{flash banks}.
@end deffn

@deffn Command {stm32l4x trustzone} num [@option{enable} | @option{disable}]
Enables or disables Global TrustZone Security, using the TZEN option bit.
If neither @option{enabled} nor @option{disable} are specified, the command will display
the TrustZone status.
@emph{Note:} This command works only with devices with TrustZone, eg. STM32L5.
@emph{Note:} This command will perform an OBL_Launch after modifying the TZEN.
@end deffn
@end deffn

@deffn {Flash Driver} {str7x}
All members of the STR7 microcontroller family from STMicroelectronics
include internal flash and use ARM7TDMI cores.
The @var{str7x} driver defines one mandatory parameter, @var{variant},
which is either @code{STR71x}, @code{STR73x} or @code{STR75x}.

@example
flash bank $_FLASHNAME str7x \
      0x40000000 0x00040000 0 0 $_TARGETNAME STR71x
@end example

@deffn {Command} {str7x disable_jtag} bank
Activate the Debug/Readout protection mechanism
for the specified flash bank.
@end deffn
@end deffn

@deffn {Flash Driver} {str9x}
Most members of the STR9 microcontroller family from STMicroelectronics
include internal flash and use ARM966E cores.
The str9 needs the flash controller to be configured using
the @command{str9x flash_config} command prior to Flash programming.

@example
flash bank $_FLASHNAME str9x 0x40000000 0x00040000 0 0 $_TARGETNAME
str9x flash_config 0 4 2 0 0x80000
@end example

@deffn {Command} {str9x flash_config} num bbsr nbbsr bbadr nbbadr
Configures the str9 flash controller.
The @var{num} parameter is a value shown by @command{flash banks}.

@itemize @bullet
@item @var{bbsr} - Boot Bank Size register
@item @var{nbbsr} - Non Boot Bank Size register
@item @var{bbadr} - Boot Bank Start Address register
@item @var{nbbadr} - Boot Bank Start Address register
@end itemize
@end deffn

@end deffn

@deffn {Flash Driver} {str9xpec}
@cindex str9xpec

Only use this driver for locking/unlocking the device or configuring the option bytes.
Use the standard str9 driver for programming.
Before using the flash commands the turbo mode must be enabled using the
@command{str9xpec enable_turbo} command.

Here is some background info to help
you better understand how this driver works. OpenOCD has two flash drivers for
the str9:
@enumerate
@item
Standard driver @option{str9x} programmed via the str9 core. Normally used for
flash programming as it is faster than the @option{str9xpec} driver.
@item
Direct programming @option{str9xpec} using the flash controller. This is an
ISC compliant (IEEE 1532) tap connected in series with the str9 core. The str9
core does not need to be running to program using this flash driver. Typical use
for this driver is locking/unlocking the target and programming the option bytes.
@end enumerate

Before we run any commands using the @option{str9xpec} driver we must first disable
the str9 core. This example assumes the @option{str9xpec} driver has been
configured for flash bank 0.
@example
# assert srst, we do not want core running
# while accessing str9xpec flash driver
adapter assert srst
# turn off target polling
poll off
# disable str9 core
str9xpec enable_turbo 0
# read option bytes
str9xpec options_read 0
# re-enable str9 core
str9xpec disable_turbo 0
poll on
reset halt
@end example
The above example will read the str9 option bytes.
When performing a unlock remember that you will not be able to halt the str9 - it
has been locked. Halting the core is not required for the @option{str9xpec} driver
as mentioned above, just issue the commands above manually or from a telnet prompt.

Several str9xpec-specific commands are defined:

@deffn {Command} {str9xpec disable_turbo} num
Restore the str9 into JTAG chain.
@end deffn

@deffn {Command} {str9xpec enable_turbo} num
Enable turbo mode, will simply remove the str9 from the chain and talk
directly to the embedded flash controller.
@end deffn

@deffn {Command} {str9xpec lock} num
Lock str9 device. The str9 will only respond to an unlock command that will
erase the device.
@end deffn

@deffn {Command} {str9xpec part_id} num
Prints the part identifier for bank @var{num}.
@end deffn

@deffn {Command} {str9xpec options_cmap} num (@option{bank0}|@option{bank1})
Configure str9 boot bank.
@end deffn

@deffn {Command} {str9xpec options_lvdsel} num (@option{vdd}|@option{vdd_vddq})
Configure str9 lvd source.
@end deffn

@deffn {Command} {str9xpec options_lvdthd} num (@option{2.4v}|@option{2.7v})
Configure str9 lvd threshold.
@end deffn

@deffn {Command} {str9xpec options_lvdwarn} bank (@option{vdd}|@option{vdd_vddq})
Configure str9 lvd reset warning source.
@end deffn

@deffn {Command} {str9xpec options_read} num
Read str9 option bytes.
@end deffn

@deffn {Command} {str9xpec options_write} num
Write str9 option bytes.
@end deffn

@deffn {Command} {str9xpec unlock} num
unlock str9 device.
@end deffn

@end deffn

@deffn {Flash Driver} {swm050}
@cindex swm050
All members of the swm050 microcontroller family from Foshan Synwit Tech.

@example
flash bank $_FLASHNAME swm050 0x0 0x2000 0 0 $_TARGETNAME
@end example

One swm050-specific command is defined:

@deffn {Command} {swm050 mass_erase} bank_id
Erases the entire flash bank.
@end deffn

@end deffn


@deffn {Flash Driver} {tms470}
Most members of the TMS470 microcontroller family from Texas Instruments
include internal flash and use ARM7TDMI cores.
This driver doesn't require the chip and bus width to be specified.

Some tms470-specific commands are defined:

@deffn {Command} {tms470 flash_keyset} key0 key1 key2 key3
Saves programming keys in a register, to enable flash erase and write commands.
@end deffn

@deffn {Command} {tms470 osc_megahertz} clock_mhz
Reports the clock speed, which is used to calculate timings.
@end deffn

@deffn {Command} {tms470 plldis} (0|1)
Disables (@var{1}) or enables (@var{0}) use of the PLL to speed up
the flash clock.
@end deffn
@end deffn

@deffn {Flash Driver} {w600}
W60x series Wi-Fi SoC from WinnerMicro
are designed with ARM Cortex-M3 and have 1M Byte QFLASH inside.
The @var{w600} driver uses the @var{target} parameter to select the
correct bank config.

@example
flash bank $_FLASHNAME w600 0x08000000 0 0 0 $_TARGETNAMEs
@end example
@end deffn

@deffn {Flash Driver} {xmc1xxx}
All members of the XMC1xxx microcontroller family from Infineon.
This driver does not require the chip and bus width to be specified.
@end deffn

@deffn {Flash Driver} {xmc4xxx}
All members of the XMC4xxx microcontroller family from Infineon.
This driver does not require the chip and bus width to be specified.

Some xmc4xxx-specific commands are defined:

@deffn {Command} {xmc4xxx flash_password} bank_id passwd1 passwd2
Saves flash protection passwords which are used to lock the user flash
@end deffn

@deffn {Command} {xmc4xxx flash_unprotect} bank_id user_level[0-1]
Removes Flash write protection from the selected user bank
@end deffn

@end deffn

@section NAND Flash Commands
@cindex NAND

Compared to NOR or SPI flash, NAND devices are inexpensive
and high density. Today's NAND chips, and multi-chip modules,
commonly hold multiple GigaBytes of data.

NAND chips consist of a number of ``erase blocks'' of a given
size (such as 128 KBytes), each of which is divided into a
number of pages (of perhaps 512 or 2048 bytes each). Each
page of a NAND flash has an ``out of band'' (OOB) area to hold
Error Correcting Code (ECC) and other metadata, usually 16 bytes
of OOB for every 512 bytes of page data.

One key characteristic of NAND flash is that its error rate
is higher than that of NOR flash. In normal operation, that
ECC is used to correct and detect errors. However, NAND
blocks can also wear out and become unusable; those blocks
are then marked "bad". NAND chips are even shipped from the
manufacturer with a few bad blocks. The highest density chips
use a technology (MLC) that wears out more quickly, so ECC
support is increasingly important as a way to detect blocks
that have begun to fail, and help to preserve data integrity
with techniques such as wear leveling.

Software is used to manage the ECC. Some controllers don't
support ECC directly; in those cases, software ECC is used.
Other controllers speed up the ECC calculations with hardware.
Single-bit error correction hardware is routine. Controllers
geared for newer MLC chips may correct 4 or more errors for
every 512 bytes of data.

You will need to make sure that any data you write using
OpenOCD includes the appropriate kind of ECC. For example,
that may mean passing the @code{oob_softecc} flag when
writing NAND data, or ensuring that the correct hardware
ECC mode is used.

The basic steps for using NAND devices include:
@enumerate
@item Declare via the command @command{nand device}
@* Do this in a board-specific configuration file,
passing parameters as needed by the controller.
@item Configure each device using @command{nand probe}.
@* Do this only after the associated target is set up,
such as in its reset-init script or in procures defined
to access that device.
@item Operate on the flash via @command{nand subcommand}
@* Often commands to manipulate the flash are typed by a human, or run
via a script in some automated way. Common task include writing a
boot loader, operating system, or other data needed to initialize or
de-brick a board.
@end enumerate

@b{NOTE:} At the time this text was written, the largest NAND
flash fully supported by OpenOCD is 2 GiBytes (16 GiBits).
This is because the variables used to hold offsets and lengths
are only 32 bits wide.
(Larger chips may work in some cases, unless an offset or length
is larger than 0xffffffff, the largest 32-bit unsigned integer.)
Some larger devices will work, since they are actually multi-chip
modules with two smaller chips and individual chipselect lines.

@anchor{nandconfiguration}
@subsection NAND Configuration Commands
@cindex NAND configuration

NAND chips must be declared in configuration scripts,
plus some additional configuration that's done after
OpenOCD has initialized.

@deffn {Config Command} {nand device} name driver target [configparams...]
Declares a NAND device, which can be read and written to
after it has been configured through @command{nand probe}.
In OpenOCD, devices are single chips; this is unlike some
operating systems, which may manage multiple chips as if
they were a single (larger) device.
In some cases, configuring a device will activate extra
commands; see the controller-specific documentation.

@b{NOTE:} This command is not available after OpenOCD
initialization has completed. Use it in board specific
configuration files, not interactively.

@itemize @bullet
@item @var{name} ... may be used to reference the NAND bank
in most other NAND commands. A number is also available.
@item @var{driver} ... identifies the NAND controller driver
associated with the NAND device being declared.
@xref{nanddriverlist,,NAND Driver List}.
@item @var{target} ... names the target used when issuing
commands to the NAND controller.
@comment Actually, it's currently a controller-specific parameter...
@item @var{configparams} ... controllers may support, or require,
additional parameters. See the controller-specific documentation
for more information.
@end itemize
@end deffn

@deffn {Command} {nand list}
Prints a summary of each device declared
using @command{nand device}, numbered from zero.
Note that un-probed devices show no details.
@example
> nand list
#0: NAND 1GiB 3,3V 8-bit (Micron) pagesize: 2048, buswidth: 8,
        blocksize: 131072, blocks: 8192
#1: NAND 1GiB 3,3V 8-bit (Micron) pagesize: 2048, buswidth: 8,
        blocksize: 131072, blocks: 8192
>
@end example
@end deffn

@deffn {Command} {nand probe} num
Probes the specified device to determine key characteristics
like its page and block sizes, and how many blocks it has.
The @var{num} parameter is the value shown by @command{nand list}.
You must (successfully) probe a device before you can use
it with most other NAND commands.
@end deffn

@subsection Erasing, Reading, Writing to NAND Flash

@deffn {Command} {nand dump} num filename offset length [oob_option]
@cindex NAND reading
Reads binary data from the NAND device and writes it to the file,
starting at the specified offset.
The @var{num} parameter is the value shown by @command{nand list}.

Use a complete path name for @var{filename}, so you don't depend
on the directory used to start the OpenOCD server.

The @var{offset} and @var{length} must be exact multiples of the
device's page size. They describe a data region; the OOB data
associated with each such page may also be accessed.

@b{NOTE:} At the time this text was written, no error correction
was done on the data that's read, unless raw access was disabled
and the underlying NAND controller driver had a @code{read_page}
method which handled that error correction.

By default, only page data is saved to the specified file.
Use an @var{oob_option} parameter to save OOB data:
@itemize @bullet
@item no oob_* parameter
@*Output file holds only page data; OOB is discarded.
@item @code{oob_raw}
@*Output file interleaves page data and OOB data;
the file will be longer than "length" by the size of the
spare areas associated with each data page.
Note that this kind of "raw" access is different from
what's implied by @command{nand raw_access}, which just
controls whether a hardware-aware access method is used.
@item @code{oob_only}
@*Output file has only raw OOB data, and will
be smaller than "length" since it will contain only the
spare areas associated with each data page.
@end itemize
@end deffn

@deffn {Command} {nand erase} num [offset length]
@cindex NAND erasing
@cindex NAND programming
Erases blocks on the specified NAND device, starting at the
specified @var{offset} and continuing for @var{length} bytes.
Both of those values must be exact multiples of the device's
block size, and the region they specify must fit entirely in the chip.
If those parameters are not specified,
the whole NAND chip will be erased.
The @var{num} parameter is the value shown by @command{nand list}.

@b{NOTE:} This command will try to erase bad blocks, when told
to do so, which will probably invalidate the manufacturer's bad
block marker.
For the remainder of the current server session, @command{nand info}
will still report that the block ``is'' bad.
@end deffn

@deffn {Command} {nand write} num filename offset [option...]
@cindex NAND writing
@cindex NAND programming
Writes binary data from the file into the specified NAND device,
starting at the specified offset. Those pages should already
have been erased; you can't change zero bits to one bits.
The @var{num} parameter is the value shown by @command{nand list}.

Use a complete path name for @var{filename}, so you don't depend
on the directory used to start the OpenOCD server.

The @var{offset} must be an exact multiple of the device's page size.
All data in the file will be written, assuming it doesn't run
past the end of the device.
Only full pages are written, and any extra space in the last
page will be filled with 0xff bytes. (That includes OOB data,
if that's being written.)

@b{NOTE:} At the time this text was written, bad blocks are
ignored. That is, this routine will not skip bad blocks,
but will instead try to write them. This can cause problems.

Provide at most one @var{option} parameter. With some
NAND drivers, the meanings of these parameters may change
if @command{nand raw_access} was used to disable hardware ECC.
@itemize @bullet
@item no oob_* parameter
@*File has only page data, which is written.
If raw access is in use, the OOB area will not be written.
Otherwise, if the underlying NAND controller driver has
a @code{write_page} routine, that routine may write the OOB
with hardware-computed ECC data.
@item @code{oob_only}
@*File has only raw OOB data, which is written to the OOB area.
Each page's data area stays untouched. @i{This can be a dangerous
option}, since it can invalidate the ECC data.
You may need to force raw access to use this mode.
@item @code{oob_raw}
@*File interleaves data and OOB data, both of which are written
If raw access is enabled, the data is written first, then the
un-altered OOB.
Otherwise, if the underlying NAND controller driver has
a @code{write_page} routine, that routine may modify the OOB
before it's written, to include hardware-computed ECC data.
@item @code{oob_softecc}
@*File has only page data, which is written.
The OOB area is filled with 0xff, except for a standard 1-bit
software ECC code stored in conventional locations.
You might need to force raw access to use this mode, to prevent
the underlying driver from applying hardware ECC.
@item @code{oob_softecc_kw}
@*File has only page data, which is written.
The OOB area is filled with 0xff, except for a 4-bit software ECC
specific to the boot ROM in Marvell Kirkwood SoCs.
You might need to force raw access to use this mode, to prevent
the underlying driver from applying hardware ECC.
@end itemize
@end deffn

@deffn {Command} {nand verify} num filename offset [option...]
@cindex NAND verification
@cindex NAND programming
Verify the binary data in the file has been programmed to the
specified NAND device, starting at the specified offset.
The @var{num} parameter is the value shown by @command{nand list}.

Use a complete path name for @var{filename}, so you don't depend
on the directory used to start the OpenOCD server.

The @var{offset} must be an exact multiple of the device's page size.
All data in the file will be read and compared to the contents of the
flash, assuming it doesn't run past the end of the device.
As with @command{nand write}, only full pages are verified, so any extra
space in the last page will be filled with 0xff bytes.

The same @var{options} accepted by @command{nand write},
and the file will be processed similarly to produce the buffers that
can be compared against the contents produced from @command{nand dump}.

@b{NOTE:} This will not work when the underlying NAND controller
driver's @code{write_page} routine must update the OOB with a
hardware-computed ECC before the data is written. This limitation may
be removed in a future release.
@end deffn

@subsection Other NAND commands
@cindex NAND other commands

@deffn {Command} {nand check_bad_blocks} num [offset length]
Checks for manufacturer bad block markers on the specified NAND
device. If no parameters are provided, checks the whole
device; otherwise, starts at the specified @var{offset} and
continues for @var{length} bytes.
Both of those values must be exact multiples of the device's
block size, and the region they specify must fit entirely in the chip.
The @var{num} parameter is the value shown by @command{nand list}.

@b{NOTE:} Before using this command you should force raw access
with @command{nand raw_access enable} to ensure that the underlying
driver will not try to apply hardware ECC.
@end deffn

@deffn {Command} {nand info} num
The @var{num} parameter is the value shown by @command{nand list}.
This prints the one-line summary from "nand list", plus for
devices which have been probed this also prints any known
status for each block.
@end deffn

@deffn {Command} {nand raw_access} num (@option{enable}|@option{disable})
Sets or clears an flag affecting how page I/O is done.
The @var{num} parameter is the value shown by @command{nand list}.

This flag is cleared (disabled) by default, but changing that
value won't affect all NAND devices. The key factor is whether
the underlying driver provides @code{read_page} or @code{write_page}
methods. If it doesn't provide those methods, the setting of
this flag is irrelevant; all access is effectively ``raw''.

When those methods exist, they are normally used when reading
data (@command{nand dump} or reading bad block markers) or
writing it (@command{nand write}). However, enabling
raw access (setting the flag) prevents use of those methods,
bypassing hardware ECC logic.
@i{This can be a dangerous option}, since writing blocks
with the wrong ECC data can cause them to be marked as bad.
@end deffn

@anchor{nanddriverlist}
@subsection NAND Driver List
As noted above, the @command{nand device} command allows
driver-specific options and behaviors.
Some controllers also activate controller-specific commands.

@deffn {NAND Driver} {at91sam9}
This driver handles the NAND controllers found on AT91SAM9 family chips from
Atmel. It takes two extra parameters: address of the NAND chip;
address of the ECC controller.
@example
nand device $NANDFLASH at91sam9 $CHIPNAME 0x40000000 0xfffffe800
@end example
AT91SAM9 chips support single-bit ECC hardware. The @code{write_page} and
@code{read_page} methods are used to utilize the ECC hardware unless they are
disabled by using the @command{nand raw_access} command. There are four
additional commands that are needed to fully configure the AT91SAM9 NAND
controller. Two are optional; most boards use the same wiring for ALE/CLE:
@deffn {Config Command} {at91sam9 cle} num addr_line
Configure the address line used for latching commands. The @var{num}
parameter is the value shown by @command{nand list}.
@end deffn
@deffn {Config Command} {at91sam9 ale} num addr_line
Configure the address line used for latching addresses. The @var{num}
parameter is the value shown by @command{nand list}.
@end deffn

For the next two commands, it is assumed that the pins have already been
properly configured for input or output.
@deffn {Config Command} {at91sam9 rdy_busy} num pio_base_addr pin
Configure the RDY/nBUSY input from the NAND device. The @var{num}
parameter is the value shown by @command{nand list}. @var{pio_base_addr}
is the base address of the PIO controller and @var{pin} is the pin number.
@end deffn
@deffn {Config Command} {at91sam9 ce} num pio_base_addr pin
Configure the chip enable input to the NAND device. The @var{num}
parameter is the value shown by @command{nand list}. @var{pio_base_addr}
is the base address of the PIO controller and @var{pin} is the pin number.
@end deffn
@end deffn

@deffn {NAND Driver} {davinci}
This driver handles the NAND controllers found on DaVinci family
chips from Texas Instruments.
It takes three extra parameters:
address of the NAND chip;
hardware ECC mode to use (@option{hwecc1},
@option{hwecc4}, @option{hwecc4_infix});
address of the AEMIF controller on this processor.
@example
nand device davinci dm355.arm 0x02000000 hwecc4 0x01e10000
@end example
All DaVinci processors support the single-bit ECC hardware,
and newer ones also support the four-bit ECC hardware.
The @code{write_page} and @code{read_page} methods are used
to implement those ECC modes, unless they are disabled using
the @command{nand raw_access} command.
@end deffn

@deffn {NAND Driver} {lpc3180}
These controllers require an extra @command{nand device}
parameter: the clock rate used by the controller.
@deffn {Command} {lpc3180 select} num [mlc|slc]
Configures use of the MLC or SLC controller mode.
MLC implies use of hardware ECC.
The @var{num} parameter is the value shown by @command{nand list}.
@end deffn

At this writing, this driver includes @code{write_page}
and @code{read_page} methods. Using @command{nand raw_access}
to disable those methods will prevent use of hardware ECC
in the MLC controller mode, but won't change SLC behavior.
@end deffn
@comment current lpc3180 code won't issue 5-byte address cycles

@deffn {NAND Driver} {mx3}
This driver handles the NAND controller in i.MX31. The mxc driver
should work for this chip as well.
@end deffn

@deffn {NAND Driver} {mxc}
This driver handles the NAND controller found in Freescale i.MX
chips. It has support for v1 (i.MX27 and i.MX31) and v2 (i.MX35).
The driver takes 3 extra arguments, chip (@option{mx27},
@option{mx31}, @option{mx35}), ecc (@option{noecc}, @option{hwecc})
and optionally if bad block information should be swapped between
main area and spare area (@option{biswap}), defaults to off.
@example
nand device mx35.nand mxc imx35.cpu mx35 hwecc biswap
@end example
@deffn {Command} {mxc biswap} bank_num [enable|disable]
Turns on/off bad block information swapping from main area,
without parameter query status.
@end deffn
@end deffn

@deffn {NAND Driver} {orion}
These controllers require an extra @command{nand device}
parameter: the address of the controller.
@example
nand device orion 0xd8000000
@end example
These controllers don't define any specialized commands.
At this writing, their drivers don't include @code{write_page}
or @code{read_page} methods, so @command{nand raw_access} won't
change any behavior.
@end deffn

@deffn {NAND Driver} {s3c2410}
@deffnx {NAND Driver} {s3c2412}
@deffnx {NAND Driver} {s3c2440}
@deffnx {NAND Driver} {s3c2443}
@deffnx {NAND Driver} {s3c6400}
These S3C family controllers don't have any special
@command{nand device} options, and don't define any
specialized commands.
At this writing, their drivers don't include @code{write_page}
or @code{read_page} methods, so @command{nand raw_access} won't
change any behavior.
@end deffn

@node Flash Programming
@chapter Flash Programming

OpenOCD implements numerous ways to program the target flash, whether internal or external.
Programming can be achieved by either using @ref{programmingusinggdb,,Programming using GDB},
or using the commands given in @ref{flashprogrammingcommands,,Flash Programming Commands}.

@*To simplify using the flash commands directly a jimtcl script is available that handles the programming and verify stage.
OpenOCD will program/verify/reset the target and optionally shutdown.

The script is executed as follows and by default the following actions will be performed.
@enumerate
@item 'init' is executed.
@item 'reset init' is called to reset and halt the target, any 'reset init' scripts are executed.
@item @code{flash write_image} is called to erase and write any flash using the filename given.
@item If the @option{preverify} parameter is given, the target is "verified" first and only flashed if this fails.
@item @code{verify_image} is called if @option{verify} parameter is given.
@item @code{reset run} is called if @option{reset} parameter is given.
@item OpenOCD is shutdown if @option{exit} parameter is given.
@end enumerate

An example of usage is given below. @xref{program}.

@example
# program and verify using elf/hex/s19. verify and reset
# are optional parameters
openocd -f board/stm32f3discovery.cfg \
	-c "program filename.elf verify reset exit"

# binary files need the flash address passing
openocd -f board/stm32f3discovery.cfg \
	-c "program filename.bin exit 0x08000000"
@end example

@node PLD/FPGA Commands
@chapter PLD/FPGA Commands
@cindex PLD
@cindex FPGA

Programmable Logic Devices (PLDs) and the more flexible
Field Programmable Gate Arrays (FPGAs) are both types of programmable hardware.
OpenOCD can support programming them.
Although PLDs are generally restrictive (cells are less functional, and
there are no special purpose cells for memory or computational tasks),
they share the same OpenOCD infrastructure.
Accordingly, both are called PLDs here.

@section PLD/FPGA Configuration and Commands

As it does for JTAG TAPs, debug targets, and flash chips (both NOR and NAND),
OpenOCD maintains a list of PLDs available for use in various commands.
Also, each such PLD requires a driver.

They are referenced by the number shown by the @command{pld devices} command,
and new PLDs are defined by @command{pld device driver_name}.

@deffn {Config Command} {pld device} driver_name tap_name [driver_options]
Defines a new PLD device, supported by driver @var{driver_name},
using the TAP named @var{tap_name}.
The driver may make use of any @var{driver_options} to configure its
behavior.
@end deffn

@deffn {Command} {pld devices}
Lists the PLDs and their numbers.
@end deffn

@deffn {Command} {pld load} num filename
Loads the file @file{filename} into the PLD identified by @var{num}.
The file format must be inferred by the driver.
@end deffn

@section PLD/FPGA Drivers, Options, and Commands

Drivers may support PLD-specific options to the @command{pld device}
definition command, and may also define commands usable only with
that particular type of PLD.

@deffn {FPGA Driver} {virtex2} [no_jstart]
Virtex-II is a family of FPGAs sold by Xilinx.
It supports the IEEE 1532 standard for In-System Configuration (ISC).

If @var{no_jstart} is non-zero, the JSTART instruction is not used after
loading the bitstream. While required for Series2, Series3, and Series6, it
breaks bitstream loading on Series7.

@deffn {Command} {virtex2 read_stat} num
Reads and displays the Virtex-II status register (STAT)
for FPGA @var{num}.
@end deffn
@end deffn

@node General Commands
@chapter General Commands
@cindex commands

The commands documented in this chapter here are common commands that
you, as a human, may want to type and see the output of. Configuration type
commands are documented elsewhere.

Intent:
@itemize @bullet
@item @b{Source Of Commands}
@* OpenOCD commands can occur in a configuration script (discussed
elsewhere) or typed manually by a human or supplied programmatically,
or via one of several TCP/IP Ports.

@item @b{From the human}
@* A human should interact with the telnet interface (default port: 4444)
or via GDB (default port 3333).

To issue commands from within a GDB session, use the @option{monitor}
command, e.g. use @option{monitor poll} to issue the @option{poll}
command. All output is relayed through the GDB session.

@item @b{Machine Interface}
The Tcl interface's intent is to be a machine interface. The default Tcl
port is 5555.
@end itemize


@section Server Commands

@deffn {Command} {exit}
Exits the current telnet session.
@end deffn

@deffn {Command} {help} [string]
With no parameters, prints help text for all commands.
Otherwise, prints each helptext containing @var{string}.
Not every command provides helptext.

Configuration commands, and commands valid at any time, are
explicitly noted in parenthesis.
In most cases, no such restriction is listed; this indicates commands
which are only available after the configuration stage has completed.
@end deffn

@deffn {Command} {usage} [string]
With no parameters, prints usage text for all commands.  Otherwise,
prints all usage text of which command, help text, and usage text
containing @var{string}.
Not every command provides helptext.
@end deffn

@deffn {Command} {sleep} msec [@option{busy}]
Wait for at least @var{msec} milliseconds before resuming.
If @option{busy} is passed, busy-wait instead of sleeping.
(This option is strongly discouraged.)
Useful in connection with script files
(@command{script} command and @command{target_name} configuration).
@end deffn

@deffn {Command} {shutdown} [@option{error}]
Close the OpenOCD server, disconnecting all clients (GDB, telnet,
other). If option @option{error} is used, OpenOCD will return a
non-zero exit code to the parent process.

Like any TCL commands, also @command{shutdown} can be redefined, e.g.:
@example
# redefine shutdown
rename shutdown original_shutdown
proc shutdown @{@} @{
    puts "This is my implementation of shutdown"
    # my own stuff before exit OpenOCD
    original_shutdown
@}
@end example
If user types CTRL-C or kills OpenOCD, either the command @command{shutdown}
or its replacement will be automatically executed before OpenOCD exits.
@end deffn

@anchor{debuglevel}
@deffn {Command} {debug_level} [n]
@cindex message level
Display debug level.
If @var{n} (from 0..4) is provided, then set it to that level.
This affects the kind of messages sent to the server log.
Level 0 is error messages only;
level 1 adds warnings;
level 2 adds informational messages;
level 3 adds debugging messages;
and level 4 adds verbose low-level debug messages.
The default is level 2, but that can be overridden on
the command line along with the location of that log
file (which is normally the server's standard output).
@xref{Running}.
@end deffn

@deffn {Command} {echo} [-n] message
Logs a message at "user" priority.
Option "-n" suppresses trailing newline.
@example
echo "Downloading kernel -- please wait"
@end example
@end deffn

@deffn {Command} {log_output} [filename | "default"]
Redirect logging to @var{filename} or set it back to default output;
the default log output channel is stderr.
@end deffn

@deffn {Command} {add_script_search_dir} [directory]
Add @var{directory} to the file/script search path.
@end deffn

@deffn {Config Command} {bindto} [@var{name}]
Specify hostname or IPv4 address on which to listen for incoming
TCP/IP connections. By default, OpenOCD will listen on the loopback
interface only. If your network environment is safe, @code{bindto
0.0.0.0} can be used to cover all available interfaces.
@end deffn

@anchor{targetstatehandling}
@section Target State handling
@cindex reset
@cindex halt
@cindex target initialization

In this section ``target'' refers to a CPU configured as
shown earlier (@pxref{CPU Configuration}).
These commands, like many, implicitly refer to
a current target which is used to perform the
various operations. The current target may be changed
by using @command{targets} command with the name of the
target which should become current.

@deffn {Command} {reg} [(number|name) [(value|'force')]]
Access a single register by @var{number} or by its @var{name}.
The target must generally be halted before access to CPU core
registers is allowed. Depending on the hardware, some other
registers may be accessible while the target is running.

@emph{With no arguments}:
list all available registers for the current target,
showing number, name, size, value, and cache status.
For valid entries, a value is shown; valid entries
which are also dirty (and will be written back later)
are flagged as such.

@emph{With number/name}: display that register's value.
Use @var{force} argument to read directly from the target,
bypassing any internal cache.

@emph{With both number/name and value}: set register's value.
Writes may be held in a writeback cache internal to OpenOCD,
so that setting the value marks the register as dirty instead
of immediately flushing that value. Resuming CPU execution
(including by single stepping) or otherwise activating the
relevant module will flush such values.

Cores may have surprisingly many registers in their
Debug and trace infrastructure:

@example
> reg
===== ARM registers
(0) r0 (/32): 0x0000D3C2 (dirty)
(1) r1 (/32): 0xFD61F31C
(2) r2 (/32)
...
(164) ETM_contextid_comparator_mask (/32)
>
@end example
@end deffn

@deffn {Command} {set_reg} dict
Set register values of the target.

@itemize
@item @var{dict} ... Tcl dictionary with pairs of register names and values.
@end itemize

For example, the following command sets the value 0 to the program counter (pc)
register and 0x1000 to the stack pointer (sp) register:

@example
set_reg @{pc 0 sp 0x1000@}
@end example
@end deffn

@deffn {Command} {get_reg} [-force] list
Get register values from the target and return them as Tcl dictionary with pairs
of register names and values.
If option "-force" is set, the register values are read directly from the
target, bypassing any caching.

@itemize
@item @var{list} ... List of register names
@end itemize

For example, the following command retrieves the values from the program
counter (pc) and stack pointer (sp) register:

@example
get_reg @{pc sp@}
@end example
@end deffn

@deffn {Command} {write_memory} address width data ['phys']
This function provides an efficient way to write to the target memory from a Tcl
script.

@itemize
@item @var{address} ... target memory address
@item @var{width} ... memory access bit size, can be 8, 16, 32 or 64
@item @var{data} ... Tcl list with the elements to write
@item ['phys'] ... treat the memory address as physical instead of virtual address
@end itemize

For example, the following command writes two 32 bit words into the target
memory at address 0x20000000:

@example
write_memory 0x20000000 32 @{0xdeadbeef 0x00230500@}
@end example
@end deffn

@deffn {Command} {read_memory} address width count ['phys']
This function provides an efficient way to read the target memory from a Tcl
script.
A Tcl list containing the requested memory elements is returned by this function.

@itemize
@item @var{address} ... target memory address
@item @var{width} ... memory access bit size, can be 8, 16, 32 or 64
@item @var{count} ... number of elements to read
@item ['phys'] ... treat the memory address as physical instead of virtual address
@end itemize

For example, the following command reads two 32 bit words from the target
memory at address 0x20000000:

@example
read_memory 0x20000000 32 2
@end example
@end deffn

@deffn {Command} {halt} [ms]
@deffnx {Command} {wait_halt} [ms]
The @command{halt} command first sends a halt request to the target,
which @command{wait_halt} doesn't.
Otherwise these behave the same: wait up to @var{ms} milliseconds,
or 5 seconds if there is no parameter, for the target to halt
(and enter debug mode).
Using 0 as the @var{ms} parameter prevents OpenOCD from waiting.

@quotation Warning
On ARM cores, software using the @emph{wait for interrupt} operation
often blocks the JTAG access needed by a @command{halt} command.
This is because that operation also puts the core into a low
power mode by gating the core clock;
but the core clock is needed to detect JTAG clock transitions.

One partial workaround uses adaptive clocking: when the core is
interrupted the operation completes, then JTAG clocks are accepted
at least until the interrupt handler completes.
However, this workaround is often unusable since the processor, board,
and JTAG adapter must all support adaptive JTAG clocking.
Also, it can't work until an interrupt is issued.

A more complete workaround is to not use that operation while you
work with a JTAG debugger.
Tasking environments generally have idle loops where the body is the
@emph{wait for interrupt} operation.
(On older cores, it is a coprocessor action;
newer cores have a @option{wfi} instruction.)
Such loops can just remove that operation, at the cost of higher
power consumption (because the CPU is needlessly clocked).
@end quotation

@end deffn

@deffn {Command} {resume} [address]
Resume the target at its current code position,
or the optional @var{address} if it is provided.
OpenOCD will wait 5 seconds for the target to resume.
@end deffn

@deffn {Command} {step} [address]
Single-step the target at its current code position,
or the optional @var{address} if it is provided.
@end deffn

@anchor{resetcommand}
@deffn {Command} {reset}
@deffnx {Command} {reset run}
@deffnx {Command} {reset halt}
@deffnx {Command} {reset init}
Perform as hard a reset as possible, using SRST if possible.
@emph{All defined targets will be reset, and target
events will fire during the reset sequence.}

The optional parameter specifies what should
happen after the reset.
If there is no parameter, a @command{reset run} is executed.
The other options will not work on all systems.
@xref{Reset Configuration}.

@itemize @minus
@item @b{run} Let the target run
@item @b{halt} Immediately halt the target
@item @b{init} Immediately halt the target, and execute the reset-init script
@end itemize
@end deffn

@deffn {Command} {soft_reset_halt}
Requesting target halt and executing a soft reset. This is often used
when a target cannot be reset and halted. The target, after reset is
released begins to execute code. OpenOCD attempts to stop the CPU and
then sets the program counter back to the reset vector. Unfortunately
the code that was executed may have left the hardware in an unknown
state.
@end deffn

@deffn {Command} {adapter assert} [signal [assert|deassert signal]]
@deffnx {Command} {adapter deassert} [signal [assert|deassert signal]]
Set values of reset signals.
Without parameters returns current status of the signals.
The @var{signal} parameter values may be
@option{srst}, indicating that srst signal is to be asserted or deasserted,
@option{trst}, indicating that trst signal is to be asserted or deasserted.

The @command{reset_config} command should already have been used
to configure how the board and the adapter treat these two
signals, and to say if either signal is even present.
@xref{Reset Configuration}.
Trying to assert a signal that is not present triggers an error.
If a signal is present on the adapter and not specified in the command,
the signal will not be modified.

@quotation Note
TRST is specially handled.
It actually signifies JTAG's @sc{reset} state.
So if the board doesn't support the optional TRST signal,
or it doesn't support it along with the specified SRST value,
JTAG reset is triggered with TMS and TCK signals
instead of the TRST signal.
And no matter how that JTAG reset is triggered, once
the scan chain enters @sc{reset} with TRST inactive,
TAP @code{post-reset} events are delivered to all TAPs
with handlers for that event.
@end quotation
@end deffn

@anchor{memoryaccess}
@section Memory access commands
@cindex memory access

These commands allow accesses of a specific size to the memory
system. Often these are used to configure the current target in some
special way. For example - one may need to write certain values to the
SDRAM controller to enable SDRAM.

@enumerate
@item Use the @command{targets} (plural) command
to change the current target.
@item In system level scripts these commands are deprecated.
Please use their TARGET object siblings to avoid making assumptions
about what TAP is the current target, or about MMU configuration.
@end enumerate

@deffn {Command} {mdd} [phys] addr [count]
@deffnx {Command} {mdw} [phys] addr [count]
@deffnx {Command} {mdh} [phys] addr [count]
@deffnx {Command} {mdb} [phys] addr [count]
Display contents of address @var{addr}, as
64-bit doublewords (@command{mdd}),
32-bit words (@command{mdw}), 16-bit halfwords (@command{mdh}),
or 8-bit bytes (@command{mdb}).
When the current target has an MMU which is present and active,
@var{addr} is interpreted as a virtual address.
Otherwise, or if the optional @var{phys} flag is specified,
@var{addr} is interpreted as a physical address.
If @var{count} is specified, displays that many units.
(If you want to process the data instead of displaying it,
see the @code{read_memory} primitives.)
@end deffn

@deffn {Command} {mwd} [phys] addr doubleword [count]
@deffnx {Command} {mww} [phys] addr word [count]
@deffnx {Command} {mwh} [phys] addr halfword [count]
@deffnx {Command} {mwb} [phys] addr byte [count]
Writes the specified @var{doubleword} (64 bits), @var{word} (32 bits),
@var{halfword} (16 bits), or @var{byte} (8-bit) value,
at the specified address @var{addr}.
When the current target has an MMU which is present and active,
@var{addr} is interpreted as a virtual address.
Otherwise, or if the optional @var{phys} flag is specified,
@var{addr} is interpreted as a physical address.
If @var{count} is specified, fills that many units of consecutive address.
@end deffn

@anchor{imageaccess}
@section Image loading commands
@cindex image loading
@cindex image dumping

@deffn {Command} {dump_image} filename address size
Dump @var{size} bytes of target memory starting at @var{address} to the
binary file named @var{filename}.
@end deffn

@deffn {Command} {fast_load}
Loads an image stored in memory by @command{fast_load_image} to the
current target. Must be preceded by fast_load_image.
@end deffn

@deffn {Command} {fast_load_image} filename address [@option{bin}|@option{ihex}|@option{elf}|@option{s19}]
Normally you should be using @command{load_image} or GDB load. However, for
testing purposes or when I/O overhead is significant(OpenOCD running on an embedded
host), storing the image in memory and uploading the image to the target
can be a way to upload e.g. multiple debug sessions when the binary does not change.
Arguments are the same as @command{load_image}, but the image is stored in OpenOCD host
memory, i.e. does not affect target. This approach is also useful when profiling
target programming performance as I/O and target programming can easily be profiled
separately.
@end deffn

@deffn {Command} {load_image} filename address [[@option{bin}|@option{ihex}|@option{elf}|@option{s19}] @option{min_addr} @option{max_length}]
Load image from file @var{filename} to target memory offset by @var{address} from its load address.
The file format may optionally be specified
(@option{bin}, @option{ihex}, @option{elf}, or @option{s19}).
In addition the following arguments may be specified:
@var{min_addr} - ignore data below @var{min_addr} (this is w.r.t. to the target's load address + @var{address})
@var{max_length} - maximum number of bytes to load.
@example
proc load_image_bin @{fname foffset address length @} @{
    # Load data from fname filename at foffset offset to
    # target at address. Load at most length bytes.
    load_image $fname [expr @{$address - $foffset@}] bin \
               $address $length
@}
@end example
@end deffn

@deffn {Command} {test_image} filename [address [@option{bin}|@option{ihex}|@option{elf}]]
Displays image section sizes and addresses
as if @var{filename} were loaded into target memory
starting at @var{address} (defaults to zero).
The file format may optionally be specified
(@option{bin}, @option{ihex}, or @option{elf})
@end deffn

@deffn {Command} {verify_image} filename address [@option{bin}|@option{ihex}|@option{elf}]
Verify @var{filename} against target memory starting at @var{address}.
The file format may optionally be specified
(@option{bin}, @option{ihex}, or @option{elf})
This will first attempt a comparison using a CRC checksum, if this fails it will try a binary compare.
@end deffn

@deffn {Command} {verify_image_checksum} filename address [@option{bin}|@option{ihex}|@option{elf}]
Verify @var{filename} against target memory starting at @var{address}.
The file format may optionally be specified
(@option{bin}, @option{ihex}, or @option{elf})
This perform a comparison using a CRC checksum only
@end deffn


@section Breakpoint and Watchpoint commands
@cindex breakpoint
@cindex watchpoint

CPUs often make debug modules accessible through JTAG, with
hardware support for a handful of code breakpoints and data
watchpoints.
In addition, CPUs almost always support software breakpoints.

@deffn {Command} {bp} [address len [@option{hw}]]
With no parameters, lists all active breakpoints.
Else sets a breakpoint on code execution starting
at @var{address} for @var{length} bytes.
This is a software breakpoint, unless @option{hw} is specified
in which case it will be a hardware breakpoint.

(@xref{arm9vectorcatch,,arm9 vector_catch}, or @pxref{xscalevectorcatch,,xscale vector_catch},
for similar mechanisms that do not consume hardware breakpoints.)
@end deffn

@deffn {Command} {rbp} @option{all} | address
Remove the breakpoint at @var{address} or all breakpoints.
@end deffn

@deffn {Command} {rwp} address
Remove data watchpoint on @var{address}
@end deffn

@deffn {Command} {wp} [address len [(@option{r}|@option{w}|@option{a}) [value [mask]]]]
With no parameters, lists all active watchpoints.
Else sets a data watchpoint on data from @var{address} for @var{length} bytes.
The watch point is an "access" watchpoint unless
the @option{r} or @option{w} parameter is provided,
defining it as respectively a read or write watchpoint.
If a @var{value} is provided, that value is used when determining if
the watchpoint should trigger. The value may be first be masked
using @var{mask} to mark ``don't care'' fields.
@end deffn


@section Real Time Transfer (RTT)

Real Time Transfer (RTT) is an interface specified by SEGGER based on basic
memory reads and writes to transfer data bidirectionally between target and host.
The specification is independent of the target architecture.
Every target that supports so called "background memory access", which means
that the target memory can be accessed by the debugger while the target is
running, can be used.
This interface is especially of interest for targets without
Serial Wire Output (SWO), such as ARM Cortex-M0, or where semihosting is not
applicable because of real-time constraints.

@quotation Note
The current implementation supports only single target devices.
@end quotation

The data transfer between host and target device is organized through
unidirectional up/down-channels for target-to-host and host-to-target
communication, respectively.

@quotation Note
The current implementation does not respect channel buffer flags.
They are used to determine what happens when writing to a full buffer, for
example.
@end quotation

Channels are exposed via raw TCP/IP connections. One or more RTT servers can be
assigned to each channel to make them accessible to an unlimited number
of TCP/IP connections.

@deffn {Command} {rtt setup} address size ID
Configure RTT for the currently selected target.
Once RTT is started, OpenOCD searches for a control block with the
identifier @var{ID} starting at the memory address @var{address} within the next
@var{size} bytes.
@end deffn

@deffn {Command} {rtt start}
Start RTT.
If the control block location is not known, OpenOCD starts searching for it.
@end deffn

@deffn {Command} {rtt stop}
Stop RTT.
@end deffn

@deffn {Command} {rtt polling_interval} [interval]
Display the polling interval.
If @var{interval} is provided, set the polling interval.
The polling interval determines (in milliseconds) how often the up-channels are
checked for new data.
@end deffn

@deffn {Command} {rtt channels}
Display a list of all channels and their properties.
@end deffn

@deffn {Command} {rtt channellist}
Return a list of all channels and their properties as Tcl list.
The list can be manipulated easily from within scripts.
@end deffn

@deffn {Command} {rtt server start} port channel
Start a TCP server on @var{port} for the channel @var{channel}.
@end deffn

@deffn {Command} {rtt server stop} port
Stop the TCP sever with port @var{port}.
@end deffn

The following example shows how to setup RTT using the SEGGER RTT implementation
on the target device.

@example
resume

rtt setup 0x20000000 2048 "SEGGER RTT"
rtt start

rtt server start 9090 0
@end example

In this example, OpenOCD searches the control block with the ID "SEGGER RTT"
starting at 0x20000000 for 2048 bytes. The RTT channel 0 is exposed through the
TCP/IP port 9090.


@section Misc Commands

@cindex profiling
@deffn {Command} {profile} seconds filename [start end]
Profiling samples the CPU's program counter as quickly as possible,
which is useful for non-intrusive stochastic profiling.
Saves up to 10000 samples in @file{filename} using ``gmon.out''
format. Optional @option{start} and @option{end} parameters allow to
limit the address range.
@end deffn

@deffn {Command} {version}
Displays a string identifying the version of this OpenOCD server.
@end deffn

@deffn {Command} {virt2phys} virtual_address
Requests the current target to map the specified @var{virtual_address}
to its corresponding physical address, and displays the result.
@end deffn

@deffn {Command} {add_help_text} 'command_name' 'help-string'
Add or replace help text on the given @var{command_name}.
@end deffn

@deffn {Command} {add_usage_text} 'command_name' 'help-string'
Add or replace usage text on the given @var{command_name}.
@end deffn

@node Architecture and Core Commands
@chapter Architecture and Core Commands
@cindex Architecture Specific Commands
@cindex Core Specific Commands

Most CPUs have specialized JTAG operations to support debugging.
OpenOCD packages most such operations in its standard command framework.
Some of those operations don't fit well in that framework, so they are
exposed here as architecture or implementation (core) specific commands.

@anchor{armhardwaretracing}
@section ARM Hardware Tracing
@cindex tracing
@cindex ETM
@cindex ETB

CPUs based on ARM cores may include standard tracing interfaces,
based on an ``Embedded Trace Module'' (ETM) which sends voluminous
address and data bus trace records to a ``Trace Port''.

@itemize
@item
Development-oriented boards will sometimes provide a high speed
trace connector for collecting that data, when the particular CPU
supports such an interface.
(The standard connector is a 38-pin Mictor, with both JTAG
and trace port support.)
Those trace connectors are supported by higher end JTAG adapters
and some logic analyzer modules; frequently those modules can
buffer several megabytes of trace data.
Configuring an ETM coupled to such an external trace port belongs
in the board-specific configuration file.
@item
If the CPU doesn't provide an external interface, it probably
has an ``Embedded Trace Buffer'' (ETB) on the chip, which is a
dedicated SRAM. 4KBytes is one common ETB size.
Configuring an ETM coupled only to an ETB belongs in the CPU-specific
(target) configuration file, since it works the same on all boards.
@end itemize

ETM support in OpenOCD doesn't seem to be widely used yet.

@quotation Issues
ETM support may be buggy, and at least some @command{etm config}
parameters should be detected by asking the ETM for them.

ETM trigger events could also implement a kind of complex
hardware breakpoint, much more powerful than the simple
watchpoint hardware exported by EmbeddedICE modules.
@emph{Such breakpoints can be triggered even when using the
dummy trace port driver}.

It seems like a GDB hookup should be possible,
as well as tracing only during specific states
(perhaps @emph{handling IRQ 23} or @emph{calls foo()}).

There should be GUI tools to manipulate saved trace data and help
analyse it in conjunction with the source code.
It's unclear how much of a common interface is shared
with the current XScale trace support, or should be
shared with eventual Nexus-style trace module support.

At this writing (November 2009) only ARM7, ARM9, and ARM11 support
for ETM modules is available. The code should be able to
work with some newer cores; but not all of them support
this original style of JTAG access.
@end quotation

@subsection ETM Configuration
ETM setup is coupled with the trace port driver configuration.

@deffn {Config Command} {etm config} target width mode clocking driver
Declares the ETM associated with @var{target}, and associates it
with a given trace port @var{driver}. @xref{traceportdrivers,,Trace Port Drivers}.

Several of the parameters must reflect the trace port capabilities,
which are a function of silicon capabilities (exposed later
using @command{etm info}) and of what hardware is connected to
that port (such as an external pod, or ETB).
The @var{width} must be either 4, 8, or 16,
except with ETMv3.0 and newer modules which may also
support 1, 2, 24, 32, 48, and 64 bit widths.
(With those versions, @command{etm info} also shows whether
the selected port width and mode are supported.)

The @var{mode} must be @option{normal}, @option{multiplexed},
or @option{demultiplexed}.
The @var{clocking} must be @option{half} or @option{full}.

@quotation Warning
With ETMv3.0 and newer, the bits set with the @var{mode} and
@var{clocking} parameters both control the mode.
This modified mode does not map to the values supported by
previous ETM modules, so this syntax is subject to change.
@end quotation

@quotation Note
You can see the ETM registers using the @command{reg} command.
Not all possible registers are present in every ETM.
Most of the registers are write-only, and are used to configure
what CPU activities are traced.
@end quotation
@end deffn

@deffn {Command} {etm info}
Displays information about the current target's ETM.
This includes resource counts from the @code{ETM_CONFIG} register,
as well as silicon capabilities (except on rather old modules).
from the @code{ETM_SYS_CONFIG} register.
@end deffn

@deffn {Command} {etm status}
Displays status of the current target's ETM and trace port driver:
is the ETM idle, or is it collecting data?
Did trace data overflow?
Was it triggered?
@end deffn

@deffn {Command} {etm tracemode} [type context_id_bits cycle_accurate branch_output]
Displays what data that ETM will collect.
If arguments are provided, first configures that data.
When the configuration changes, tracing is stopped
and any buffered trace data is invalidated.

@itemize
@item @var{type} ... describing how data accesses are traced,
when they pass any ViewData filtering that was set up.
The value is one of
@option{none} (save nothing),
@option{data} (save data),
@option{address} (save addresses),
@option{all} (save data and addresses)
@item @var{context_id_bits} ... 0, 8, 16, or 32
@item @var{cycle_accurate} ... @option{enable} or @option{disable}
cycle-accurate instruction tracing.
Before ETMv3, enabling this causes much extra data to be recorded.
@item @var{branch_output} ... @option{enable} or @option{disable}.
Disable this unless you need to try reconstructing the instruction
trace stream without an image of the code.
@end itemize
@end deffn

@deffn {Command} {etm trigger_debug} (@option{enable}|@option{disable})
Displays whether ETM triggering debug entry (like a breakpoint) is
enabled or disabled, after optionally modifying that configuration.
The default behaviour is @option{disable}.
Any change takes effect after the next @command{etm start}.

By using script commands to configure ETM registers, you can make the
processor enter debug state automatically when certain conditions,
more complex than supported by the breakpoint hardware, happen.
@end deffn

@subsection ETM Trace Operation

After setting up the ETM, you can use it to collect data.
That data can be exported to files for later analysis.
It can also be parsed with OpenOCD, for basic sanity checking.

To configure what is being traced, you will need to write
various trace registers using @command{reg ETM_*} commands.
For the definitions of these registers, read ARM publication
@emph{IHI 0014, ``Embedded Trace Macrocell, Architecture Specification''}.
Be aware that most of the relevant registers are write-only,
and that ETM resources are limited. There are only a handful
of address comparators, data comparators, counters, and so on.

Examples of scenarios you might arrange to trace include:

@itemize
@item Code flow within a function, @emph{excluding} subroutines
it calls. Use address range comparators to enable tracing
for instruction access within that function's body.
@item Code flow within a function, @emph{including} subroutines
it calls. Use the sequencer and address comparators to activate
tracing on an ``entered function'' state, then deactivate it by
exiting that state when the function's exit code is invoked.
@item Code flow starting at the fifth invocation of a function,
combining one of the above models with a counter.
@item CPU data accesses to the registers for a particular device,
using address range comparators and the ViewData logic.
@item Such data accesses only during IRQ handling, combining the above
model with sequencer triggers which on entry and exit to the IRQ handler.
@item @emph{... more}
@end itemize

At this writing, September 2009, there are no Tcl utility
procedures to help set up any common tracing scenarios.

@deffn {Command} {etm analyze}
Reads trace data into memory, if it wasn't already present.
Decodes and prints the data that was collected.
@end deffn

@deffn {Command} {etm dump} filename
Stores the captured trace data in @file{filename}.
@end deffn

@deffn {Command} {etm image} filename [base_address] [type]
Opens an image file.
@end deffn

@deffn {Command} {etm load} filename
Loads captured trace data from @file{filename}.
@end deffn

@deffn {Command} {etm start}
Starts trace data collection.
@end deffn

@deffn {Command} {etm stop}
Stops trace data collection.
@end deffn

@anchor{traceportdrivers}
@subsection Trace Port Drivers

To use an ETM trace port it must be associated with a driver.

@deffn {Trace Port Driver} {dummy}
Use the @option{dummy} driver if you are configuring an ETM that's
not connected to anything (on-chip ETB or off-chip trace connector).
@emph{This driver lets OpenOCD talk to the ETM, but it does not expose
any trace data collection.}
@deffn {Config Command} {etm_dummy config} target
Associates the ETM for @var{target} with a dummy driver.
@end deffn
@end deffn

@deffn {Trace Port Driver} {etb}
Use the @option{etb} driver if you are configuring an ETM
to use on-chip ETB memory.
@deffn {Config Command} {etb config} target etb_tap
Associates the ETM for @var{target} with the ETB at @var{etb_tap}.
You can see the ETB registers using the @command{reg} command.
@end deffn
@deffn {Command} {etb trigger_percent} [percent]
This displays, or optionally changes, ETB behavior after the
ETM's configured @emph{trigger} event fires.
It controls how much more trace data is saved after the (single)
trace trigger becomes active.

@itemize
@item The default corresponds to @emph{trace around} usage,
recording 50 percent data before the event and the rest
afterwards.
@item The minimum value of @var{percent} is 2 percent,
recording almost exclusively data before the trigger.
Such extreme @emph{trace before} usage can help figure out
what caused that event to happen.
@item The maximum value of @var{percent} is 100 percent,
recording data almost exclusively after the event.
This extreme @emph{trace after} usage might help sort out
how the event caused trouble.
@end itemize
@c REVISIT allow "break" too -- enter debug mode.
@end deffn

@end deffn

@anchor{armcrosstrigger}
@section ARM Cross-Trigger Interface
@cindex CTI

The ARM Cross-Trigger Interface (CTI) is a generic CoreSight component
that connects event sources like tracing components or CPU cores with each
other through a common trigger matrix (CTM). For ARMv8 architecture, a
CTI is mandatory for core run control and each core has an individual
CTI instance attached to it. OpenOCD has limited support for CTI using
the @emph{cti} group of commands.

@deffn {Command} {cti create} cti_name @option{-dap} dap_name @option{-ap-num} apn @option{-baseaddr} base_address
Creates a CTI instance @var{cti_name} on the DAP instance @var{dap_name} on MEM-AP
@var{apn}. The @var{base_address} must match the base address of the CTI
on the respective MEM-AP. All arguments are mandatory. This creates a
new command @command{$cti_name} which is used for various purposes
including additional configuration.
@end deffn

@deffn {Command} {$cti_name enable} @option{on|off}
Enable (@option{on}) or disable (@option{off}) the CTI.
@end deffn

@deffn {Command} {$cti_name dump}
Displays a register dump of the CTI.
@end deffn

@deffn {Command} {$cti_name write} @var{reg_name} @var{value}
Write @var{value} to the CTI register with the symbolic name @var{reg_name}.
@end deffn

@deffn {Command} {$cti_name read} @var{reg_name}
Print the value read from the CTI register with the symbolic name @var{reg_name}.
@end deffn

@deffn {Command} {$cti_name ack} @var{event}
Acknowledge a CTI @var{event}.
@end deffn

@deffn {Command} {$cti_name channel} @var{channel_number} @var{operation}
Perform a specific channel operation, the possible operations are:
gate, ungate, set, clear and pulse
@end deffn

@deffn {Command} {$cti_name testmode} @option{on|off}
Enable (@option{on}) or disable (@option{off}) the integration test mode
of the CTI.
@end deffn

@deffn {Command} {cti names}
Prints a list of names of all CTI objects created. This command is mainly
useful in TCL scripting.
@end deffn

@section Generic ARM
@cindex ARM

These commands should be available on all ARM processors.
They are available in addition to other core-specific
commands that may be available.

@deffn {Command} {arm core_state} [@option{arm}|@option{thumb}]
Displays the core_state, optionally changing it to process
either @option{arm} or @option{thumb} instructions.
The target may later be resumed in the currently set core_state.
(Processors may also support the Jazelle state, but
that is not currently supported in OpenOCD.)
@end deffn

@deffn {Command} {arm disassemble} address [count [@option{thumb}]]
@cindex disassemble
Disassembles @var{count} instructions starting at @var{address}.
If @var{count} is not specified, a single instruction is disassembled.
If @option{thumb} is specified, or the low bit of the address is set,
Thumb2 (mixed 16/32-bit) instructions are used;
else ARM (32-bit) instructions are used.
(Processors may also support the Jazelle state, but
those instructions are not currently understood by OpenOCD.)

Note that all Thumb instructions are Thumb2 instructions,
so older processors (without Thumb2 support) will still
see correct disassembly of Thumb code.
Also, ThumbEE opcodes are the same as Thumb2,
with a handful of exceptions.
ThumbEE disassembly currently has no explicit support.
@end deffn

@deffn {Command} {arm mcr} pX op1 CRn CRm op2 value
Write @var{value} to a coprocessor @var{pX} register
passing parameters @var{CRn},
@var{CRm}, opcodes @var{opc1} and @var{opc2},
and using the MCR instruction.
(Parameter sequence matches the ARM instruction, but omits
an ARM register.)
@end deffn

@deffn {Command} {arm mrc} pX coproc op1 CRn CRm op2
Read a coprocessor @var{pX} register passing parameters @var{CRn},
@var{CRm}, opcodes @var{opc1} and @var{opc2},
and the MRC instruction.
Returns the result so it can be manipulated by Jim scripts.
(Parameter sequence matches the ARM instruction, but omits
an ARM register.)
@end deffn

@deffn {Command} {arm reg}
Display a table of all banked core registers, fetching the current value from every
core mode if necessary.
@end deffn

@deffn {Command} {arm semihosting} [@option{enable}|@option{disable}]
@cindex ARM semihosting
Display status of semihosting, after optionally changing that status.

Semihosting allows for code executing on an ARM target to use the
I/O facilities on the host computer i.e. the system where OpenOCD
is running. The target application must be linked against a library
implementing the ARM semihosting convention that forwards operation
requests by using a special SVC instruction that is trapped at the
Supervisor Call vector by OpenOCD.
@end deffn

@deffn {Command} {arm semihosting_redirect} (@option{disable} | @option{tcp} <port>
[@option{debug}|@option{stdio}|@option{all})
@cindex ARM semihosting
Redirect semihosting messages to a specified TCP port.

This command redirects debug (READC, WRITEC and WRITE0) and stdio (READ, WRITE)
semihosting operations to the specified TCP port.
The command allows to select which type of operations to redirect (debug, stdio, all (default)).
Note: for stdio operations, only I/O from/to ':tt' file descriptors are redirected.
@end deffn

@deffn {Command} {arm semihosting_cmdline} [@option{enable}|@option{disable}]
@cindex ARM semihosting
Set the command line to be passed to the debugger.

@example
arm semihosting_cmdline argv0 argv1 argv2 ...
@end example

This option lets one set the command line arguments to be passed to
the program. The first argument (argv0) is the program name in a
standard C environment (argv[0]). Depending on the program (not much
programs look at argv[0]), argv0 is ignored and can be any string.
@end deffn

@deffn {Command} {arm semihosting_fileio} [@option{enable}|@option{disable}]
@cindex ARM semihosting
Display status of semihosting fileio, after optionally changing that
status.

Enabling this option forwards semihosting I/O to GDB process using the
File-I/O remote protocol extension. This is especially useful for
interacting with remote files or displaying console messages in the
debugger.
@end deffn

@deffn {Command} {arm semihosting_resexit} [@option{enable}|@option{disable}]
@cindex ARM semihosting
Enable resumable SEMIHOSTING_SYS_EXIT.

When SEMIHOSTING_SYS_EXIT is called outside a debug session,
things are simple, the openocd process calls exit() and passes
the value returned by the target.

When SEMIHOSTING_SYS_EXIT is called during a debug session,
by default execution returns to the debugger, leaving the
debugger in a HALT state, similar to the state entered when
encountering a break.

In some use cases, it is useful to have SEMIHOSTING_SYS_EXIT
return normally, as any semihosting call, and do not break
to the debugger.
The standard allows this to happen, but the condition
to trigger it is a bit obscure ("by performing an RDI_Execute
request or equivalent").

To make the SEMIHOSTING_SYS_EXIT call return normally, enable
this option (default: disabled).
@end deffn

@deffn {Command} {arm semihosting_read_user_param}
@cindex ARM semihosting
Read parameter of the semihosting call from the target. Usable in
semihosting-user-cmd-0x10* event handlers, returning a string.

When the target makes semihosting call with operation number from range 0x100-
0x107, an optional string parameter can be passed to the server. This parameter
is valid during the run of the event handlers and is accessible with this
command.
@end deffn

@section ARMv4 and ARMv5 Architecture
@cindex ARMv4
@cindex ARMv5

The ARMv4 and ARMv5 architectures are widely used in embedded systems,
and introduced core parts of the instruction set in use today.
That includes the Thumb instruction set, introduced in the ARMv4T
variant.

@subsection ARM7 and ARM9 specific commands
@cindex ARM7
@cindex ARM9

These commands are specific to ARM7 and ARM9 cores, like ARM7TDMI, ARM720T,
ARM9TDMI, ARM920T or ARM926EJ-S.
They are available in addition to the ARM commands,
and any other core-specific commands that may be available.

@deffn {Command} {arm7_9 dbgrq} [@option{enable}|@option{disable}]
Displays the value of the flag controlling use of the
EmbeddedIce DBGRQ signal to force entry into debug mode,
instead of breakpoints.
If a boolean parameter is provided, first assigns that flag.

This should be
safe for all but ARM7TDMI-S cores (like NXP LPC).
This feature is enabled by default on most ARM9 cores,
including ARM9TDMI, ARM920T, and ARM926EJ-S.
@end deffn

@deffn {Command} {arm7_9 dcc_downloads} [@option{enable}|@option{disable}]
@cindex DCC
Displays the value of the flag controlling use of the debug communications
channel (DCC) to write larger (>128 byte) amounts of memory.
If a boolean parameter is provided, first assigns that flag.

DCC downloads offer a huge speed increase, but might be
unsafe, especially with targets running at very low speeds. This command was introduced
with OpenOCD rev. 60, and requires a few bytes of working area.
@end deffn

@deffn {Command} {arm7_9 fast_memory_access} [@option{enable}|@option{disable}]
Displays the value of the flag controlling use of memory writes and reads
that don't check completion of the operation.
If a boolean parameter is provided, first assigns that flag.

This provides a huge speed increase, especially with USB JTAG
cables (FT2232), but might be unsafe if used with targets running at very low
speeds, like the 32kHz startup clock of an AT91RM9200.
@end deffn

@subsection ARM9 specific commands
@cindex ARM9

ARM9-family cores are built around ARM9TDMI or ARM9E (including ARM9EJS)
integer processors.
Such cores include the ARM920T, ARM926EJ-S, and ARM966.

@c 9-june-2009: tried this on arm920t, it didn't work.
@c no-params always lists nothing caught, and that's how it acts.
@c 23-oct-2009: doesn't work _consistently_ ... as if the ICE
@c versions have different rules about when they commit writes.

@anchor{arm9vectorcatch}
@deffn {Command} {arm9 vector_catch} [@option{all}|@option{none}|list]
@cindex vector_catch
Vector Catch hardware provides a sort of dedicated breakpoint
for hardware events such as reset, interrupt, and abort.
You can use this to conserve normal breakpoint resources,
so long as you're not concerned with code that branches directly
to those hardware vectors.

This always finishes by listing the current configuration.
If parameters are provided, it first reconfigures the
vector catch hardware to intercept
@option{all} of the hardware vectors,
@option{none} of them,
or a list with one or more of the following:
@option{reset} @option{undef} @option{swi} @option{pabt} @option{dabt}
@option{irq} @option{fiq}.
@end deffn

@subsection ARM920T specific commands
@cindex ARM920T

These commands are available to ARM920T based CPUs,
which are implementations of the ARMv4T architecture
built using the ARM9TDMI integer core.
They are available in addition to the ARM, ARM7/ARM9,
and ARM9 commands.

@deffn {Command} {arm920t cache_info}
Print information about the caches found. This allows to see whether your target
is an ARM920T (2x16kByte cache) or ARM922T (2x8kByte cache).
@end deffn

@deffn {Command} {arm920t cp15} regnum [value]
Display cp15 register @var{regnum};
else if a @var{value} is provided, that value is written to that register.
This uses "physical access" and the register number is as
shown in bits 38..33 of table 9-9 in the ARM920T TRM.
(Not all registers can be written.)
@end deffn

@deffn {Command} {arm920t read_cache} filename
Dump the content of ICache and DCache to a file named @file{filename}.
@end deffn

@deffn {Command} {arm920t read_mmu} filename
Dump the content of the ITLB and DTLB to a file named @file{filename}.
@end deffn

@subsection ARM926ej-s specific commands
@cindex ARM926ej-s

These commands are available to ARM926ej-s based CPUs,
which are implementations of the ARMv5TEJ architecture
based on the ARM9EJ-S integer core.
They are available in addition to the ARM, ARM7/ARM9,
and ARM9 commands.

The Feroceon cores also support these commands, although
they are not built from ARM926ej-s designs.

@deffn {Command} {arm926ejs cache_info}
Print information about the caches found.
@end deffn

@subsection ARM966E specific commands
@cindex ARM966E

These commands are available to ARM966 based CPUs,
which are implementations of the ARMv5TE architecture.
They are available in addition to the ARM, ARM7/ARM9,
and ARM9 commands.

@deffn {Command} {arm966e cp15} regnum [value]
Display cp15 register @var{regnum};
else if a @var{value} is provided, that value is written to that register.
The six bit @var{regnum} values are bits 37..32 from table 7-2 of the
ARM966E-S TRM.
There is no current control over bits 31..30 from that table,
as required for BIST support.
@end deffn

@subsection XScale specific commands
@cindex XScale

Some notes about the debug implementation on the XScale CPUs:

The XScale CPU provides a special debug-only mini-instruction cache
(mini-IC) in which exception vectors and target-resident debug handler
code are placed by OpenOCD. In order to get access to the CPU, OpenOCD
must point vector 0 (the reset vector) to the entry of the debug
handler. However, this means that the complete first cacheline in the
mini-IC is marked valid, which makes the CPU fetch all exception
handlers from the mini-IC, ignoring the code in RAM.

To address this situation, OpenOCD provides the @code{xscale
vector_table} command, which allows the user to explicitly write
individual entries to either the high or low vector table stored in
the mini-IC.

It is recommended to place a pc-relative indirect branch in the vector
table, and put the branch destination somewhere in memory. Doing so
makes sure the code in the vector table stays constant regardless of
code layout in memory:
@example
_vectors:
        ldr     pc,[pc,#0x100-8]
        ldr     pc,[pc,#0x100-8]
        ldr     pc,[pc,#0x100-8]
        ldr     pc,[pc,#0x100-8]
        ldr     pc,[pc,#0x100-8]
        ldr     pc,[pc,#0x100-8]
        ldr     pc,[pc,#0x100-8]
        ldr     pc,[pc,#0x100-8]
        .org 0x100
        .long real_reset_vector
        .long real_ui_handler
        .long real_swi_handler
        .long real_pf_abort
        .long real_data_abort
        .long 0 /* unused */
        .long real_irq_handler
        .long real_fiq_handler
@end example

Alternatively, you may choose to keep some or all of the mini-IC
vector table entries synced with those written to memory by your
system software. The mini-IC can not be modified while the processor
is executing, but for each vector table entry not previously defined
using the @code{xscale vector_table} command, OpenOCD will copy the
value from memory to the mini-IC every time execution resumes from a
halt. This is done for both high and low vector tables (although the
table not in use may not be mapped to valid memory, and in this case
that copy operation will silently fail). This means that you will
need to briefly halt execution at some strategic point during system
start-up; e.g., after the software has initialized the vector table,
but before exceptions are enabled. A breakpoint can be used to
accomplish this once the appropriate location in the start-up code has
been identified. A watchpoint over the vector table region is helpful
in finding the location if you're not sure. Note that the same
situation exists any time the vector table is modified by the system
software.

The debug handler must be placed somewhere in the address space using
the @code{xscale debug_handler} command. The allowed locations for the
debug handler are either (0x800 - 0x1fef800) or (0xfe000800 -
0xfffff800). The default value is 0xfe000800.

XScale has resources to support two hardware breakpoints and two
watchpoints. However, the following restrictions on watchpoint
functionality apply: (1) the value and mask arguments to the @code{wp}
command are not supported, (2) the watchpoint length must be a
power of two and not less than four, and can not be greater than the
watchpoint address, and (3) a watchpoint with a length greater than
four consumes all the watchpoint hardware resources. This means that
at any one time, you can have enabled either two watchpoints with a
length of four, or one watchpoint with a length greater than four.

These commands are available to XScale based CPUs,
which are implementations of the ARMv5TE architecture.

@deffn {Command} {xscale analyze_trace}
Displays the contents of the trace buffer.
@end deffn

@deffn {Command} {xscale cache_clean_address} address
Changes the address used when cleaning the data cache.
@end deffn

@deffn {Command} {xscale cache_info}
Displays information about the CPU caches.
@end deffn

@deffn {Command} {xscale cp15} regnum [value]
Display cp15 register @var{regnum};
else if a @var{value} is provided, that value is written to that register.
@end deffn

@deffn {Command} {xscale debug_handler} target address
Changes the address used for the specified target's debug handler.
@end deffn

@deffn {Command} {xscale dcache} [@option{enable}|@option{disable}]
Enables or disable the CPU's data cache.
@end deffn

@deffn {Command} {xscale dump_trace} filename
Dumps the raw contents of the trace buffer to @file{filename}.
@end deffn

@deffn {Command} {xscale icache} [@option{enable}|@option{disable}]
Enables or disable the CPU's instruction cache.
@end deffn

@deffn {Command} {xscale mmu} [@option{enable}|@option{disable}]
Enables or disable the CPU's memory management unit.
@end deffn

@deffn {Command} {xscale trace_buffer} [@option{enable}|@option{disable} [@option{fill} [n] | @option{wrap}]]
Displays the trace buffer status, after optionally
enabling or disabling the trace buffer
and modifying how it is emptied.
@end deffn

@deffn {Command} {xscale trace_image} filename [offset [type]]
Opens a trace image from @file{filename}, optionally rebasing
its segment addresses by @var{offset}.
The image @var{type} may be one of
@option{bin} (binary), @option{ihex} (Intel hex),
@option{elf} (ELF file), @option{s19} (Motorola s19),
@option{mem}, or @option{builder}.
@end deffn

@anchor{xscalevectorcatch}
@deffn {Command} {xscale vector_catch} [mask]
@cindex vector_catch
Display a bitmask showing the hardware vectors to catch.
If the optional parameter is provided, first set the bitmask to that value.

The mask bits correspond with bit 16..23 in the DCSR:
@example
0x01    Trap Reset
0x02    Trap Undefined Instructions
0x04    Trap Software Interrupt
0x08    Trap Prefetch Abort
0x10    Trap Data Abort
0x20    reserved
0x40    Trap IRQ
0x80    Trap FIQ
@end example
@end deffn

@deffn {Command} {xscale vector_table} [(@option{low}|@option{high}) index value]
@cindex vector_table

Set an entry in the mini-IC vector table. There are two tables: one for
low vectors (at 0x00000000), and one for high vectors (0xFFFF0000), each
holding the 8 exception vectors. @var{index} can be 1-7, because vector 0
points to the debug handler entry and can not be overwritten.
@var{value} holds the 32-bit opcode that is placed in the mini-IC.

Without arguments, the current settings are displayed.

@end deffn

@section ARMv6 Architecture
@cindex ARMv6

@subsection ARM11 specific commands
@cindex ARM11

@deffn {Command} {arm11 memwrite burst} [@option{enable}|@option{disable}]
Displays the value of the memwrite burst-enable flag,
which is enabled by default.
If a boolean parameter is provided, first assigns that flag.
Burst writes are only used for memory writes larger than 1 word.
They improve performance by assuming that the CPU has read each data
word over JTAG and completed its write before the next word arrives,
instead of polling for a status flag to verify that completion.
This is usually safe, because JTAG runs much slower than the CPU.
@end deffn

@deffn {Command} {arm11 memwrite error_fatal} [@option{enable}|@option{disable}]
Displays the value of the memwrite error_fatal flag,
which is enabled by default.
If a boolean parameter is provided, first assigns that flag.
When set, certain memory write errors cause earlier transfer termination.
@end deffn

@deffn {Command} {arm11 step_irq_enable} [@option{enable}|@option{disable}]
Displays the value of the flag controlling whether
IRQs are enabled during single stepping;
they are disabled by default.
If a boolean parameter is provided, first assigns that.
@end deffn

@deffn {Command} {arm11 vcr} [value]
@cindex vector_catch
Displays the value of the @emph{Vector Catch Register (VCR)},
coprocessor 14 register 7.
If @var{value} is defined, first assigns that.

Vector Catch hardware provides dedicated breakpoints
for certain hardware events.
The specific bit values are core-specific (as in fact is using
coprocessor 14 register 7 itself) but all current ARM11
cores @emph{except the ARM1176} use the same six bits.
@end deffn

@section ARMv7 and ARMv8 Architecture
@cindex ARMv7
@cindex ARMv8

@subsection ARMv7-A specific commands
@cindex Cortex-A

@deffn {Command} {cortex_a cache_info}
display information about target caches
@end deffn

@deffn {Command} {cortex_a dacrfixup} [@option{on}|@option{off}]
Work around issues with software breakpoints when the program text is
mapped read-only by the operating system. This option sets the CP15 DACR
to "all-manager" to bypass MMU permission checks on memory access.
Defaults to 'off'.
@end deffn

@deffn {Command} {cortex_a dbginit}
Initialize core debug
Enables debug by unlocking the Software Lock and clearing sticky powerdown indications
@end deffn

@deffn {Command} {cortex_a smp} [on|off]
Display/set the current SMP mode
@end deffn

@deffn {Command} {cortex_a smp_gdb} [core_id]
Display/set the current core displayed in GDB
@end deffn

@deffn {Command} {cortex_a maskisr} [@option{on}|@option{off}]
Selects whether interrupts will be processed when single stepping
@end deffn

@deffn {Command} {cache_config l2x}  [base way]
configure l2x cache
@end deffn

@deffn {Command} {cortex_a mmu dump} [@option{0}|@option{1}|@option{addr} address [@option{num_entries}]]
Dump the MMU translation table from TTB0 or TTB1 register, or from physical
memory location @var{address}. When dumping the table from @var{address}, print at most
@var{num_entries} page table entries. @var{num_entries} is optional, if omitted, the maximum
possible (4096) entries are printed.
@end deffn

@subsection ARMv7-R specific commands
@cindex Cortex-R

@deffn {Command} {cortex_r4 dbginit}
Initialize core debug
Enables debug by unlocking the Software Lock and clearing sticky powerdown indications
@end deffn

@deffn {Command} {cortex_r4 maskisr} [@option{on}|@option{off}]
Selects whether interrupts will be processed when single stepping
@end deffn


@subsection ARM CoreSight TPIU and SWO specific commands
@cindex tracing
@cindex SWO
@cindex SWV
@cindex TPIU

ARM CoreSight provides several modules to generate debugging
information internally (ITM, DWT and ETM). Their output is directed
through TPIU or SWO modules to be captured externally either on an SWO pin (this
configuration is called SWV) or on a synchronous parallel trace port.

ARM CoreSight provides independent HW blocks named TPIU and SWO each with its
own functionality. Embedded in Cortex-M3 and M4, ARM provides an optional HW
block that includes both TPIU and SWO functionalities and is again named TPIU,
which causes quite some confusion.
The registers map of all the TPIU and SWO implementations allows using a single
driver that detects at runtime the features available.

The @command{tpiu} is used for either TPIU or SWO.
A convenient alias @command{swo} is available to help distinguish, in scripts,
the commands for SWO from the commands for TPIU.

@deffn {Command} {swo} ...
Alias of @command{tpiu ...}. Can be used in scripts to distinguish the commands
for SWO from the commands for TPIU.
@end deffn

@deffn {Command} {tpiu create} tpiu_name configparams...
Creates a TPIU or a SWO object. The two commands are equivalent.
Add the object in a list and add new commands (@command{@var{tpiu_name}})
which are used for various purposes including additional configuration.

@itemize @bullet
@item @var{tpiu_name} -- the name of the TPIU or SWO object.
This name is also used to create the object's command, referred to here
as @command{$tpiu_name}, and in other places where the TPIU or SWO needs to be identified.
@item @var{configparams} -- all parameters accepted by @command{$tpiu_name configure} are permitted.

You @emph{must} set here the AP and MEM_AP base_address through @code{-dap @var{dap_name}},
@code{-ap-num @var{ap_number}} and @code{-baseaddr @var{base_address}}.
@end itemize
@end deffn

@deffn {Command} {tpiu names}
Lists all the TPIU or SWO objects created so far. The two commands are equivalent.
@end deffn

@deffn {Command} {tpiu init}
Initialize all registered TPIU and SWO. The two commands are equivalent.
These commands are used internally during initialization. They can be issued
at any time after the initialization, too.
@end deffn

@deffn {Command} {$tpiu_name cget} queryparm
Each configuration parameter accepted by @command{$tpiu_name configure} can be
individually queried, to return its current value.
The @var{queryparm} is a parameter name accepted by that command, such as @code{-dap}.
@end deffn

@deffn {Command} {$tpiu_name configure} configparams...
The options accepted by this command may also be specified as parameters
to @command{tpiu create}. Their values can later be queried one at a time by
using the @command{$tpiu_name cget} command.

@itemize @bullet
@item @code{-dap} @var{dap_name} -- names the DAP used to access this
TPIU. @xref{dapdeclaration,,DAP declaration}, on how to create and manage DAP instances.

@item @code{-ap-num} @var{ap_number} -- sets DAP access port for TPIU,
@var{ap_number} is the numeric index of the DAP AP the TPIU is connected to.

@item @code{-baseaddr} @var{base_address} -- sets the TPIU @var{base_address} where
to access the TPIU in the DAP AP memory space.

@item @code{-protocol} (@option{sync}|@option{uart}|@option{manchester}) -- sets the
protocol used for trace data:
@itemize @minus
@item @option{sync} -- synchronous parallel trace output mode, using @var{port_width}
 data bits (default);
@item @option{uart} -- use asynchronous SWO mode with NRZ (same as regular UART 8N1) coding;
@item @option{manchester} -- use asynchronous SWO mode with Manchester coding.
@end itemize

@item @code{-event} @var{event_name} @var{event_body} -- assigns an event handler,
a TCL string which is evaluated when the event is triggered. The events
@code{pre-enable}, @code{post-enable}, @code{pre-disable} and @code{post-disable}
are defined for TPIU/SWO.
A typical use case for the event @code{pre-enable} is to enable the trace clock
of the TPIU.

@item @code{-output} (@option{external}|@option{:}@var{port}|@var{filename}|@option{-}) -- specifies
the destination of the trace data:
@itemize @minus
@item @option{external} -- configure TPIU/SWO to let user capture trace
output externally, either with an additional UART or with a logic analyzer (default);
@item @option{-} -- configure TPIU/SWO and debug adapter to gather trace data
and forward it to @command{tcl_trace} command;
@item @option{:}@var{port} -- configure TPIU/SWO and debug adapter to gather
trace data, open a TCP server at port @var{port} and send the trace data to
each connected client;
@item @var{filename} -- configure TPIU/SWO and debug adapter to
gather trace data and append it to @var{filename}, which can be
either a regular file or a named pipe.
@end itemize

@item @code{-traceclk} @var{TRACECLKIN_freq} -- mandatory parameter.
Specifies the frequency in Hz of the trace clock. For the TPIU embedded in
Cortex-M3 or M4, this is usually the same frequency as HCLK. For protocol
@option{sync} this is twice the frequency of the pin data rate.

@item @code{-pin-freq} @var{trace_freq} -- specifies the expected data rate
in Hz of the SWO pin. Parameter used only on protocols @option{uart} and
@option{manchester}. Can be omitted to let the adapter driver select the
maximum supported rate automatically.

@item @code{-port-width} @var{port_width} -- sets to @var{port_width} the width
of the synchronous parallel port used for trace output. Parameter used only on
protocol @option{sync}. If not specified, default value is @var{1}.

@item @code{-formatter} (@option{0}|@option{1}) -- specifies if the formatter
should be enabled. Parameter used only on protocol @option{sync}. If not specified,
default value is @var{0}.
@end itemize
@end deffn

@deffn {Command} {$tpiu_name enable}
Uses the parameters specified by the previous @command{$tpiu_name configure}
to configure and enable the TPIU or the SWO.
If required, the adapter is also configured and enabled to receive the trace
data.
This command can be used before @command{init}, but it will take effect only
after the @command{init}.
@end deffn

@deffn {Command} {$tpiu_name disable}
Disable the TPIU or the SWO, terminating the receiving of the trace data.
@end deffn



Example usage:
@enumerate
@item STM32L152 board is programmed with an application that configures
PLL to provide core clock with 24MHz frequency; to use ITM output it's
enough to:
@example
#include <libopencm3/cm3/itm.h>
    ...
    	ITM_STIM8(0) = c;
    ...
@end example
(the most obvious way is to use the first stimulus port for printf,
for that this ITM_STIM8 assignment can be used inside _write(); to make it
blocking to avoid data loss, add @code{while (!(ITM_STIM8(0) &
ITM_STIM_FIFOREADY));});
@item An FT2232H UART is connected to the SWO pin of the board;
@item Commands to configure UART for 12MHz baud rate:
@example
$ setserial /dev/ttyUSB1 spd_cust divisor 5
$ stty -F /dev/ttyUSB1 38400
@end example
(FT2232H's base frequency is 60MHz, spd_cust allows to alias 38400
baud with our custom divisor to get 12MHz)
@item @code{itmdump -f /dev/ttyUSB1 -d1}
@item OpenOCD invocation line:
@example
openocd -f interface/stlink.cfg \
-c "transport select hla_swd" \
-f target/stm32l1.cfg \
-c "stm32l1.tpiu configure -protocol uart" \
-c "stm32l1.tpiu configure -traceclk 24000000 -pin-freq ********" \
-c "stm32l1.tpiu enable"
@end example
@end enumerate

@subsection ARMv7-M specific commands
@cindex tracing
@cindex SWO
@cindex SWV
@cindex ITM
@cindex ETM

@deffn {Command} {itm port} @var{port} (@option{0}|@option{1}|@option{on}|@option{off})
Enable or disable trace output for ITM stimulus @var{port} (counting
from 0). Port 0 is enabled on target creation automatically.
@end deffn

@deffn {Command} {itm ports} (@option{0}|@option{1}|@option{on}|@option{off})
Enable or disable trace output for all ITM stimulus ports.
@end deffn

@subsection Cortex-M specific commands
@cindex Cortex-M

@deffn {Command} {cortex_m maskisr} (@option{auto}|@option{on}|@option{off}|@option{steponly})
Control masking (disabling) interrupts during target step/resume.

The @option{auto} option handles interrupts during stepping in a way that they
get served but don't disturb the program flow. The step command first allows
pending interrupt handlers to execute, then disables interrupts and steps over
the next instruction where the core was halted. After the step interrupts
are enabled again. If the interrupt handlers don't complete within 500ms,
the step command leaves with the core running.

The @option{steponly} option disables interrupts during single-stepping but
enables them during normal execution. This can be used as a partial workaround
for 702596 erratum in Cortex-M7 r0p1. See "Cortex-M7 (AT610) and Cortex-M7 with
FPU (AT611) Software Developer Errata Notice" from ARM for further details.

Note that a free hardware (FPB) breakpoint is required for the @option{auto}
option. If no breakpoint is available at the time of the step, then the step
is taken with interrupts enabled, i.e. the same way the @option{off} option
does.

Default is @option{auto}.
@end deffn

@deffn {Command} {cortex_m vector_catch} [@option{all}|@option{none}|list]
@cindex vector_catch
Vector Catch hardware provides dedicated breakpoints
for certain hardware events.

Parameters request interception of
@option{all} of these hardware event vectors,
@option{none} of them,
or one or more of the following:
@option{hard_err} for a HardFault exception;
@option{mm_err} for a MemManage exception;
@option{bus_err} for a BusFault exception;
@option{irq_err},
@option{state_err},
@option{chk_err}, or
@option{nocp_err} for various UsageFault exceptions; or
@option{reset}.
If NVIC setup code does not enable them,
MemManage, BusFault, and UsageFault exceptions
are mapped to HardFault.
UsageFault checks for
divide-by-zero and unaligned access
must also be explicitly enabled.

This finishes by listing the current vector catch configuration.
@end deffn

@deffn {Command} {cortex_m reset_config} (@option{sysresetreq}|@option{vectreset})
Control reset handling if hardware srst is not fitted
@xref{reset_config,,reset_config}.

@itemize @minus
@item @option{sysresetreq} use AIRCR SYSRESETREQ to reset system.
@item @option{vectreset} use AIRCR VECTRESET to reset system (default).
@end itemize

Using @option{vectreset} is a safe option for Cortex-M3, M4 and M7 cores.
This however has the disadvantage of only resetting the core, all peripherals
are unaffected. A solution would be to use a @code{reset-init} event handler
to manually reset the peripherals.
@xref{targetevents,,Target Events}.

Cortex-M0, M0+ and M1 do not support @option{vectreset}, use @option{sysresetreq}
instead.
@end deffn

@subsection ARMv8-A specific commands
@cindex ARMv8-A
@cindex aarch64

@deffn {Command} {aarch64 cache_info}
Display information about target caches
@end deffn

@deffn {Command} {aarch64 dbginit}
This command enables debugging by clearing the OS Lock and sticky power-down and reset
indications. It also establishes the expected, basic cross-trigger configuration the aarch64
target code relies on. In a configuration file, the command would typically be called from a
@code{reset-end} or @code{reset-deassert-post} handler, to re-enable debugging after a system reset.
However, normally it is not necessary to use the command at all.
@end deffn

@deffn {Command} {aarch64 disassemble} address [count]
@cindex disassemble
Disassembles @var{count} instructions starting at @var{address}.
If @var{count} is not specified, a single instruction is disassembled.
@end deffn

@deffn {Command} {aarch64 smp} [on|off]
Display, enable or disable SMP handling mode. The state of SMP handling influences the way targets in an SMP group
are handled by the run control. With SMP handling enabled, issuing halt or resume to one core will trigger
halting or resuming of all cores in the group. The command @code{target smp} defines which targets are in the SMP
group. With SMP handling disabled, all targets need to be treated individually.
@end deffn

@deffn {Command} {aarch64 maskisr} [@option{on}|@option{off}]
Selects whether interrupts will be processed when single stepping. The default configuration is
@option{on}.
@end deffn

@deffn {Command} {$target_name catch_exc} [@option{off}|@option{sec_el1}|@option{sec_el3}|@option{nsec_el1}|@option{nsec_el2}]+
Cause @command{$target_name} to halt when an exception is taken. Any combination of
Secure (sec) EL1/EL3 or Non-Secure (nsec) EL1/EL2 is valid. The target
@command{$target_name} will halt before taking the exception. In order to resume
the target, the exception catch must be disabled again with @command{$target_name catch_exc off}.
Issuing the command without options prints the current configuration.
@end deffn

@section EnSilica eSi-RISC Architecture

eSi-RISC is a highly configurable microprocessor architecture for embedded systems
provided by EnSilica. (See: @url{http://www.ensilica.com/risc-ip/}.)

@subsection eSi-RISC Configuration

@deffn {Command} {esirisc cache_arch} (@option{harvard}|@option{von_neumann})
Configure the caching architecture. Targets with the @code{UNIFIED_ADDRESS_SPACE}
option disabled employ a Harvard architecture. By default, @option{von_neumann} is assumed.
@end deffn

@deffn {Command} {esirisc hwdc} (@option{all}|@option{none}|mask ...)
Configure hardware debug control. The HWDC register controls which exceptions return
control back to the debugger. Possible masks are @option{all}, @option{none},
@option{reset}, @option{interrupt}, @option{syscall}, @option{error}, and @option{debug}.
By default, @option{reset}, @option{error}, and @option{debug} are enabled.
@end deffn

@subsection eSi-RISC Operation

@deffn {Command} {esirisc flush_caches}
Flush instruction and data caches. This command requires that the target is halted
when the command is issued and configured with an instruction or data cache.
@end deffn

@subsection eSi-Trace Configuration

eSi-RISC targets may be configured with support for instruction tracing. Trace
data may be written to an in-memory buffer or FIFO. If a FIFO is configured, DMA
is typically employed to move trace data off-device using a high-speed
peripheral (eg. SPI). Collected trace data is encoded in one of three different
formats. At a minimum, @command{esirisc trace buffer} or @command{esirisc trace
fifo} must be issued along with @command{esirisc trace format} before trace data
can be collected.

OpenOCD provides rudimentary analysis of collected trace data. If more detail is
needed, collected trace data can be dumped to a file and processed by external
tooling.

@quotation Issues
OpenOCD is unable to process trace data sent to a FIFO. A potential workaround
for this issue is to configure DMA to copy trace data to an in-memory buffer,
which can then be passed to the @command{esirisc trace analyze} and
@command{esirisc trace dump} commands.

It is possible to corrupt trace data when using a FIFO if the peripheral
responsible for draining data from the FIFO is not fast enough. This can be
managed by enabling flow control, however this can impact timing-sensitive
software operation on the CPU.
@end quotation

@deffn {Command} {esirisc trace buffer} address size [@option{wrap}]
Configure trace buffer using the provided address and size. If the @option{wrap}
option is specified, trace collection will continue once the end of the buffer
is reached. By default, wrap is disabled.
@end deffn

@deffn {Command} {esirisc trace fifo} address
Configure trace FIFO using the provided address.
@end deffn

@deffn {Command} {esirisc trace flow_control} (@option{enable}|@option{disable})
Enable or disable stalling the CPU to collect trace data. By default, flow
control is disabled.
@end deffn

@deffn {Command} {esirisc trace format} (@option{full}|@option{branch}|@option{icache}) pc_bits
Configure trace format and number of PC bits to be captured. @option{pc_bits}
must be within 1 and 31 as the LSB is not collected. If external tooling is used
to analyze collected trace data, these values must match.

Supported trace formats:
@itemize
@item @option{full} capture full trace data, allowing execution history and
timing to be determined.
@item @option{branch} capture taken branch instructions and branch target
addresses.
@item @option{icache} capture instruction cache misses.
@end itemize
@end deffn

@deffn {Command} {esirisc trace trigger start} (@option{condition}) [start_data start_mask]
Configure trigger start condition using the provided start data and mask. A
brief description of each condition is provided below; for more detail on how
these values are used, see the eSi-RISC Architecture Manual.

Supported conditions:
@itemize
@item @option{none} manual tracing (see @command{esirisc trace start}).
@item @option{pc} start tracing if the PC matches start data and mask.
@item @option{load} start tracing if the effective address of a load
instruction matches start data and mask.
@item @option{store} start tracing if the effective address of a store
instruction matches start data and mask.
@item @option{exception} start tracing if the EID of an exception matches start
data and mask.
@item @option{eret} start tracing when an @code{ERET} instruction is executed.
@item @option{wait} start tracing when a @code{WAIT} instruction is executed.
@item @option{stop} start tracing when a @code{STOP} instruction is executed.
@item @option{high} start tracing when an external signal is a logical high.
@item @option{low} start tracing when an external signal is a logical low.
@end itemize
@end deffn

@deffn {Command} {esirisc trace trigger stop} (@option{condition}) [stop_data stop_mask]
Configure trigger stop condition using the provided stop data and mask. A brief
description of each condition is provided below; for more detail on how these
values are used, see the eSi-RISC Architecture Manual.

Supported conditions:
@itemize
@item @option{none} manual tracing (see @command{esirisc trace stop}).
@item @option{pc} stop tracing if the PC matches stop data and mask.
@item @option{load} stop tracing if the effective address of a load
instruction matches stop data and mask.
@item @option{store} stop tracing if the effective address of a store
instruction matches stop data and mask.
@item @option{exception} stop tracing if the EID of an exception matches stop
data and mask.
@item @option{eret} stop tracing when an @code{ERET} instruction is executed.
@item @option{wait} stop tracing when a @code{WAIT} instruction is executed.
@item @option{stop} stop tracing when a @code{STOP} instruction is executed.
@end itemize
@end deffn

@deffn {Command} {esirisc trace trigger delay} (@option{trigger}) [cycles]
Configure trigger start/stop delay in clock cycles.

Supported triggers:
@itemize
@item @option{none} no delay to start or stop collection.
@item @option{start} delay @option{cycles} after trigger to start collection.
@item @option{stop} delay @option{cycles} after trigger to stop collection.
@item @option{both} delay @option{cycles} after both triggers to start or stop
collection.
@end itemize
@end deffn

@subsection eSi-Trace Operation

@deffn {Command} {esirisc trace init}
Initialize trace collection. This command must be called any time the
configuration changes. If a trace buffer has been configured, the contents will
be overwritten when trace collection starts.
@end deffn

@deffn {Command} {esirisc trace info}
Display trace configuration.
@end deffn

@deffn {Command} {esirisc trace status}
Display trace collection status.
@end deffn

@deffn {Command} {esirisc trace start}
Start manual trace collection.
@end deffn

@deffn {Command} {esirisc trace stop}
Stop manual trace collection.
@end deffn

@deffn {Command} {esirisc trace analyze} [address size]
Analyze collected trace data. This command may only be used if a trace buffer
has been configured. If a trace FIFO has been configured, trace data must be
copied to an in-memory buffer identified by the @option{address} and
@option{size} options using DMA.
@end deffn

@deffn {Command} {esirisc trace dump} [address size] @file{filename}
Dump collected trace data to file. This command may only be used if a trace
buffer has been configured. If a trace FIFO has been configured, trace data must
be copied to an in-memory buffer identified by the @option{address} and
@option{size} options using DMA.
@end deffn

@section Intel Architecture

Intel Quark X10xx is the first product in the Quark family of SoCs. It is an IA-32
(Pentium x86 ISA) compatible SoC. The core CPU in the X10xx is codenamed Lakemont.
Lakemont version 1 (LMT1) is used in X10xx. The CPU TAP (Lakemont TAP) is used for
software debug and the CLTAP is used for SoC level operations.
Useful docs are here: https://communities.intel.com/community/makers/documentation
@itemize
@item Intel Quark SoC X1000 OpenOCD/GDB/Eclipse App Note (web search for doc num 330015)
@item Intel Quark SoC X1000 Debug Operations User Guide (web search for doc num 329866)
@item Intel Quark SoC X1000 Datasheet (web search for doc num 329676)
@end itemize

@subsection x86 32-bit specific commands
The three main address spaces for x86 are memory, I/O and configuration space.
These commands allow a user to read and write to the 64Kbyte I/O address space.

@deffn {Command} {x86_32 idw} address
Display the contents of a 32-bit I/O port from address range 0x0000 - 0xffff.
@end deffn

@deffn {Command} {x86_32 idh} address
Display the contents of a 16-bit I/O port from address range 0x0000 - 0xffff.
@end deffn

@deffn {Command} {x86_32 idb} address
Display the contents of a 8-bit I/O port from address range 0x0000 - 0xffff.
@end deffn

@deffn {Command} {x86_32 iww} address
Write the contents of a 32-bit I/O port to address range 0x0000 - 0xffff.
@end deffn

@deffn {Command} {x86_32 iwh} address
Write the contents of a 16-bit I/O port to address range 0x0000 - 0xffff.
@end deffn

@deffn {Command} {x86_32 iwb} address
Write the contents of a 8-bit I/O port to address range 0x0000 - 0xffff.
@end deffn

@section OpenRISC Architecture

The OpenRISC CPU is a soft core. It is used in a programmable SoC which can be
configured with any of the TAP / Debug Unit available.

@subsection TAP and Debug Unit selection commands
@deffn {Command} {tap_select} (@option{vjtag}|@option{mohor}|@option{xilinx_bscan})
Select between the Altera Virtual JTAG , Xilinx Virtual JTAG and Mohor TAP.
@end deffn
@deffn {Command} {du_select} (@option{adv}|@option{mohor}) [option]
Select between the Advanced Debug Interface and the classic one.

An option can be passed as a second argument to the debug unit.

When using the Advanced Debug Interface, option = 1 means the RTL core is
configured with ADBG_USE_HISPEED = 1. This configuration skips status checking
between bytes while doing read or write bursts.
@end deffn

@subsection Registers commands
@deffn {Command} {addreg} [name] [address] [feature] [reg_group]
Add a new register in the cpu register list. This register will be
included in the generated target descriptor file.

@strong{[feature]} must be "org.gnu.gdb.or1k.group[0..10]".

@strong{[reg_group]} can be anything. The default register list defines "system",
 "dmmu", "immu", "dcache", "icache", "mac", "debug", "perf", "power", "pic"
 and "timer" groups.

@emph{example:}
@example
addreg rtest 0x1234 org.gnu.gdb.or1k.group0 system
@end example

@end deffn

@section RISC-V Architecture

@uref{http://riscv.org/, RISC-V} is a free and open ISA. OpenOCD supports JTAG
debug of RV32 and RV64 cores in heterogeneous multicore systems of up to 32
harts. (It's possible to increase this limit to 1024 by changing
RISCV_MAX_HARTS in riscv.h.) OpenOCD primarily supports 0.13 of the RISC-V
Debug Specification, but there is also support for legacy targets that
implement version 0.11.

@subsection RISC-V Terminology

A @emph{hart} is a hardware thread. A hart may share resources (eg. FPU) with
another hart, or may be a separate core.  RISC-V treats those the same, and
OpenOCD exposes each hart as a separate core.

@subsection Vector Registers

For harts that implement the vector extension, OpenOCD provides access to the
relevant CSRs, as well as the vector registers (v0-v31). The size of each
vector register is dependent on the value of vlenb. RISC-V allows each vector
register to be divided into selected-width elements, and this division can be
changed at run-time. Because OpenOCD cannot update register definitions at
run-time, it exposes each vector register to gdb as a union of fields of
vectors so that users can easily access individual bytes, shorts, words,
longs, and quads inside each vector register. It is left to gdb or
higher-level debuggers to present this data in a more intuitive format.

In the XML register description, the vector registers (when vlenb=16) look as
follows:

@example
<feature name="org.gnu.gdb.riscv.vector">
<vector id="bytes" type="uint8" count="16"/>
<vector id="shorts" type="uint16" count="8"/>
<vector id="words" type="uint32" count="4"/>
<vector id="longs" type="uint64" count="2"/>
<vector id="quads" type="uint128" count="1"/>
<union id="riscv_vector">
<field name="b" type="bytes"/>
<field name="s" type="shorts"/>
<field name="w" type="words"/>
<field name="l" type="longs"/>
<field name="q" type="quads"/>
</union>
<reg name="v0" bitsize="128" regnum="4162" save-restore="no"
        type="riscv_vector" group="vector"/>
...
<reg name="v31" bitsize="128" regnum="4193" save-restore="no"
        type="riscv_vector" group="vector"/>
</feature>
@end example

@subsection RISC-V Debug Configuration Commands

@deffn {Command} {riscv dump_sample_buf} [base64]
Dump and clear the contents of the sample buffer. Which samples are collected
is configured with @code{riscv memory_sample}. If the optional base64
argument is passed, the raw buffer is dumped in base64 format, so that
external tools can gather the data efficiently.
@end deffn

@deffn {Config Command} {riscv expose_csrs} n[-m|=name] [...]
Configure which CSRs to expose in addition to the standard ones. The CSRs to expose
can be specified as individual register numbers or register ranges (inclusive). For the
individually listed CSRs, a human-readable name can optionally be set using the @code{n=name}
syntax, which will get @code{csr_} prepended to it. If no name is provided, the register will be
named @code{csr<n>}.

By default OpenOCD attempts to expose only CSRs that are mentioned in a spec,
and then only if the corresponding extension appears to be implemented. This
command can be used if OpenOCD gets this wrong, or if the target implements custom
CSRs.

@example
# Expose a single RISC-V CSR number 128 under the name "csr128":
$_TARGETNAME expose_csrs 128

# Expose multiple RISC-V CSRs 128..132 under names "csr128" through "csr132":
$_TARGETNAME expose_csrs 128-132

# Expose a single RISC-V CSR number 1996 under custom name "csr_myregister":
$_TARGETNAME expose_csrs 1996=myregister
@end example
@end deffn

@deffn {Config Command} {riscv expose_custom} n[-m|=name] [...]
The RISC-V Debug Specification allows targets to expose custom registers
through abstract commands. (See Section ******* in that document.) This command
configures individual registers or register ranges (inclusive) that shall be exposed.
Number 0 indicates the first custom register, whose abstract command number is 0xc000.
For individually listed registers, a human-readable name can be optionally provided
using the @code{n=name} syntax, which will get @code{custom_} prepended to it. If no
name is provided, the register will be named @code{custom<n>}.

@example
# Expose one RISC-V custom register with number 0xc010 (0xc000 + 16)
# under the name "custom16":
$_TARGETNAME expose_custom 16

# Expose a range of RISC-V custom registers with numbers 0xc010 .. 0xc018
# (0xc000+16 .. 0xc000+24) under the names "custom16" through "custom24":
$_TARGETNAME expose_custom 16-24

# Expose one RISC-V custom register with number 0xc020 (0xc000 + 32) under
# user-defined name "custom_myregister":
$_TARGETNAME expose_custom 32=myregister
@end example
@end deffn

@deffn {Command} {riscv memory_sample} bucket address|clear [size=4]
Configure OpenOCD to frequently read size bytes at the given addresses.
Execute the command with no arguments to see the current configuration. Use
clear to stop using a given bucket.

OpenOCD will allocate a 1MB sample buffer, and when it fills up no more
samples will be collected until it is emptied with @code{riscv
dump_sample_buf}.
@end deffn

@deffn {Command} {riscv repeat_read} count address [size=4]
Quickly read count words of the given size from address. This can be useful
to read out a buffer that's memory-mapped to be accessed through a single
address, or to sample a changing value in a memory-mapped device.
@end deffn

@deffn {Command} {riscv set_command_timeout_sec} [seconds]
Set the wall-clock timeout (in seconds) for individual commands. The default
should work fine for all but the slowest targets (eg. simulators).
@end deffn

@deffn {Command} {riscv set_reset_timeout_sec} [seconds]
Set the maximum time to wait for a hart to come out of reset after reset is
deasserted.
@end deffn

@deffn {Command} {riscv set_scratch_ram} none|[address]
Set the address of 16 bytes of scratch RAM the debugger can use, or 'none'.
This is used to access 64-bit floating point registers on 32-bit targets.
@end deffn

@deffn Command {riscv set_mem_access} method1 [method2] [method3]
Specify which RISC-V memory access method(s) shall be used, and in which order
of priority. At least one method must be specified.

Available methods are:
@itemize
@item @code{progbuf} - Use RISC-V Debug Program Buffer to access memory.
@item @code{sysbus} - Access memory via RISC-V Debug System Bus interface.
@item @code{abstract} - Access memory via RISC-V Debug abstract commands.
@end itemize

By default, all memory access methods are enabled in the following order:
@code{progbuf sysbus abstract}.

This command can be used to change the memory access methods if the default
behavior is not suitable for a particular target.
@end deffn

@deffn {Command} {riscv set_enable_virtual} on|off
When on, memory accesses are performed on physical or virtual memory depending
on the current system configuration. When off (default), all memory accessses are performed
on physical memory.
@end deffn

@deffn {Command} {riscv set_enable_virt2phys} on|off
When on (default), memory accesses are performed on physical or virtual memory
depending on the current satp configuration. When off, all memory accessses are
performed on physical memory.
@end deffn

@deffn {Command} {riscv resume_order} normal|reversed
Some software assumes all harts are executing nearly continuously. Such
software may be sensitive to the order that harts are resumed in. On harts
that don't support hasel, this option allows the user to choose the order the
harts are resumed in. If you are using this option, it's probably masking a
race condition problem in your code.

Normal order is from lowest hart index to highest. This is the default
behavior. Reversed order is from highest hart index to lowest.
@end deffn

@deffn {Command} {riscv set_ir} (@option{idcode}|@option{dtmcs}|@option{dmi}) [value]
Set the IR value for the specified JTAG register.  This is useful, for
example, when using the existing JTAG interface on a Xilinx FPGA by
way of BSCANE2 primitives that only permit a limited selection of IR
values.

When utilizing version 0.11 of the RISC-V Debug Specification,
@option{dtmcs} and @option{dmi} set the IR values for the DTMCONTROL
and DBUS registers, respectively.
@end deffn

@deffn {Command} {riscv use_bscan_tunnel} value
Enable or disable use of a BSCAN tunnel to reach the Debug Module. Supply the
width of the DM transport TAP's instruction register to enable. Supply a
value of 0 to disable.

This BSCAN tunnel interface is specific to SiFive IP. Anybody may implement
it, but currently there is no good documentation on it. In a nutshell, this
feature scans USER4 into a Xilinx TAP to select the tunnel device (assuming
hardware is present and it is hooked up to the Xilinx USER4 IR) and
encapsulates a tunneled scan directive into a DR scan into the Xilinx TAP. A
tunneled DR scan consists of:
@enumerate
@item 1 bit that selects IR when 0, or DR when 1
@item 7 bits that encode the width of the desired tunneled scan
@item A width+1 stream of bits for the tunneled TDI. The plus one is because there is a one-clock skew between TDI of Xilinx chain and TDO from tunneled chain.
@item 3 bits of zero that the tunnel uses to go back to idle state.
@end enumerate

@end deffn

@deffn {Command} {riscv set_bscan_tunnel_ir} value
Allows the use_bscan_tunnel feature to target non Xilinx device by
specifying the JTAG TAP IR used to access the bscan tunnel.
@end deffn

@deffn {Command} {riscv set_maskisr} [@option{off}|@option{steponly}]
Selects whether interrupts will be disabled when single stepping. The default configuration is @option{off}.
This feature is only useful on hardware that always steps into interrupts and doesn't support dcsr.stepie=0.
Keep in mind, disabling the option does not guarantee that single stepping will go into interrupt handlers.
To make that happen, dcsr.stepie would have to be written to 1 as well.
@end deffn

@deffn {Command} {riscv set_ebreakm} on|off
Control dcsr.ebreakm. When on (default), M-mode ebreak instructions trap to
OpenOCD. When off, they generate a breakpoint exception handled internally.
@end deffn

@deffn {Command} {riscv set_ebreaks} on|off
Control dcsr.ebreaks. When on (default), S-mode ebreak instructions trap to
OpenOCD. When off, they generate a breakpoint exception handled internally.
@end deffn

@deffn {Command} {riscv set_ebreaku} on|off
Control dcsr.ebreaku. When on (default), U-mode ebreak instructions trap to
OpenOCD. When off, they generate a breakpoint exception handled internally.
@end deffn

@subsection RISC-V Authentication Commands

The following commands can be used to authenticate to a RISC-V system. Eg.  a
trivial challenge-response protocol could be implemented as follows in a
configuration file, immediately following @command{init}:
@example
set challenge [riscv authdata_read]
riscv authdata_write [expr @{$challenge + 1@}]
@end example

@deffn {Command} {riscv authdata_read} [index=0]
Return the 32-bit value read from authdata or authdata0 (index=0), or
authdata1 (index=1).
@end deffn

@deffn {Command} {riscv authdata_write} [index=0] value
Write the 32-bit value to authdata or authdata0 (index=0), or authdata1
(index=1).
@end deffn

@subsection RISC-V DMI Commands

The following commands allow direct access to the Debug Module Interface, which
can be used to interact with custom debug features.

@deffn {Command} {riscv dmi_read} address
Perform a 32-bit DMI read at address, returning the value.
@end deffn

@deffn {Command} {riscv dmi_write} address value
Perform a 32-bit DMI write of value at address.
@end deffn

@section ARC Architecture
@cindex ARC

Synopsys DesignWare ARC Processors are a family of 32-bit CPUs that SoC
designers can optimize for a wide range of uses, from deeply embedded to
high-performance host applications in a variety of market segments. See more
at: @url{http://www.synopsys.com/IP/ProcessorIP/ARCProcessors/Pages/default.aspx}.
OpenOCD currently supports ARC EM processors.
There is a set ARC-specific OpenOCD commands that allow low-level
access to the core and provide necessary support for ARC extensibility and
configurability capabilities. ARC processors has much more configuration
capabilities than most of the other processors and in addition there is an
extension interface that allows SoC designers to add custom registers and
instructions. For the OpenOCD that mostly means that set of core and AUX
registers in target will vary and is not fixed for a particular processor
model. To enable extensibility several TCL commands are provided that allow to
describe those optional registers in OpenOCD configuration files. Moreover
those commands allow for a dynamic target features discovery.


@subsection General ARC commands

@deffn {Config Command} {arc add-reg} configparams

Add a new register to processor target. By default newly created register is
marked as not existing. @var{configparams} must have following required
arguments:

@itemize @bullet

@item @code{-name} name
@*Name of a register.

@item @code{-num} number
@*Architectural register number: core register number or AUX register number.

@item @code{-feature} XML_feature
@*Name of GDB XML target description feature.

@end itemize

@var{configparams} may have following optional arguments:

@itemize @bullet

@item @code{-gdbnum} number
@*GDB register number. It is recommended to not assign GDB register number
manually, because there would be a risk that two register will have same
number. When register GDB number is not set with this option, then register
will get a previous register number + 1. This option is required only for those
registers that must be at particular address expected by GDB.

@item @code{-core}
@*This option specifies that register is a core registers. If not - this is an
AUX register. AUX registers and core registers reside in different address
spaces.

@item @code{-bcr}
@*This options specifies that register is a BCR register. BCR means Build
Configuration Registers - this is a special type of AUX registers that are read
only and non-volatile, that is - they never change their value. Therefore OpenOCD
never invalidates values of those registers in internal caches. Because BCR is a
type of AUX registers, this option cannot be used with @code{-core}.

@item @code{-type} type_name
@*Name of type of this register. This can be either one of the basic GDB types,
or a custom types described with @command{arc add-reg-type-[flags|struct]}.

@item @code{-g}
@* If specified then this is a "general" register. General registers are always
read by OpenOCD on context save (when core has just been halted) and is always
transferred to GDB client in a response to g-packet. Contrary to this,
non-general registers are read and sent to GDB client on-demand. In general it
is not recommended to apply this option to custom registers.

@end itemize

@end deffn

@deffn {Config Command} {arc add-reg-type-flags} -name name flags...
Adds new register type of ``flags'' class. ``Flags'' types can contain only
one-bit fields. Each flag definition looks like @code{-flag name bit-position}.
@end deffn

@anchor{add-reg-type-struct}
@deffn {Config Command} {arc add-reg-type-struct} -name name structs...
Adds new register type of ``struct'' class. ``Struct'' types can contain either
bit-fields or fields of other types, however at the moment only bit fields are
supported. Structure bit field definition looks like @code{-bitfield name
startbit endbit}.
@end deffn

@deffn {Command} {arc get-reg-field} reg-name field-name
Returns value of bit-field in a register. Register must be ``struct'' register
type, @xref{add-reg-type-struct}. command definition.
@end deffn

@deffn {Command} {arc set-reg-exists} reg-names...
Specify that some register exists. Any amount of names can be passed
as an argument for a single command invocation.
@end deffn

@subsection ARC JTAG commands

@deffn {Command} {arc jtag set-aux-reg} regnum value
This command writes value to AUX register via its number. This command access
register in target directly via JTAG, bypassing any OpenOCD internal caches,
therefore it is unsafe to use if that register can be operated by other means.

@end deffn

@deffn {Command} {arc jtag set-core-reg} regnum value
This command is similar to @command{arc jtag set-aux-reg} but is for core
registers.
@end deffn

@deffn {Command} {arc jtag get-aux-reg} regnum
This command returns the value storded in AUX register via its number. This commands access
register in target directly via JTAG, bypassing any OpenOCD internal caches,
therefore it is unsafe to use if that register can be operated by other means.

@end deffn

@deffn {Command} {arc jtag get-core-reg} regnum
This command is similar to @command{arc jtag get-aux-reg} but is for core
registers.
@end deffn

@section STM8 Architecture
@uref{http://st.com/stm8/, STM8} is a 8-bit microcontroller platform from
STMicroelectronics, based on a proprietary 8-bit core architecture.

OpenOCD supports debugging STM8 through the STMicroelectronics debug
protocol SWIM, @pxref{swimtransport,,SWIM}.

@anchor{softwaredebugmessagesandtracing}
@section Software Debug Messages and Tracing
@cindex Linux-ARM DCC support
@cindex tracing
@cindex libdcc
@cindex DCC
OpenOCD can process certain requests from target software, when
the target uses appropriate libraries.
The most powerful mechanism is semihosting, but there is also
a lighter weight mechanism using only the DCC channel.

Currently @command{target_request debugmsgs}
is supported only for @option{arm7_9} and @option{cortex_m} cores.
These messages are received as part of target polling, so
you need to have @command{poll on} active to receive them.
They are intrusive in that they will affect program execution
times. If that is a problem, @pxref{armhardwaretracing,,ARM Hardware Tracing}.

See @file{libdcc} in the contrib dir for more details.
In addition to sending strings, characters, and
arrays of various size integers from the target,
@file{libdcc} also exports a software trace point mechanism.
The target being debugged may
issue trace messages which include a 24-bit @dfn{trace point} number.
Trace point support includes two distinct mechanisms,
each supported by a command:

@itemize
@item @emph{History} ... A circular buffer of trace points
can be set up, and then displayed at any time.
This tracks where code has been, which can be invaluable in
finding out how some fault was triggered.

The buffer may overflow, since it collects records continuously.
It may be useful to use some of the 24 bits to represent a
particular event, and other bits to hold data.

@item @emph{Counting} ... An array of counters can be set up,
and then displayed at any time.
This can help establish code coverage and identify hot spots.

The array of counters is directly indexed by the trace point
number, so trace points with higher numbers are not counted.
@end itemize

Linux-ARM kernels have a ``Kernel low-level debugging
via EmbeddedICE DCC channel'' option (CONFIG_DEBUG_ICEDCC,
depends on CONFIG_DEBUG_LL) which uses this mechanism to
deliver messages before a serial console can be activated.
This is not the same format used by @file{libdcc}.
Other software, such as the U-Boot boot loader, sometimes
does the same thing.

@deffn {Command} {target_request debugmsgs} [@option{enable}|@option{disable}|@option{charmsg}]
Displays current handling of target DCC message requests.
These messages may be sent to the debugger while the target is running.
The optional @option{enable} and @option{charmsg} parameters
both enable the messages, while @option{disable} disables them.

With @option{charmsg} the DCC words each contain one character,
as used by Linux with CONFIG_DEBUG_ICEDCC;
otherwise the libdcc format is used.
@end deffn

@deffn {Command} {trace history} [@option{clear}|count]
With no parameter, displays all the trace points that have triggered
in the order they triggered.
With the parameter @option{clear}, erases all current trace history records.
With a @var{count} parameter, allocates space for that many
history records.
@end deffn

@deffn {Command} {trace point} [@option{clear}|identifier]
With no parameter, displays all trace point identifiers and how many times
they have been triggered.
With the parameter @option{clear}, erases all current trace point counters.
With a numeric @var{identifier} parameter, creates a new a trace point counter
and associates it with that identifier.

@emph{Important:} The identifier and the trace point number
are not related except by this command.
These trace point numbers always start at zero (from server startup,
or after @command{trace point clear}) and count up from there.
@end deffn


@node JTAG Commands
@chapter JTAG Commands
@cindex JTAG Commands
Most general purpose JTAG commands have been presented earlier.
(@xref{jtagspeed,,JTAG Speed}, @ref{Reset Configuration}, and @ref{TAP Declaration}.)
Lower level JTAG commands, as presented here,
may be needed to work with targets which require special
attention during operations such as reset or initialization.

To use these commands you will need to understand some
of the basics of JTAG, including:

@itemize @bullet
@item A JTAG scan chain consists of a sequence of individual TAP
devices such as a CPUs.
@item Control operations involve moving each TAP through the same
standard state machine (in parallel)
using their shared TMS and clock signals.
@item Data transfer involves shifting data through the chain of
instruction or data registers of each TAP, writing new register values
while the reading previous ones.
@item Data register sizes are a function of the instruction active in
a given TAP, while instruction register sizes are fixed for each TAP.
All TAPs support a BYPASS instruction with a single bit data register.
@item The way OpenOCD differentiates between TAP devices is by
shifting different instructions into (and out of) their instruction
registers.
@end itemize

@section Low Level JTAG Commands

These commands are used by developers who need to access
JTAG instruction or data registers, possibly controlling
the order of TAP state transitions.
If you're not debugging OpenOCD internals, or bringing up a
new JTAG adapter or a new type of TAP device (like a CPU or
JTAG router), you probably won't need to use these commands.
In a debug session that doesn't use JTAG for its transport protocol,
these commands are not available.

@deffn {Command} {drscan} tap [numbits value]+ [@option{-endstate} tap_state]
Loads the data register of @var{tap} with a series of bit fields
that specify the entire register.
Each field is @var{numbits} bits long with
a numeric @var{value} (hexadecimal encouraged).
The return value holds the original value of each
of those fields.

For example, a 38 bit number might be specified as one
field of 32 bits then one of 6 bits.
@emph{For portability, never pass fields which are more
than 32 bits long. Many OpenOCD implementations do not
support 64-bit (or larger) integer values.}

All TAPs other than @var{tap} must be in BYPASS mode.
The single bit in their data registers does not matter.

When @var{tap_state} is specified, the JTAG state machine is left
in that state.
For example @sc{drpause} might be specified, so that more
instructions can be issued before re-entering the @sc{run/idle} state.
If the end state is not specified, the @sc{run/idle} state is entered.

@quotation Warning
OpenOCD does not record information about data register lengths,
so @emph{it is important that you get the bit field lengths right}.
Remember that different JTAG instructions refer to different
data registers, which may have different lengths.
Moreover, those lengths may not be fixed;
the SCAN_N instruction can change the length of
the register accessed by the INTEST instruction
(by connecting a different scan chain).
@end quotation
@end deffn

@deffn {Command} {flush_count}
Returns the number of times the JTAG queue has been flushed.
This may be used for performance tuning.

For example, flushing a queue over USB involves a
minimum latency, often several milliseconds, which does
not change with the amount of data which is written.
You may be able to identify performance problems by finding
tasks which waste bandwidth by flushing small transfers too often,
instead of batching them into larger operations.
@end deffn

@deffn {Command} {irscan} [tap instruction]+ [@option{-endstate} tap_state]
For each @var{tap} listed, loads the instruction register
with its associated numeric @var{instruction}.
(The number of bits in that instruction may be displayed
using the @command{scan_chain} command.)
For other TAPs, a BYPASS instruction is loaded.

When @var{tap_state} is specified, the JTAG state machine is left
in that state.
For example @sc{irpause} might be specified, so the data register
can be loaded before re-entering the @sc{run/idle} state.
If the end state is not specified, the @sc{run/idle} state is entered.

@quotation Note
OpenOCD currently supports only a single field for instruction
register values, unlike data register values.
For TAPs where the instruction register length is more than 32 bits,
portable scripts currently must issue only BYPASS instructions.
@end quotation
@end deffn

@deffn {Command} {pathmove} start_state [next_state ...]
Start by moving to @var{start_state}, which
must be one of the @emph{stable} states.
Unless it is the only state given, this will often be the
current state, so that no TCK transitions are needed.
Then, in a series of single state transitions
(conforming to the JTAG state machine) shift to
each @var{next_state} in sequence, one per TCK cycle.
The final state must also be stable.
@end deffn

@deffn {Command} {runtest} @var{num_cycles}
Move to the @sc{run/idle} state, and execute at least
@var{num_cycles} of the JTAG clock (TCK).
Instructions often need some time
to execute before they take effect.
@end deffn

@c tms_sequence (short|long)
@c ... temporary, debug-only, other than USBprog bug workaround...

@deffn {Command} {verify_ircapture} (@option{enable}|@option{disable})
Verify values captured during @sc{ircapture} and returned
during IR scans. Default is enabled, but this can be
overridden by @command{verify_jtag}.
This flag is ignored when validating JTAG chain configuration.
@end deffn

@deffn {Command} {verify_jtag} (@option{enable}|@option{disable})
Enables verification of DR and IR scans, to help detect
programming errors. For IR scans, @command{verify_ircapture}
must also be enabled.
Default is enabled.
@end deffn

@section TAP state names
@cindex TAP state names

The @var{tap_state} names used by OpenOCD in the @command{drscan},
@command{irscan}, and @command{pathmove} commands are the same
as those used in SVF boundary scan documents, except that
SVF uses @sc{idle} instead of @sc{run/idle}.

@itemize @bullet
@item @b{RESET} ... @emph{stable} (with TMS high);
acts as if TRST were pulsed
@item @b{RUN/IDLE} ... @emph{stable}; don't assume this always means IDLE
@item @b{DRSELECT}
@item @b{DRCAPTURE}
@item @b{DRSHIFT} ... @emph{stable}; TDI/TDO shifting
through the data register
@item @b{DREXIT1}
@item @b{DRPAUSE} ... @emph{stable}; data register ready
for update or more shifting
@item @b{DREXIT2}
@item @b{DRUPDATE}
@item @b{IRSELECT}
@item @b{IRCAPTURE}
@item @b{IRSHIFT} ... @emph{stable}; TDI/TDO shifting
through the instruction register
@item @b{IREXIT1}
@item @b{IRPAUSE} ... @emph{stable}; instruction register ready
for update or more shifting
@item @b{IREXIT2}
@item @b{IRUPDATE}
@end itemize

Note that only six of those states are fully ``stable'' in the
face of TMS fixed (low except for @sc{reset})
and a free-running JTAG clock. For all the
others, the next TCK transition changes to a new state.

@itemize @bullet
@item From @sc{drshift} and @sc{irshift}, clock transitions will
produce side effects by changing register contents. The values
to be latched in upcoming @sc{drupdate} or @sc{irupdate} states
may not be as expected.
@item @sc{run/idle}, @sc{drpause}, and @sc{irpause} are reasonable
choices after @command{drscan} or @command{irscan} commands,
since they are free of JTAG side effects.
@item @sc{run/idle} may have side effects that appear at non-JTAG
levels, such as advancing the ARM9E-S instruction pipeline.
Consult the documentation for the TAP(s) you are working with.
@end itemize

@node Boundary Scan Commands
@chapter Boundary Scan Commands

One of the original purposes of JTAG was to support
boundary scan based hardware testing.
Although its primary focus is to support On-Chip Debugging,
OpenOCD also includes some boundary scan commands.

@section SVF: Serial Vector Format
@cindex Serial Vector Format
@cindex SVF

The Serial Vector Format, better known as @dfn{SVF}, is a
way to represent JTAG test patterns in text files.
In a debug session using JTAG for its transport protocol,
OpenOCD supports running such test files.

@deffn {Command} {svf} @file{filename} [@option{-tap @var{tapname}}] [@option{[-]quiet}] @
                     [@option{[-]nil}] [@option{[-]progress}] [@option{[-]ignore_error}]
This issues a JTAG reset (Test-Logic-Reset) and then
runs the SVF script from @file{filename}.

Arguments can be specified in any order; the optional dash doesn't
affect their semantics.

Command options:
@itemize @minus
@item @option{-tap @var{tapname}} ignore IR and DR headers and footers
specified by the SVF file with HIR, TIR, HDR and TDR commands;
instead, calculate them automatically according to the current JTAG
chain configuration, targeting @var{tapname};
@item @option{[-]quiet} do not log every command before execution;
@item @option{[-]nil} ``dry run'', i.e., do not perform any operations
on the real interface;
@item @option{[-]progress} enable progress indication;
@item @option{[-]ignore_error} continue execution despite TDO check
errors.
@end itemize
@end deffn

@section XSVF: Xilinx Serial Vector Format
@cindex Xilinx Serial Vector Format
@cindex XSVF

The Xilinx Serial Vector Format, better known as @dfn{XSVF}, is a
binary representation of SVF which is optimized for use with
Xilinx devices.
In a debug session using JTAG for its transport protocol,
OpenOCD supports running such test files.

@quotation Important
Not all XSVF commands are supported.
@end quotation

@deffn {Command} {xsvf} (tapname|@option{plain}) filename [@option{virt2}] [@option{quiet}]
This issues a JTAG reset (Test-Logic-Reset) and then
runs the XSVF script from @file{filename}.
When a @var{tapname} is specified, the commands are directed at
that TAP.
When @option{virt2} is specified, the @sc{xruntest} command counts
are interpreted as TCK cycles instead of microseconds.
Unless the @option{quiet} option is specified,
messages are logged for comments and some retries.
@end deffn

The OpenOCD sources also include two utility scripts
for working with XSVF; they are not currently installed
after building the software.
You may find them useful:

@itemize
@item @emph{svf2xsvf} ... converts SVF files into the extended XSVF
syntax understood by the @command{xsvf} command; see notes below.
@item @emph{xsvfdump} ... converts XSVF files into a text output format;
understands the OpenOCD extensions.
@end itemize

The input format accepts a handful of non-standard extensions.
These include three opcodes corresponding to SVF extensions
from Lattice Semiconductor (LCOUNT, LDELAY, LDSR), and
two opcodes supporting a more accurate translation of SVF
(XTRST, XWAITSTATE).
If @emph{xsvfdump} shows a file is using those opcodes, it
probably will not be usable with other XSVF tools.


@section IPDBG: JTAG-Host server
@cindex IPDBG JTAG-Host server
@cindex IPDBG

IPDBG is a set of tools to debug IP-Cores. It comprises, among others, a logic analyzer and an arbitrary
waveform generator. These are synthesize-able hardware descriptions of
logic circuits in addition to software for control, visualization and further analysis.
In a session using JTAG for its transport protocol, OpenOCD supports the function
of a JTAG-Host. The JTAG-Host is needed to connect the circuit over JTAG to the
control-software. For more details see @url{http://ipdbg.org}.

@deffn {Command} {ipdbg} [@option{-start|-stop}] @option{-tap @var{tapname}} @option{-hub @var{ir_value} [@var{dr_length}]} [@option{-port @var{number}}] [@option{-tool @var{number}}] [@option{-vir [@var{vir_value} [@var{length} [@var{instr_code}]]]}]
Starts or stops a IPDBG JTAG-Host server. Arguments can be specified in any order.

Command options:
@itemize @bullet
@item @option{-start|-stop} starts or stops a IPDBG JTAG-Host server (default: start).
@item @option{-tap @var{tapname}} targeting the TAP @var{tapname}.
@item @option{-hub @var{ir_value}} states that the JTAG hub is
reachable with dr-scans while the JTAG instruction register has the value @var{ir_value}.
@item @option{-port @var{number}} tcp port number where the JTAG-Host is listening.
@item @option{-tool @var{number}} number of the tool/feature. These corresponds to the ports "data_(up/down)_(0..6)" at the JtagHub.
@item @option{-vir [@var{vir_value} [@var{length} [@var{instr_code}]]]} On some devices, the user data-register is only reachable if there is a
specific value in a second dr. This second dr is called vir (virtual ir). With this parameter given, the IPDBG satisfies this condition prior an
access to the IPDBG-Hub. The value shifted into the vir is given by the first parameter @var{vir_value} (default: 0x11). The second
parameter @var{length} is the length of the vir data register (default: 5). With the @var{instr_code} (default: 0x00e) parameter the ir value to
shift data through vir can be configured.
@end itemize
@end deffn

Examples:
@example
ipdbg -start -tap xc6s.tap -hub 0x02 -port 4242 -tool 4
@end example
Starts a server listening on tcp-port 4242 which connects to tool 4.
The connection is through the TAP of a Xilinx Spartan 6 on USER1 instruction (tested with a papillion pro board).

@example
ipdbg -start -tap 10m50.tap -hub 0x00C -vir -port 60000 -tool 1
@end example
Starts a server listening on tcp-port 60000 which connects to tool 1 (data_up_1/data_down_1).
The connection is through the TAP of a Intel MAX10 virtual jtag component (sld_instance_index is 0; sld_ir_width is smaller than 5).

@node Utility Commands
@chapter Utility Commands
@cindex Utility Commands

@section RAM testing
@cindex RAM testing

There is often a need to stress-test random access memory (RAM) for
errors. OpenOCD comes with a Tcl implementation of well-known memory
testing procedures allowing the detection of all sorts of issues with
electrical wiring, defective chips, PCB layout and other common
hardware problems.

To use them, you usually need to initialise your RAM controller first;
consult your SoC's documentation to get the recommended list of
register operations and translate them to the corresponding
@command{mww}/@command{mwb} commands.

Load the memory testing functions with

@example
source [find tools/memtest.tcl]
@end example

to get access to the following facilities:

@deffn {Command} {memTestDataBus} address
Test the data bus wiring in a memory region by performing a walking
1's test at a fixed address within that region.
@end deffn

@deffn {Command} {memTestAddressBus} baseaddress size
Perform a walking 1's test on the relevant bits of the address and
check for aliasing. This test will find single-bit address failures
such as stuck-high, stuck-low, and shorted pins.
@end deffn

@deffn {Command} {memTestDevice} baseaddress size
Test the integrity of a physical memory device by performing an
increment/decrement test over the entire region. In the process every
storage bit in the device is tested as zero and as one.
@end deffn

@deffn {Command} {runAllMemTests} baseaddress size
Run all of the above tests over a specified memory region.
@end deffn

@section Firmware recovery helpers
@cindex Firmware recovery

OpenOCD includes an easy-to-use script to facilitate mass-market
devices recovery with JTAG.

For quickstart instructions run:
@example
openocd -f tools/firmware-recovery.tcl -c firmware_help
@end example

@node GDB and OpenOCD
@chapter GDB and OpenOCD
@cindex GDB
OpenOCD complies with the remote gdbserver protocol and, as such, can be used
to debug remote targets.
Setting up GDB to work with OpenOCD can involve several components:

@itemize
@item The OpenOCD server support for GDB may need to be configured.
@xref{gdbconfiguration,,GDB Configuration}.
@item GDB's support for OpenOCD may need configuration,
as shown in this chapter.
@item If you have a GUI environment like Eclipse,
that also will probably need to be configured.
@end itemize

Of course, the version of GDB you use will need to be one which has
been built to know about the target CPU you're using. It's probably
part of the tool chain you're using. For example, if you are doing
cross-development for ARM on an x86 PC, instead of using the native
x86 @command{gdb} command you might use @command{arm-none-eabi-gdb}
if that's the tool chain used to compile your code.

@section Connecting to GDB
@cindex Connecting to GDB
Use GDB 6.7 or newer with OpenOCD if you run into trouble. For
instance GDB 6.3 has a known bug that produces bogus memory access
errors, which has since been fixed; see
@url{http://osdir.com/ml/gdb.bugs.discuss/2004-12/msg00018.html}

OpenOCD can communicate with GDB in two ways:

@enumerate
@item
A socket (TCP/IP) connection is typically started as follows:
@example
target extended-remote localhost:3333
@end example
This would cause GDB to connect to the gdbserver on the local pc using port 3333.

The extended remote protocol is a super-set of the remote protocol and should
be the preferred choice. More details are available in GDB documentation
@url{https://sourceware.org/gdb/onlinedocs/gdb/Connecting.html}

To speed-up typing, any GDB command can be abbreviated, including the extended
remote command above that becomes:
@example
tar ext :3333
@end example

@b{Note:} If any backward compatibility issue requires using the old remote
protocol in place of the extended remote one, the former protocol is still
available through the command:
@example
target remote localhost:3333
@end example

@item
A pipe connection is typically started as follows:
@example
target extended-remote | \
       openocd -c "gdb_port pipe; log_output openocd.log"
@end example
This would cause GDB to run OpenOCD and communicate using pipes (stdin/stdout).
Using this method has the advantage of GDB starting/stopping OpenOCD for the debug
session. log_output sends the log output to a file to ensure that the pipe is
not saturated when using higher debug level outputs.
@end enumerate

To list the available OpenOCD commands type @command{monitor help} on the
GDB command line.

@section Sample GDB session startup

With the remote protocol, GDB sessions start a little differently
than they do when you're debugging locally.
Here's an example showing how to start a debug session with a
small ARM program.
In this case the program was linked to be loaded into SRAM on a Cortex-M3.
Most programs would be written into flash (address 0) and run from there.

@example
$ arm-none-eabi-gdb example.elf
(gdb) target extended-remote localhost:3333
Remote debugging using localhost:3333
...
(gdb) monitor reset halt
...
(gdb) load
Loading section .vectors, size 0x100 lma 0x20000000
Loading section .text, size 0x5a0 lma 0x20000100
Loading section .data, size 0x18 lma 0x200006a0
Start address 0x2000061c, load size 1720
Transfer rate: 22 KB/sec, 573 bytes/write.
(gdb) continue
Continuing.
...
@end example

You could then interrupt the GDB session to make the program break,
type @command{where} to show the stack, @command{list} to show the
code around the program counter, @command{step} through code,
set breakpoints or watchpoints, and so on.

@section Configuring GDB for OpenOCD

OpenOCD supports the gdb @option{qSupported} packet, this enables information
to be sent by the GDB remote server (i.e. OpenOCD) to GDB. Typical information includes
packet size and the device's memory map.
You do not need to configure the packet size by hand,
and the relevant parts of the memory map should be automatically
set up when you declare (NOR) flash banks.

However, there are other things which GDB can't currently query.
You may need to set those up by hand.
As OpenOCD starts up, you will often see a line reporting
something like:

@example
Info : lm3s.cpu: hardware has 6 breakpoints, 4 watchpoints
@end example

You can pass that information to GDB with these commands:

@example
set remote hardware-breakpoint-limit 6
set remote hardware-watchpoint-limit 4
@end example

With that particular hardware (Cortex-M3) the hardware breakpoints
only work for code running from flash memory. Most other ARM systems
do not have such restrictions.

Rather than typing such commands interactively, you may prefer to
save them in a file and have GDB execute them as it starts, perhaps
using a @file{.gdbinit} in your project directory or starting GDB
using @command{gdb -x filename}.

@section Programming using GDB
@cindex Programming using GDB
@anchor{programmingusinggdb}

By default the target memory map is sent to GDB. This can be disabled by
the following OpenOCD configuration option:
@example
gdb_memory_map disable
@end example
For this to function correctly a valid flash configuration must also be set
in OpenOCD. For faster performance you should also configure a valid
working area.

Informing GDB of the memory map of the target will enable GDB to protect any
flash areas of the target and use hardware breakpoints by default. This means
that the OpenOCD option @command{gdb_breakpoint_override} is not required when
using a memory map. @xref{gdbbreakpointoverride,,gdb_breakpoint_override}.

To view the configured memory map in GDB, use the GDB command @option{info mem}.
All other unassigned addresses within GDB are treated as RAM.

GDB 6.8 and higher set any memory area not in the memory map as inaccessible.
This can be changed to the old behaviour by using the following GDB command
@example
set mem inaccessible-by-default off
@end example

If @command{gdb_flash_program enable} is also used, GDB will be able to
program any flash memory using the vFlash interface.

GDB will look at the target memory map when a load command is given, if any
areas to be programmed lie within the target flash area the vFlash packets
will be used.

If the target needs configuring before GDB programming, set target
event gdb-flash-erase-start:
@example
$_TARGETNAME configure -event gdb-flash-erase-start BODY
@end example
@xref{targetevents,,Target Events}, for other GDB programming related events.

To verify any flash programming the GDB command @option{compare-sections}
can be used.

@section Using GDB as a non-intrusive memory inspector
@cindex Using GDB as a non-intrusive memory inspector
@anchor{gdbmeminspect}

If your project controls more than a blinking LED, let's say a heavy industrial
robot or an experimental nuclear reactor, stopping the controlling process
just because you want to attach GDB is not a good option.

OpenOCD does not support GDB non-stop mode (might be implemented in the future).
Though there is a possible setup where the target does not get stopped
and GDB treats it as it were running.
If the target supports background access to memory while it is running,
you can use GDB in this mode to inspect memory (mainly global variables)
without any intrusion of the target process.

Remove default setting of gdb-attach event. @xref{targetevents,,Target Events}.
Place following command after target configuration:
@example
$_TARGETNAME configure -event gdb-attach @{@}
@end example

If any of installed flash banks does not support probe on running target,
switch off gdb_memory_map:
@example
gdb_memory_map disable
@end example

Ensure GDB is configured without interrupt-on-connect.
Some GDB versions set it by default, some does not.
@example
set remote interrupt-on-connect off
@end example

If you switched gdb_memory_map off, you may want to setup GDB memory map
manually or issue @command{set mem inaccessible-by-default off}

Now you can issue GDB command @command{target extended-remote ...} and inspect memory
of a running target. Do not use GDB commands @command{continue},
@command{step} or @command{next} as they synchronize GDB with your target
and GDB would require stopping the target to get the prompt back.

Do not use this mode under an IDE like Eclipse as it caches values of
previously shown variables.

It's also possible to connect more than one GDB to the same target by the
target's configuration option @code{-gdb-max-connections}. This allows, for
example, one GDB to run a script that continuously polls a set of variables
while other GDB can be used interactively. Be extremely careful in this case,
because the two GDB can easily get out-of-sync.

@section RTOS Support
@cindex RTOS Support
@anchor{gdbrtossupport}

OpenOCD includes RTOS support, this will however need enabling as it defaults to disabled.
It can be enabled by passing @option{-rtos} arg to the target. @xref{rtostype,,RTOS Type}.

@xref{Threads, Debugging Programs with Multiple Threads,
Debugging Programs with Multiple Threads, gdb, GDB manual}, for details about relevant
GDB commands.

@* An example setup is below:

@example
$_TARGETNAME configure -rtos auto
@end example

This will attempt to auto detect the RTOS within your application.

Currently supported rtos's include:
@itemize @bullet
@item @option{eCos}
@item @option{ThreadX}
@item @option{FreeRTOS}
@item @option{linux}
@item @option{ChibiOS}
@item @option{embKernel}
@item @option{mqx}
@item @option{uCOS-III}
@item @option{nuttx}
@item @option{RIOT}
@item @option{hwthread} (This is not an actual RTOS. @xref{usingopenocdsmpwithgdb,,Using OpenOCD SMP with GDB}.)
@item @option{Zephyr}
@end itemize

Before an RTOS can be detected, it must export certain symbols; otherwise, it cannot
be used by OpenOCD. Below is a list of the required symbols for each supported RTOS.

@table @code
@item eCos symbols
Cyg_Thread::thread_list, Cyg_Scheduler_Base::current_thread.
@item ThreadX symbols
_tx_thread_current_ptr, _tx_thread_created_ptr, _tx_thread_created_count.
@item FreeRTOS symbols
@raggedright
pxCurrentTCB, pxReadyTasksLists, xDelayedTaskList1, xDelayedTaskList2,
pxDelayedTaskList, pxOverflowDelayedTaskList, xPendingReadyList,
uxCurrentNumberOfTasks, uxTopUsedPriority.
@end raggedright
@item linux symbols
init_task.
@item ChibiOS symbols
rlist, ch_debug, chSysInit.
@item embKernel symbols
Rtos::sCurrentTask, Rtos::sListReady, Rtos::sListSleep,
Rtos::sListSuspended, Rtos::sMaxPriorities, Rtos::sCurrentTaskCount.
@item mqx symbols
_mqx_kernel_data, MQX_init_struct.
@item uC/OS-III symbols
OSRunning, OSTCBCurPtr, OSTaskDbgListPtr, OSTaskQty.
@item nuttx symbols
g_readytorun, g_tasklisttable.
@item RIOT symbols
@raggedright
sched_threads, sched_num_threads, sched_active_pid, max_threads,
_tcb_name_offset.
@end raggedright
@item Zephyr symbols
_kernel, _kernel_openocd_offsets, _kernel_openocd_size_t_size
@end table

For most RTOS supported the above symbols will be exported by default. However for
some, eg. FreeRTOS, uC/OS-III and Zephyr, extra steps must be taken.

Zephyr must be compiled with the DEBUG_THREAD_INFO option. This will generate some symbols
with information needed in order to build the list of threads.

FreeRTOS and uC/OS-III RTOSes may require additional OpenOCD-specific file to be linked
along with the project:

@table @code
@item FreeRTOS
contrib/rtos-helpers/FreeRTOS-openocd.c
@item uC/OS-III
contrib/rtos-helpers/uCOS-III-openocd.c
@end table

@anchor{usingopenocdsmpwithgdb}
@section Using OpenOCD SMP with GDB
@cindex SMP
@cindex RTOS
@cindex hwthread
OpenOCD includes a pseudo RTOS called @emph{hwthread} that presents CPU cores
("hardware threads") in an SMP system as threads to GDB. With this extension,
GDB can be used to inspect the state of an SMP system in a natural way.
After halting the system, using the GDB command @command{info threads} will
list the context of each active CPU core in the system. GDB's @command{thread}
command can be used to switch the view to a different CPU core.
The @command{step} and @command{stepi} commands can be used to step a specific core
while other cores are free-running or remain halted, depending on the
scheduler-locking mode configured in GDB.

@section Legacy SMP core switching support
@quotation Note
This method is deprecated in favor of the @emph{hwthread} pseudo RTOS.
@end quotation

For SMP support following GDB serial protocol packet have been defined :
@itemize @bullet
@item j - smp status request
@item J - smp set request
@end itemize

OpenOCD implements :
@itemize @bullet
@item @option{jc} packet for reading core id displayed by
GDB connection. Reply is @option{XXXXXXXX} (8 hex digits giving core id) or
 @option{E01} for target not smp.
@item @option{JcXXXXXXXX} (8 hex digits) packet for setting core id displayed at next GDB continue
(core id -1 is reserved for returning to normal resume mode). Reply @option{E01}
for target not smp or @option{OK} on success.
@end itemize

Handling of this packet within GDB can be done :
@itemize @bullet
@item by the creation of an internal variable (i.e @option{_core}) by mean
of function allocate_computed_value allowing following GDB command.
@example
set $_core 1
#Jc01 packet is sent
print $_core
#jc packet is sent and result is affected in $
@end example

@item by the usage of GDB maintenance command as described in following example (2 cpus in SMP with
core id 0 and 1 @pxref{definecputargetsworkinginsmp,,Define CPU targets working in SMP}).

@example
# toggle0 : force display of coreid 0
define toggle0
maint packet Jc0
continue
main packet Jc-1
end
# toggle1 : force display of coreid 1
define toggle1
maint packet Jc1
continue
main packet Jc-1
end
@end example
@end itemize

@node Tcl Scripting API
@chapter Tcl Scripting API
@cindex Tcl Scripting API
@cindex Tcl scripts
@section API rules

Tcl commands are stateless; e.g. the @command{telnet} command has
a concept of currently active target, the Tcl API proc's take this sort
of state information as an argument to each proc.

There are three main types of return values: single value, name value
pair list and lists.

Name value pair. The proc 'foo' below returns a name/value pair
list.

@example
>  set foo(me)  Duane
>  set foo(you) Oyvind
>  set foo(mouse) Micky
>  set foo(duck) Donald
@end example

If one does this:

@example
>  set foo
@end example

The result is:

@example
me Duane you Oyvind mouse Micky duck Donald
@end example

Thus, to get the names of the associative array is easy:

@verbatim
foreach { name value } [set foo] {
        puts "Name: $name, Value: $value"
}
@end verbatim

Lists returned should be relatively small. Otherwise, a range
should be passed in to the proc in question.

@section Internal low-level Commands

By "low-level", we mean commands that a human would typically not
invoke directly.

@itemize
@item @b{flash banks} <@var{driver}> <@var{base}> <@var{size}> <@var{chip_width}> <@var{bus_width}> <@var{target}> [@option{driver options} ...]

Return information about the flash banks

@item @b{capture} <@var{command}>

Run <@var{command}> and return full log output that was produced during
its execution. Example:

@example
> capture "reset init"
@end example

@end itemize

OpenOCD commands can consist of two words, e.g. "flash banks". The
@file{startup.tcl} "unknown" proc will translate this into a Tcl proc
called "flash_banks".

@section Tcl RPC server
@cindex RPC

OpenOCD provides a simple RPC server that allows to run arbitrary Tcl
commands and receive the results.

To access it, your application needs to connect to a configured TCP port
(see @command{tcl_port}). Then it can pass any string to the
interpreter terminating it with @code{0x1a} and wait for the return
value (it will be terminated with @code{0x1a} as well). This can be
repeated as many times as desired without reopening the connection.

It is not needed anymore to prefix the OpenOCD commands with
@code{ocd_} to get the results back. But sometimes you might need the
@command{capture} command.

See @file{contrib/rpc_examples/} for specific client implementations.

@section Tcl RPC server notifications
@cindex RPC Notifications

Notifications are sent asynchronously to other commands being executed over
the RPC server, so the port must be polled continuously.

Target event, state and reset notifications are emitted as Tcl associative arrays
in the following format.

@verbatim
type target_event event [event-name]
type target_state state [state-name]
type target_reset mode [reset-mode]
@end verbatim

@deffn {Command} {tcl_notifications} [on/off]
Toggle output of target notifications to the current Tcl RPC server.
Only available from the Tcl RPC server.
Defaults to off.

@end deffn

@section Tcl RPC server trace output
@cindex RPC trace output

Trace data is sent asynchronously to other commands being executed over
the RPC server, so the port must be polled continuously.

Target trace data is emitted as a Tcl associative array in the following format.

@verbatim
type target_trace data [trace-data-hex-encoded]
@end verbatim

@deffn {Command} {tcl_trace} [on/off]
Toggle output of target trace data to the current Tcl RPC server.
Only available from the Tcl RPC server.
Defaults to off.

See an example application here:
@url{https://github.com/apmorton/OpenOcdTraceUtil} [OpenOcdTraceUtil]

@end deffn

@node FAQ
@chapter FAQ
@cindex faq
@enumerate
@anchor{faqrtck}
@item @b{RTCK, also known as: Adaptive Clocking - What is it?}
@cindex RTCK
@cindex adaptive clocking
@*

In digital circuit design it is often referred to as ``clock
synchronisation'' the JTAG interface uses one clock (TCK or TCLK)
operating at some speed, your CPU target is operating at another.
The two clocks are not synchronised, they are ``asynchronous''

In order for the two to work together they must be synchronised
well enough to work; JTAG can't go ten times faster than the CPU,
for example. There are 2 basic options:
@enumerate
@item
Use a special "adaptive clocking" circuit to change the JTAG
clock rate to match what the CPU currently supports.
@item
The JTAG clock must be fixed at some speed that's enough slower than
the CPU clock that all TMS and TDI transitions can be detected.
@end enumerate

@b{Does this really matter?} For some chips and some situations, this
is a non-issue, like a 500MHz ARM926 with a 5 MHz JTAG link;
the CPU has no difficulty keeping up with JTAG.
Startup sequences are often problematic though, as are other
situations where the CPU clock rate changes (perhaps to save
power).

For example, Atmel AT91SAM chips start operation from reset with
a 32kHz system clock. Boot firmware may activate the main oscillator
and PLL before switching to a faster clock (perhaps that 500 MHz
ARM926 scenario).
If you're using JTAG to debug that startup sequence, you must slow
the JTAG clock to sometimes 1 to 4kHz. After startup completes,
JTAG can use a faster clock.

Consider also debugging a 500MHz ARM926 hand held battery powered
device that enters a low power ``deep sleep'' mode, at 32kHz CPU
clock, between keystrokes unless it has work to do. When would
that 5 MHz JTAG clock be usable?

@b{Solution #1 - A special circuit}

In order to make use of this,
your CPU, board, and JTAG adapter must all support the RTCK
feature. Not all of them support this; keep reading!

The RTCK ("Return TCK") signal in some ARM chips is used to help with
this problem. ARM has a good description of the problem described at
this link: @url{http://www.arm.com/support/faqdev/4170.html} [checked
28/nov/2008]. Link title: ``How does the JTAG synchronisation logic
work? / how does adaptive clocking work?''.

The nice thing about adaptive clocking is that ``battery powered hand
held device example'' - the adaptiveness works perfectly all the
time. One can set a break point or halt the system in the deep power
down code, slow step out until the system speeds up.

Note that adaptive clocking may also need to work at the board level,
when a board-level scan chain has multiple chips.
Parallel clock voting schemes are good way to implement this,
both within and between chips, and can easily be implemented
with a CPLD.
It's not difficult to have logic fan a module's input TCK signal out
to each TAP in the scan chain, and then wait until each TAP's RTCK comes
back with the right polarity before changing the output RTCK signal.
Texas Instruments makes some clock voting logic available
for free (with no support) in VHDL form; see
@url{http://tiexpressdsp.com/index.php/Adaptive_Clocking}

@b{Solution #2 - Always works - but may be slower}

Often this is a perfectly acceptable solution.

In most simple terms: Often the JTAG clock must be 1/10 to 1/12 of
the target clock speed. But what that ``magic division'' is varies
depending on the chips on your board.
@b{ARM rule of thumb} Most ARM based systems require an 6:1 division;
ARM11 cores use an 8:1 division.
@b{Xilinx rule of thumb} is 1/12 the clock speed.

Note: most full speed FT2232 based JTAG adapters are limited to a
maximum of 6MHz. The ones using USB high speed chips (FT2232H)
often support faster clock rates (and adaptive clocking).

You can still debug the 'low power' situations - you just need to
either use a fixed and very slow JTAG clock rate ... or else
manually adjust the clock speed at every step. (Adjusting is painful
and tedious, and is not always practical.)

It is however easy to ``code your way around it'' - i.e.: Cheat a little,
have a special debug mode in your application that does a ``high power
sleep''. If you are careful - 98% of your problems can be debugged
this way.

Note that on ARM you may need to avoid using the @emph{wait for interrupt}
operation in your idle loops even if you don't otherwise change the CPU
clock rate.
That operation gates the CPU clock, and thus the JTAG clock; which
prevents JTAG access. One consequence is not being able to @command{halt}
cores which are executing that @emph{wait for interrupt} operation.

To set the JTAG frequency use the command:

@example
# Example: 1.234MHz
adapter speed 1234
@end example


@item @b{Win32 Pathnames} Why don't backslashes work in Windows paths?

OpenOCD uses Tcl and a backslash is an escape char. Use @{ and @}
around Windows filenames.

@example
> echo \a

> echo @{\a@}
\a
> echo "\a"

>
@end example


@item @b{Missing: cygwin1.dll} OpenOCD complains about a missing cygwin1.dll.

Make sure you have Cygwin installed, or at least a version of OpenOCD that
claims to come with all the necessary DLLs. When using Cygwin, try launching
OpenOCD from the Cygwin shell.

@item @b{Breakpoint Issue} I'm trying to set a breakpoint using GDB (or a front-end like Insight or
Eclipse), but OpenOCD complains that "Info: arm7_9_common.c:213
arm7_9_add_breakpoint(): sw breakpoint requested, but software breakpoints not enabled".

GDB issues software breakpoints when a normal breakpoint is requested, or to implement
source-line single-stepping. On ARMv4T systems, like ARM7TDMI, ARM720T or ARM920T,
software breakpoints consume one of the two available hardware breakpoints.

@item @b{LPC2000 Flash} When erasing or writing LPC2000 on-chip flash, the operation fails at random.

Make sure the core frequency specified in the @option{flash lpc2000} line matches the
clock at the time you're programming the flash. If you've specified the crystal's
frequency, make sure the PLL is disabled. If you've specified the full core speed
(e.g. 60MHz), make sure the PLL is enabled.

@item @b{Amontec Chameleon} When debugging using an Amontec Chameleon in its JTAG Accelerator configuration,
I keep getting "Error: amt_jtagaccel.c:184 amt_wait_scan_busy(): amt_jtagaccel timed
out while waiting for end of scan, rtck was disabled".

Make sure your PC's parallel port operates in EPP mode. You might have to try several
settings in your PC BIOS (ECP, EPP, and different versions of those).

@item @b{Data Aborts} When debugging with OpenOCD and GDB (plain GDB, Insight, or Eclipse),
I get lots of "Error: arm7_9_common.c:1771 arm7_9_read_memory():
memory read caused data abort".

The errors are non-fatal, and are the result of GDB trying to trace stack frames
beyond the last valid frame. It might be possible to prevent this by setting up
a proper "initial" stack frame, if you happen to know what exactly has to
be done, feel free to add this here.

@b{Simple:} In your startup code - push 8 registers of zeros onto the
stack before calling main(). What GDB is doing is ``climbing'' the run
time stack by reading various values on the stack using the standard
call frame for the target. GDB keeps going - until one of 2 things
happen @b{#1} an invalid frame is found, or @b{#2} some huge number of
stackframes have been processed. By pushing zeros on the stack, GDB
gracefully stops.

@b{Debugging Interrupt Service Routines} - In your ISR before you call
your C code, do the same - artificially push some zeros onto the stack,
remember to pop them off when the ISR is done.

@b{Also note:} If you have a multi-threaded operating system, they
often do not @b{in the interest of saving memory} waste these few
bytes. Painful...


@item @b{JTAG Reset Config} I get the following message in the OpenOCD console (or log file):
"Warning: arm7_9_common.c:679 arm7_9_assert_reset(): srst resets test logic, too".

This warning doesn't indicate any serious problem, as long as you don't want to
debug your core right out of reset. Your .cfg file specified @option{reset_config
trst_and_srst srst_pulls_trst} to tell OpenOCD that either your board,
your debugger or your target uC (e.g. LPC2000) can't assert the two reset signals
independently. With this setup, it's not possible to halt the core right out of
reset, everything else should work fine.

@item @b{USB Power} When using OpenOCD in conjunction with Amontec JTAGkey and the Yagarto
toolchain (Eclipse, arm-elf-gcc, arm-elf-gdb), the debugging seems to be
unstable. When single-stepping over large blocks of code, GDB and OpenOCD
quit with an error message. Is there a stability issue with OpenOCD?

No, this is not a stability issue concerning OpenOCD. Most users have solved
this issue by simply using a self-powered USB hub, which they connect their
Amontec JTAGkey to. Apparently, some computers do not provide a USB power
supply stable enough for the Amontec JTAGkey to be operated.

@b{Laptops running on battery have this problem too...}

@item @b{GDB Disconnects} When using the Amontec JTAGkey, sometimes OpenOCD crashes with the following
error message: "Error: gdb_server.c:101 gdb_get_char(): read: 10054".
What does that mean and what might be the reason for this?

Error code 10054 corresponds to WSAECONNRESET, which means that the debugger (GDB)
has closed the connection to OpenOCD. This might be a GDB issue.

@item @b{LPC2000 Flash} In the configuration file in the section where flash device configurations
are described, there is a parameter for specifying the clock frequency
for LPC2000 internal flash devices (e.g. @option{flash bank $_FLASHNAME lpc2000
0x0 0x40000 0 0 $_TARGETNAME lpc2000_v1 14746 calc_checksum}), which must be
specified in kilohertz. However, I do have a quartz crystal of a
frequency that contains fractions of kilohertz (e.g. 14,745,600 Hz,
i.e. 14,745.600 kHz). Is it possible to specify real numbers for the
clock frequency?

No. The clock frequency specified here must be given as an integral number.
However, this clock frequency is used by the In-Application-Programming (IAP)
routines of the LPC2000 family only, which seems to be very tolerant concerning
the given clock frequency, so a slight difference between the specified clock
frequency and the actual clock frequency will not cause any trouble.

@item @b{Command Order} Do I have to keep a specific order for the commands in the configuration file?

Well, yes and no. Commands can be given in arbitrary order, yet the
devices listed for the JTAG scan chain must be given in the right
order (jtag newdevice), with the device closest to the TDO-Pin being
listed first. In general, whenever objects of the same type exist
which require an index number, then these objects must be given in the
right order (jtag newtap, targets and flash banks - a target
references a jtag newtap and a flash bank references a target).

You can use the ``scan_chain'' command to verify and display the tap order.

Also, some commands can't execute until after @command{init} has been
processed. Such commands include @command{nand probe} and everything
else that needs to write to controller registers, perhaps for setting
up DRAM and loading it with code.

@anchor{faqtaporder}
@item @b{JTAG TAP Order} Do I have to declare the TAPS in some
particular order?

Yes; whenever you have more than one, you must declare them in
the same order used by the hardware.

Many newer devices have multiple JTAG TAPs. For example:
STMicroelectronics STM32 chips have two TAPs, a ``boundary scan TAP'' and
``Cortex-M3'' TAP. Example: The STM32 reference manual, Document ID:
RM0008, Section 26.5, Figure 259, page 651/681, the ``TDI'' pin is
connected to the boundary scan TAP, which then connects to the
Cortex-M3 TAP, which then connects to the TDO pin.

Thus, the proper order for the STM32 chip is: (1) The Cortex-M3, then
(2) The boundary scan TAP. If your board includes an additional JTAG
chip in the scan chain (for example a Xilinx CPLD or FPGA) you could
place it before or after the STM32 chip in the chain. For example:

@itemize @bullet
@item OpenOCD_TDI(output) -> STM32 TDI Pin (BS Input)
@item STM32 BS TDO (output) -> STM32 Cortex-M3 TDI (input)
@item STM32 Cortex-M3 TDO (output) -> SM32 TDO Pin
@item STM32 TDO Pin (output) -> Xilinx TDI Pin (input)
@item Xilinx TDO Pin -> OpenOCD TDO (input)
@end itemize

The ``jtag device'' commands would thus be in the order shown below. Note:

@itemize @bullet
@item jtag newtap Xilinx tap -irlen ...
@item jtag newtap stm32  cpu -irlen ...
@item jtag newtap stm32  bs  -irlen ...
@item # Create the debug target and say where it is
@item target create stm32.cpu -chain-position stm32.cpu ...
@end itemize


@item @b{SYSCOMP} Sometimes my debugging session terminates with an error. When I look into the
log file, I can see these error messages: Error: arm7_9_common.c:561
arm7_9_execute_sys_speed(): timeout waiting for SYSCOMP

TODO.

@end enumerate

@node Tcl Crash Course
@chapter Tcl Crash Course
@cindex Tcl

Not everyone knows Tcl - this is not intended to be a replacement for
learning Tcl, the intent of this chapter is to give you some idea of
how the Tcl scripts work.

This chapter is written with two audiences in mind. (1) OpenOCD users
who need to understand a bit more of how Jim-Tcl works so they can do
something useful, and (2) those that want to add a new command to
OpenOCD.

@section Tcl Rule #1
There is a famous joke, it goes like this:
@enumerate
@item Rule #1: The wife is always correct
@item Rule #2: If you think otherwise, See Rule #1
@end enumerate

The Tcl equal is this:

@enumerate
@item Rule #1: Everything is a string
@item Rule #2: If you think otherwise, See Rule #1
@end enumerate

As in the famous joke, the consequences of Rule #1 are profound. Once
you understand Rule #1, you will understand Tcl.

@section Tcl Rule #1b
There is a second pair of rules.
@enumerate
@item Rule #1: Control flow does not exist. Only commands
@* For example: the classic FOR loop or IF statement is not a control
flow item, they are commands, there is no such thing as control flow
in Tcl.
@item Rule #2: If you think otherwise, See Rule #1
@* Actually what happens is this: There are commands that by
convention, act like control flow key words in other languages. One of
those commands is the word ``for'', another command is ``if''.
@end enumerate

@section Per Rule #1 - All Results are strings
Every Tcl command results in a string. The word ``result'' is used
deliberately. No result is just an empty string. Remember: @i{Rule #1 -
Everything is a string}

@section Tcl Quoting Operators
In life of a Tcl script, there are two important periods of time, the
difference is subtle.
@enumerate
@item Parse Time
@item Evaluation Time
@end enumerate

The two key items here are how ``quoted things'' work in Tcl. Tcl has
three primary quoting constructs, the [square-brackets] the
@{curly-braces@} and ``double-quotes''

By now you should know $VARIABLES always start with a $DOLLAR
sign. BTW: To set a variable, you actually use the command ``set'', as
in ``set VARNAME VALUE'' much like the ancient BASIC language ``let x
= 1'' statement, but without the equal sign.

@itemize @bullet
@item @b{[square-brackets]}
@* @b{[square-brackets]} are command substitutions. It operates much
like Unix Shell `back-ticks`. The result of a [square-bracket]
operation is exactly 1 string. @i{Remember Rule #1 - Everything is a
string}. These two statements are roughly identical:
@example
    # bash example
    X=`date`
    echo "The Date is: $X"
    # Tcl example
    set X [date]
    puts "The Date is: $X"
@end example
@item @b{``double-quoted-things''}
@* @b{``double-quoted-things''} are just simply quoted
text. $VARIABLES and [square-brackets] are expanded in place - the
result however is exactly 1 string. @i{Remember Rule #1 - Everything
is a string}
@example
    set x "Dinner"
    puts "It is now \"[date]\", $x is in 1 hour"
@end example
@item @b{@{Curly-Braces@}}
@*@b{@{Curly-Braces@}} are magic: $VARIABLES and [square-brackets] are
parsed, but are NOT expanded or executed. @{Curly-Braces@} are like
'single-quote' operators in BASH shell scripts, with the added
feature: @{curly-braces@} can be nested, single quotes can not. @{@{@{this is
nested 3 times@}@}@} NOTE: [date] is a bad example;
at this writing, Jim/OpenOCD does not have a date command.
@end itemize

@section Consequences of Rule 1/2/3/4

The consequences of Rule 1 are profound.

@subsection Tokenisation & Execution.

Of course, whitespace, blank lines and #comment lines are handled in
the normal way.

As a script is parsed, each (multi) line in the script file is
tokenised and according to the quoting rules. After tokenisation, that
line is immediately executed.

Multi line statements end with one or more ``still-open''
@{curly-braces@} which - eventually - closes a few lines later.

@subsection Command Execution

Remember earlier: There are no ``control flow''
statements in Tcl. Instead there are COMMANDS that simply act like
control flow operators.

Commands are executed like this:

@enumerate
@item Parse the next line into (argc) and (argv[]).
@item Look up (argv[0]) in a table and call its function.
@item Repeat until End Of File.
@end enumerate

It sort of works like this:
@example
    for(;;)@{
        ReadAndParse( &argc, &argv );

        cmdPtr = LookupCommand( argv[0] );

        (*cmdPtr->Execute)( argc, argv );
    @}
@end example

When the command ``proc'' is parsed (which creates a procedure
function) it gets 3 parameters on the command line. @b{1} the name of
the proc (function), @b{2} the list of parameters, and @b{3} the body
of the function. Not the choice of words: LIST and BODY. The PROC
command stores these items in a table somewhere so it can be found by
``LookupCommand()''

@subsection The FOR command

The most interesting command to look at is the FOR command. In Tcl,
the FOR command is normally implemented in C. Remember, FOR is a
command just like any other command.

When the ascii text containing the FOR command is parsed, the parser
produces 5 parameter strings, @i{(If in doubt: Refer to Rule #1)} they
are:

@enumerate 0
@item The ascii text 'for'
@item The start text
@item The test expression
@item The next text
@item The body text
@end enumerate

Sort of reminds you of ``main( int argc, char **argv )'' does it not?
Remember @i{Rule #1 - Everything is a string.} The key point is this:
Often many of those parameters are in @{curly-braces@} - thus the
variables inside are not expanded or replaced until later.

Remember that every Tcl command looks like the classic ``main( argc,
argv )'' function in C. In JimTCL - they actually look like this:

@example
int
MyCommand( Jim_Interp *interp,
           int *argc,
           Jim_Obj * const *argvs );
@end example

Real Tcl is nearly identical. Although the newer versions have
introduced a byte-code parser and interpreter, but at the core, it
still operates in the same basic way.

@subsection FOR command implementation

To understand Tcl it is perhaps most helpful to see the FOR
command. Remember, it is a COMMAND not a control flow structure.

In Tcl there are two underlying C helper functions.

Remember Rule #1 - You are a string.

The @b{first} helper parses and executes commands found in an ascii
string. Commands can be separated by semicolons, or newlines. While
parsing, variables are expanded via the quoting rules.

The @b{second} helper evaluates an ascii string as a numerical
expression and returns a value.

Here is an example of how the @b{FOR} command could be
implemented. The pseudo code below does not show error handling.
@example
void Execute_AsciiString( void *interp, const char *string );

int Evaluate_AsciiExpression( void *interp, const char *string );

int
MyForCommand( void *interp,
              int argc,
              char **argv )
@{
   if( argc != 5 )@{
       SetResult( interp, "WRONG number of parameters");
       return ERROR;
   @}

   // argv[0] = the ascii string just like C

   // Execute the start statement.
   Execute_AsciiString( interp, argv[1] );

   // Top of loop test
   for(;;)@{
        i = Evaluate_AsciiExpression(interp, argv[2]);
        if( i == 0 )
            break;

        // Execute the body
        Execute_AsciiString( interp, argv[3] );

        // Execute the LOOP part
        Execute_AsciiString( interp, argv[4] );
    @}

    // Return no error
    SetResult( interp, "" );
    return SUCCESS;
@}
@end example

Every other command IF, WHILE, FORMAT, PUTS, EXPR, everything works
in the same basic way.

@section OpenOCD Tcl Usage

@subsection source and find commands
@b{Where:} In many configuration files
@* Example: @b{ source [find FILENAME] }
@*Remember the parsing rules
@enumerate
@item The @command{find} command is in square brackets,
and is executed with the parameter FILENAME. It should find and return
the full path to a file with that name; it uses an internal search path.
The RESULT is a string, which is substituted into the command line in
place of the bracketed @command{find} command.
(Don't try to use a FILENAME which includes the "#" character.
That character begins Tcl comments.)
@item The @command{source} command is executed with the resulting filename;
it reads a file and executes as a script.
@end enumerate
@subsection format command
@b{Where:} Generally occurs in numerous places.
@* Tcl has no command like @b{printf()}, instead it has @b{format}, which is really more like
@b{sprintf()}.
@b{Example}
@example
    set x 6
    set y 7
    puts [format "The answer: %d" [expr @{$x * $y@}]]
@end example
@enumerate
@item The SET command creates 2 variables, X and Y.
@item The double [nested] EXPR command performs math
@* The EXPR command produces numerical result as a string.
@* Refer to Rule #1
@item The format command is executed, producing a single string
@* Refer to Rule #1.
@item The PUTS command outputs the text.
@end enumerate
@subsection Body or Inlined Text
@b{Where:} Various TARGET scripts.
@example
#1 Good
   proc someproc @{@} @{
       ... multiple lines of stuff ...
   @}
   $_TARGETNAME configure -event FOO someproc
#2 Good - no variables
   $_TARGETNAME configure -event foo "this ; that;"
#3 Good Curly Braces
   $_TARGETNAME configure -event FOO @{
        puts "Time: [date]"
   @}
#4 DANGER DANGER DANGER
   $_TARGETNAME configure -event foo "puts \"Time: [date]\""
@end example
@enumerate
@item The $_TARGETNAME is an OpenOCD variable convention.
@*@b{$_TARGETNAME} represents the last target created, the value changes
each time a new target is created. Remember the parsing rules. When
the ascii text is parsed, the @b{$_TARGETNAME} becomes a simple string,
the name of the target which happens to be a TARGET (object)
command.
@item The 2nd parameter to the @option{-event} parameter is a TCBODY
@*There are 4 examples:
@enumerate
@item The TCLBODY is a simple string that happens to be a proc name
@item The TCLBODY is several simple commands separated by semicolons
@item The TCLBODY is a multi-line @{curly-brace@} quoted string
@item The TCLBODY is a string with variables that get expanded.
@end enumerate

In the end, when the target event FOO occurs the TCLBODY is
evaluated. Method @b{#1} and @b{#2} are functionally identical. For
Method @b{#3} and @b{#4} it is more interesting. What is the TCLBODY?

Remember the parsing rules. In case #3, @{curly-braces@} mean the
$VARS and [square-brackets] are expanded later, when the EVENT occurs,
and the text is evaluated. In case #4, they are replaced before the
``Target Object Command'' is executed. This occurs at the same time
$_TARGETNAME is replaced. In case #4 the date will never
change. @{BTW: [date] is a bad example; at this writing,
Jim/OpenOCD does not have a date command@}
@end enumerate
@subsection Global Variables
@b{Where:} You might discover this when writing your own procs @* In
simple terms: Inside a PROC, if you need to access a global variable
you must say so. See also ``upvar''. Example:
@example
proc myproc @{ @} @{
     set y 0 #Local variable Y
     global x #Global variable X
     puts [format "X=%d, Y=%d" $x $y]
@}
@end example
@section Other Tcl Hacks
@b{Dynamic variable creation}
@example
# Dynamically create a bunch of variables.
for @{ set x 0 @} @{ $x < 32 @} @{ set x [expr @{$x + 1@}]@} @{
    # Create var name
    set vn [format "BIT%d" $x]
    # Make it a global
    global $vn
    # Set it.
    set $vn [expr @{1 << $x@}]
@}
@end example
@b{Dynamic proc/command creation}
@example
# One "X" function - 5 uart functions.
foreach who @{A B C D E@}
   proc [format "show_uart%c" $who] @{ @} "show_UARTx $who"
@}
@end example

@node License
@appendix The GNU Free Documentation License.
@include fdl.texi

@node OpenOCD Concept Index
@comment DO NOT use the plain word ``Index'', reason: CYGWIN filename
@comment case issue with ``Index.html'' and ``index.html''
@comment Occurs when creating ``--html --no-split'' output
@comment This fix is based on: http://sourceware.org/ml/binutils/2006-05/msg00215.html
@unnumbered OpenOCD Concept Index

@printindex cp

@node Command and Driver Index
@unnumbered Command and Driver Index
@printindex fn

@bye
