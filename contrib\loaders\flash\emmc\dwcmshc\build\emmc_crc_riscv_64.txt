
D:/work/2022/al9000/sw/debugger/al_openocd/contrib/loaders/flash/emmc/dwcmshc/build/emmc_crc_riscv_64.elf:     file format elf64-littleriscv


Disassembly of section .text:

0000000061000000 <_start>:
    61000000:	00001117          	auipc	sp,0x1
    61000004:	86810113          	addi	sp,sp,-1944 # 61000868 <out_buf>
    61000008:	148000ef          	jal	ra,61000150 <emmc_dwcmshc>
    6100000c:	00100073          	ebreak

0000000061000010 <emmc_wait_fifo>:
    61000010:	00050713          	mv	a4,a0
    61000014:	00072683          	lw	a3,0(a4)
    61000018:	00472783          	lw	a5,4(a4)
    6100001c:	0006869b          	sext.w	a3,a3
    61000020:	0007851b          	sext.w	a0,a5
    61000024:	fea688e3          	beq	a3,a0,61000014 <emmc_wait_fifo+0x4>
    61000028:	00008067          	ret

000000006100002c <emmc_poll_int>:
    6100002c:	00100693          	li	a3,1
    61000030:	00b696bb          	sllw	a3,a3,a1
    61000034:	03052703          	lw	a4,48(a0)
    61000038:	0007071b          	sext.w	a4,a4
    6100003c:	00e6f7b3          	and	a5,a3,a4
    61000040:	00070613          	mv	a2,a4
    61000044:	fe0788e3          	beqz	a5,61000034 <emmc_poll_int+0x8>
    61000048:	0107571b          	srliw	a4,a4,0x10
    6100004c:	fe0714e3          	bnez	a4,61000034 <emmc_poll_int+0x8>
    61000050:	00100793          	li	a5,1
    61000054:	00b797bb          	sllw	a5,a5,a1
    61000058:	00c7e7b3          	or	a5,a5,a2
    6100005c:	0007879b          	sext.w	a5,a5
    61000060:	02f52823          	sw	a5,48(a0)
    61000064:	00000513          	li	a0,0
    61000068:	00008067          	ret

000000006100006c <emmc_write_block>:
    6100006c:	183a07b7          	lui	a5,0x183a0
    61000070:	00b52423          	sw	a1,8(a0)
    61000074:	08278793          	addi	a5,a5,130 # 183a0082 <_start-0x48c5ff7e>
    61000078:	00f52623          	sw	a5,12(a0)
    6100007c:	03052783          	lw	a5,48(a0)
    61000080:	0007871b          	sext.w	a4,a5
    61000084:	0107f793          	andi	a5,a5,16
    61000088:	fe078ae3          	beqz	a5,6100007c <emmc_write_block+0x10>
    6100008c:	0107579b          	srliw	a5,a4,0x10
    61000090:	fe0796e3          	bnez	a5,6100007c <emmc_write_block+0x10>
    61000094:	20060793          	addi	a5,a2,512
    61000098:	00062703          	lw	a4,0(a2)
    6100009c:	00460613          	addi	a2,a2,4
    610000a0:	02e52023          	sw	a4,32(a0)
    610000a4:	fec79ae3          	bne	a5,a2,61000098 <emmc_write_block+0x2c>
    610000a8:	03052783          	lw	a5,48(a0)
    610000ac:	0007879b          	sext.w	a5,a5
    610000b0:	0027f693          	andi	a3,a5,2
    610000b4:	00078713          	mv	a4,a5
    610000b8:	fe0688e3          	beqz	a3,610000a8 <emmc_write_block+0x3c>
    610000bc:	0107d79b          	srliw	a5,a5,0x10
    610000c0:	fe0794e3          	bnez	a5,610000a8 <emmc_write_block+0x3c>
    610000c4:	00276793          	ori	a5,a4,2
    610000c8:	0007879b          	sext.w	a5,a5
    610000cc:	02f52823          	sw	a5,48(a0)
    610000d0:	00008067          	ret

00000000610000d4 <emmc_read_block>:
    610000d4:	fe010113          	addi	sp,sp,-32
    610000d8:	00813823          	sd	s0,16(sp)
    610000dc:	00913423          	sd	s1,8(sp)
    610000e0:	01213023          	sd	s2,0(sp)
    610000e4:	00113c23          	sd	ra,24(sp)
    610000e8:	113a07b7          	lui	a5,0x113a0
    610000ec:	09278793          	addi	a5,a5,146 # 113a0092 <_start-0x4fc5ff6e>
    610000f0:	00c52423          	sw	a2,8(a0)
    610000f4:	00f52623          	sw	a5,12(a0)
    610000f8:	00058913          	mv	s2,a1
    610000fc:	00500593          	li	a1,5
    61000100:	00050413          	mv	s0,a0
    61000104:	00068493          	mv	s1,a3
    61000108:	f25ff0ef          	jal	ra,6100002c <emmc_poll_int>
    6100010c:	00000793          	li	a5,0
    61000110:	0007871b          	sext.w	a4,a5
    61000114:	02976263          	bltu	a4,s1,61000138 <emmc_read_block+0x64>
    61000118:	00040513          	mv	a0,s0
    6100011c:	01013403          	ld	s0,16(sp)
    61000120:	01813083          	ld	ra,24(sp)
    61000124:	00813483          	ld	s1,8(sp)
    61000128:	00013903          	ld	s2,0(sp)
    6100012c:	00100593          	li	a1,1
    61000130:	02010113          	addi	sp,sp,32
    61000134:	ef9ff06f          	j	6100002c <emmc_poll_int>
    61000138:	02042683          	lw	a3,32(s0)
    6100013c:	00279713          	slli	a4,a5,0x2
    61000140:	00e90733          	add	a4,s2,a4
    61000144:	00d72023          	sw	a3,0(a4)
    61000148:	00178793          	addi	a5,a5,1
    6100014c:	fc5ff06f          	j	61000110 <emmc_read_block+0x3c>

0000000061000150 <emmc_dwcmshc>:
    61000150:	fa010113          	addi	sp,sp,-96
    61000154:	04813823          	sd	s0,80(sp)
    61000158:	04913423          	sd	s1,72(sp)
    6100015c:	05213023          	sd	s2,64(sp)
    61000160:	03313c23          	sd	s3,56(sp)
    61000164:	03513423          	sd	s5,40(sp)
    61000168:	03613023          	sd	s6,32(sp)
    6100016c:	01713c23          	sd	s7,24(sp)
    61000170:	01813823          	sd	s8,16(sp)
    61000174:	01913423          	sd	s9,8(sp)
    61000178:	01a13023          	sd	s10,0(sp)
    6100017c:	04113c23          	sd	ra,88(sp)
    61000180:	03413823          	sd	s4,48(sp)
    61000184:	00050a93          	mv	s5,a0
    61000188:	00058913          	mv	s2,a1
    6100018c:	00060493          	mv	s1,a2
    61000190:	00068993          	mv	s3,a3
    61000194:	4025db13          	srai	s6,a1,0x2
    61000198:	fff00413          	li	s0,-1
    6100019c:	00058b93          	mv	s7,a1
    610001a0:	00000c17          	auipc	s8,0x0
    610001a4:	6c8c0c13          	addi	s8,s8,1736 # 61000868 <out_buf>
    610001a8:	fff00c93          	li	s9,-1
    610001ac:	00000d17          	auipc	s10,0x0
    610001b0:	0bcd0d13          	addi	s10,s10,188 # 61000268 <crc32_table>
    610001b4:	04904063          	bgtz	s1,610001f4 <emmc_dwcmshc+0xa4>
    610001b8:	05813083          	ld	ra,88(sp)
    610001bc:	00040513          	mv	a0,s0
    610001c0:	05013403          	ld	s0,80(sp)
    610001c4:	04813483          	ld	s1,72(sp)
    610001c8:	04013903          	ld	s2,64(sp)
    610001cc:	03813983          	ld	s3,56(sp)
    610001d0:	03013a03          	ld	s4,48(sp)
    610001d4:	02813a83          	ld	s5,40(sp)
    610001d8:	02013b03          	ld	s6,32(sp)
    610001dc:	01813b83          	ld	s7,24(sp)
    610001e0:	01013c03          	ld	s8,16(sp)
    610001e4:	00813c83          	ld	s9,8(sp)
    610001e8:	00013d03          	ld	s10,0(sp)
    610001ec:	06010113          	addi	sp,sp,96
    610001f0:	00008067          	ret
    610001f4:	00048a1b          	sext.w	s4,s1
    610001f8:	00995463          	bge	s2,s1,61000200 <emmc_dwcmshc+0xb0>
    610001fc:	000b8a1b          	sext.w	s4,s7
    61000200:	00098613          	mv	a2,s3
    61000204:	000b0693          	mv	a3,s6
    61000208:	000c0593          	mv	a1,s8
    6100020c:	000a8513          	mv	a0,s5
    61000210:	ec5ff0ef          	jal	ra,610000d4 <emmc_read_block>
    61000214:	00000617          	auipc	a2,0x0
    61000218:	65460613          	addi	a2,a2,1620 # 61000868 <out_buf>
    6100021c:	000a0713          	mv	a4,s4
    61000220:	fff7071b          	addiw	a4,a4,-1
    61000224:	01971863          	bne	a4,s9,61000234 <emmc_dwcmshc+0xe4>
    61000228:	414484bb          	subw	s1,s1,s4
    6100022c:	0019899b          	addiw	s3,s3,1
    61000230:	f85ff06f          	j	610001b4 <emmc_dwcmshc+0x64>
    61000234:	0084169b          	slliw	a3,s0,0x8
    61000238:	0184579b          	srliw	a5,s0,0x18
    6100023c:	00064403          	lbu	s0,0(a2)
    61000240:	00160613          	addi	a2,a2,1
    61000244:	0087c7b3          	xor	a5,a5,s0
    61000248:	02079793          	slli	a5,a5,0x20
    6100024c:	01e7d793          	srli	a5,a5,0x1e
    61000250:	00fd07b3          	add	a5,s10,a5
    61000254:	0007a403          	lw	s0,0(a5)
    61000258:	00d44433          	xor	s0,s0,a3
    6100025c:	0004041b          	sext.w	s0,s0
    61000260:	fc1ff06f          	j	61000220 <emmc_dwcmshc+0xd0>

Disassembly of section .rodata:

0000000061000268 <crc32_table>:
    61000268:	0000                	unimp
    6100026a:	0000                	unimp
    6100026c:	04c11db7          	lui	s11,0x4c11
    61000270:	3b6e                	fld	fs6,248(sp)
    61000272:	0982                	c.slli64	s3
    61000274:	26d9                	addiw	a3,a3,22
    61000276:	76dc0d43          	fmadd.q	fs10,fs8,fa3,fa4,rne
    6100027a:	1304                	addi	s1,sp,416
    6100027c:	17c56b6b          	0x17c56b6b
    61000280:	4db2                	lw	s11,12(sp)
    61000282:	1a86                	slli	s5,s5,0x21
    61000284:	5005                	c.li	zero,-31
    61000286:	edb81e47          	fmsub.h	ft8,fa6,fs11,ft9,rtz
    6100028a:	2608                	fld	fa0,8(a2)
    6100028c:	22c9f00f          	0x22c9f00f
    61000290:	d6d6                	sw	s5,108(sp)
    61000292:	2f8a                	fld	ft11,128(sp)
    61000294:	cb61                	beqz	a4,61000364 <crc32_table+0xfc>
    61000296:	9b642b4b          	fnmsub.d	fs6,fs0,fs6,fs3,rdn
    6100029a:	350c                	fld	fa1,40(a0)
    6100029c:	31cd86d3          	0x31cd86d3
    610002a0:	a00a                	fsd	ft2,0(sp)
    610002a2:	3c8e                	fld	fs9,224(sp)
    610002a4:	bdbd                	j	61000122 <emmc_read_block+0x4e>
    610002a6:	db70384f          	fnmadd.d	fa6,ft0,fs7,fs11,rup
    610002aa:	4c11                	li	s8,4
    610002ac:	48d0c6c7          	fmsub.s	fa3,ft1,fa3,fs1,rmm
    610002b0:	e01e                	sd	t2,0(sp)
    610002b2:	fda94593          	xori	a1,s2,-38
    610002b6:	4152                	lw	sp,20(sp)
    610002b8:	adac                	fsd	fa1,88(a1)
    610002ba:	5f15                	li	t5,-27
    610002bc:	5bd4b01b          	0x5bd4b01b
    610002c0:	96c2                	add	a3,a3,a6
    610002c2:	8b755697          	auipc	a3,0x8b755
    610002c6:	5256                	lw	tp,116(sp)
    610002c8:	36c8                	fld	fa0,168(a3)
    610002ca:	6a19                	lui	s4,0x6
    610002cc:	6ed82b7f          	0x6ed82b7f
    610002d0:	0da6                	slli	s11,s11,0x9
    610002d2:	1011639b          	0x1011639b
    610002d6:	675a                	ld	a4,400(sp)
    610002d8:	4014                	lw	a3,0(s0)
    610002da:	791d                	lui	s2,0xfffe7
    610002dc:	7ddc5da3          	0x7ddc5da3
    610002e0:	7b7a                	ld	s6,440(sp)
    610002e2:	709f 66cd 745e      	0x745e66cd709f
    610002e8:	b6e0                	fsd	fs0,232(a3)
    610002ea:	ab579823          	sh	s5,-1360(a5)
    610002ee:	9ce2                	add	s9,s9,s8
    610002f0:	8d8e                	mv	s11,gp
    610002f2:	91a1                	srli	a1,a1,0x28
    610002f4:	9039                	srli	s0,s0,0x2e
    610002f6:	9560                	0x9560
    610002f8:	c03c                	sw	a5,64(s0)
    610002fa:	dd8b8b27          	0xdd8b8b27
    610002fe:	8fe6                	mv	t6,s9
    61000300:	fb52                	sd	s4,432(sp)
    61000302:	82a5                	srli	a3,a3,0x9
    61000304:	e6e5                	bnez	a3,610003ec <crc32_table+0x184>
    61000306:	8664                	0x8664
    61000308:	5b58                	lw	a4,52(a4)
    6100030a:	46efbe2b          	0x46efbe2b
    6100030e:	baea                	fsd	fs10,368(sp)
    61000310:	6036                	0x6036
    61000312:	b7a9                	j	6100025c <emmc_dwcmshc+0x10c>
    61000314:	7d81                	lui	s11,0xfffe0
    61000316:	b368                	fsd	fa0,224(a4)
    61000318:	2d84                	fld	fs1,24(a1)
    6100031a:	3033ad2f          	0x3033ad2f
    6100031e:	a9ee                	fsd	fs11,208(sp)
    61000320:	16ea                	slli	a3,a3,0x3a
    61000322:	a4ad                	j	6100058c <crc32_table+0x324>
    61000324:	0b5d                	addi	s6,s6,23
    61000326:	a06c                	fsd	fa1,192(s0)
    61000328:	6d90                	ld	a2,24(a1)
    6100032a:	d432                	sw	a2,40(sp)
    6100032c:	d0f37027          	0xd0f37027
    61000330:	56fe                	lw	a3,252(sp)
    61000332:	ddb0                	sw	a2,120(a1)
    61000334:	4b49                	li	s6,18
    61000336:	d971                	beqz	a0,6100030a <crc32_table+0xa2>
    61000338:	1b4c                	addi	a1,sp,436
    6100033a:	c736                	sw	a3,140(sp)
    6100033c:	c3f706fb          	0xc3f706fb
    61000340:	2022                	fld	ft0,8(sp)
    61000342:	ceb4                	sw	a3,88(a3)
    61000344:	3d95                	addiw	s11,s11,-27
    61000346:	ca75                	beqz	a2,6100043a <crc32_table+0x1d2>
    61000348:	8028                	0x8028
    6100034a:	f23a                	sd	a4,288(sp)
    6100034c:	9d9f f6fb bb46      	0xbb46f6fb9d9f
    61000352:	fbb8                	sd	a4,112(a5)
    61000354:	a6f1                	j	61000720 <stack+0xb8>
    61000356:	ff79                	bnez	a4,61000334 <crc32_table+0xcc>
    61000358:	f6f4                	sd	a3,232(a3)
    6100035a:	e13e                	sd	a5,128(sp)
    6100035c:	e5ffeb43          	fmadd.h	fs6,ft11,ft11,ft8,unknown
    61000360:	cd9a                	sw	t1,216(sp)
    61000362:	e8bc                	sd	a5,80(s1)
    61000364:	d02d                	beqz	s0,610002c6 <crc32_table+0x5e>
    61000366:	ec7d                	bnez	s0,61000464 <crc32_table+0x1fc>
    61000368:	34867077          	0x34867077
    6100036c:	6dc0                	ld	s0,152(a1)
    6100036e:	4b193047          	fmsub.d	ft0,fs2,fa7,fs1,rup
    61000372:	3d04                	fld	fs1,56(a0)
    61000374:	56ae                	lw	a3,232(sp)
    61000376:	39c5                	addiw	s3,s3,-15
    61000378:	278206ab          	0x278206ab
    6100037c:	1b1c                	addi	a5,sp,432
    6100037e:	3dc52343          	fmadd.h	ft6,fa0,ft8,ft7,rdn
    61000382:	2e00                	fld	fs0,24(a2)
    61000384:	2072                	fld	ft0,280(sp)
    61000386:	2ac1                	addiw	s5,s5,16
    61000388:	128e9dcf          	fnmadd.d	fs11,ft9,fs0,ft2,rtz
    6100038c:	8078                	0x8078
    6100038e:	a6a1164f          	fnmadd.q	fa2,ft2,fa0,fs4,rtz
    61000392:	1b0c                	addi	a1,sp,432
    61000394:	bb16                	fsd	ft5,432(sp)
    61000396:	1fcd                	addi	t6,t6,-13
    61000398:	018aeb13          	ori	s6,s5,24
    6100039c:	f6a4                	sd	s1,104(a3)
    6100039e:	d07d054b          	fnmsub.s	fa0,fs10,ft7,fs10,rne
    610003a2:	0808                	addi	a0,sp,16
    610003a4:	cdca                	sw	s2,216(sp)
    610003a6:	0cc9                	addi	s9,s9,18
    610003a8:	7897ab07          	flw	fs6,1929(a5)
    610003ac:	b6b0                	fsd	fa2,104(a3)
    610003ae:	7c56                	ld	s8,368(sp)
    610003b0:	9069                	srli	s0,s0,0x3a
    610003b2:	7115                	addi	sp,sp,-224
    610003b4:	8dde                	mv	s11,s7
    610003b6:	75d4                	ld	a3,168(a1)
    610003b8:	6b93dddb          	0x6b93dddb
    610003bc:	c06c                	sw	a1,68(s0)
    610003be:	6f52                	ld	t5,272(sp)
    610003c0:	e6b5                	bnez	a3,6100042c <crc32_table+0x1c4>
    610003c2:	6211                	lui	tp,0x4
    610003c4:	fb02                	sd	zero,432(sp)
    610003c6:	66d0                	ld	a2,136(a3)
    610003c8:	5e9f46bf 5a5e5b08 	0x5a5e5b085e9f46bf
    610003d0:	7dd1                	lui	s11,0xffff4
    610003d2:	571d                	li	a4,-25
    610003d4:	6066                	0x6066
    610003d6:	53dc                	lw	a5,36(a5)
    610003d8:	4d9b3063          	0x4d9b3063
    610003dc:	2dd4                	fld	fa3,152(a1)
    610003de:	495a                	lw	s2,148(sp)
    610003e0:	0b0d                	addi	s6,s6,3
    610003e2:	4419                	li	s0,6
    610003e4:	16ba                	slli	a3,a3,0x2e
    610003e6:	40d8                	lw	a4,4(s1)
    610003e8:	aca5c697          	auipc	a3,0xaca5c
    610003ec:	db20                	sw	s0,112(a4)
    610003ee:	a864                	fsd	fs1,208(s0)
    610003f0:	fdf9                	bnez	a1,610003ce <crc32_table+0x166>
    610003f2:	e04ea527          	fsw	ft4,-502(t4)
    610003f6:	a1e6                	fsd	fs9,192(sp)
    610003f8:	bfa1b04b          	fnmsub.q	ft0,ft3,fs10,fs7,rup
    610003fc:	adfc                	fsd	fa5,216(a1)
    610003fe:	bb60                	fsd	fs0,240(a4)
    61000400:	8b25                	andi	a4,a4,9
    61000402:	9692b623          	sd	s1,-1684(t0)
    61000406:	b2e2                	fsd	fs8,352(sp)
    61000408:	8aad2b2f          	0x8aad2b2f
    6100040c:	3698                	fld	fa4,40(a3)
    6100040e:	8e6c                	0x8e6c
    61000410:	1041                	c.nop	-16
    61000412:	0df6832f          	0xdf6832f
    61000416:	87ee                	mv	a5,s11
    61000418:	99a95df3          	csrrwi	s11,0x99a,18
    6100041c:	4044                	lw	s1,4(s0)
    6100041e:	9d68                	0x9d68
    61000420:	669d                	lui	a3,0x7
    61000422:	7b2a902b          	0x7b2a902b
    61000426:	94ea                	add	s1,s1,s10
    61000428:	e0b41de7          	0xe0b41de7
    6100042c:	0050                	addi	a2,sp,4
    6100042e:	e475                	bnez	s0,6100051a <crc32_table+0x2b2>
    61000430:	2689                	addiw	a3,a3,2
    61000432:	e936                	sd	a3,144(sp)
    61000434:	3b3e                	fld	fs6,488(sp)
    61000436:	6b3bedf7          	0x6b3bedf7
    6100043a:	f3b0                	sd	a2,96(a5)
    6100043c:	768c                	ld	a1,40(a3)
    6100043e:	f771                	bnez	a4,6100040a <crc32_table+0x1a2>
    61000440:	5055                	c.li	zero,-11
    61000442:	fa32                	sd	a2,304(sp)
    61000444:	4de2                	lw	s11,24(sp)
    61000446:	f05ffef3          	csrrci	t4,0xf05,31
    6100044a:	c6bc                	sw	a5,72(a3)
    6100044c:	ede8                	sd	a0,216(a1)
    6100044e:	c27d                	beqz	a2,61000534 <crc32_table+0x2cc>
    61000450:	cb31                	beqz	a4,610004a4 <crc32_table+0x23c>
    61000452:	cf3e                	sw	a5,156(sp)
    61000454:	d686                	sw	ra,108(sp)
    61000456:	8683cbff          	0x8683cbff
    6100045a:	d5b8                	sw	a4,104(a1)
    6100045c:	9b34                	0x9b34
    6100045e:	d179                	beqz	a0,61000424 <crc32_table+0x1bc>
    61000460:	bded                	j	6100035a <crc32_table+0xf2>
    61000462:	dc3a                	sw	a4,56(sp)
    61000464:	a05a                	fsd	fs6,0(sp)
    61000466:	e0eed8fb          	0xe0eed8fb
    6100046a:	690c                	ld	a1,16(a0)
    6100046c:	fd59                	bnez	a0,6100040a <crc32_table+0x1a2>
    6100046e:	6dcd                	lui	s11,0x13
    61000470:	db80                	sw	s0,48(a5)
    61000472:	608e                	ld	ra,192(sp)
    61000474:	644fc637          	lui	a2,0x644fc
    61000478:	9632                	add	a2,a2,a2
    6100047a:	7a08                	ld	a0,48(a2)
    6100047c:	8b85                	andi	a5,a5,1
    6100047e:	7ec9                	lui	t4,0xffff2
    61000480:	ad5c                	fsd	fa5,152(a0)
    61000482:	738a                	ld	t2,160(sp)
    61000484:	774bb0eb          	0x774bb0eb
    61000488:	0d56                	slli	s10,s10,0x15
    6100048a:	4f04                	lw	s1,24(a4)
    6100048c:	10e1                	addi	ra,ra,-8
    6100048e:	4bc5                	li	s7,17
    61000490:	3638                	fld	fa4,104(a2)
    61000492:	4686                	lw	a3,64(sp)
    61000494:	42472b8f          	0x42472b8f
    61000498:	7b8a                	ld	s7,160(sp)
    6100049a:	5c00                	lw	s0,56(s0)
    6100049c:	663d                	lui	a2,0xf
    6100049e:	58c1                	li	a7,-16
    610004a0:	40e4                	lw	s1,68(s1)
    610004a2:	5582                	lw	a1,32(sp)
    610004a4:	51435d53          	0x51435d53
    610004a8:	3b9e                	fld	fs7,480(sp)
    610004aa:	251d                	addiw	a0,a0,7
    610004ac:	2629                	addiw	a2,a2,10
    610004ae:	21dc                	fld	fa5,128(a1)
    610004b0:	00f0                	addi	a2,sp,76
    610004b2:	2c9f 1d47 285e      	0x285e1d472c9f
    610004b8:	4d42                	lw	s10,16(sp)
    610004ba:	3619                	addiw	a2,a2,-26
    610004bc:	50f5                	li	ra,-3
    610004be:	32d8                	fld	fa4,160(a3)
    610004c0:	762c                	ld	a1,104(a2)
    610004c2:	6b9b3f9b          	0x6b9b3f9b
    610004c6:	3b5a                	fld	fs6,432(sp)
    610004c8:	d626                	sw	s1,44(sp)
    610004ca:	0315                	addi	t1,t1,5
    610004cc:	cb91                	beqz	a5,610004e0 <crc32_table+0x278>
    610004ce:	07d4                	addi	a3,sp,964
    610004d0:	ed48                	sd	a0,152(a0)
    610004d2:	f0ff0a97          	auipc	s5,0xf0ff0
    610004d6:	0e56                	slli	t3,t3,0x15
    610004d8:	a0fa                	fsd	ft10,64(sp)
    610004da:	1011                	c.nop	-28
    610004dc:	bd4d                	j	6100038e <crc32_table+0x126>
    610004de:	14d0                	addi	a2,sp,612
    610004e0:	9b94                	0x9b94
    610004e2:	86231993          	0x86231993
    610004e6:	1d52                	slli	s10,s10,0x34
    610004e8:	560e                	lw	a2,224(sp)
    610004ea:	4bb9f12f          	0x4bb9f12f
    610004ee:	f5ee                	sd	s11,232(sp)
    610004f0:	6d60                	ld	s0,216(a0)
    610004f2:	f8ad                	bnez	s1,61000464 <crc32_table+0x1fc>
    610004f4:	fc6c70d7          	vsetivli	ra,24,e8,mf4,ta,ma
    610004f8:	20d2                	fld	ft1,272(sp)
    610004fa:	3d65e22b          	0x3d65e22b
    610004fe:	e6ea                	sd	s10,328(sp)
    61000500:	1bbc                	addi	a5,sp,504
    61000502:	eba9                	bnez	a5,61000554 <crc32_table+0x2ec>
    61000504:	ef68060b          	0xef68060b
    61000508:	bbb6                	fsd	fa3,496(sp)
    6100050a:	a601d727          	vsuxseg6ei16.v	v14,(gp),v0
    6100050e:	d3e6                	sw	s9,228(sp)
    61000510:	80d8                	0x80d8
    61000512:	dea5                	beqz	a3,6100048a <crc32_table+0x222>
    61000514:	da649d6f          	jal	s10,60f49aba <_start-0xb6546>
    61000518:	cd6a                	sw	s10,152(sp)
    6100051a:	d0ddc423          	0xd0ddc423
    6100051e:	c0e2                	sw	s8,64(sp)
    61000520:	f604                	sd	s1,40(a2)
    61000522:	cda1                	beqz	a1,6100057a <crc32_table+0x312>
    61000524:	c960ebb3          	0xc960ebb3
    61000528:	8d7e                	mv	s10,t6
    6100052a:	bd3e                	fsd	fa5,184(sp)
    6100052c:	90c9                	srli	s1,s1,0x32
    6100052e:	b610b9ff          	0xb610b9ff
    61000532:	b4bc                	fsd	fa5,104(s1)
    61000534:	b07daba7          	fsw	ft7,-1257(s11) # 12b17 <_start-0x60fed4e9>
    61000538:	fba2                	sd	s0,496(sp)
    6100053a:	ae3a                	fsd	fa4,280(sp)
    6100053c:	e615                	bnez	a2,61000568 <crc32_table+0x300>
    6100053e:	c0ccaafb          	0xc0ccaafb
    61000542:	a7b8                	fsd	fa4,72(a5)
    61000544:	a379dd7b          	0xa379dd7b
    61000548:	60c6                	ld	ra,80(sp)
    6100054a:	9b36                	add	s6,s6,a3
    6100054c:	7d71                	lui	s10,0xffffc
    6100054e:	5ba89ff7          	0x5ba89ff7
    61000552:	92b4                	0x92b4
    61000554:	461f 9675 161a      	0x161a9675461f
    6100055a:	8832                	mv	a6,a2
    6100055c:	0bad                	addi	s7,s7,11
    6100055e:	2d748cf3          	0x2d748cf3
    61000562:	81b0                	0x81b0
    61000564:	857130c3          	fmadd.h	ft1,ft2,fs7,fa6,rup
    61000568:	9099                	srli	s1,s1,0x26
    6100056a:	5d8a                	lw	s11,160(sp)
    6100056c:	8d2e                	mv	s10,a1
    6100056e:	abf7594b          	fnmsub.d	fs2,fa4,ft11,fs5,unknown
    61000572:	5408                	lw	a0,40(s0)
    61000574:	b640                	fsd	fs0,168(a2)
    61000576:	50c9                	li	ra,-14
    61000578:	e645                	bnez	a2,61000620 <crc32_table+0x3b8>
    6100057a:	4e8e                	lw	t4,192(sp)
    6100057c:	fbf2                	sd	t3,496(sp)
    6100057e:	dd2b4a4f          	fnmadd.h	fs4,fs6,fs2,fs11,rmm
    61000582:	470c                	lw	a1,8(a4)
    61000584:	c09c                	sw	a5,0(s1)
    61000586:	43cd                	li	t2,19
    61000588:	7d21                	lui	s10,0xfffe8
    6100058a:	7b82                	ld	s7,32(sp)
    6100058c:	6096                	ld	ra,320(sp)
    6100058e:	464f7f43          	fmadd.q	ft10,ft10,ft4,fs0
    61000592:	7200                	ld	s0,32(a2)
    61000594:	5bf8                	lw	a4,116(a5)
    61000596:	76c1                	lui	a3,0xffff0
    61000598:	0bfd                	addi	s7,s7,31
    6100059a:	6886                	ld	a7,64(sp)
    6100059c:	164a                	slli	a2,a2,0x32
    6100059e:	30936c47          	fmsub.s	fs8,ft6,fs1,ft6,unknown
    610005a2:	6104                	ld	s1,0(a0)
    610005a4:	2d24                	fld	fs1,88(a0)
    610005a6:	65c5                	lui	a1,0x11
    610005a8:	4be9                	li	s7,26
    610005aa:	565e119b          	0x565e119b
    610005ae:	155a                	slli	a0,a0,0x36
    610005b0:	18197087          	0x18197087
    610005b4:	6d30                	ld	a2,88(a0)
    610005b6:	1cd8                	addi	a4,sp,628
    610005b8:	3d35                	addiw	s10,s10,-19
    610005ba:	029f 2082 065e      	0x65e2082029f
    610005c0:	0b1d065b          	0xb1d065b
    610005c4:	1bec                	addi	a1,sp,508
    610005c6:	0fdc                	addi	a5,sp,980
    610005c8:	a651                	j	6100094c <out_buf+0xe4>
    610005ca:	bbe63793          	sltiu	a5,a2,-1090
    610005ce:	3352                	fld	ft6,304(sp)
    610005d0:	3e119d3f 3ad08088 	0x3ad080883e119d3f
    610005d8:	d08d                	beqz	s1,610004fa <crc32_table+0x292>
    610005da:	cd3a2497          	auipc	s1,0xcd3a2
    610005de:	2056                	fld	ft0,336(sp)
    610005e0:	2d15ebe3          	bltu	a1,a7,610010b6 <out_buf+0x84e>
    610005e4:	f654                	sd	a3,168(a2)
    610005e6:	29d4                	fld	fa3,144(a1)
    610005e8:	2679                	addiw	a2,a2,30
    610005ea:	c5a9                	beqz	a1,61000634 <crc32_table+0x3cc>
    610005ec:	3bce                	fld	fs7,240(sp)
    610005ee:	c168                	sw	a0,68(a0)
    610005f0:	cc2b1d17          	auipc	s10,0xcc2b1
    610005f4:	00a0                	addi	s0,sp,72
    610005f6:	c8ea                	sw	s10,80(sp)
    610005f8:	50a5                	li	ra,-23
    610005fa:	d6ad                	beqz	a3,61000564 <crc32_table+0x2fc>
    610005fc:	4d12                	lw	s10,4(sp)
    610005fe:	d26c                	sw	a1,100(a2)
    61000600:	df2f6bcb          	fnmsub.q	fs7,ft10,fs2,fs11,unknown
    61000604:	767c                	ld	a5,232(a2)
    61000606:	dbee                	sw	s11,244(sp)
    61000608:	cbc1                	beqz	a5,61000698 <stack+0x30>
    6100060a:	e3a1                	bnez	a5,6100064a <crc32_table+0x3e2>
    6100060c:	d676                	sw	t4,44(sp)
    6100060e:	e760                	sd	s0,200(a4)
    61000610:	ea23f0af          	0xea23f0af
    61000614:	ed18                	sd	a4,24(a0)
    61000616:	eee2                	sd	s8,344(sp)
    61000618:	bd1d                	j	6100044e <crc32_table+0x1e6>
    6100061a:	f0a5                	bnez	s1,6100057a <crc32_table+0x312>
    6100061c:	a0aa                	fsd	fa0,64(sp)
    6100061e:	f464                	sd	s1,232(s0)
    61000620:	f9278673          	0xf9278673
    61000624:	9bc4                	0x9bc4
    61000626:	fde6                	sd	s9,248(sp)
    61000628:	fd09                	bnez	a0,61000542 <crc32_table+0x2da>
    6100062a:	89b8                	0x89b8
    6100062c:	e0be                	sd	a5,64(sp)
    6100062e:	8d79                	and	a0,a0,a4
    61000630:	803ac667          	0x803ac667
    61000634:	dbd0                	sw	a2,52(a5)
    61000636:	8bd584fb          	0x8bd584fb
    6100063a:	9abc                	0x9abc
    6100063c:	9662                	add	a2,a2,s8
    6100063e:	9e7d                	0x9e7d
    61000640:	933eb0bb          	0x933eb0bb
    61000644:	ad0c                	fsd	fa1,24(a0)
    61000646:	10b197ff          	ukaddw	a5,gp,a1
    6100064a:	afb0                	fsd	fa2,88(a5)
    6100064c:	0d06                	slli	s10,s10,0x1
    6100064e:	ab71                	j	61000bea <out_buf+0x382>
    61000650:	2bdf a632 3668      	0x3668a6322bdf
    61000656:	666da2f3          	csrrs	t0,0x666,s11
    6100065a:	bcb4                	fsd	fa3,120(s1)
    6100065c:	7bda                	ld	s7,432(sp)
    6100065e:	b875                	j	60ffff1a <_start-0xe6>
    61000660:	b5365d03          	lhu	s10,-1197(a2) # eb53 <_start-0x60ff14ad>
    61000664:	40b4                	lw	a3,64(s1)
    61000666:	          	0xb1f7

Disassembly of section .data:

0000000061000668 <stack>:
    61000668:	5309                	li	t1,-30
    6100066a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100066e:	0000                	unimp
    61000670:	5309                	li	t1,-30
    61000672:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000676:	0000                	unimp
    61000678:	5309                	li	t1,-30
    6100067a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100067e:	0000                	unimp
    61000680:	5309                	li	t1,-30
    61000682:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000686:	0000                	unimp
    61000688:	5309                	li	t1,-30
    6100068a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100068e:	0000                	unimp
    61000690:	5309                	li	t1,-30
    61000692:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000696:	0000                	unimp
    61000698:	5309                	li	t1,-30
    6100069a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100069e:	0000                	unimp
    610006a0:	5309                	li	t1,-30
    610006a2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610006a6:	0000                	unimp
    610006a8:	5309                	li	t1,-30
    610006aa:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610006ae:	0000                	unimp
    610006b0:	5309                	li	t1,-30
    610006b2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610006b6:	0000                	unimp
    610006b8:	5309                	li	t1,-30
    610006ba:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610006be:	0000                	unimp
    610006c0:	5309                	li	t1,-30
    610006c2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610006c6:	0000                	unimp
    610006c8:	5309                	li	t1,-30
    610006ca:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610006ce:	0000                	unimp
    610006d0:	5309                	li	t1,-30
    610006d2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610006d6:	0000                	unimp
    610006d8:	5309                	li	t1,-30
    610006da:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610006de:	0000                	unimp
    610006e0:	5309                	li	t1,-30
    610006e2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610006e6:	0000                	unimp
    610006e8:	5309                	li	t1,-30
    610006ea:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610006ee:	0000                	unimp
    610006f0:	5309                	li	t1,-30
    610006f2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610006f6:	0000                	unimp
    610006f8:	5309                	li	t1,-30
    610006fa:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610006fe:	0000                	unimp
    61000700:	5309                	li	t1,-30
    61000702:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000706:	0000                	unimp
    61000708:	5309                	li	t1,-30
    6100070a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100070e:	0000                	unimp
    61000710:	5309                	li	t1,-30
    61000712:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000716:	0000                	unimp
    61000718:	5309                	li	t1,-30
    6100071a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100071e:	0000                	unimp
    61000720:	5309                	li	t1,-30
    61000722:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000726:	0000                	unimp
    61000728:	5309                	li	t1,-30
    6100072a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100072e:	0000                	unimp
    61000730:	5309                	li	t1,-30
    61000732:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000736:	0000                	unimp
    61000738:	5309                	li	t1,-30
    6100073a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100073e:	0000                	unimp
    61000740:	5309                	li	t1,-30
    61000742:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000746:	0000                	unimp
    61000748:	5309                	li	t1,-30
    6100074a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100074e:	0000                	unimp
    61000750:	5309                	li	t1,-30
    61000752:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000756:	0000                	unimp
    61000758:	5309                	li	t1,-30
    6100075a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100075e:	0000                	unimp
    61000760:	5309                	li	t1,-30
    61000762:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000766:	0000                	unimp
    61000768:	5309                	li	t1,-30
    6100076a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100076e:	0000                	unimp
    61000770:	5309                	li	t1,-30
    61000772:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000776:	0000                	unimp
    61000778:	5309                	li	t1,-30
    6100077a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100077e:	0000                	unimp
    61000780:	5309                	li	t1,-30
    61000782:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000786:	0000                	unimp
    61000788:	5309                	li	t1,-30
    6100078a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100078e:	0000                	unimp
    61000790:	5309                	li	t1,-30
    61000792:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000796:	0000                	unimp
    61000798:	5309                	li	t1,-30
    6100079a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100079e:	0000                	unimp
    610007a0:	5309                	li	t1,-30
    610007a2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610007a6:	0000                	unimp
    610007a8:	5309                	li	t1,-30
    610007aa:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610007ae:	0000                	unimp
    610007b0:	5309                	li	t1,-30
    610007b2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610007b6:	0000                	unimp
    610007b8:	5309                	li	t1,-30
    610007ba:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610007be:	0000                	unimp
    610007c0:	5309                	li	t1,-30
    610007c2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610007c6:	0000                	unimp
    610007c8:	5309                	li	t1,-30
    610007ca:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610007ce:	0000                	unimp
    610007d0:	5309                	li	t1,-30
    610007d2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610007d6:	0000                	unimp
    610007d8:	5309                	li	t1,-30
    610007da:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610007de:	0000                	unimp
    610007e0:	5309                	li	t1,-30
    610007e2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610007e6:	0000                	unimp
    610007e8:	5309                	li	t1,-30
    610007ea:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610007ee:	0000                	unimp
    610007f0:	5309                	li	t1,-30
    610007f2:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610007f6:	0000                	unimp
    610007f8:	5309                	li	t1,-30
    610007fa:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    610007fe:	0000                	unimp
    61000800:	5309                	li	t1,-30
    61000802:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000806:	0000                	unimp
    61000808:	5309                	li	t1,-30
    6100080a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100080e:	0000                	unimp
    61000810:	5309                	li	t1,-30
    61000812:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000816:	0000                	unimp
    61000818:	5309                	li	t1,-30
    6100081a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100081e:	0000                	unimp
    61000820:	5309                	li	t1,-30
    61000822:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000826:	0000                	unimp
    61000828:	5309                	li	t1,-30
    6100082a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100082e:	0000                	unimp
    61000830:	5309                	li	t1,-30
    61000832:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000836:	0000                	unimp
    61000838:	5309                	li	t1,-30
    6100083a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100083e:	0000                	unimp
    61000840:	5309                	li	t1,-30
    61000842:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000846:	0000                	unimp
    61000848:	5309                	li	t1,-30
    6100084a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100084e:	0000                	unimp
    61000850:	5309                	li	t1,-30
    61000852:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    61000856:	0000                	unimp
    61000858:	5309                	li	t1,-30
    6100085a:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
    6100085e:	0000                	unimp
    61000860:	5309                	li	t1,-30
    61000862:	00000867          	jalr	a6,zero # 0 <_start-0x61000000>
	...

Disassembly of section .bss:

0000000061000868 <out_buf>:
	...

Disassembly of section .riscv.attributes:

0000000000000000 <.riscv.attributes>:
   0:	1b41                	addi	s6,s6,-16
   2:	0000                	unimp
   4:	7200                	ld	s0,32(a2)
   6:	7369                	lui	t1,0xffffa
   8:	01007663          	bgeu	zero,a6,14 <_start-0x60ffffec>
   c:	0011                	c.nop	4
   e:	0000                	unimp
  10:	1004                	addi	s1,sp,32
  12:	7205                	lui	tp,0xfffe1
  14:	3676                	fld	fa2,376(sp)
  16:	6934                	ld	a3,80(a0)
  18:	7032                	0x7032
  1a:	0030                	addi	a2,sp,8

Disassembly of section .comment:

0000000000000000 <.comment>:
   0:	3a434347          	fmsub.d	ft6,ft6,ft4,ft7,rmm
   4:	2820                	fld	fs0,80(s0)
   6:	29554e47          	fmsub.s	ft8,fa0,fs5,ft5,rmm
   a:	3120                	fld	fs0,96(a0)
   c:	2e30                	fld	fa2,88(a2)
   e:	2e32                	fld	ft8,264(sp)
  10:	0030                	addi	a2,sp,8

Disassembly of section .debug_line:

0000000000000000 <.debug_line>:
   0:	000000a3          	sb	zero,1(zero) # 1 <_start-0x60ffffff>
   4:	007d0003          	lb	zero,7(s10) # 2d2b15f7 <_start-0x33d4ea09>
   8:	0000                	unimp
   a:	0101                	addi	sp,sp,0
   c:	000d0efb          	dkhmx8	t4,s10,zero
  10:	0101                	addi	sp,sp,0
  12:	0101                	addi	sp,sp,0
  14:	0000                	unimp
  16:	0100                	addi	s0,sp,128
  18:	0000                	unimp
  1a:	4401                	li	s0,0
  1c:	2f3a                	fld	ft10,392(sp)
  1e:	6b726f77          	0x6b726f77
  22:	3230322f          	0x3230322f
  26:	2f32                	fld	ft10,264(sp)
  28:	6c61                	lui	s8,0x18
  2a:	3039                	0x3039
  2c:	3030                	fld	fa2,96(s0)
  2e:	2f77732f          	0x2f77732f
  32:	6564                	ld	s1,200(a0)
  34:	7562                	ld	a0,56(sp)
  36:	72656767          	0x72656767
  3a:	5f6c612f          	0x5f6c612f
  3e:	6e65706f          	j	57724 <_start-0x60fa88dc>
  42:	2f64636f          	jal	t1,46338 <_start-0x60fb9cc8>
  46:	746e6f63          	bltu	t3,t1,7a4 <_start-0x60fff85c>
  4a:	6972                	ld	s2,280(sp)
  4c:	2f62                	fld	ft10,24(sp)
  4e:	6f6c                	ld	a1,216(a4)
  50:	6461                	lui	s0,0x18
  52:	7265                	lui	tp,0xffff9
  54:	6c662f73          	csrrs	t5,0x6c6,a2
  58:	7361                	lui	t1,0xffff8
  5a:	2f68                	fld	fa0,216(a4)
  5c:	6d65                	lui	s10,0x19
  5e:	636d                	lui	t1,0x1b
  60:	6377642f          	0x6377642f
  64:	736d                	lui	t1,0xffffb
  66:	6368                	ld	a0,192(a4)
  68:	6372732f          	0x6372732f
  6c:	6f6f622f          	0x6f6f622f
  70:	2f74                	fld	fa3,216(a4)
  72:	6972                	ld	s2,280(sp)
  74:	00766373          	csrrsi	t1,0x7,12
  78:	7700                	ld	s0,40(a4)
  7a:	6172                	ld	sp,280(sp)
  7c:	7070                	ld	a2,224(s0)
  7e:	7265                	lui	tp,0xffff9
  80:	532e                	lw	t1,232(sp)
  82:	0100                	addi	s0,sp,128
  84:	0000                	unimp
  86:	0000                	unimp
  88:	0209                	addi	tp,tp,2
  8a:	0000                	unimp
  8c:	6100                	ld	s0,0(a0)
  8e:	0000                	unimp
  90:	0000                	unimp
  92:	03010d03          	lb	s10,48(sp)
  96:	0901                	addi	s2,s2,0
  98:	0008                	0x8
  9a:	0301                	addi	t1,t1,0
  9c:	0901                	addi	s2,s2,0
  9e:	0004                	0x4
  a0:	0901                	addi	s2,s2,0
  a2:	0004                	0x4
  a4:	0100                	addi	s0,sp,128
  a6:	7f01                	lui	t5,0xfffe0
  a8:	0004                	0x4
  aa:	0300                	addi	s0,sp,384
  ac:	3f00                	fld	fs0,56(a4)
  ae:	0001                	nop
  b0:	0100                	addi	s0,sp,128
  b2:	fb01                	bnez	a4,ffffffffffffffc2 <out_buf+0xffffffff9efff75a>
  b4:	0d0e                	slli	s10,s10,0x3
  b6:	0100                	addi	s0,sp,128
  b8:	0101                	addi	sp,sp,0
  ba:	0001                	nop
  bc:	0000                	unimp
  be:	0001                	nop
  c0:	0100                	addi	s0,sp,128
  c2:	3a44                	fld	fs1,176(a2)
  c4:	726f772f          	0x726f772f
  c8:	30322f6b          	0x30322f6b
  cc:	3232                	fld	ft4,296(sp)
  ce:	396c612f          	0x396c612f
  d2:	3030                	fld	fa2,96(s0)
  d4:	2f30                	fld	fa2,88(a4)
  d6:	642f7773          	csrrci	a4,0x642,30
  da:	6265                	lui	tp,0x19
  dc:	6775                	lui	a4,0x1d
  de:	2f726567          	0x2f726567
  e2:	6c61                	lui	s8,0x18
  e4:	6f5f 6570 6f6e      	0x6f6e65706f5f
  ea:	632f6463          	bltu	t5,s2,712 <_start-0x60fff8ee>
  ee:	72746e6f          	jal	t3,47014 <_start-0x60fb8fec>
  f2:	6269                	lui	tp,0x1a
  f4:	616f6c2f          	0x616f6c2f
  f8:	6564                	ld	s1,200(a0)
  fa:	7372                	ld	t1,312(sp)
  fc:	616c662f          	0x616c662f
 100:	652f6873          	csrrsi	a6,0x652,30
 104:	6d6d                	lui	s10,0x1b
 106:	77642f63          	0x77642f63
 10a:	68736d63          	bltu	t1,t2,7a4 <_start-0x60fff85c>
 10e:	72732f63          	0x72732f63
 112:	3a640063          	beq	s0,t1,4b2 <_start-0x60fffb4e>
 116:	775c                	ld	a5,168(a4)
 118:	5c6b726f          	jal	tp,b76de <_start-0x60f48922>
 11c:	3032                	fld	ft0,296(sp)
 11e:	3232                	fld	ft4,296(sp)
 120:	615c                	ld	a5,128(a0)
 122:	396c                	fld	fa1,240(a0)
 124:	3030                	fld	fa2,96(s0)
 126:	5c30                	lw	a2,120(s0)
 128:	735c7773          	csrrci	a4,0x735,24
 12c:	6b64                	ld	s1,208(a4)
 12e:	735c                	ld	a5,160(a4)
 130:	612d636f          	jal	t1,d6742 <_start-0x60f298be>
 134:	7570                	ld	a2,232(a0)
 136:	732d                	lui	t1,0xfffeb
 138:	6b64                	ld	s1,208(a4)
 13a:	745c                	ld	a5,168(s0)
 13c:	736c6f6f          	jal	t5,c6872 <_start-0x60f3978e>
 140:	775c                	ld	a5,168(a4)
 142:	6e69                	lui	t3,0x1a
 144:	725c                	ld	a5,160(a2)
 146:	7369                	lui	t1,0xffffa
 148:	725c7663          	bgeu	s8,t0,874 <_start-0x60fff78c>
 14c:	7369                	lui	t1,0xffffa
 14e:	6e2d7663          	bgeu	s10,sp,83a <_start-0x60fff7c6>
 152:	6375                	lui	t1,0x1d
 154:	656c                	ld	a1,200(a0)
 156:	2d69                	addiw	s10,s10,26
 158:	6c65                	lui	s8,0x19
 15a:	5c66                	lw	s8,120(sp)
 15c:	6e69                	lui	t3,0x1a
 15e:	64756c63          	bltu	a0,t2,7b6 <_start-0x60fff84a>
 162:	5c65                	li	s8,-7
 164:	616d                	addi	sp,sp,240
 166:	6e696863          	bltu	s2,t1,856 <_start-0x60fff7aa>
 16a:	0065                	c.nop	25
 16c:	3a64                	fld	fs1,240(a2)
 16e:	775c                	ld	a5,168(a4)
 170:	5c6b726f          	jal	tp,b7736 <_start-0x60f488ca>
 174:	3032                	fld	ft0,296(sp)
 176:	3232                	fld	ft4,296(sp)
 178:	615c                	ld	a5,128(a0)
 17a:	396c                	fld	fa1,240(a0)
 17c:	3030                	fld	fa2,96(s0)
 17e:	5c30                	lw	a2,120(s0)
 180:	735c7773          	csrrci	a4,0x735,24
 184:	6b64                	ld	s1,208(a4)
 186:	735c                	ld	a5,160(a4)
 188:	612d636f          	jal	t1,d679a <_start-0x60f29866>
 18c:	7570                	ld	a2,232(a0)
 18e:	732d                	lui	t1,0xfffeb
 190:	6b64                	ld	s1,208(a4)
 192:	745c                	ld	a5,168(s0)
 194:	736c6f6f          	jal	t5,c68ca <_start-0x60f39736>
 198:	775c                	ld	a5,168(a4)
 19a:	6e69                	lui	t3,0x1a
 19c:	725c                	ld	a5,160(a2)
 19e:	7369                	lui	t1,0xffffa
 1a0:	725c7663          	bgeu	s8,t0,8cc <_start-0x60fff734>
 1a4:	7369                	lui	t1,0xffffa
 1a6:	6e2d7663          	bgeu	s10,sp,892 <_start-0x60fff76e>
 1aa:	6375                	lui	t1,0x1d
 1ac:	656c                	ld	a1,200(a0)
 1ae:	2d69                	addiw	s10,s10,26
 1b0:	6c65                	lui	s8,0x19
 1b2:	5c66                	lw	s8,120(sp)
 1b4:	6e69                	lui	t3,0x1a
 1b6:	64756c63          	bltu	a0,t2,80e <_start-0x60fff7f2>
 1ba:	5c65                	li	s8,-7
 1bc:	00737973          	csrrci	s2,0x7,6
 1c0:	6400                	ld	s0,8(s0)
 1c2:	736d6377          	0x736d6377
 1c6:	6368                	ld	a0,192(a4)
 1c8:	632e                	ld	t1,200(sp)
 1ca:	0100                	addi	s0,sp,128
 1cc:	0000                	unimp
 1ce:	645f 6665 7561      	0x75616665645f
 1d4:	746c                	ld	a1,232(s0)
 1d6:	745f 7079 7365      	0x73657079745f
 1dc:	682e                	ld	a6,200(sp)
 1de:	0200                	addi	s0,sp,256
 1e0:	0000                	unimp
 1e2:	735f 6474 6e69      	0x6e696474735f
 1e8:	2e74                	fld	fa3,216(a2)
 1ea:	0068                	addi	a0,sp,12
 1ec:	00000003          	lb	zero,0(zero) # 0 <_start-0x61000000>
 1f0:	0105                	addi	sp,sp,1
 1f2:	0900                	addi	s0,sp,144
 1f4:	1002                	c.slli	zero,0x20
 1f6:	0000                	unimp
 1f8:	0061                	c.nop	24
 1fa:	0000                	unimp
 1fc:	1500                	addi	s0,sp,672
 1fe:	0505                	addi	a0,a0,1
 200:	04090103          	lb	sp,64(s2) # fffffffffffe7040 <out_buf+0xffffffff9efe67d8>
 204:	0100                	addi	s0,sp,128
 206:	00090103          	lb	sp,0(s2)
 20a:	0100                	addi	s0,sp,128
 20c:	0a05                	addi	s4,s4,1
 20e:	00090003          	lb	zero,0(s2)
 212:	0100                	addi	s0,sp,128
 214:	0905                	addi	s2,s2,1
 216:	00090203          	lb	tp,0(s2)
 21a:	0100                	addi	s0,sp,128
 21c:	0c05                	addi	s8,s8,1
 21e:	0306                	slli	t1,t1,0x1
 220:	0900                	addi	s0,sp,144
 222:	0000                	unimp
 224:	0301                	addi	t1,t1,0
 226:	0901                	addi	s2,s2,0
 228:	0004                	0x4
 22a:	0301                	addi	t1,t1,0
 22c:	0004097f          	radd16	s2,s0,zero
 230:	0501                	addi	a0,a0,0
 232:	0609                	addi	a2,a2,2
 234:	04090103          	lb	sp,64(s2)
 238:	0100                	addi	s0,sp,128
 23a:	0c05                	addi	s8,s8,1
 23c:	0306                	slli	t1,t1,0x1
 23e:	0900                	addi	s0,sp,144
 240:	0000                	unimp
 242:	0501                	addi	a0,a0,0
 244:	060a                	slli	a2,a2,0x2
 246:	04097d03          	0x4097d03
 24a:	0100                	addi	s0,sp,128
 24c:	0505                	addi	a0,a0,1
 24e:	04090503          	lb	a0,64(s2)
 252:	0100                	addi	s0,sp,128
 254:	0105                	addi	sp,sp,1
 256:	0306                	slli	t1,t1,0x1
 258:	0901                	addi	s2,s2,0
 25a:	0000                	unimp
 25c:	0601                	addi	a2,a2,0
 25e:	04090303          	lb	t1,64(s2)
 262:	0100                	addi	s0,sp,128
 264:	0505                	addi	a0,a0,1
 266:	00090103          	lb	sp,0(s2)
 26a:	0100                	addi	s0,sp,128
 26c:	00090103          	lb	sp,0(s2)
 270:	0100                	addi	s0,sp,128
 272:	08090103          	lb	sp,128(s2)
 276:	0100                	addi	s0,sp,128
 278:	0905                	addi	s2,s2,1
 27a:	00090203          	lb	tp,0(s2)
 27e:	0100                	addi	s0,sp,128
 280:	1105                	addi	sp,sp,-31
 282:	0306                	slli	t1,t1,0x1
 284:	0900                	addi	s0,sp,144
 286:	0000                	unimp
 288:	0501                	addi	a0,a0,0
 28a:	0609                	addi	a2,a2,2
 28c:	08090103          	lb	sp,128(s2)
 290:	0100                	addi	s0,sp,128
 292:	00090103          	lb	sp,0(s2)
 296:	0100                	addi	s0,sp,128
 298:	0b05                	addi	s6,s6,1
 29a:	0306                	slli	t1,t1,0x1
 29c:	0900                	addi	s0,sp,144
 29e:	0000                	unimp
 2a0:	0501                	addi	a0,a0,0
 2a2:	0016                	c.slli	zero,0x5
 2a4:	0402                	c.slli64	s0
 2a6:	0301                	addi	t1,t1,0
 2a8:	0900                	addi	s0,sp,144
 2aa:	000c                	0xc
 2ac:	0501                	addi	a0,a0,0
 2ae:	0605                	addi	a2,a2,1
 2b0:	08090303          	lb	t1,128(s2)
 2b4:	0100                	addi	s0,sp,128
 2b6:	1c05                	addi	s8,s8,-31
 2b8:	0306                	slli	t1,t1,0x1
 2ba:	0900                	addi	s0,sp,144
 2bc:	0000                	unimp
 2be:	0501                	addi	a0,a0,0
 2c0:	0900030f          	0x900030f
 2c4:	0008                	0x8
 2c6:	0501                	addi	a0,a0,0
 2c8:	0605                	addi	a2,a2,1
 2ca:	08090103          	lb	sp,128(s2)
 2ce:	0100                	addi	s0,sp,128
 2d0:	00090003          	lb	zero,0(s2)
 2d4:	0100                	addi	s0,sp,128
 2d6:	04090003          	lb	zero,64(s2)
 2da:	0100                	addi	s0,sp,128
 2dc:	00090103          	lb	sp,0(s2)
 2e0:	0100                	addi	s0,sp,128
 2e2:	0105                	addi	sp,sp,1
 2e4:	0306                	slli	t1,t1,0x1
 2e6:	0901                	addi	s2,s2,0
 2e8:	0000                	unimp
 2ea:	0601                	addi	a2,a2,0
 2ec:	08090303          	lb	t1,128(s2)
 2f0:	0100                	addi	s0,sp,128
 2f2:	0505                	addi	a0,a0,1
 2f4:	00090103          	lb	sp,0(s2)
 2f8:	0100                	addi	s0,sp,128
 2fa:	00090103          	lb	sp,0(s2)
 2fe:	0100                	addi	s0,sp,128
 300:	00090203          	lb	tp,0(s2)
 304:	0100                	addi	s0,sp,128
 306:	00090003          	lb	zero,0(s2)
 30a:	0100                	addi	s0,sp,128
 30c:	0306                	slli	t1,t1,0x1
 30e:	0901                	addi	s2,s2,0
 310:	0000                	unimp
 312:	0301                	addi	t1,t1,0
 314:	0004097f          	radd16	s2,s0,zero
 318:	0601                	addi	a2,a2,0
 31a:	04090003          	lb	zero,64(s2)
 31e:	0100                	addi	s0,sp,128
 320:	00090103          	lb	sp,0(s2)
 324:	0100                	addi	s0,sp,128
 326:	00090003          	lb	zero,0(s2)
 32a:	0100                	addi	s0,sp,128
 32c:	0200                	addi	s0,sp,256
 32e:	0104                	addi	s1,sp,128
 330:	08090003          	lb	zero,128(s2)
 334:	0100                	addi	s0,sp,128
 336:	0200                	addi	s0,sp,256
 338:	0104                	addi	s1,sp,128
 33a:	00090103          	lb	sp,0(s2)
 33e:	0100                	addi	s0,sp,128
 340:	0905                	addi	s2,s2,1
 342:	0200                	addi	s0,sp,256
 344:	0104                	addi	s1,sp,128
 346:	00090203          	lb	tp,0(s2)
 34a:	0100                	addi	s0,sp,128
 34c:	1105                	addi	sp,sp,-31
 34e:	0200                	addi	s0,sp,256
 350:	0104                	addi	s1,sp,128
 352:	0306                	slli	t1,t1,0x1
 354:	0900                	addi	s0,sp,144
 356:	0000                	unimp
 358:	0501                	addi	a0,a0,0
 35a:	0009                	c.nop	2
 35c:	0402                	c.slli64	s0
 35e:	0601                	addi	a2,a2,0
 360:	08090103          	lb	sp,128(s2)
 364:	0100                	addi	s0,sp,128
 366:	0200                	addi	s0,sp,256
 368:	0104                	addi	s1,sp,128
 36a:	00090103          	lb	sp,0(s2)
 36e:	0100                	addi	s0,sp,128
 370:	0b05                	addi	s6,s6,1
 372:	0200                	addi	s0,sp,256
 374:	0104                	addi	s1,sp,128
 376:	0306                	slli	t1,t1,0x1
 378:	0900                	addi	s0,sp,144
 37a:	0000                	unimp
 37c:	0501                	addi	a0,a0,0
 37e:	0016                	c.slli	zero,0x5
 380:	0402                	c.slli64	s0
 382:	0301                	addi	t1,t1,0
 384:	0900                	addi	s0,sp,144
 386:	0008                	0x8
 388:	0501                	addi	a0,a0,0
 38a:	0009                	c.nop	2
 38c:	0402                	c.slli64	s0
 38e:	04030603          	lb	a2,64(t1) # 1d040 <_start-0x60fe2fc0>
 392:	0c09                	addi	s8,s8,2
 394:	0100                	addi	s0,sp,128
 396:	0200                	addi	s0,sp,256
 398:	0304                	addi	s1,sp,384
 39a:	00090003          	lb	zero,0(s2)
 39e:	0100                	addi	s0,sp,128
 3a0:	0505                	addi	a0,a0,1
 3a2:	0200                	addi	s0,sp,256
 3a4:	0304                	addi	s1,sp,384
 3a6:	0306                	slli	t1,t1,0x1
 3a8:	0004097f          	radd16	s2,s0,zero
 3ac:	0501                	addi	a0,a0,0
 3ae:	0009                	c.nop	2
 3b0:	0402                	c.slli64	s0
 3b2:	09010303          	lb	t1,144(sp)
 3b6:	0004                	0x4
 3b8:	0001                	nop
 3ba:	0402                	c.slli64	s0
 3bc:	00030603          	lb	a2,0(t1)
 3c0:	0409                	addi	s0,s0,2
 3c2:	0100                	addi	s0,sp,128
 3c4:	2605                	addiw	a2,a2,1
 3c6:	0200                	addi	s0,sp,256
 3c8:	0304                	addi	s1,sp,384
 3ca:	00097f03          	0x97f03
 3ce:	0100                	addi	s0,sp,128
 3d0:	0e05                	addi	t3,t3,1
 3d2:	0200                	addi	s0,sp,256
 3d4:	0304                	addi	s1,sp,384
 3d6:	00090003          	lb	zero,0(s2)
 3da:	0100                	addi	s0,sp,128
 3dc:	0505                	addi	a0,a0,1
 3de:	0200                	addi	s0,sp,256
 3e0:	0304                	addi	s1,sp,384
 3e2:	0306                	slli	t1,t1,0x1
 3e4:	0900                	addi	s0,sp,144
 3e6:	0000                	unimp
 3e8:	0601                	addi	a2,a2,0
 3ea:	04090203          	lb	tp,64(s2)
 3ee:	0100                	addi	s0,sp,128
 3f0:	0905                	addi	s2,s2,1
 3f2:	00090203          	lb	tp,0(s2)
 3f6:	0100                	addi	s0,sp,128
 3f8:	1105                	addi	sp,sp,-31
 3fa:	0306                	slli	t1,t1,0x1
 3fc:	0900                	addi	s0,sp,144
 3fe:	0000                	unimp
 400:	0501                	addi	a0,a0,0
 402:	0609                	addi	a2,a2,2
 404:	08090103          	lb	sp,128(s2)
 408:	0100                	addi	s0,sp,128
 40a:	00090103          	lb	sp,0(s2)
 40e:	0100                	addi	s0,sp,128
 410:	0b05                	addi	s6,s6,1
 412:	0306                	slli	t1,t1,0x1
 414:	0900                	addi	s0,sp,144
 416:	0000                	unimp
 418:	0501                	addi	a0,a0,0
 41a:	0016                	c.slli	zero,0x5
 41c:	0402                	c.slli64	s0
 41e:	0301                	addi	t1,t1,0
 420:	0900                	addi	s0,sp,144
 422:	000c                	0xc
 424:	0501                	addi	a0,a0,0
 426:	0605                	addi	a2,a2,1
 428:	08090303          	lb	t1,128(s2)
 42c:	0100                	addi	s0,sp,128
 42e:	00090003          	lb	zero,0(s2)
 432:	0100                	addi	s0,sp,128
 434:	0c090003          	lb	zero,192(s2)
 438:	0100                	addi	s0,sp,128
 43a:	0105                	addi	sp,sp,1
 43c:	0306                	slli	t1,t1,0x1
 43e:	0902                	c.slli64	s2
 440:	0000                	unimp
 442:	0601                	addi	a2,a2,0
 444:	04090303          	lb	t1,64(s2)
 448:	0100                	addi	s0,sp,128
 44a:	0505                	addi	a0,a0,1
 44c:	00090103          	lb	sp,0(s2)
 450:	0100                	addi	s0,sp,128
 452:	00090203          	lb	tp,0(s2)
 456:	0100                	addi	s0,sp,128
 458:	00090003          	lb	zero,0(s2)
 45c:	0100                	addi	s0,sp,128
 45e:	0105                	addi	sp,sp,1
 460:	0306                	slli	t1,t1,0x1
 462:	097d                	addi	s2,s2,31
 464:	0000                	unimp
 466:	0501                	addi	a0,a0,0
 468:	0305                	addi	t1,t1,1
 46a:	0904                	addi	s1,sp,144
 46c:	0014                	0x14
 46e:	0301                	addi	t1,t1,0
 470:	0008097f          	radd16	s2,a6,zero
 474:	0601                	addi	a2,a2,0
 476:	04090003          	lb	zero,64(s2)
 47a:	0100                	addi	s0,sp,128
 47c:	00090103          	lb	sp,0(s2)
 480:	0100                	addi	s0,sp,128
 482:	00090003          	lb	zero,0(s2)
 486:	0100                	addi	s0,sp,128
 488:	04090003          	lb	zero,64(s2)
 48c:	0100                	addi	s0,sp,128
 48e:	00090103          	lb	sp,0(s2)
 492:	0100                	addi	s0,sp,128
 494:	0105                	addi	sp,sp,1
 496:	0306                	slli	t1,t1,0x1
 498:	0000097b          	dkhmx8	s2,zero,zero
 49c:	0501                	addi	a0,a0,0
 49e:	0305                	addi	t1,t1,1
 4a0:	0905                	addi	s2,s2,1
 4a2:	0004                	0x4
 4a4:	0501                	addi	a0,a0,0
 4a6:	0301                	addi	t1,t1,0
 4a8:	0004097b          	dkhmx8	s2,s0,zero
 4ac:	0501                	addi	a0,a0,0
 4ae:	0305                	addi	t1,t1,1
 4b0:	0905                	addi	s2,s2,1
 4b2:	0008                	0x8
 4b4:	0601                	addi	a2,a2,0
 4b6:	04090203          	lb	tp,64(s2)
 4ba:	0100                	addi	s0,sp,128
 4bc:	0e05                	addi	t3,t3,1
 4be:	0200                	addi	s0,sp,256
 4c0:	0104                	addi	s1,sp,128
 4c2:	04090003          	lb	zero,64(s2)
 4c6:	0100                	addi	s0,sp,128
 4c8:	0505                	addi	a0,a0,1
 4ca:	0200                	addi	s0,sp,256
 4cc:	0104                	addi	s1,sp,128
 4ce:	0306                	slli	t1,t1,0x1
 4d0:	0900                	addi	s0,sp,144
 4d2:	0000                	unimp
 4d4:	0601                	addi	a2,a2,0
 4d6:	08090203          	lb	tp,128(s2)
 4da:	0100                	addi	s0,sp,128
 4dc:	0105                	addi	sp,sp,1
 4de:	0306                	slli	t1,t1,0x1
 4e0:	0901                	addi	s2,s2,0
 4e2:	0004                	0x4
 4e4:	0501                	addi	a0,a0,0
 4e6:	0305                	addi	t1,t1,1
 4e8:	0010097f          	radd16	s2,zero,ra
 4ec:	0501                	addi	a0,a0,0
 4ee:	0301                	addi	t1,t1,0
 4f0:	0901                	addi	s2,s2,0
 4f2:	0004                	0x4
 4f4:	0501                	addi	a0,a0,0
 4f6:	0305                	addi	t1,t1,1
 4f8:	0004097f          	radd16	s2,s0,zero
 4fc:	0501                	addi	a0,a0,0
 4fe:	0009                	c.nop	2
 500:	0402                	c.slli64	s0
 502:	7f030603          	lb	a2,2032(t1)
 506:	0409                	addi	s0,s0,2
 508:	0100                	addi	s0,sp,128
 50a:	1705                	addi	a4,a4,-31
 50c:	0200                	addi	s0,sp,256
 50e:	0304                	addi	s1,sp,384
 510:	0306                	slli	t1,t1,0x1
 512:	0900                	addi	s0,sp,144
 514:	0000                	unimp
 516:	0501                	addi	a0,a0,0
 518:	001c                	0x1c
 51a:	0402                	c.slli64	s0
 51c:	7f030603          	lb	a2,2032(t1)
 520:	1009                	c.nop	-30
 522:	0100                	addi	s0,sp,128
 524:	0809                	addi	a6,a6,2
 526:	0000                	unimp
 528:	0101                	addi	sp,sp,0
 52a:	00000283          	lb	t0,0(zero) # 0 <_start-0x61000000>
 52e:	01500003          	lb	zero,21(zero) # 15 <_start-0x60ffffeb>
 532:	0000                	unimp
 534:	0101                	addi	sp,sp,0
 536:	000d0efb          	dkhmx8	t4,s10,zero
 53a:	0101                	addi	sp,sp,0
 53c:	0101                	addi	sp,sp,0
 53e:	0000                	unimp
 540:	0100                	addi	s0,sp,128
 542:	0000                	unimp
 544:	4401                	li	s0,0
 546:	2f3a                	fld	ft10,392(sp)
 548:	6b726f77          	0x6b726f77
 54c:	3230322f          	0x3230322f
 550:	2f32                	fld	ft10,264(sp)
 552:	6c61                	lui	s8,0x18
 554:	3039                	0x3039
 556:	3030                	fld	fa2,96(s0)
 558:	2f77732f          	0x2f77732f
 55c:	6564                	ld	s1,200(a0)
 55e:	7562                	ld	a0,56(sp)
 560:	72656767          	0x72656767
 564:	5f6c612f          	0x5f6c612f
 568:	6e65706f          	j	57c4e <_start-0x60fa83b2>
 56c:	2f64636f          	jal	t1,46862 <_start-0x60fb979e>
 570:	746e6f63          	bltu	t3,t1,cce <_start-0x60fff332>
 574:	6972                	ld	s2,280(sp)
 576:	2f62                	fld	ft10,24(sp)
 578:	6f6c                	ld	a1,216(a4)
 57a:	6461                	lui	s0,0x18
 57c:	7265                	lui	tp,0xffff9
 57e:	6c662f73          	csrrs	t5,0x6c6,a2
 582:	7361                	lui	t1,0xffff8
 584:	2f68                	fld	fa0,216(a4)
 586:	6d65                	lui	s10,0x19
 588:	636d                	lui	t1,0x1b
 58a:	6377642f          	0x6377642f
 58e:	736d                	lui	t1,0xffffb
 590:	6368                	ld	a0,192(a4)
 592:	6372732f          	0x6372732f
 596:	6400                	ld	s0,8(s0)
 598:	5c3a                	lw	s8,172(sp)
 59a:	6b726f77          	0x6b726f77
 59e:	325c                	fld	fa5,160(a2)
 5a0:	3230                	fld	fa2,96(a2)
 5a2:	5c32                	lw	s8,44(sp)
 5a4:	6c61                	lui	s8,0x18
 5a6:	3039                	0x3039
 5a8:	3030                	fld	fa2,96(s0)
 5aa:	735c                	ld	a5,160(a4)
 5ac:	64735c77          	0x64735c77
 5b0:	6f735c6b          	0x6f735c6b
 5b4:	70612d63          	0x70612d63
 5b8:	2d75                	addiw	s10,s10,29
 5ba:	5c6b6473          	csrrsi	s0,0x5c6,22
 5be:	6f74                	ld	a3,216(a4)
 5c0:	5c736c6f          	jal	s8,37386 <_start-0x60fc8c7a>
 5c4:	5c6e6977          	0x5c6e6977
 5c8:	6972                	ld	s2,280(sp)
 5ca:	5c766373          	csrrsi	t1,0x5c7,12
 5ce:	6972                	ld	s2,280(sp)
 5d0:	2d766373          	csrrsi	t1,0x2d7,12
 5d4:	756e                	ld	a0,248(sp)
 5d6:	69656c63          	bltu	a0,s6,c6e <_start-0x60fff392>
 5da:	652d                	lui	a0,0xb
 5dc:	666c                	ld	a1,200(a2)
 5de:	695c                	ld	a5,144(a0)
 5e0:	636e                	ld	t1,216(sp)
 5e2:	756c                	ld	a1,232(a0)
 5e4:	6564                	ld	s1,200(a0)
 5e6:	6d5c                	ld	a5,152(a0)
 5e8:	6361                	lui	t1,0x18
 5ea:	6968                	ld	a0,208(a0)
 5ec:	656e                	ld	a0,216(sp)
 5ee:	6400                	ld	s0,8(s0)
 5f0:	5c3a                	lw	s8,172(sp)
 5f2:	6b726f77          	0x6b726f77
 5f6:	325c                	fld	fa5,160(a2)
 5f8:	3230                	fld	fa2,96(a2)
 5fa:	5c32                	lw	s8,44(sp)
 5fc:	6c61                	lui	s8,0x18
 5fe:	3039                	0x3039
 600:	3030                	fld	fa2,96(s0)
 602:	735c                	ld	a5,160(a4)
 604:	64735c77          	0x64735c77
 608:	6f735c6b          	0x6f735c6b
 60c:	70612d63          	0x70612d63
 610:	2d75                	addiw	s10,s10,29
 612:	5c6b6473          	csrrsi	s0,0x5c6,22
 616:	6f74                	ld	a3,216(a4)
 618:	5c736c6f          	jal	s8,373de <_start-0x60fc8c22>
 61c:	5c6e6977          	0x5c6e6977
 620:	6972                	ld	s2,280(sp)
 622:	5c766373          	csrrsi	t1,0x5c7,12
 626:	6972                	ld	s2,280(sp)
 628:	2d766373          	csrrsi	t1,0x2d7,12
 62c:	756e                	ld	a0,248(sp)
 62e:	69656c63          	bltu	a0,s6,cc6 <_start-0x60fff33a>
 632:	652d                	lui	a0,0xb
 634:	666c                	ld	a1,200(a2)
 636:	695c                	ld	a5,144(a0)
 638:	636e                	ld	t1,216(sp)
 63a:	756c                	ld	a1,232(a0)
 63c:	6564                	ld	s1,200(a0)
 63e:	735c                	ld	a5,160(a4)
 640:	7379                	lui	t1,0xffffe
 642:	0000                	unimp
 644:	7764                	ld	s1,232(a4)
 646:	68736d63          	bltu	t1,t2,ce0 <_start-0x60fff320>
 64a:	72635f63          	bge	t1,t1,d88 <_start-0x60fff278>
 64e:	00632e63          	0x632e63
 652:	0001                	nop
 654:	5f00                	lw	s0,56(a4)
 656:	6564                	ld	s1,200(a0)
 658:	6166                	ld	sp,88(sp)
 65a:	6c75                	lui	s8,0x1d
 65c:	5f74                	lw	a3,124(a4)
 65e:	7974                	ld	a3,240(a0)
 660:	6570                	ld	a2,200(a0)
 662:	00682e73          	csrrs	t3,0x6,a6
 666:	0002                	c.slli64	zero
 668:	5f00                	lw	s0,56(a4)
 66a:	69647473          	csrrci	s0,0x696,8
 66e:	746e                	ld	s0,248(sp)
 670:	682e                	ld	a6,200(sp)
 672:	0300                	addi	s0,sp,384
 674:	0000                	unimp
 676:	7764                	ld	s1,232(a4)
 678:	68736d63          	bltu	t1,t2,d12 <_start-0x60fff2ee>
 67c:	00682e63          	0x682e63
 680:	0001                	nop
 682:	0000                	unimp
 684:	0105                	addi	sp,sp,1
 686:	0900                	addi	s0,sp,144
 688:	5002                	0x5002
 68a:	0001                	nop
 68c:	0061                	c.nop	24
 68e:	0000                	unimp
 690:	0300                	addi	s0,sp,384
 692:	050100c7          	fmsub.h	ft1,ft2,fa6,ft0,rne
 696:	0305                	addi	t1,t1,1
 698:	0901                	addi	s2,s2,0
 69a:	0000                	unimp
 69c:	0301                	addi	t1,t1,0
 69e:	0901                	addi	s2,s2,0
 6a0:	0000                	unimp
 6a2:	0301                	addi	t1,t1,0
 6a4:	0901                	addi	s2,s2,0
 6a6:	0000                	unimp
 6a8:	0301                	addi	t1,t1,0
 6aa:	0901                	addi	s2,s2,0
 6ac:	0000                	unimp
 6ae:	0501                	addi	a0,a0,0
 6b0:	0601                	addi	a2,a2,0
 6b2:	00097c03          	0x97c03
 6b6:	0100                	addi	s0,sp,128
 6b8:	34090003          	lb	zero,832(s2)
 6bc:	0100                	addi	s0,sp,128
 6be:	0905                	addi	s2,s2,1
 6c0:	10090d03          	lb	s10,256(s2)
 6c4:	0100                	addi	s0,sp,128
 6c6:	0e05                	addi	t3,t3,1
 6c8:	04097403          	0x4097403
 6cc:	0100                	addi	s0,sp,128
 6ce:	0905                	addi	s2,s2,1
 6d0:	08090c03          	lb	s8,128(s2)
 6d4:	0100                	addi	s0,sp,128
 6d6:	0e05                	addi	t3,t3,1
 6d8:	08090103          	lb	sp,128(s2)
 6dc:	0100                	addi	s0,sp,128
 6de:	2b05                	addiw	s6,s6,1
 6e0:	04090203          	lb	tp,64(s2)
 6e4:	0100                	addi	s0,sp,128
 6e6:	0a05                	addi	s4,s4,1
 6e8:	0306                	slli	t1,t1,0x1
 6ea:	0974                	addi	a3,sp,156
 6ec:	0008                	0x8
 6ee:	0501                	addi	a0,a0,0
 6f0:	0305                	addi	t1,t1,1
 6f2:	00040913          	mv	s2,s0
 6f6:	0501                	addi	a0,a0,0
 6f8:	0601                	addi	a2,a2,0
 6fa:	00090203          	lb	tp,0(s2)
 6fe:	0100                	addi	s0,sp,128
 700:	0905                	addi	s2,s2,1
 702:	0306                	slli	t1,t1,0x1
 704:	096d                	addi	s2,s2,27
 706:	003c                	addi	a5,sp,8
 708:	0301                	addi	t1,t1,0
 70a:	0904                	addi	s1,sp,144
 70c:	000c                	0xc
 70e:	0301                	addi	t1,t1,0
 710:	0901                	addi	s2,s2,0
 712:	0000                	unimp
 714:	0301                	addi	t1,t1,0
 716:	0902                	c.slli64	s2
 718:	0000                	unimp
 71a:	0301                	addi	t1,t1,0
 71c:	0901                	addi	s2,s2,0
 71e:	0014                	0x14
 720:	0501                	addi	a0,a0,0
 722:	7c030613          	addi	a2,t1,1984 # ffffffffffffe7c0 <out_buf+0xffffffff9effdf58>
 726:	0809                	addi	a6,a6,2
 728:	0100                	addi	s0,sp,128
 72a:	0e05                	addi	t3,t3,1
 72c:	0306                	slli	t1,t1,0x1
 72e:	0904                	addi	s1,sp,144
 730:	0004                	0x4
 732:	0501                	addi	a0,a0,0
 734:	0619                	addi	a2,a2,6
 736:	00090003          	lb	zero,0(s2)
 73a:	0100                	addi	s0,sp,128
 73c:	0e05                	addi	t3,t3,1
 73e:	04090003          	lb	zero,64(s2)
 742:	0100                	addi	s0,sp,128
 744:	0905                	addi	s2,s2,1
 746:	0306                	slli	t1,t1,0x1
 748:	0906                	slli	s2,s2,0x1
 74a:	0004                	0x4
 74c:	0501                	addi	a0,a0,0
 74e:	0003060f          	0x3060f
 752:	0009                	c.nop	2
 754:	0100                	addi	s0,sp,128
 756:	0905                	addi	s2,s2,1
 758:	0306                	slli	t1,t1,0x1
 75a:	0901                	addi	s2,s2,0
 75c:	0004                	0x4
 75e:	0501                	addi	a0,a0,0
 760:	0614                	addi	a3,sp,768
 762:	00090003          	lb	zero,0(s2)
 766:	0100                	addi	s0,sp,128
 768:	0d05                	addi	s10,s10,1
 76a:	0306                	slli	t1,t1,0x1
 76c:	0008097b          	dkhmx8	s2,a6,zero
 770:	0501                	addi	a0,a0,0
 772:	0618                	addi	a4,sp,768
 774:	00090003          	lb	zero,0(s2)
 778:	0100                	addi	s0,sp,128
 77a:	3205                	addiw	tp,tp,-31
 77c:	04090003          	lb	zero,64(s2)
 780:	0100                	addi	s0,sp,128
 782:	4205                	li	tp,1
 784:	04090003          	lb	zero,64(s2)
 788:	0100                	addi	s0,sp,128
 78a:	3905                	addiw	s2,s2,-31
 78c:	08090003          	lb	zero,128(s2)
 790:	0100                	addi	s0,sp,128
 792:	2b05                	addiw	s6,s6,1
 794:	04090003          	lb	zero,64(s2)
 798:	0100                	addi	s0,sp,128
 79a:	1105                	addi	sp,sp,-31
 79c:	10090003          	lb	zero,256(s2)
 7a0:	0100                	addi	s0,sp,128
 7a2:	0d05                	addi	s10,s10,1
 7a4:	0306                	slli	t1,t1,0x1
 7a6:	0901                	addi	s2,s2,0
 7a8:	0008                	0x8
 7aa:	0901                	addi	s2,s2,0
 7ac:	0004                	0x4
 7ae:	0100                	addi	s0,sp,128
 7b0:	01              	Address 0x00000000000007b0 is out of bounds.


Disassembly of section .debug_info:

0000000000000000 <.debug_info>:
   0:	002a                	c.slli	zero,0xa
   2:	0000                	unimp
   4:	0002                	c.slli64	zero
   6:	0000                	unimp
   8:	0000                	unimp
   a:	0108                	addi	a0,sp,128
   c:	0000                	unimp
   e:	0000                	unimp
  10:	0000                	unimp
  12:	6100                	ld	s0,0(a0)
  14:	0000                	unimp
  16:	0000                	unimp
  18:	0010                	0x10
  1a:	6100                	ld	s0,0(a0)
	...
  24:	00000067          	jr	zero # 0 <_start-0x61000000>
  28:	00b5                	addi	ra,ra,13
  2a:	0000                	unimp
  2c:	8001                	c.srli64	s0
  2e:	00000287          	vle8.v	v5,(zero),v0.t
  32:	0004                	0x4
  34:	0014                	0x14
  36:	0000                	unimp
  38:	0108                	addi	a0,sp,128
  3a:	01da                	slli	gp,gp,0x16
  3c:	0000                	unimp
  3e:	750c                	ld	a1,40(a0)
  40:	0001                	nop
  42:	6700                	ld	s0,8(a4)
  44:	0000                	unimp
  46:	1000                	addi	s0,sp,32
  48:	0000                	unimp
  4a:	0061                	c.nop	24
  4c:	0000                	unimp
  4e:	4000                	lw	s0,0(s0)
  50:	0001                	nop
  52:	0000                	unimp
  54:	0000                	unimp
  56:	a700                	fsd	fs0,8(a4)
  58:	0000                	unimp
  5a:	0200                	addi	s0,sp,256
  5c:	0601                	addi	a2,a2,0
  5e:	00f8                	addi	a4,sp,76
  60:	0000                	unimp
  62:	0000cd03          	lbu	s10,0(ra)
  66:	0200                	addi	s0,sp,256
  68:	0040182b          	0x40182b
  6c:	0000                	unimp
  6e:	0102                	c.slli64	sp
  70:	f608                	sd	a0,40(a2)
  72:	0000                	unimp
  74:	0200                	addi	s0,sp,256
  76:	0502                	c.slli64	a0
  78:	0236                	slli	tp,tp,0xd
  7a:	0000                	unimp
  7c:	0202                	c.slli64	tp
  7e:	00011607          	flh	fa2,0(sp)
  82:	0400                	addi	s0,sp,512
  84:	0504                	addi	s1,sp,640
  86:	6e69                	lui	t3,0x1a
  88:	0074                	addi	a3,sp,12
  8a:	00015103          	lhu	sp,0(sp)
  8e:	0200                	addi	s0,sp,256
  90:	0068194f          	fnmadd.s	fs2,fa6,ft6,ft0,rtz
  94:	0000                	unimp
  96:	0402                	c.slli64	s0
  98:	00010907          	vle8.v	v18,(sp),v0.t
  9c:	0200                	addi	s0,sp,256
  9e:	0508                	addi	a0,sp,640
  a0:	00000247          	fmsub.s	ft4,ft0,ft0,ft0,rne
  a4:	0802                	c.slli64	a6
  a6:	00010407          	vle8.v	v8,(sp),v0.t
  aa:	0300                	addi	s0,sp,384
  ac:	000000cf          	fnmadd.s	ft1,ft0,ft0,ft0,rne
  b0:	34131803          	lh	a6,833(t1)
  b4:	0000                	unimp
  b6:	0300                	addi	s0,sp,384
  b8:	00000153          	fadd.s	ft2,ft0,ft0,rne
  bc:	5c143003          	ld	zero,1473(s0) # 185c1 <_start-0x60fe7a3f>
  c0:	0000                	unimp
  c2:	0500                	addi	s0,sp,640
  c4:	0089                	addi	ra,ra,2
  c6:	0000                	unimp
  c8:	8906                	mv	s2,ra
  ca:	0000                	unimp
  cc:	0700                	addi	s0,sp,896
  ce:	000000d7          	vadd.vv	v1,v0,v0,v0.t
  d2:	3901                	addiw	s2,s2,-32
  d4:	d406                	sw	ra,40(sp)
  d6:	0000                	unimp
  d8:	0061                	c.nop	24
  da:	0000                	unimp
  dc:	7c00                	ld	s0,56(s0)
  de:	0000                	unimp
  e0:	0000                	unimp
  e2:	0000                	unimp
  e4:	0100                	addi	s0,sp,128
  e6:	439c                	lw	a5,0(a5)
  e8:	0001                	nop
  ea:	0800                	addi	s0,sp,16
  ec:	000000c3          	fmadd.s	ft1,ft0,ft0,ft0,rne
  f0:	3901                	addiw	s2,s2,-32
  f2:	4329                	li	t1,10
  f4:	0001                	nop
  f6:	0000                	unimp
  f8:	0000                	unimp
  fa:	0800                	addi	s0,sp,16
  fc:	0240                	addi	s0,sp,260
  fe:	0000                	unimp
 100:	3901                	addiw	s2,s2,-32
 102:	493e                	lw	s2,204(sp)
 104:	0001                	nop
 106:	7200                	ld	s0,32(a2)
 108:	0000                	unimp
 10a:	0800                	addi	s0,sp,16
 10c:	025f 0000 3901      	0x39010000025f
 112:	0000894f          	fnmadd.s	fs2,ft1,ft0,ft0,rne
 116:	d100                	sw	s0,32(a0)
 118:	0000                	unimp
 11a:	0800                	addi	s0,sp,16
 11c:	01d1                	addi	gp,gp,20
 11e:	0000                	unimp
 120:	3901                	addiw	s2,s2,-32
 122:	8960                	0x8960
 124:	0000                	unimp
 126:	0a00                	addi	s0,sp,272
 128:	0001                	nop
 12a:	0900                	addi	s0,sp,144
 12c:	0069                	c.nop	26
 12e:	3b01                	addiw	s6,s6,-32
 130:	890e                	mv	s2,gp
 132:	0000                	unimp
 134:	4300                	lw	s0,0(a4)
 136:	0001                	nop
 138:	0a00                	addi	s0,sp,272
 13a:	010c                	addi	a1,sp,128
 13c:	6100                	ld	s0,0(a0)
 13e:	0000                	unimp
 140:	0000                	unimp
 142:	01ce                	slli	gp,gp,0x13
 144:	0000                	unimp
 146:	0128                	addi	a0,sp,136
 148:	0000                	unimp
 14a:	025a010b          	0x25a010b
 14e:	0078                	addi	a4,sp,12
 150:	015b010b          	0x15b010b
 154:	0035                	c.nop	13
 156:	380c                	fld	fa1,48(s0)
 158:	0001                	nop
 15a:	0061                	c.nop	24
 15c:	0000                	unimp
 15e:	ce00                	sw	s0,24(a2)
 160:	0001                	nop
 162:	0b00                	addi	s0,sp,400
 164:	5a01                	li	s4,-32
 166:	5a01f303          	0x5a01f303
 16a:	015b010b          	0x15b010b
 16e:	0031                	c.nop	12
 170:	0d00                	addi	s0,sp,656
 172:	9508                	0x9508
 174:	0000                	unimp
 176:	0d00                	addi	s0,sp,656
 178:	8908                	0x8908
 17a:	0000                	unimp
 17c:	0700                	addi	s0,sp,896
 17e:	015c                	addi	a5,sp,132
 180:	0000                	unimp
 182:	1e01                	addi	t3,t3,-32
 184:	6c06                	ld	s8,64(sp)
 186:	0000                	unimp
 188:	0061                	c.nop	24
 18a:	0000                	unimp
 18c:	6800                	ld	s0,16(s0)
 18e:	0000                	unimp
 190:	0000                	unimp
 192:	0000                	unimp
 194:	0100                	addi	s0,sp,128
 196:	c89c                	sw	a5,16(s1)
 198:	0001                	nop
 19a:	0e00                	addi	s0,sp,784
 19c:	000000c3          	fmadd.s	ft1,ft0,ft0,ft0,rne
 1a0:	1e01                	addi	t3,t3,-32
 1a2:	432a                	lw	t1,136(sp)
 1a4:	0001                	nop
 1a6:	0100                	addi	s0,sp,128
 1a8:	0e5a                	slli	t3,t3,0x16
 1aa:	025f 0000 1e01      	0x1e010000025f
 1b0:	893e                	mv	s2,a5
 1b2:	0000                	unimp
 1b4:	0100                	addi	s0,sp,128
 1b6:	0240085b          	0x240085b
 1ba:	0000                	unimp
 1bc:	1e01                	addi	t3,t3,-32
 1be:	c856                	sw	s5,16(sp)
 1c0:	0001                	nop
 1c2:	a200                	fsd	fs0,0(a2)
 1c4:	0001                	nop
 1c6:	0900                	addi	s0,sp,144
 1c8:	0069                	c.nop	26
 1ca:	2001                	0x2001
 1cc:	890e                	mv	s2,gp
 1ce:	0000                	unimp
 1d0:	f100                	sd	s0,32(a0)
 1d2:	0001                	nop
 1d4:	0f00                	addi	s0,sp,912
 1d6:	016d                	addi	sp,sp,27
 1d8:	0000                	unimp
 1da:	2001                	0x2001
 1dc:	8911                	andi	a0,a0,4
 1de:	0000                	unimp
 1e0:	3b00                	fld	fs0,48(a4)
 1e2:	0002                	c.slli64	zero
 1e4:	0f00                	addi	s0,sp,912
 1e6:	0129                	addi	sp,sp,10
 1e8:	0000                	unimp
 1ea:	2101                	sext.w	sp,sp
 1ec:	7d0d                	lui	s10,0xfffe3
 1ee:	0000                	unimp
 1f0:	8400                	0x8400
 1f2:	0002                	c.slli64	zero
 1f4:	0000                	unimp
 1f6:	080d                	addi	a6,a6,3
 1f8:	009a                	slli	ra,ra,0x6
 1fa:	0000                	unimp
 1fc:	3310                	fld	fa2,32(a4)
 1fe:	0001                	nop
 200:	0100                	addi	s0,sp,128
 202:	050e                	slli	a0,a0,0x3
 204:	0055                	c.nop	21
 206:	0000                	unimp
 208:	002c                	addi	a1,sp,8
 20a:	6100                	ld	s0,0(a0)
 20c:	0000                	unimp
 20e:	0000                	unimp
 210:	0040                	addi	s0,sp,4
 212:	0000                	unimp
 214:	0000                	unimp
 216:	0000                	unimp
 218:	9c01                	subw	s0,s0,s0
 21a:	023d                	addi	tp,tp,15
 21c:	0000                	unimp
 21e:	c308                	sw	a0,0(a4)
 220:	0000                	unimp
 222:	0100                	addi	s0,sp,128
 224:	260e                	fld	fa2,192(sp)
 226:	00000143          	fmadd.s	ft2,ft0,ft0,ft0,rne
 22a:	02df 0000 5a0e      	0x5a0e000002df
 230:	0002                	c.slli64	zero
 232:	0100                	addi	s0,sp,128
 234:	390e                	fld	fs2,224(sp)
 236:	007d                	c.nop	31
 238:	0000                	unimp
 23a:	5b01                	li	s6,-32
 23c:	00016d0f          	0x16d0f
 240:	0100                	addi	s0,sp,128
 242:	0e10                	addi	a2,sp,784
 244:	0089                	addi	ra,ra,2
 246:	0000                	unimp
 248:	0318                	addi	a4,sp,384
 24a:	0000                	unimp
 24c:	5011                	c.li	zero,-28
 24e:	0002                	c.slli64	zero
 250:	0100                	addi	s0,sp,128
 252:	1710                	addi	a2,sp,928
 254:	0089                	addi	ra,ra,2
 256:	0000                	unimp
 258:	5f01                	li	t5,-32
 25a:	0001290f          	0x1290f
 25e:	0100                	addi	s0,sp,128
 260:	0d11                	addi	s10,s10,4
 262:	007d                	c.nop	31
 264:	0000                	unimp
 266:	034e                	slli	t1,t1,0x13
 268:	0000                	unimp
 26a:	1200                	addi	s0,sp,288
 26c:	000000e7          	jalr	zero # 0 <_start-0x61000000>
 270:	0301                	addi	t1,t1,0
 272:	0000890b          	0x890b
 276:	1000                	addi	s0,sp,32
 278:	0000                	unimp
 27a:	0061                	c.nop	24
 27c:	0000                	unimp
 27e:	1c00                	addi	s0,sp,560
 280:	0000                	unimp
 282:	0000                	unimp
 284:	0000                	unimp
 286:	0100                	addi	s0,sp,128
 288:	089c                	addi	a5,sp,80
 28a:	0141                	addi	sp,sp,16
 28c:	0000                	unimp
 28e:	0301                	addi	t1,t1,0
 290:	4924                	lw	s1,80(a0)
 292:	0001                	nop
 294:	a600                	fsd	fs0,8(a2)
 296:	09000003          	lb	zero,144(zero) # 90 <_start-0x60ffff70>
 29a:	01007077          	0x1007077
 29e:	0e05                	addi	t3,t3,1
 2a0:	0089                	addi	ra,ra,2
 2a2:	0000                	unimp
 2a4:	03dc                	addi	a5,sp,452
 2a6:	0000                	unimp
 2a8:	7209                	lui	tp,0xfffe2
 2aa:	0070                	addi	a2,sp,12
 2ac:	0501                	addi	a0,a0,0
 2ae:	8916                	mv	s2,t0
 2b0:	0000                	unimp
 2b2:	1300                	addi	s0,sp,416
 2b4:	0004                	0x4
 2b6:	0000                	unimp
 2b8:	df00                	sw	s0,56(a4)
 2ba:	0001                	nop
 2bc:	0400                	addi	s0,sp,512
 2be:	3600                	fld	fs0,40(a2)
 2c0:	0001                	nop
 2c2:	0800                	addi	s0,sp,16
 2c4:	da01                	beqz	a2,1d4 <_start-0x60fffe2c>
 2c6:	0001                	nop
 2c8:	0c00                	addi	s0,sp,528
 2ca:	0290                	addi	a2,sp,320
 2cc:	0000                	unimp
 2ce:	00000067          	jr	zero # 0 <_start-0x61000000>
 2d2:	0150                	addi	a2,sp,132
 2d4:	6100                	ld	s0,0(a0)
 2d6:	0000                	unimp
 2d8:	0000                	unimp
 2da:	0114                	addi	a3,sp,128
 2dc:	0000                	unimp
 2de:	0000                	unimp
 2e0:	0000                	unimp
 2e2:	052a                	slli	a0,a0,0xa
 2e4:	0000                	unimp
 2e6:	0102                	c.slli64	sp
 2e8:	f806                	sd	ra,48(sp)
 2ea:	0000                	unimp
 2ec:	0300                	addi	s0,sp,384
 2ee:	00cd                	addi	ra,ra,19
 2f0:	0000                	unimp
 2f2:	2b02                	fld	fs6,0(sp)
 2f4:	4018                	lw	a4,0(s0)
 2f6:	0000                	unimp
 2f8:	0200                	addi	s0,sp,256
 2fa:	0801                	addi	a6,a6,0
 2fc:	00f6                	slli	ra,ra,0x1d
 2fe:	0000                	unimp
 300:	0202                	c.slli64	tp
 302:	3605                	addiw	a2,a2,-31
 304:	0002                	c.slli64	zero
 306:	0200                	addi	s0,sp,256
 308:	0702                	c.slli64	a4
 30a:	0116                	slli	sp,sp,0x5
 30c:	0000                	unimp
 30e:	00026603          	lwu	a2,0(tp) # fffffffffffe2000 <out_buf+0xffffffff9efe1798>
 312:	0200                	addi	s0,sp,256
 314:	184d                	addi	a6,a6,-13
 316:	0061                	c.nop	24
 318:	0000                	unimp
 31a:	0404                	addi	s1,sp,512
 31c:	6905                	lui	s2,0x1
 31e:	746e                	ld	s0,248(sp)
 320:	0300                	addi	s0,sp,384
 322:	0151                	addi	sp,sp,20
 324:	0000                	unimp
 326:	4f02                	lw	t5,0(sp)
 328:	7419                	lui	s0,0xfffe6
 32a:	0000                	unimp
 32c:	0200                	addi	s0,sp,256
 32e:	0704                	addi	s1,sp,896
 330:	0109                	addi	sp,sp,2
 332:	0000                	unimp
 334:	7405                	lui	s0,0xfffe1
 336:	0000                	unimp
 338:	0200                	addi	s0,sp,256
 33a:	0508                	addi	a0,sp,640
 33c:	00000247          	fmsub.s	ft4,ft0,ft0,ft0,rne
 340:	0802                	c.slli64	a6
 342:	00010407          	vle8.v	v8,(sp),v0.t
 346:	0300                	addi	s0,sp,384
 348:	000000cf          	fnmadd.s	ft1,ft0,ft0,ft0,rne
 34c:	34131803          	lh	a6,833(t1)
 350:	0000                	unimp
 352:	0300                	addi	s0,sp,384
 354:	0268                	addi	a0,sp,268
 356:	0000                	unimp
 358:	55132c03          	lw	s8,1361(t1)
 35c:	0000                	unimp
 35e:	0300                	addi	s0,sp,384
 360:	00000153          	fadd.s	ft2,ft0,ft0,rne
 364:	68143003          	ld	zero,1665(s0) # fffffffffffe1681 <out_buf+0xffffffff9efe0e19>
 368:	0000                	unimp
 36a:	0600                	addi	s0,sp,768
 36c:	00a6                	slli	ra,ra,0x9
 36e:	0000                	unimp
 370:	00007b07          	vle64.v	v22,(zero),v0.t
 374:	c700                	sw	s0,8(a4)
 376:	0000                	unimp
 378:	0800                	addi	s0,sp,16
 37a:	00000087          	vle8.v	v1,(zero),v0.t
 37e:	b70500ff          	0xb70500ff
 382:	0000                	unimp
 384:	0900                	addi	s0,sp,144
 386:	02fa                	slli	t0,t0,0x1e
 388:	0000                	unimp
 38a:	0301                	addi	t1,t1,0
 38c:	0000c71b          	0xc71b
 390:	0900                	addi	s0,sp,144
 392:	00026803          	lwu	a6,0(tp) # 0 <_start-0x61000000>
 396:	0061                	c.nop	24
 398:	0000                	unimp
 39a:	0700                	addi	s0,sp,896
 39c:	008e                	slli	ra,ra,0x3
 39e:	0000                	unimp
 3a0:	000000f3          	0xf3
 3a4:	870a                	mv	a4,sp
 3a6:	0000                	unimp
 3a8:	ff00                	sd	s0,56(a4)
 3aa:	0001                	nop
 3ac:	0609                	addi	a2,a2,2
 3ae:	01000003          	lb	zero,16(zero) # 10 <_start-0x60fffff0>
 3b2:	1046                	c.slli	zero,0x31
 3b4:	00e2                	slli	ra,ra,0x18
 3b6:	0000                	unimp
 3b8:	0309                	addi	t1,t1,2
 3ba:	0868                	addi	a0,sp,28
 3bc:	6100                	ld	s0,0(a0)
 3be:	0000                	unimp
 3c0:	0000                	unimp
 3c2:	00030e0b          	0x30e0b
 3c6:	0100                	addi	s0,sp,128
 3c8:	00610547          	fmsub.s	fa0,ft2,ft6,ft0,rne
 3cc:	0000                	unimp
 3ce:	0150                	addi	a2,sp,132
 3d0:	6100                	ld	s0,0(a0)
 3d2:	0000                	unimp
 3d4:	0000                	unimp
 3d6:	0114                	addi	a3,sp,128
 3d8:	0000                	unimp
 3da:	0000                	unimp
 3dc:	0000                	unimp
 3de:	9c01                	subw	s0,s0,s0
 3e0:	01d0                	addi	a2,sp,196
 3e2:	0000                	unimp
 3e4:	c30c                	sw	a1,0(a4)
 3e6:	0000                	unimp
 3e8:	0100                	addi	s0,sp,128
 3ea:	01d02547          	fmsub.s	fa0,ft0,ft9,ft0,rdn
 3ee:	0000                	unimp
 3f0:	044a                	slli	s0,s0,0x12
 3f2:	0000                	unimp
 3f4:	850c                	0x850c
 3f6:	0002                	c.slli64	zero
 3f8:	0100                	addi	s0,sp,128
 3fa:	009a3847          	fmsub.s	fa6,fs4,fs1,ft0,rup
 3fe:	0000                	unimp
 400:	04a9                	addi	s1,s1,10
 402:	0000                	unimp
 404:	f40c                	sd	a1,40(s0)
 406:	0002                	c.slli64	zero
 408:	0100                	addi	s0,sp,128
 40a:	00614847          	fmsub.s	fa6,ft2,ft6,ft0,rmm
 40e:	0000                	unimp
 410:	0508                	addi	a0,sp,640
 412:	0000                	unimp
 414:	700c                	ld	a1,32(s0)
 416:	0002                	c.slli64	zero
 418:	0100                	addi	s0,sp,128
 41a:	00615347          	fmsub.s	ft6,ft2,ft6,ft0,unknown
 41e:	0000                	unimp
 420:	0564                	addi	s1,sp,652
 422:	0000                	unimp
 424:	630d                	lui	t1,0x3
 426:	6372                	ld	t1,280(sp)
 428:	0100                	addi	s0,sp,128
 42a:	0e49                	addi	t3,t3,18
 42c:	00a6                	slli	ra,ra,0x9
 42e:	0000                	unimp
 430:	05c0                	addi	s0,sp,708
 432:	0000                	unimp
 434:	7b0e                	ld	s6,224(sp)
 436:	0002                	c.slli64	zero
 438:	0100                	addi	s0,sp,128
 43a:	094a                	slli	s2,s2,0x12
 43c:	0061                	c.nop	24
 43e:	0000                	unimp
 440:	061e                	slli	a2,a2,0x7
 442:	0000                	unimp
 444:	f00e                	sd	gp,32(sp)
 446:	0002                	c.slli64	zero
 448:	0100                	addi	s0,sp,128
 44a:	184a                	slli	a6,a6,0x32
 44c:	0061                	c.nop	24
 44e:	0000                	unimp
 450:	0642                	slli	a2,a2,0x10
 452:	0000                	unimp
 454:	690d                	lui	s2,0x3
 456:	0100                	addi	s0,sp,128
 458:	0061094b          	fnmsub.s	fs2,ft2,ft6,ft0,rne
 45c:	0000                	unimp
 45e:	0679                	addi	a2,a2,30
 460:	0000                	unimp
 462:	0002140f          	0x2140f
 466:	0061                	c.nop	24
 468:	0000                	unimp
 46a:	d600                	sw	s0,40(a2)
 46c:	0001                	nop
 46e:	1000                	addi	s0,sp,32
 470:	5a01                	li	s4,-32
 472:	8502                	jr	a0
 474:	1000                	addi	s0,sp,32
 476:	5b01                	li	s6,-32
 478:	8802                	jr	a6
 47a:	1000                	addi	s0,sp,32
 47c:	5c01                	li	s8,-32
 47e:	8302                	jr	t1
 480:	1000                	addi	s0,sp,32
 482:	5d01                	li	s10,-32
 484:	8602                	jr	a2
 486:	0000                	unimp
 488:	1100                	addi	s0,sp,160
 48a:	b208                	fsd	fa0,32(a2)
 48c:	0000                	unimp
 48e:	1200                	addi	s0,sp,288
 490:	000000d7          	vadd.vv	v1,v0,v0,v0.t
 494:	000000d7          	vadd.vv	v1,v0,v0,v0.t
 498:	3004                	fld	fs1,32(s0)
 49a:	0006                	c.slli	zero,0x1

Disassembly of section .debug_abbrev:

0000000000000000 <.debug_abbrev>:
   0:	1101                	addi	sp,sp,-32
   2:	1000                	addi	s0,sp,32
   4:	1106                	slli	sp,sp,0x21
   6:	1201                	addi	tp,tp,-32
   8:	0301                	addi	t1,t1,0
   a:	1b0e                	slli	s6,s6,0x23
   c:	250e                	fld	fa0,192(sp)
   e:	130e                	slli	t1,t1,0x23
  10:	0005                	c.nop	1
  12:	0000                	unimp
  14:	1101                	addi	sp,sp,-32
  16:	2501                	sext.w	a0,a0
  18:	130e                	slli	t1,t1,0x23
  1a:	1b0e030b          	0x1b0e030b
  1e:	110e                	slli	sp,sp,0x23
  20:	1201                	addi	tp,tp,-32
  22:	00171007          	flh	ft0,1(a4) # 1d001 <_start-0x60fe2fff>
  26:	0200                	addi	s0,sp,256
  28:	0024                	addi	s1,sp,8
  2a:	0b3e0b0b          	0xb3e0b0b
  2e:	00000e03          	lb	t3,0(zero) # 0 <_start-0x61000000>
  32:	03001603          	lh	a2,48(zero) # 30 <_start-0x60ffffd0>
  36:	3a0e                	fld	fs4,224(sp)
  38:	390b3b0b          	0x390b3b0b
  3c:	0013490b          	0x13490b
  40:	0400                	addi	s0,sp,512
  42:	0024                	addi	s1,sp,8
  44:	0b3e0b0b          	0xb3e0b0b
  48:	00000803          	lb	a6,0(zero) # 0 <_start-0x61000000>
  4c:	3505                	addiw	a0,a0,-31
  4e:	4900                	lw	s0,16(a0)
  50:	06000013          	li	zero,96
  54:	0026                	c.slli	zero,0x9
  56:	1349                	addi	t1,t1,-14
  58:	0000                	unimp
  5a:	3f012e07          	flw	ft8,1008(sp)
  5e:	0319                	addi	t1,t1,6
  60:	3a0e                	fld	fs4,224(sp)
  62:	390b3b0b          	0x390b3b0b
  66:	1119270b          	0x1119270b
  6a:	1201                	addi	tp,tp,-32
  6c:	97184007          	flq	ft0,-1679(a6)
  70:	1942                	slli	s2,s2,0x30
  72:	1301                	addi	t1,t1,-32
  74:	0000                	unimp
  76:	0508                	addi	a0,sp,640
  78:	0300                	addi	s0,sp,384
  7a:	3a0e                	fld	fs4,224(sp)
  7c:	390b3b0b          	0x390b3b0b
  80:	0213490b          	0x213490b
  84:	09000017          	auipc	zero,0x9000
  88:	0034                	addi	a3,sp,8
  8a:	0b3a0803          	lb	a6,179(s4) # 60b3 <_start-0x60ff9f4d>
  8e:	0b390b3b          	0xb390b3b
  92:	1349                	addi	t1,t1,-14
  94:	1702                	slli	a4,a4,0x20
  96:	0000                	unimp
  98:	890a                	mv	s2,sp
  9a:	0182                	c.slli64	gp
  9c:	1101                	addi	sp,sp,-32
  9e:	3101                	addiw	sp,sp,-32
  a0:	00130113          	addi	sp,t1,1 # 3001 <_start-0x60ffcfff>
  a4:	0b00                	addi	s0,sp,400
  a6:	828a                	mv	t0,sp
  a8:	0001                	nop
  aa:	1802                	slli	a6,a6,0x20
  ac:	4291                	li	t0,4
  ae:	0018                	0x18
  b0:	0c00                	addi	s0,sp,528
  b2:	8289                	srli	a3,a3,0x2
  b4:	0101                	addi	sp,sp,0
  b6:	0111                	addi	sp,sp,4
  b8:	4295                	li	t0,5
  ba:	3119                	addiw	sp,sp,-26
  bc:	0d000013          	li	zero,208
  c0:	0b0b000f          	0xb0b000f
  c4:	1349                	addi	t1,t1,-14
  c6:	0000                	unimp
  c8:	050e                	slli	a0,a0,0x3
  ca:	0300                	addi	s0,sp,384
  cc:	3a0e                	fld	fs4,224(sp)
  ce:	390b3b0b          	0x390b3b0b
  d2:	0213490b          	0x213490b
  d6:	0018                	0x18
  d8:	0f00                	addi	s0,sp,912
  da:	0034                	addi	a3,sp,8
  dc:	0b3a0e03          	lb	t3,179(s4)
  e0:	0b390b3b          	0xb390b3b
  e4:	1349                	addi	t1,t1,-14
  e6:	1702                	slli	a4,a4,0x20
  e8:	0000                	unimp
  ea:	2e10                	fld	fa2,24(a2)
  ec:	3f01                	addiw	t5,t5,-32
  ee:	0319                	addi	t1,t1,6
  f0:	3a0e                	fld	fs4,224(sp)
  f2:	390b3b0b          	0x390b3b0b
  f6:	4919270b          	0x4919270b
  fa:	12011113          	0x12011113
  fe:	97184007          	flq	ft0,-1679(a6)
 102:	1942                	slli	s2,s2,0x30
 104:	1301                	addi	t1,t1,-32
 106:	0000                	unimp
 108:	3411                	addiw	s0,s0,-28
 10a:	0300                	addi	s0,sp,384
 10c:	3a0e                	fld	fs4,224(sp)
 10e:	390b3b0b          	0x390b3b0b
 112:	0213490b          	0x213490b
 116:	0018                	0x18
 118:	1200                	addi	s0,sp,288
 11a:	012e                	slli	sp,sp,0xb
 11c:	0e03193f 0b3b0b3a 	0xb3b0b3a0e03193f
 124:	0b39                	addi	s6,s6,14
 126:	13491927          	fsh	fs4,306(s2) # 3132 <_start-0x60ffcece>
 12a:	0111                	addi	sp,sp,4
 12c:	0712                	slli	a4,a4,0x4
 12e:	1840                	addi	s0,sp,52
 130:	00194297          	auipc	t0,0x194
 134:	0000                	unimp
 136:	1101                	addi	sp,sp,-32
 138:	2501                	sext.w	a0,a0
 13a:	130e                	slli	t1,t1,0x23
 13c:	1b0e030b          	0x1b0e030b
 140:	110e                	slli	sp,sp,0x23
 142:	1201                	addi	tp,tp,-32
 144:	00171007          	flh	ft0,1(a4)
 148:	0200                	addi	s0,sp,256
 14a:	0024                	addi	s1,sp,8
 14c:	0b3e0b0b          	0xb3e0b0b
 150:	00000e03          	lb	t3,0(zero) # 0 <_start-0x61000000>
 154:	03001603          	lh	a2,48(zero) # 30 <_start-0x60ffffd0>
 158:	3a0e                	fld	fs4,224(sp)
 15a:	390b3b0b          	0x390b3b0b
 15e:	0013490b          	0x13490b
 162:	0400                	addi	s0,sp,512
 164:	0024                	addi	s1,sp,8
 166:	0b3e0b0b          	0xb3e0b0b
 16a:	00000803          	lb	a6,0(zero) # 0 <_start-0x61000000>
 16e:	2605                	addiw	a2,a2,1
 170:	4900                	lw	s0,16(a0)
 172:	06000013          	li	zero,96
 176:	0035                	c.nop	13
 178:	1349                	addi	t1,t1,-14
 17a:	0000                	unimp
 17c:	49010107          	vlsseg3e8.v	v2,(sp),a6,v0.t
 180:	00130113          	addi	sp,t1,1
 184:	0800                	addi	s0,sp,16
 186:	0021                	c.nop	8
 188:	1349                	addi	t1,t1,-14
 18a:	00000b2f          	0xb2f
 18e:	3409                	addiw	s0,s0,-30
 190:	0300                	addi	s0,sp,384
 192:	3a0e                	fld	fs4,224(sp)
 194:	390b3b0b          	0x390b3b0b
 198:	0213490b          	0x213490b
 19c:	0018                	0x18
 19e:	0a00                	addi	s0,sp,272
 1a0:	0021                	c.nop	8
 1a2:	1349                	addi	t1,t1,-14
 1a4:	0000052f          	0x52f
 1a8:	3f012e0b          	0x3f012e0b
 1ac:	0319                	addi	t1,t1,6
 1ae:	3a0e                	fld	fs4,224(sp)
 1b0:	390b3b0b          	0x390b3b0b
 1b4:	4919270b          	0x4919270b
 1b8:	12011113          	0x12011113
 1bc:	97184007          	flq	ft0,-1679(a6)
 1c0:	1942                	slli	s2,s2,0x30
 1c2:	1301                	addi	t1,t1,-32
 1c4:	0000                	unimp
 1c6:	050c                	addi	a1,sp,640
 1c8:	0300                	addi	s0,sp,384
 1ca:	3a0e                	fld	fs4,224(sp)
 1cc:	390b3b0b          	0x390b3b0b
 1d0:	0213490b          	0x213490b
 1d4:	0d000017          	auipc	zero,0xd000
 1d8:	0034                	addi	a3,sp,8
 1da:	0b3a0803          	lb	a6,179(s4)
 1de:	0b390b3b          	0xb390b3b
 1e2:	1349                	addi	t1,t1,-14
 1e4:	1702                	slli	a4,a4,0x20
 1e6:	0000                	unimp
 1e8:	340e                	fld	fs0,224(sp)
 1ea:	0300                	addi	s0,sp,384
 1ec:	3a0e                	fld	fs4,224(sp)
 1ee:	390b3b0b          	0x390b3b0b
 1f2:	0213490b          	0x213490b
 1f6:	0f000017          	auipc	zero,0xf000
 1fa:	8289                	srli	a3,a3,0x2
 1fc:	0101                	addi	sp,sp,0
 1fe:	0111                	addi	sp,sp,4
 200:	1331                	addi	t1,t1,-20
 202:	0000                	unimp
 204:	8a10                	0x8a10
 206:	0182                	c.slli64	gp
 208:	0200                	addi	s0,sp,256
 20a:	9118                	0x9118
 20c:	1842                	slli	a6,a6,0x30
 20e:	0000                	unimp
 210:	0f11                	addi	t5,t5,4
 212:	0b00                	addi	s0,sp,400
 214:	0013490b          	0x13490b
 218:	1200                	addi	s0,sp,288
 21a:	002e                	c.slli	zero,0xb
 21c:	193c193f 0e030e6e 	0xe030e6e193c193f
 224:	0b3a                	slli	s6,s6,0xe
 226:	0b390b3b          	0xb390b3b
 22a:	0000                	unimp
	...

Disassembly of section .debug_aranges:

0000000000000000 <.debug_aranges>:
   0:	002c                	addi	a1,sp,8
   2:	0000                	unimp
   4:	0002                	c.slli64	zero
   6:	0000                	unimp
   8:	0000                	unimp
   a:	0008                	0x8
   c:	0000                	unimp
   e:	0000                	unimp
  10:	0000                	unimp
  12:	6100                	ld	s0,0(a0)
  14:	0000                	unimp
  16:	0000                	unimp
  18:	0010                	0x10
	...
  2e:	0000                	unimp
  30:	002c                	addi	a1,sp,8
  32:	0000                	unimp
  34:	0002                	c.slli64	zero
  36:	002e                	c.slli	zero,0xb
  38:	0000                	unimp
  3a:	0008                	0x8
  3c:	0000                	unimp
  3e:	0000                	unimp
  40:	0010                	0x10
  42:	6100                	ld	s0,0(a0)
  44:	0000                	unimp
  46:	0000                	unimp
  48:	0140                	addi	s0,sp,132
	...
  5e:	0000                	unimp
  60:	002c                	addi	a1,sp,8
  62:	0000                	unimp
  64:	0002                	c.slli64	zero
  66:	02b9                	addi	t0,t0,14
  68:	0000                	unimp
  6a:	0008                	0x8
  6c:	0000                	unimp
  6e:	0000                	unimp
  70:	0150                	addi	a2,sp,132
  72:	6100                	ld	s0,0(a0)
  74:	0000                	unimp
  76:	0000                	unimp
  78:	0114                	addi	a3,sp,128
	...

Disassembly of section .debug_str:

0000000000000000 <.debug_str>:
   0:	3a44                	fld	fs1,176(a2)
   2:	726f772f          	0x726f772f
   6:	30322f6b          	0x30322f6b
   a:	3232                	fld	ft4,296(sp)
   c:	396c612f          	0x396c612f
  10:	3030                	fld	fa2,96(s0)
  12:	2f30                	fld	fa2,88(a4)
  14:	642f7773          	csrrci	a4,0x642,30
  18:	6265                	lui	tp,0x19
  1a:	6775                	lui	a4,0x1d
  1c:	2f726567          	0x2f726567
  20:	6c61                	lui	s8,0x18
  22:	6f5f 6570 6f6e      	0x6f6e65706f5f
  28:	632f6463          	bltu	t5,s2,650 <_start-0x60fff9b0>
  2c:	72746e6f          	jal	t3,46f52 <_start-0x60fb90ae>
  30:	6269                	lui	tp,0x1a
  32:	616f6c2f          	0x616f6c2f
  36:	6564                	ld	s1,200(a0)
  38:	7372                	ld	t1,312(sp)
  3a:	616c662f          	0x616c662f
  3e:	652f6873          	csrrsi	a6,0x652,30
  42:	6d6d                	lui	s10,0x1b
  44:	77642f63          	0x77642f63
  48:	68736d63          	bltu	t1,t2,6e2 <_start-0x60fff91e>
  4c:	72732f63          	0x72732f63
  50:	6f622f63          	0x6f622f63
  54:	722f746f          	jal	s0,f7776 <_start-0x60f0888a>
  58:	7369                	lui	t1,0xffffa
  5a:	775c7663          	bgeu	s8,s5,7c6 <_start-0x60fff83a>
  5e:	6172                	ld	sp,280(sp)
  60:	7070                	ld	a2,224(s0)
  62:	7265                	lui	tp,0xffff9
  64:	532e                	lw	t1,232(sp)
  66:	4400                	lw	s0,8(s0)
  68:	5c3a                	lw	s8,172(sp)
  6a:	6b726f77          	0x6b726f77
  6e:	325c                	fld	fa5,160(a2)
  70:	3230                	fld	fa2,96(a2)
  72:	5c32                	lw	s8,44(sp)
  74:	6c61                	lui	s8,0x18
  76:	3039                	0x3039
  78:	3030                	fld	fa2,96(s0)
  7a:	735c                	ld	a5,160(a4)
  7c:	65645c77          	0x65645c77
  80:	7562                	ld	a0,56(sp)
  82:	72656767          	0x72656767
  86:	615c                	ld	a5,128(a0)
  88:	5f6c                	lw	a1,124(a4)
  8a:	6e65706f          	j	57770 <_start-0x60fa8890>
  8e:	5c64636f          	jal	t1,46654 <_start-0x60fb99ac>
  92:	746e6f63          	bltu	t3,t1,7f0 <_start-0x60fff810>
  96:	6972                	ld	s2,280(sp)
  98:	5c62                	lw	s8,56(sp)
  9a:	6f6c                	ld	a1,216(a4)
  9c:	6461                	lui	s0,0x18
  9e:	7265                	lui	tp,0xffff9
  a0:	6c665c73          	csrrwi	s8,0x6c6,12
  a4:	7361                	lui	t1,0xffff8
  a6:	5c68                	lw	a0,124(s0)
  a8:	6d65                	lui	s10,0x19
  aa:	636d                	lui	t1,0x1b
  ac:	645c                	ld	a5,136(s0)
  ae:	736d6377          	0x736d6377
  b2:	6368                	ld	a0,192(a4)
  b4:	4700                	lw	s0,8(a4)
  b6:	554e                	lw	a0,240(sp)
  b8:	4120                	lw	s0,64(a0)
  ba:	2e322053          	0x2e322053
  be:	312e3633          	0x312e3633
  c2:	6300                	ld	s0,0(a4)
  c4:	7274                	ld	a3,224(a2)
  c6:	5f6c                	lw	a1,124(a4)
  c8:	6162                	ld	sp,24(sp)
  ca:	5f006573          	csrrsi	a0,0x5f0,0
  ce:	755f 6e69 3874      	0x38746e69755f
  d4:	745f 6500 6d6d      	0x6d6d6500745f
  da:	65725f63          	bge	tp,s7,738 <_start-0x60fff8c8>
  de:	6461                	lui	s0,0x18
  e0:	625f 6f6c 6b63      	0x6b636f6c625f
  e6:	6500                	ld	s0,8(a0)
  e8:	6d6d                	lui	s10,0x1b
  ea:	61775f63          	bge	a4,s7,708 <_start-0x60fff8f8>
  ee:	7469                	lui	s0,0xffffa
  f0:	665f 6669 006f      	0x6f6669665f
  f6:	6e75                	lui	t3,0x1d
  f8:	6e676973          	csrrsi	s2,0x6e6,14
  fc:	6465                	lui	s0,0x19
  fe:	6320                	ld	s0,64(a4)
 100:	6168                	ld	a0,192(a0)
 102:	0072                	c.slli	zero,0x1c
 104:	6f6c                	ld	a1,216(a4)
 106:	676e                	ld	a4,216(sp)
 108:	7520                	ld	s0,104(a0)
 10a:	736e                	ld	t1,248(sp)
 10c:	6769                	lui	a4,0x1a
 10e:	656e                	ld	a0,216(sp)
 110:	2064                	fld	fs1,192(s0)
 112:	6e69                	lui	t3,0x1a
 114:	0074                	addi	a3,sp,12
 116:	726f6873          	csrrsi	a6,0x726,30
 11a:	2074                	fld	fa3,192(s0)
 11c:	6e75                	lui	t3,0x1d
 11e:	6e676973          	csrrsi	s2,0x6e6,14
 122:	6465                	lui	s0,0x19
 124:	6920                	ld	s0,80(a0)
 126:	746e                	ld	s0,248(sp)
 128:	6400                	ld	s0,8(s0)
 12a:	5f656e6f          	jal	t3,56720 <_start-0x60fa98e0>
 12e:	6c66                	ld	s8,88(sp)
 130:	6761                	lui	a4,0x18
 132:	6500                	ld	s0,8(a0)
 134:	6d6d                	lui	s10,0x1b
 136:	6f705f63          	blez	s7,834 <_start-0x60fff7cc>
 13a:	6c6c                	ld	a1,216(s0)
 13c:	695f 746e 7700      	0x7700746e695f
 142:	5f6b726f          	jal	tp,b7738 <_start-0x60f488c8>
 146:	7261                	lui	tp,0xffff8
 148:	6165                	addi	sp,sp,112
 14a:	735f 6174 7472      	0x74726174735f
 150:	5f00                	lw	s0,56(a4)
 152:	755f 6e69 3374      	0x33746e69755f
 158:	5f32                	lw	t5,44(sp)
 15a:	0074                	addi	a3,sp,12
 15c:	6d65                	lui	s10,0x19
 15e:	636d                	lui	t1,0x1b
 160:	775f 6972 6574      	0x65746972775f
 166:	625f 6f6c 6b63      	0x6b636f6c625f
 16c:	6900                	ld	s0,16(a0)
 16e:	746e                	ld	s0,248(sp)
 170:	765f 6c61 4400      	0x44006c61765f
 176:	2f3a                	fld	ft10,392(sp)
 178:	6b726f77          	0x6b726f77
 17c:	3230322f          	0x3230322f
 180:	2f32                	fld	ft10,264(sp)
 182:	6c61                	lui	s8,0x18
 184:	3039                	0x3039
 186:	3030                	fld	fa2,96(s0)
 188:	2f77732f          	0x2f77732f
 18c:	6564                	ld	s1,200(a0)
 18e:	7562                	ld	a0,56(sp)
 190:	72656767          	0x72656767
 194:	5f6c612f          	0x5f6c612f
 198:	6e65706f          	j	5787e <_start-0x60fa8782>
 19c:	2f64636f          	jal	t1,46492 <_start-0x60fb9b6e>
 1a0:	746e6f63          	bltu	t3,t1,8fe <_start-0x60fff702>
 1a4:	6972                	ld	s2,280(sp)
 1a6:	2f62                	fld	ft10,24(sp)
 1a8:	6f6c                	ld	a1,216(a4)
 1aa:	6461                	lui	s0,0x18
 1ac:	7265                	lui	tp,0xffff9
 1ae:	6c662f73          	csrrs	t5,0x6c6,a2
 1b2:	7361                	lui	t1,0xffff8
 1b4:	2f68                	fld	fa0,216(a4)
 1b6:	6d65                	lui	s10,0x19
 1b8:	636d                	lui	t1,0x1b
 1ba:	6377642f          	0x6377642f
 1be:	736d                	lui	t1,0xffffb
 1c0:	6368                	ld	a0,192(a4)
 1c2:	6372732f          	0x6372732f
 1c6:	6377642f          	0x6377642f
 1ca:	736d                	lui	t1,0xffffb
 1cc:	6368                	ld	a0,192(a4)
 1ce:	632e                	ld	t1,200(sp)
 1d0:	7700                	ld	s0,40(a4)
 1d2:	5f64726f          	jal	tp,477c8 <_start-0x60fb8838>
 1d6:	00746e63          	bltu	s0,t2,1f2 <_start-0x60fffe0e>
 1da:	20554e47          	fmsub.s	ft8,fa0,ft5,ft4,rmm
 1de:	20373143          	fmadd.s	ft2,fa4,ft3,ft4,rup
 1e2:	3031                	0x3031
 1e4:	322e                	fld	ft4,232(sp)
 1e6:	302e                	fld	ft0,232(sp)
 1e8:	2d20                	fld	fs0,88(a0)
 1ea:	616d                	addi	sp,sp,240
 1ec:	6372                	ld	t1,280(sp)
 1ee:	3d68                	fld	fa0,248(a0)
 1f0:	7672                	ld	a2,312(sp)
 1f2:	3436                	fld	fs0,360(sp)
 1f4:	2069                	0x2069
 1f6:	6d2d                	lui	s10,0xb
 1f8:	6261                	lui	tp,0x18
 1fa:	3d69                	addiw	s10,s10,-6
 1fc:	706c                	ld	a1,224(s0)
 1fe:	3436                	fld	fs0,360(sp)
 200:	2d20                	fld	fs0,88(a0)
 202:	746d                	lui	s0,0xffffb
 204:	6e75                	lui	t3,0x1d
 206:	3d65                	addiw	s10,s10,-7
 208:	6f72                	ld	t5,280(sp)
 20a:	74656b63          	bltu	a0,t1,960 <_start-0x60fff6a0>
 20e:	2d20                	fld	fs0,88(a0)
 210:	616d                	addi	sp,sp,240
 212:	6372                	ld	t1,280(sp)
 214:	3d68                	fld	fa0,248(a0)
 216:	7672                	ld	a2,312(sp)
 218:	3436                	fld	fs0,360(sp)
 21a:	2069                	0x2069
 21c:	672d                	lui	a4,0xb
 21e:	2d20                	fld	fs0,88(a0)
 220:	2d20734f          	fnmadd.h	ft6,ft0,fs2,ft5
 224:	6e66                	ld	t3,88(sp)
 226:	75622d6f          	jal	s10,2297c <_start-0x60fdd684>
 22a:	6c69                	lui	s8,0x1a
 22c:	6974                	ld	a3,208(a0)
 22e:	206e                	fld	ft0,216(sp)
 230:	662d                	lui	a2,0xb
 232:	4950                	lw	a2,20(a0)
 234:	68730043          	fmadd.s	ft0,ft6,ft7,fa3,rne
 238:	2074726f          	jal	tp,47c3e <_start-0x60fb83c2>
 23c:	6e69                	lui	t3,0x1a
 23e:	0074                	addi	a3,sp,12
 240:	7562                	ld	a0,56(sp)
 242:	6666                	ld	a2,88(sp)
 244:	7265                	lui	tp,0xffff9
 246:	6c00                	ld	s0,24(s0)
 248:	20676e6f          	jal	t3,7644e <_start-0x60f89bb2>
 24c:	6e69                	lui	t3,0x1a
 24e:	0074                	addi	a3,sp,12
 250:	61656c63          	bltu	a0,s6,868 <_start-0x60fff798>
 254:	5f72                	lw	t5,60(sp)
 256:	6572                	ld	a0,280(sp)
 258:	6c660067          	jr	1734(a2) # b6c6 <_start-0x60ff493a>
 25c:	6761                	lui	a4,0x18
 25e:	6f5f 6666 6573      	0x657366666f5f
 264:	0074                	addi	a3,sp,12
 266:	5f5f 6e69 3374      	0x33746e695f5f
 26c:	5f32                	lw	t5,44(sp)
 26e:	0074                	addi	a3,sp,12
 270:	6c62                	ld	s8,24(sp)
 272:	5f6b636f          	jal	t1,b6868 <_start-0x60f49798>
 276:	6461                	lui	s0,0x18
 278:	7264                	ld	s1,224(a2)
 27a:	6300                	ld	s0,0(a4)
 27c:	7275                	lui	tp,0xffffd
 27e:	635f 756f 746e      	0x746e756f635f
 284:	6200                	ld	s0,0(a2)
 286:	6f6c                	ld	a1,216(a4)
 288:	735f6b63          	bltu	t5,s5,9be <_start-0x60fff642>
 28c:	7a69                	lui	s4,0xffffa
 28e:	0065                	c.nop	25
 290:	3a44                	fld	fs1,176(a2)
 292:	726f772f          	0x726f772f
 296:	30322f6b          	0x30322f6b
 29a:	3232                	fld	ft4,296(sp)
 29c:	396c612f          	0x396c612f
 2a0:	3030                	fld	fa2,96(s0)
 2a2:	2f30                	fld	fa2,88(a4)
 2a4:	642f7773          	csrrci	a4,0x642,30
 2a8:	6265                	lui	tp,0x19
 2aa:	6775                	lui	a4,0x1d
 2ac:	2f726567          	0x2f726567
 2b0:	6c61                	lui	s8,0x18
 2b2:	6f5f 6570 6f6e      	0x6f6e65706f5f
 2b8:	632f6463          	bltu	t5,s2,8e0 <_start-0x60fff720>
 2bc:	72746e6f          	jal	t3,471e2 <_start-0x60fb8e1e>
 2c0:	6269                	lui	tp,0x1a
 2c2:	616f6c2f          	0x616f6c2f
 2c6:	6564                	ld	s1,200(a0)
 2c8:	7372                	ld	t1,312(sp)
 2ca:	616c662f          	0x616c662f
 2ce:	652f6873          	csrrsi	a6,0x652,30
 2d2:	6d6d                	lui	s10,0x1b
 2d4:	77642f63          	0x77642f63
 2d8:	68736d63          	bltu	t1,t2,972 <_start-0x60fff68e>
 2dc:	72732f63          	0x72732f63
 2e0:	77642f63          	0x77642f63
 2e4:	68736d63          	bltu	t1,t2,97e <_start-0x60fff682>
 2e8:	72635f63          	bge	t1,t1,a26 <_start-0x60fff5da>
 2ec:	00632e63          	0x632e63
 2f0:	5f637263          	bgeu	t1,s6,8d4 <_start-0x60fff72c>
 2f4:	6e756f63          	bltu	a0,t2,9f2 <_start-0x60fff60e>
 2f8:	0074                	addi	a3,sp,12
 2fa:	33637263          	bgeu	t1,s6,61e <_start-0x60fff9e2>
 2fe:	5f32                	lw	t5,44(sp)
 300:	6174                	ld	a3,192(a0)
 302:	6c62                	ld	s8,24(sp)
 304:	0065                	c.nop	25
 306:	5f74756f          	jal	a0,480fc <_start-0x60fb7f04>
 30a:	7562                	ld	a0,56(sp)
 30c:	0066                	c.slli	zero,0x19
 30e:	6d65                	lui	s10,0x19
 310:	636d                	lui	t1,0x1b
 312:	645f 6377 736d      	0x736d6377645f
 318:	6368                	ld	a0,192(a4)
	...

Disassembly of section .debug_loc:

0000000000000000 <.debug_loc>:
   0:	00c4                	addi	s1,sp,68
   2:	0000                	unimp
   4:	0000                	unimp
   6:	0000                	unimp
   8:	000000fb          	dkhmx8	ra,zero,zero
   c:	0000                	unimp
   e:	0000                	unimp
  10:	0001                	nop
  12:	fb5a                	sd	s6,432(sp)
  14:	0000                	unimp
  16:	0000                	unimp
  18:	0000                	unimp
  1a:	1000                	addi	s0,sp,32
  1c:	0001                	nop
  1e:	0000                	unimp
  20:	0000                	unimp
  22:	0100                	addi	s0,sp,128
  24:	5800                	lw	s0,48(s0)
  26:	0110                	addi	a2,sp,128
  28:	0000                	unimp
  2a:	0000                	unimp
  2c:	0000                	unimp
  2e:	00000127          	vse8.v	v2,(zero),v0.t
  32:	0000                	unimp
  34:	0000                	unimp
  36:	0001                	nop
  38:	275a                	fld	fa4,400(sp)
  3a:	0001                	nop
  3c:	0000                	unimp
  3e:	0000                	unimp
  40:	2800                	fld	fs0,16(s0)
  42:	0001                	nop
  44:	0000                	unimp
  46:	0000                	unimp
  48:	0400                	addi	s0,sp,512
  4a:	f300                	sd	s0,32(a4)
  4c:	5a01                	li	s4,-32
  4e:	289f 0001 0000      	0x1289f
  54:	0000                	unimp
  56:	4000                	lw	s0,0(s0)
  58:	0001                	nop
  5a:	0000                	unimp
  5c:	0000                	unimp
  5e:	0100                	addi	s0,sp,128
  60:	5800                	lw	s0,48(s0)
	...
  72:	00c4                	addi	s1,sp,68
  74:	0000                	unimp
  76:	0000                	unimp
  78:	0000                	unimp
  7a:	00f0                	addi	a2,sp,76
  7c:	0000                	unimp
  7e:	0000                	unimp
  80:	0000                	unimp
  82:	0001                	nop
  84:	0000f05b          	0xf05b
  88:	0000                	unimp
  8a:	0000                	unimp
  8c:	1c00                	addi	s0,sp,560
  8e:	0001                	nop
  90:	0000                	unimp
  92:	0000                	unimp
  94:	0100                	addi	s0,sp,128
  96:	6200                	ld	s0,0(a2)
  98:	011c                	addi	a5,sp,128
  9a:	0000                	unimp
  9c:	0000                	unimp
  9e:	0000                	unimp
  a0:	0128                	addi	a0,sp,136
  a2:	0000                	unimp
  a4:	0000                	unimp
  a6:	0000                	unimp
  a8:	0004                	0x4
  aa:	9f5b01f3          	0x9f5b01f3
  ae:	0128                	addi	a0,sp,136
  b0:	0000                	unimp
  b2:	0000                	unimp
  b4:	0000                	unimp
  b6:	0140                	addi	s0,sp,132
  b8:	0000                	unimp
  ba:	0000                	unimp
  bc:	0000                	unimp
  be:	0001                	nop
  c0:	0062                	c.slli	zero,0x18
	...
  ce:	0000                	unimp
  d0:	c400                	sw	s0,8(s0)
  d2:	0000                	unimp
  d4:	0000                	unimp
  d6:	0000                	unimp
  d8:	fb00                	sd	s0,48(a4)
  da:	0000                	unimp
  dc:	0000                	unimp
  de:	0000                	unimp
  e0:	0100                	addi	s0,sp,128
  e2:	5c00                	lw	s0,56(s0)
  e4:	000000fb          	dkhmx8	ra,zero,zero
  e8:	0000                	unimp
  ea:	0000                	unimp
  ec:	0140                	addi	s0,sp,132
  ee:	0000                	unimp
  f0:	0000                	unimp
  f2:	0000                	unimp
  f4:	0004                	0x4
  f6:	9f5c01f3          	0x9f5c01f3
	...
 10a:	00c4                	addi	s1,sp,68
 10c:	0000                	unimp
 10e:	0000                	unimp
 110:	0000                	unimp
 112:	000000fb          	dkhmx8	ra,zero,zero
 116:	0000                	unimp
 118:	0000                	unimp
 11a:	0001                	nop
 11c:	fb5d                	bnez	a4,d2 <_start-0x60ffff2e>
 11e:	0000                	unimp
 120:	0000                	unimp
 122:	0000                	unimp
 124:	4000                	lw	s0,0(s0)
 126:	0001                	nop
 128:	0000                	unimp
 12a:	0000                	unimp
 12c:	0400                	addi	s0,sp,512
 12e:	f300                	sd	s0,32(a4)
 130:	5d01                	li	s10,-32
 132:	009f 0000 0000      	0x9f
	...
 140:	0000                	unimp
 142:	fc00                	sd	s0,56(s0)
	...
 14c:	0001                	nop
 14e:	0000                	unimp
 150:	0000                	unimp
 152:	0200                	addi	s0,sp,256
 154:	3000                	fld	fs0,32(s0)
 156:	009f 0001 0000      	0x1009f
 15c:	0000                	unimp
 15e:	2700                	fld	fs0,8(a4)
 160:	0001                	nop
 162:	0000                	unimp
 164:	0000                	unimp
 166:	0100                	addi	s0,sp,128
 168:	5f00                	lw	s0,56(a4)
 16a:	0128                	addi	a0,sp,136
 16c:	0000                	unimp
 16e:	0000                	unimp
 170:	0000                	unimp
 172:	0138                	addi	a4,sp,136
 174:	0000                	unimp
 176:	0000                	unimp
 178:	0000                	unimp
 17a:	0001                	nop
 17c:	385f 0001 0000      	0x1385f
 182:	0000                	unimp
 184:	3c00                	fld	fs0,56(s0)
 186:	0001                	nop
 188:	0000                	unimp
 18a:	0000                	unimp
 18c:	0300                	addi	s0,sp,384
 18e:	7f00                	ld	s0,56(a4)
 190:	9f01                	subw	a4,a4,s0
	...
 1a2:	005c                	addi	a5,sp,4
 1a4:	0000                	unimp
 1a6:	0000                	unimp
 1a8:	0000                	unimp
 1aa:	0088                	addi	a0,sp,64
 1ac:	0000                	unimp
 1ae:	0000                	unimp
 1b0:	0000                	unimp
 1b2:	0001                	nop
 1b4:	885c                	0x885c
 1b6:	0000                	unimp
 1b8:	0000                	unimp
 1ba:	0000                	unimp
 1bc:	9800                	0x9800
 1be:	0000                	unimp
 1c0:	0000                	unimp
 1c2:	0000                	unimp
 1c4:	0400                	addi	s0,sp,512
 1c6:	7f00                	ld	s0,56(a4)
 1c8:	7c80                	ld	s0,56(s1)
 1ca:	989f 0000 0000      	0x989f
 1d0:	0000                	unimp
 1d2:	c400                	sw	s0,8(s0)
 1d4:	0000                	unimp
 1d6:	0000                	unimp
 1d8:	0000                	unimp
 1da:	0400                	addi	s0,sp,512
 1dc:	f300                	sd	s0,32(a4)
 1de:	5c01                	li	s8,-32
 1e0:	009f 0000 0000      	0x9f
	...
 1ee:	0000                	unimp
 1f0:	8800                	0x8800
 1f2:	0000                	unimp
 1f4:	0000                	unimp
 1f6:	0000                	unimp
 1f8:	9000                	0x9000
 1fa:	0000                	unimp
 1fc:	0000                	unimp
 1fe:	0000                	unimp
 200:	0b00                	addi	s0,sp,400
 202:	7c00                	ld	s0,56(s0)
 204:	7f00                	ld	s0,56(a4)
 206:	1c00                	addi	s0,sp,560
 208:	32048023          	sb	zero,800(s1) # 2e3a28fa <_start-0x32c5d706>
 20c:	9f25                	addw	a4,a4,s1
 20e:	0090                	addi	a2,sp,64
 210:	0000                	unimp
 212:	0000                	unimp
 214:	0000                	unimp
 216:	0094                	addi	a3,sp,64
 218:	0000                	unimp
 21a:	0000                	unimp
 21c:	0000                	unimp
 21e:	007c000b          	0x7c000b
 222:	231c007f          	ursub16	zero,s8,a7
 226:	03fc                	addi	a5,sp,460
 228:	2532                	fld	fa0,264(sp)
 22a:	009f 0000 0000      	0x9f
	...
 238:	0000                	unimp
 23a:	7400                	ld	s0,40(s0)
 23c:	0000                	unimp
 23e:	0000                	unimp
 240:	0000                	unimp
 242:	8800                	0x8800
 244:	0000                	unimp
 246:	0000                	unimp
 248:	0000                	unimp
 24a:	0100                	addi	s0,sp,128
 24c:	5e00                	lw	s0,56(a2)
 24e:	00a0                	addi	s0,sp,72
 250:	0000                	unimp
 252:	0000                	unimp
 254:	0000                	unimp
 256:	00b0                	addi	a2,sp,72
 258:	0000                	unimp
 25a:	0000                	unimp
 25c:	0000                	unimp
 25e:	0001                	nop
 260:	b05f 0000 0000      	0xb05f
 266:	0000                	unimp
 268:	c400                	sw	s0,8(s0)
 26a:	0000                	unimp
 26c:	0000                	unimp
 26e:	0000                	unimp
 270:	0100                	addi	s0,sp,128
 272:	5e00                	lw	s0,56(a2)
	...
 284:	0074                	addi	a3,sp,12
 286:	0000                	unimp
 288:	0000                	unimp
 28a:	0000                	unimp
 28c:	0088                	addi	a0,sp,64
 28e:	0000                	unimp
 290:	0000                	unimp
 292:	0000                	unimp
 294:	007e0007          	0x7e0007
 298:	2534                	fld	fa3,72(a0)
 29a:	1a31                	addi	s4,s4,-20
 29c:	a09f 0000 0000      	0xa09f
 2a2:	0000                	unimp
 2a4:	b000                	fsd	fs0,32(s0)
 2a6:	0000                	unimp
 2a8:	0000                	unimp
 2aa:	0000                	unimp
 2ac:	0700                	addi	s0,sp,896
 2ae:	7f00                	ld	s0,56(a4)
 2b0:	3100                	fld	fs0,32(a0)
 2b2:	3125                	addiw	sp,sp,-23
 2b4:	9f1a                	add	t5,t5,t1
 2b6:	00b0                	addi	a2,sp,72
 2b8:	0000                	unimp
 2ba:	0000                	unimp
 2bc:	0000                	unimp
 2be:	00c4                	addi	s1,sp,68
 2c0:	0000                	unimp
 2c2:	0000                	unimp
 2c4:	0000                	unimp
 2c6:	007e0007          	0x7e0007
 2ca:	2531                	addiw	a0,a0,12
 2cc:	1a31                	addi	s4,s4,-20
 2ce:	009f 0000 0000      	0x9f
	...
 2dc:	0000                	unimp
 2de:	1c00                	addi	s0,sp,560
 2e0:	0000                	unimp
 2e2:	0000                	unimp
 2e4:	0000                	unimp
 2e6:	5800                	lw	s0,48(s0)
 2e8:	0000                	unimp
 2ea:	0000                	unimp
 2ec:	0000                	unimp
 2ee:	0100                	addi	s0,sp,128
 2f0:	5a00                	lw	s0,48(a2)
 2f2:	0058                	addi	a4,sp,4
 2f4:	0000                	unimp
 2f6:	0000                	unimp
 2f8:	0000                	unimp
 2fa:	005c                	addi	a5,sp,4
 2fc:	0000                	unimp
 2fe:	0000                	unimp
 300:	0000                	unimp
 302:	0004                	0x4
 304:	9f5a01f3          	0x9f5a01f3
	...
 318:	002c                	addi	a1,sp,8
 31a:	0000                	unimp
 31c:	0000                	unimp
 31e:	0000                	unimp
 320:	003c                	addi	a5,sp,8
 322:	0000                	unimp
 324:	0000                	unimp
 326:	0000                	unimp
 328:	0001                	nop
 32a:	3c5e                	fld	fs8,496(sp)
 32c:	0000                	unimp
 32e:	0000                	unimp
 330:	0000                	unimp
 332:	5c00                	lw	s0,56(s0)
 334:	0000                	unimp
 336:	0000                	unimp
 338:	0000                	unimp
 33a:	0100                	addi	s0,sp,128
 33c:	5c00                	lw	s0,56(s0)
	...
 34e:	001c                	0x1c
 350:	0000                	unimp
 352:	0000                	unimp
 354:	0000                	unimp
 356:	0024                	addi	s1,sp,8
 358:	0000                	unimp
 35a:	0000                	unimp
 35c:	0000                	unimp
 35e:	0002                	c.slli64	zero
 360:	9f30                	0x9f30
 362:	002c                	addi	a1,sp,8
 364:	0000                	unimp
 366:	0000                	unimp
 368:	0000                	unimp
 36a:	003c                	addi	a5,sp,8
 36c:	0000                	unimp
 36e:	0000                	unimp
 370:	0000                	unimp
 372:	0008                	0x8
 374:	007e                	c.slli	zero,0x1f
 376:	3125007b          	dkmda32	zero,a0,s2
 37a:	9f1a                	add	t5,t5,t1
 37c:	003c                	addi	a5,sp,8
 37e:	0000                	unimp
 380:	0000                	unimp
 382:	0000                	unimp
 384:	005c                	addi	a5,sp,4
 386:	0000                	unimp
 388:	0000                	unimp
 38a:	0000                	unimp
 38c:	0008                	0x8
 38e:	007c                	addi	a5,sp,12
 390:	3125007b          	dkmda32	zero,a0,s2
 394:	9f1a                	add	t5,t5,t1
	...
 3ae:	0004                	0x4
 3b0:	0000                	unimp
 3b2:	0000                	unimp
 3b4:	0000                	unimp
 3b6:	0001                	nop
 3b8:	045a                	slli	s0,s0,0x16
 3ba:	0000                	unimp
 3bc:	0000                	unimp
 3be:	0000                	unimp
 3c0:	1c00                	addi	s0,sp,560
 3c2:	0000                	unimp
 3c4:	0000                	unimp
 3c6:	0000                	unimp
 3c8:	0100                	addi	s0,sp,128
 3ca:	5e00                	lw	s0,56(a2)
	...
 3dc:	0004                	0x4
 3de:	0000                	unimp
 3e0:	0000                	unimp
 3e2:	0000                	unimp
 3e4:	0010                	0x10
 3e6:	0000                	unimp
 3e8:	0000                	unimp
 3ea:	0000                	unimp
 3ec:	0002                	c.slli64	zero
 3ee:	9f30                	0x9f30
 3f0:	0010                	0x10
 3f2:	0000                	unimp
 3f4:	0000                	unimp
 3f6:	0000                	unimp
 3f8:	001c                	0x1c
 3fa:	0000                	unimp
 3fc:	0000                	unimp
 3fe:	0000                	unimp
 400:	0001                	nop
 402:	005d                	c.nop	23
	...
 410:	0000                	unimp
 412:	0400                	addi	s0,sp,512
 414:	0000                	unimp
 416:	0000                	unimp
 418:	0000                	unimp
 41a:	1400                	addi	s0,sp,544
 41c:	0000                	unimp
 41e:	0000                	unimp
 420:	0000                	unimp
 422:	0200                	addi	s0,sp,256
 424:	3000                	fld	fs0,32(s0)
 426:	149f 0000 0000      	0x149f
 42c:	0000                	unimp
 42e:	1c00                	addi	s0,sp,560
 430:	0000                	unimp
 432:	0000                	unimp
 434:	0000                	unimp
 436:	0100                	addi	s0,sp,128
 438:	5a00                	lw	s0,48(a2)
	...
 452:	0064                	addi	s1,sp,12
 454:	0000                	unimp
 456:	0000                	unimp
 458:	0000                	unimp
 45a:	0001                	nop
 45c:	645a                	ld	s0,400(sp)
 45e:	0000                	unimp
 460:	0000                	unimp
 462:	0000                	unimp
 464:	8800                	0x8800
 466:	0000                	unimp
 468:	0000                	unimp
 46a:	0000                	unimp
 46c:	0100                	addi	s0,sp,128
 46e:	6500                	ld	s0,8(a0)
 470:	0088                	addi	a0,sp,64
 472:	0000                	unimp
 474:	0000                	unimp
 476:	0000                	unimp
 478:	00a4                	addi	s1,sp,72
 47a:	0000                	unimp
 47c:	0000                	unimp
 47e:	0000                	unimp
 480:	0004                	0x4
 482:	9f5a01f3          	0x9f5a01f3
 486:	00a4                	addi	s1,sp,72
 488:	0000                	unimp
 48a:	0000                	unimp
 48c:	0000                	unimp
 48e:	0114                	addi	a3,sp,128
 490:	0000                	unimp
 492:	0000                	unimp
 494:	0000                	unimp
 496:	0001                	nop
 498:	0065                	c.nop	25
	...
 4ae:	0000                	unimp
 4b0:	6400                	ld	s0,8(s0)
 4b2:	0000                	unimp
 4b4:	0000                	unimp
 4b6:	0000                	unimp
 4b8:	0100                	addi	s0,sp,128
 4ba:	5b00                	lw	s0,48(a4)
 4bc:	0064                	addi	s1,sp,12
 4be:	0000                	unimp
 4c0:	0000                	unimp
 4c2:	0000                	unimp
 4c4:	0090                	addi	a2,sp,64
 4c6:	0000                	unimp
 4c8:	0000                	unimp
 4ca:	0000                	unimp
 4cc:	0001                	nop
 4ce:	00009067          	0x9067
 4d2:	0000                	unimp
 4d4:	0000                	unimp
 4d6:	a400                	fsd	fs0,8(s0)
 4d8:	0000                	unimp
 4da:	0000                	unimp
 4dc:	0000                	unimp
 4de:	0400                	addi	s0,sp,512
 4e0:	f300                	sd	s0,32(a4)
 4e2:	5b01                	li	s6,-32
 4e4:	a49f 0000 0000      	0xa49f
 4ea:	0000                	unimp
 4ec:	1400                	addi	s0,sp,544
 4ee:	0001                	nop
 4f0:	0000                	unimp
 4f2:	0000                	unimp
 4f4:	0100                	addi	s0,sp,128
 4f6:	6700                	ld	s0,8(a4)
	...
 510:	0064                	addi	s1,sp,12
 512:	0000                	unimp
 514:	0000                	unimp
 516:	0000                	unimp
 518:	0001                	nop
 51a:	645c                	ld	a5,136(s0)
 51c:	0000                	unimp
 51e:	0000                	unimp
 520:	0000                	unimp
 522:	7800                	ld	s0,48(s0)
 524:	0000                	unimp
 526:	0000                	unimp
 528:	0000                	unimp
 52a:	0100                	addi	s0,sp,128
 52c:	5900                	lw	s0,48(a0)
 52e:	00a4                	addi	s1,sp,72
 530:	0000                	unimp
 532:	0000                	unimp
 534:	0000                	unimp
 536:	00dc                	addi	a5,sp,68
 538:	0000                	unimp
 53a:	0000                	unimp
 53c:	0000                	unimp
 53e:	0001                	nop
 540:	e459                	bnez	s0,5ce <_start-0x60fffa32>
 542:	0000                	unimp
 544:	0000                	unimp
 546:	0000                	unimp
 548:	1400                	addi	s0,sp,544
 54a:	0001                	nop
 54c:	0000                	unimp
 54e:	0000                	unimp
 550:	0100                	addi	s0,sp,128
 552:	5900                	lw	s0,48(a0)
	...
 56c:	0064                	addi	s1,sp,12
 56e:	0000                	unimp
 570:	0000                	unimp
 572:	0000                	unimp
 574:	0001                	nop
 576:	645d                	lui	s0,0x17
 578:	0000                	unimp
 57a:	0000                	unimp
 57c:	0000                	unimp
 57e:	8000                	0x8000
 580:	0000                	unimp
 582:	0000                	unimp
 584:	0000                	unimp
 586:	0100                	addi	s0,sp,128
 588:	6300                	ld	s0,0(a4)
 58a:	00a4                	addi	s1,sp,72
 58c:	0000                	unimp
 58e:	0000                	unimp
 590:	0000                	unimp
 592:	00e0                	addi	s0,sp,76
 594:	0000                	unimp
 596:	0000                	unimp
 598:	0000                	unimp
 59a:	0001                	nop
 59c:	0000e463          	bltu	ra,zero,5a4 <_start-0x60fffa5c>
 5a0:	0000                	unimp
 5a2:	0000                	unimp
 5a4:	1400                	addi	s0,sp,544
 5a6:	0001                	nop
 5a8:	0000                	unimp
 5aa:	0000                	unimp
 5ac:	0100                	addi	s0,sp,128
 5ae:	6300                	ld	s0,0(a4)
	...
 5c8:	0064                	addi	s1,sp,12
 5ca:	0000                	unimp
 5cc:	0000                	unimp
 5ce:	0000                	unimp
 5d0:	ff090003          	lb	zero,-16(s2)
 5d4:	649f 0000 0000      	0x649f
 5da:	0000                	unimp
 5dc:	7400                	ld	s0,40(s0)
 5de:	0000                	unimp
 5e0:	0000                	unimp
 5e2:	0000                	unimp
 5e4:	0100                	addi	s0,sp,128
 5e6:	5800                	lw	s0,48(s0)
 5e8:	00a4                	addi	s1,sp,72
 5ea:	0000                	unimp
 5ec:	0000                	unimp
 5ee:	0000                	unimp
 5f0:	00f0                	addi	a2,sp,76
 5f2:	0000                	unimp
 5f4:	0000                	unimp
 5f6:	0000                	unimp
 5f8:	0001                	nop
 5fa:	1058                	addi	a4,sp,36
 5fc:	0001                	nop
 5fe:	0000                	unimp
 600:	0000                	unimp
 602:	1400                	addi	s0,sp,544
 604:	0001                	nop
 606:	0000                	unimp
 608:	0000                	unimp
 60a:	0100                	addi	s0,sp,128
 60c:	5800                	lw	s0,48(s0)
	...
 626:	0064                	addi	s1,sp,12
 628:	0000                	unimp
 62a:	0000                	unimp
 62c:	0000                	unimp
 62e:	0002                	c.slli64	zero
 630:	9f30                	0x9f30
	...
 64a:	0064                	addi	s1,sp,12
 64c:	0000                	unimp
 64e:	0000                	unimp
 650:	0000                	unimp
 652:	0002                	c.slli64	zero
 654:	9f30                	0x9f30
 656:	00d0                	addi	a2,sp,68
 658:	0000                	unimp
 65a:	0000                	unimp
 65c:	0000                	unimp
 65e:	00d4                	addi	a3,sp,68
 660:	0000                	unimp
 662:	0000                	unimp
 664:	0000                	unimp
 666:	0001                	nop
 668:	005e                	c.slli	zero,0x17
	...
 67e:	0000                	unimp
 680:	6400                	ld	s0,8(s0)
 682:	0000                	unimp
 684:	0000                	unimp
 686:	0000                	unimp
 688:	0200                	addi	s0,sp,256
 68a:	3000                	fld	fs0,32(s0)
 68c:	b09f 0000 0000      	0xb09f
 692:	0000                	unimp
 694:	d000                	sw	s0,32(s0)
 696:	0000                	unimp
 698:	0000                	unimp
 69a:	0000                	unimp
 69c:	0200                	addi	s0,sp,256
 69e:	3000                	fld	fs0,32(s0)
 6a0:	d09f 0000 0000      	0xd09f
 6a6:	0000                	unimp
 6a8:	f400                	sd	s0,40(s0)
 6aa:	0000                	unimp
 6ac:	0000                	unimp
 6ae:	0000                	unimp
 6b0:	0d00                	addi	s0,sp,656
 6b2:	7c00                	ld	s0,56(s0)
 6b4:	0300                	addi	s0,sp,384
 6b6:	0868                	addi	a0,sp,28
 6b8:	6100                	ld	s0,0(a0)
 6ba:	0000                	unimp
 6bc:	0000                	unimp
 6be:	9f1c                	0x9f1c
	...

Disassembly of section .debug_frame:

0000000000000000 <.debug_frame>:
   0:	000c                	0xc
   2:	0000                	unimp
   4:	ffffffff          	0xffffffff
   8:	7c010003          	lb	zero,1984(sp)
   c:	0d01                	addi	s10,s10,0
   e:	0002                	c.slli64	zero
  10:	0014                	0x14
  12:	0000                	unimp
  14:	0000                	unimp
  16:	0000                	unimp
  18:	0010                	0x10
  1a:	6100                	ld	s0,0(a0)
  1c:	0000                	unimp
  1e:	0000                	unimp
  20:	001c                	0x1c
  22:	0000                	unimp
  24:	0000                	unimp
  26:	0000                	unimp
  28:	0014                	0x14
  2a:	0000                	unimp
  2c:	0000                	unimp
  2e:	0000                	unimp
  30:	002c                	addi	a1,sp,8
  32:	6100                	ld	s0,0(a0)
  34:	0000                	unimp
  36:	0000                	unimp
  38:	0040                	addi	s0,sp,4
  3a:	0000                	unimp
  3c:	0000                	unimp
  3e:	0000                	unimp
  40:	0014                	0x14
  42:	0000                	unimp
  44:	0000                	unimp
  46:	0000                	unimp
  48:	006c                	addi	a1,sp,12
  4a:	6100                	ld	s0,0(a0)
  4c:	0000                	unimp
  4e:	0000                	unimp
  50:	0068                	addi	a0,sp,12
  52:	0000                	unimp
  54:	0000                	unimp
  56:	0000                	unimp
  58:	0034                	addi	a3,sp,8
  5a:	0000                	unimp
  5c:	0000                	unimp
  5e:	0000                	unimp
  60:	00d4                	addi	a3,sp,68
  62:	6100                	ld	s0,0(a0)
  64:	0000                	unimp
  66:	0000                	unimp
  68:	007c                	addi	a5,sp,12
  6a:	0000                	unimp
  6c:	0000                	unimp
  6e:	0000                	unimp
  70:	0e44                	addi	s1,sp,788
  72:	5020                	lw	s0,96(s0)
  74:	0488                	addi	a0,sp,576
  76:	0689                	addi	a3,a3,2
  78:	0892                	slli	a7,a7,0x4
  7a:	0281                	addi	t0,t0,0
  7c:	0a78                	addi	a4,sp,284
  7e:	44c8                	lw	a0,12(s1)
  80:	44c1                	li	s1,16
  82:	44c9                	li	s1,18
  84:	48d2                	lw	a7,20(sp)
  86:	000e                	c.slli	zero,0x3
  88:	0b44                	addi	s1,sp,404
  8a:	0000                	unimp
  8c:	0000                	unimp
  8e:	0000                	unimp
  90:	000c                	0xc
  92:	0000                	unimp
  94:	ffffffff          	0xffffffff
  98:	7c010003          	lb	zero,1984(sp)
  9c:	0d01                	addi	s10,s10,0
  9e:	0002                	c.slli64	zero
  a0:	0054                	addi	a3,sp,4
  a2:	0000                	unimp
  a4:	0090                	addi	a2,sp,64
  a6:	0000                	unimp
  a8:	0150                	addi	a2,sp,132
  aa:	6100                	ld	s0,0(a0)
  ac:	0000                	unimp
  ae:	0000                	unimp
  b0:	0114                	addi	a3,sp,128
  b2:	0000                	unimp
  b4:	0000                	unimp
  b6:	0000                	unimp
  b8:	0e44                	addi	s1,sp,788
  ba:	7060                	ld	s0,224(s0)
  bc:	0488                	addi	a0,sp,576
  be:	0689                	addi	a3,a3,2
  c0:	0892                	slli	a7,a7,0x4
  c2:	0e950a93          	addi	s5,a0,233 # b0e9 <_start-0x60ff4f17>
  c6:	1096                	slli	ra,ra,0x25
  c8:	14981297          	auipc	t0,0x14981
  cc:	1699                	addi	a3,a3,-26
  ce:	189a                	slli	a7,a7,0x26
  d0:	0281                	addi	t0,t0,0
  d2:	0c94                	addi	a3,sp,592
  d4:	0a78                	addi	a4,sp,284
  d6:	48c1                	li	a7,16
  d8:	44c8                	lw	a0,12(s1)
  da:	44c9                	li	s1,18
  dc:	44d2                	lw	s1,20(sp)
  de:	44d444d3          	0x44d444d3
  e2:	44d5                	li	s1,21
  e4:	44d6                	lw	s1,84(sp)
  e6:	44d844d7          	vmadc.vxm	v9,v13,a6,v0
  ea:	44d9                	li	s1,22
  ec:	44da                	lw	s1,148(sp)
  ee:	000e                	c.slli	zero,0x3
  f0:	0b44                	addi	s1,sp,404
  f2:	0000                	unimp
  f4:	0000                	unimp
	...
